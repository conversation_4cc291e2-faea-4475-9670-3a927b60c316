<!DOCTYPE html>
<html lang="zh-CN">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" http-equiv="X-UA-Compatible" content="IE=edge,width=device-width,initial-scale=1.0,viewport-fit=cover">
        <title>
            <%= htmlWebpackPlugin.options.title %>
        </title>
        <script>
            var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
        </script>
        <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
        <% if (process.env.NODE_ENV === 'development' && htmlWebpackPlugin.options.cdn) { %>
            <% for(var css of htmlWebpackPlugin.options.cdn.css) { %>
              <link href="<%= css %>" rel="stylesheet">
            <% } %>
            <% for(var js of htmlWebpackPlugin.options.cdn.js) { %>
              <script src="<%= js %>" async></script>
            <% } %>
        <% } %>
    </head>

    <body>
        <noscript>
            <strong>Please enable JavaScript to continue.</strong>
        </noscript>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>

</html>