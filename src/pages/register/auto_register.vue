<template>
  <!-- 自定义了导航，目前没有加上导航，因为：如果有审批就不需要左上角那个返回，那个返回没什么用 因此把开启了自定义导航，
    后续需要就在pages.json中的 navigationStyle去掉 或者加上自定义导航就行了-->
  <view :style="theme.style" class="auto-register">
    <view class="auto-register-form">
      <view class="form-company">{{ queryData.organization_name || queryData.company_name }}</view>
      <u-form :model="form" :rules="rules" ref="uForm" labelPosition="top" label-width="160rpx">
        <u-form-item label="手机号" prop="phone" required>
          <u-input v-model="form.phone" clearable />
        </u-form-item>
        <u-form-item label="人员编号" prop="personNo" required>
          <u-input v-model="form.personNo" clearable />
        </u-form-item>
        <view class="person-no-tip">建议输入：手机号/工号/学生号/证件号/单位规定的其他编号</view>
        <u-form-item label="姓名" prop="name" required>
          <u-input v-model="form.name" clearable />
        </u-form-item>
        <u-form-item label="身份证号" prop="id_number" v-if="isShowIdCard">
          <u-input
            placeholder="请输入身份证号"
            border="surround"
            v-model="form.id_number"
            clearable
          ></u-input>
        </u-form-item>
        <!-- 根据后台配置是否显示所属部门 -->
        <u-form-item label="所属部门" v-if="form.register_input_department"  prop="departmentName" :required="form.register_department_required">
          <u-input
            :placeholder="form.register_department_tips"
            border="surround"
            v-model="form.departmentName"
            clearable
          ></u-input>
        </u-form-item>
      </u-form>
    </view>
    <view class="register-footer">
      <u-button
        class="regisiter-submit"
        text="确定"
        iconColor="#fff"
        type="primary"
        :color="variables.bgLinearGradient1"
        @click="submitHandle"
      ></u-button>
    </view>
    <!-- 开启审批的弹窗 -->
    <popup-layout :show.sync="showApprovePopup" title="提示" content="自注册审批已提交，需等待管理员审批。" alignCenter>
      <template slot="footer">
        <view class="popup-footer m-t-50">
          <u-button :custom-style="confirmStyle" text="确定" @click="confirmHandle"></u-button>
        </view>
      </template>
    </popup-layout>
    <!-- 未开启审批的弹窗 -->
    <popup-layout :show.sync="showNoApprovePopup" title="提示" :content="popupContent" alignCenter>
      <view class="content-box">
        <view class="title text-center m-b-30">提示</view>
        <view class="content">
          <view>自注册成功-可点击“录入人脸”进行人脸采集。</view>
          <view>说明：人脸采集仅用于刷脸支付识别。</view>
        </view>
        <view class="popup-footer m-t-60">
          <u-button :custom-style="confirmStyle" text="录入人脸" @click="gotoHandle('face')"></u-button>
          <view class="m-t-20"></view>
          <u-button :custom-style="cancelStyle" text="跳过，进入首页" @click="gotoHandle('home')"></u-button>
        </view>
      </view>
    </popup-layout>
  </view>
</template>

<script>
import { apiBookingScanRegisterPost, apiBookingApproveRegisterGetAllowRegisterPost } from '@/api/register'
import Cache from '@/utils/cache'
import { mapActions, mapGetters } from 'vuex'
import popupLayout from '@/components/popup/popup-layout'
import { encrypted } from '@/utils/aesUtil'

export default {
  components: { popupLayout },
  data() {
    return {
      queryData: {},
      form: {
        phone: '',
        personNo: '',
        name: '',
        id_number: '',
        departmentName: '', // 根据后台配置是否显示所属部门
        register_input_department: false, // 是否显示部门
        register_department_tips: '', // 提示
        register_department_required: false // 后续通过接口获取是否必填
      },
      rules: {
        phone: [
          {
            required: true,
            message: '请输入手机号',
            // 可以单个或者同时写两个触发验证方式
            trigger: ['change', 'blur']
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // uni.$u.test.mobile()就是返回true或者false的
              return uni.$u.test.mobile(value)
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur']
          }
        ],
        personNo: [
          {
            required: true,
            message: '请输入人员编号',
            // 可以单个或者同时写两个触发验证方式
            trigger: ['change', 'blur']
          }
        ],
        name: [
          {
            required: true,
            message: '请输入姓名',
            // 可以单个或者同时写两个触发验证方式
            trigger: ['change', 'blur']
          }
        ],
        id_number: [
          {
            // required: true,
            message: '请输入身份证号',
            // 可以单个或者同时写两个触发验证方式
            trigger: ['change', 'blur']
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              return value && uni.$u.test.idCard(value)
            },
            message: '身份证号不正确',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur']
          }
        ]
      },
      showNoApprovePopup: false,
      showApprovePopup: false,
      popupContent: '自注册审批已提交，需等待管理员审批。',
      confirmStyle: {
        backgroundColor: this.$variables.colorPrimary,
        color: '#fff',
        width: '50%',
        height: '65rpx'
      },
      cancelStyle: {
        width: '50%',
        height: '65rpx',
        color: '#333333',
        border: '1px solid #797979'
      },
      isShowIdCard: false
    }
  },
  computed: {
    // ...mapGetters(['platform'])
  },
  onReady() {
    // uni.hideTabBar()
    //如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    // this.$refs.uForm.setRules(this.rules)
  },
  onLoad(option) {
    // uni.hideTabBar()
    // #ifdef H5
    // this.$nextTick(_ => {
    // 	document.getElementsByTagName('uni-page-head')[0].style.display = 'none'
    // })
    // #endif
    this.queryData = option
    if (this.queryData.person_no) {
      this.form.personNo = this.queryData.person_no
    }
    if (this.queryData.name) {
      this.form.name = this.queryData.name
    }
    if (this.queryData.phone) {
      this.form.phone = this.queryData.phone
    }
    if (this.queryData.company_id) {
      this.getSetting(this.queryData.company_id)
    }
    console.log(this.queryData)
  },
  watch: {},
  onShow() {},
  methods: {
    // 验证表单
    submitHandle() {
      this.$refs.uForm
        .validate()
        .then(res => {
          // uni.$u.toast('校验通过')
          let params = {
            company_id: +this.queryData.company_id,
            // organization_id: +this.queryData.organization_id,
            person_no: this.form.personNo,
            phone: this.form.phone,
            name: this.form.name
          }
          if (this.form.id_number) {
            params.id_number = encrypted(this.form.id_number)
          }
          if (this.form.register_input_department && this.form.departmentName) {
            params.department_name = this.form.departmentName
          }
          this.sendRegisterData(params)
        })
        .catch(errors => {
          // uni.$u.toast('校验失败')
        })
    },
    async sendRegisterData(params) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      this.isLoading = true
      const [err, res] = await this.$to(apiBookingScanRegisterPost(params))
      uni.hideLoading()
      this.isLoading = false
      if (err) {
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        uni.$u.toast(res.msg)
        this.showNoApprovePopup = true
        // this.resetForm()
      } else if (res.code === 200) {
        // 200表示需要审批的情况
        this.showApprovePopup = true
        this.resetForm()
      } else {
        uni.$u.toast(res.msg)
      }
    },
    async resetForm() {
      // this.form = {
      // 	phone: '',
      // 	personNo: '',
      // 	name: ''
      // }
      this.$refs.uForm.resetFields()
      // 很奇怪，得给个延时
      await this.$sleep(100)
      this.$refs.uForm.clearValidate()
    },
    gotoHandle(type) {
      if (type === 'face') {
        this.$miRouter.push({
          path: '/pages_info/face_gather/face_gather',
          query: {
            company_id: +this.queryData.company_id,
            phone: this.form.phone,
            person_no: this.form.personNo
          }
        })
      }
      if (type === 'home') {
        this.$miRouter.push({
          path: `/pages/index/index`
        })
      }
      this.resetForm()
      this.showNoApprovePopup = false
    },
    confirmHandle() {
      this.showApprovePopup = false
      if (this.queryData.redirect) {
        this.$miRouter.replace({
          path: this.queryData.redirect
        })
      }
    },
    // 获取是否显示设置
    async getSetting (id) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      this.isLoading = true
      const [err, res] = await this.$to(apiBookingApproveRegisterGetAllowRegisterPost({
        company_id: id
      }))
      uni.hideLoading()
      this.isLoading = false
      if (err) {
        uni.$u.toast(err.message)
        return
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        this.isShowIdCard = data.allow_register_id_number
        // 获取部门字段是否必填
        this.form.register_input_department = data.register_input_department || false
        this.form.register_department_tips = data.register_department_tips || ''
        this.form.register_department_required = data.register_department_required || false
        // 动态设置部门字段的验证规则
        this.setDepartmentRule()
      } 
    },
    // 设置部门字段验证规则
    setDepartmentRule() {
      if (this.form.register_department_required) {
        // 部门必填
        this.rules.departmentName = [
          {
            required: true,
            message: '请输入所属部门',
            trigger: ['change', 'blur']
          }
        ]
      } else {
        // 部门非必填
        this.rules.departmentName = []
      }
      // 更新表单规则
      this.$nextTick(() => {
        if (this.$refs.uForm) {
          this.$refs.uForm.setRules(this.rules)
        }
      })
    }
  }
}
</script>

<style lang="scss">
.auto-register {
  .form-company {
    font-size: 36rpx;
    // font-weight: 700;
  }
  .auto-register-form {
    padding: 30rpx;
    margin-bottom: 160rpx;
    background-color: #fff;
    .person-no-tip {
      margin-left: 20rpx;
      font-size: 20rpx;
      color: #7f7f7f;
    }
    ::v-deep.u-form-item__body__left__content__required {
      position: relative;
      left: 0;
    }
  }

  .register-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    right: 0;
    padding: 40rpx;
    background-color: #fff;
  }
}
</style>
