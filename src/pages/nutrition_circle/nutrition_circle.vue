<template>
  <view :style="theme.style" :class="['nutrition-wrapper', platform === 'psbc' ? 'p-t-60' : '']">
    <view v-if="platform === 'psbc'" class="navbar">
      <u-navbar title="营养圈" :fixed="true" @leftClick="gotoIndexPage"></u-navbar>
    </view>
    <view class="nutrition-circle">
      <view class="header-wrapper">
        <view class="header-wrapper-firstLabel">
          <u-row justify="space-between" gutter="32">
            <u-col span="4">
              <view class="breakfast" @click="clickThreeMealRecommended('breakfast')">
                <text>营养早餐</text>
                <text>BREAKFAST</text>
              </view>
            </u-col>
            <u-col span="4">
              <view class="lunch" @click="clickThreeMealRecommended('lunch')">
                <text>健康午餐</text>
                <text>LUNCH</text>
              </view>
            </u-col>
            <u-col span="4">
              <view class="dinner" @click="clickThreeMealRecommended('dinner')">
                <text>低卡晚餐</text>
                <text>DINNER</text>
              </view>
            </u-col>
          </u-row>
          <u-row justify="space-between" gutter="32">
            <u-col span="6">
              <view class="checkFood" @click="goToIngredient">
                <text>了解食物营养</text>
                <u-button shape="circle" color="#FFC634" text="查食材" size="small"></u-button>
              </view>
            </u-col>
            <u-col span="6">
              <view class="recognizeFood" @click="goToRecognition">
                <text>菜品识别</text>
                <u-button shape="circle" color="#6EDA3C" text="去识别" size="small"></u-button>
              </view>
            </u-col>
          </u-row>
        </view>
        <view class="header-wrapper-secondLabel">
          <view class="recipeRecommendation">
            <view class="recipeRecommendation-header flex">
              <view class="recipeRecommendation-header-title">食谱推荐</view>
              <view class="recipeRecommendation-header-more flex" @click="goToPage">
                更多食谱
                <u-icon name="arrow-right" color="#8F9295" size="24"></u-icon>
              </view>
            </view>
            <view class="recipeRecommendation-content">
              <view class="recipe-plain-wrap-banner m-b-20">
                <swiper class="swiper-wrapper" :indicator-dots="false" :autoplay="true" @change="changeSwiper">
                  <swiper-item v-for="(item, i) in indexMealPlanList.data" :key="i">
                    <view
                      :style="{ backgroundImage: 'url(' + item.image + ')' }"
                      class="selected-wrapp selected-item flex row-center flex-col m-b-20"
                      @click="clickMenuPlanDetails(item)"
                    >
                      <view class="title m-b-20">{{ item.name }}</view>
                      <view class="text">{{ item.use_count ? item.use_count : 0 }}人正在使用</view>
                    </view>
                  </swiper-item>
                </swiper>
                <!-- 指示器 -->
                <view class="swiper-dot-wrapper flex flex-center">
                  <view
                    v-for="(j, k) in indexMealPlanList.data"
                    :key="k"
                    :class="['swiper-dot', currentSwiper === k ? 'active' : '']"
                  ></view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- <view class="diet-recommend header-item" @click="dietaryManagementPath">
          <view class="title">饮食推荐</view>
          <view class="text">膳食营养搭配</view>
          <view class="btn">立即查看</view>
        </view>
        <view class="health-assessment header-item" @click="healthyEvaluationPath">
          <view class="title">健康测评</view>
          <view class="text">检测个人身体状况</view>
          <view class="btn">立即查看</view>
        </view> -->
      </view>
      <view class="article-list">
        <u-tabs
          :current="currentTabs"
          :list="articleTabs"
          line-color="#12E294"
          :active-style="{ fontWeight: 500, color: '#101010' }"
          @change="tabsChange"
        ></u-tabs>

        <view class="information-lists">
          <block v-if="articleListData[tabsType] && articleListData[tabsType].list.length">
            <view
              class="information-item flex"
              v-for="(item, index) in articleListData[tabsType].list"
              :key="index"
              @click="clickArticlePath(item)"
            >
              <view class="flex-1 m-l-8 information-item-left">
                <view class="f-w-500 line-2 text-title p-r-30">{{ item.title }}</view>
                <view class="xs muted flex row-between">
                  <view class="flex text-center row-center">
                    <view class="">{{ item.author }}</view>
                  </view>
                  <!-- <view class="text-center">{{ item.release_time }}</view> -->
                  <view class="flex text-center row-center p-r-30">
                    <text class="m-l-6">{{ item.read_number }}人浏览</text>
                  </view>
                </view>
              </view>
              <u-image
                width="150rpx"
                height="112rpx"
                radius="10rpx"
                :src="
                  item.image
                    ? item.image
                    : 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/5067c24dac9548be9ebe8f29d88d3dad1688112556266.jpg'
                "
              ></u-image>
            </view>
            <view
              v-if="articleListData[tabsType].totalCount === articleListData[tabsType].list.length"
              class="text-center muted p-t-30 p-b-20"
            >
              人家也是有底线的
            </view>
          </block>
          <view class="text-center muted p-b-20 p-t-20" v-else>暂无文章推送</view>
          <!-- <u-empty class="p-b-20 p-t-20" v-else mode="data" text="暂无文章推送"></u-empty> -->
        </view>
      </view>
    </view>
    <custom-tabbar type="nutrition_circle"></custom-tabbar>
    <non-member :showMember="showMember" :content="modalContent" :show-confirm-button="showConfirmbtn" @setShowMember="setShowMember"></non-member>
  </view>
</template>

<script>
import Cache from '@/utils/cache'
import { mapGetters } from 'vuex'
import { getRect, deepClone, debounce, to } from '@/utils/util.js'
import { getApiHealthyIndexMealPlan, getApiArticleList, getApiArticleCategory } from '@/api/healthy.js'
import checkMember from '@/mixins/checkMember.js'
export default {
  data() {
    return {
      // pageNum: 1, //查询的页码
      pageSize: 10, //显示条目数,默认100
      currentTabs: 0,
      tabsType: 'is_recommend',
      articleTabs: [
        {
          name: '推荐',
          id: 'is_recommend'
        }
      ],
      articleListData: {}, // 数据
      articleList: [],
      userInfo: {},
      indexMealPlanList: {}, // 数据列表
      currentSwiper: 0,
      // flag: false
    }
  },
  mixins: [checkMember],
  computed: {
    ...mapGetters(['platform', 'noPermissions'])
  },
  onReady() {
    uni.hideTabBar()
  },
  onLoad() {
    uni.hideTabBar()
    // #ifdef H5
    this.$nextTick(_ => {
      document.getElementsByTagName('uni-page-head')[0].style.display = 'none'
    })
    // #endif
    this.getArticleCategory()
    // uni.$on('routerChange',data=>{
    //   console.log('路由', this.$Route.path)
    //   if (!data[0]&&this.$Route.path.indexOf('/pages/nutrition_circle/nutrition_circle')) {
    //     this.isPermissions()
    //   }
    // })
  },
  watch: {
    noPermissions(newVal) {
      if (!this.noPermissions && this.$Route.path === '/pages/nutrition_circle/nutrition_circle') {
        this.isPermissions()
      }
    }
  },
  onShow() {
    this.userInfo = Cache.get('userInfo')
    this.getHealthyIndexMealPlan()
  },
  destroyed(){
    uni.$off('routerChange')
  },
  methods: {
    // 轮播图
    getHealthyIndexMealPlan() {
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
			getApiHealthyIndexMealPlan()
				.then(res => {
					uni.hideLoading()
					if (res.code === 0) {
            console.log('食谱推荐', res.data)
						this.indexMealPlanList = res.data.results
						if (res.data.results.is_menu_plan) {
							this.menuPlanDetailData(res.data.results)
						}
					} else {
						uni.hideLoading()
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
    menuPlanDetailData(data) {
			let meal = {
				breakfast: {
					name: '早餐',
					totalKcal: 0,
					mealTime: '6:30-8:30',
					meal_type: 'breakfast',
					checkedFoodId: []
				},
				lunch: {
					name: '午餐',
					totalKcal: 0,
					mealTime: '11:30-13:30',
					meal_type: 'lunch',
					checkedFoodId: []
				},
				dinner: {
					name: '晚餐',
					totalKcal: 0,
					mealTime: '18:00-20:00',
					meal_type: 'dinner',
					checkedFoodId: []
				}
			}
    },
    clickMenuPlanDetails(item) {
			this.$miRouter.push({
				path: '/pages_health_pomr/menu_plan/detail',
				query: {
					id: item.id // 餐段
				}
			})
		},
    changeSwiper(e) {
			this.currentSwiper = e.detail.current
		},
    async tabsChange(e) {
      this.currentTabs = e.index
      this.tabsType = this.articleTabs[this.currentTabs].id
      if (this.articleListData[this.tabsType] && this.articleListData[this.tabsType].list.length === 0) {
        await this.getArticleList(this.tabsType)
      }
    },
    clickThreeMealRecommended(type) {
			this.$miRouter.push({
				path: '/pages_nutrition_circle/diet_recommended/three_meal_recommended',
				query: {
					meal_type: type // 餐段
				}
			})
		},
    // 健康文章
    getArticleCategory() {
      getApiArticleCategory()
        .then(res => {
          if (res.code == 0) {
            this.articleTabs = [
              {
                name: '推荐',
                id: 'is_recommend'
              },
              ...res.data.results
            ]
            this.articleTabs.forEach(v => {
              this.$set(this.articleListData, v.id, {
                totalCount: 0,
                pageNum: 1, //查询的页码
                // pageSize: 10, //显示条目数,默认100
                isLoading: false,
                isNoMoreData: false,
                top: 0,
                list: []
              })
            })
            this.getArticleList(this.tabsType)
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 健康文章
    // refresh
    async getArticleList(tabsType) {
      if (this.articleListData[tabsType].isLoading) return

      this.$showLoading({
        title: '获取咨询文章...',
        mask: true
      })
      this.articleListData[tabsType].isLoading = true
      let params = {
        page: this.articleListData[tabsType].pageNum,
        page_size: this.pageSize
      }
      if (tabsType === 'is_recommend') {
        params.is_recommend = true
      } else {
        params.categorys = [tabsType]
      }
      const [err, res] = await to(getApiArticleList(params))
      this.articleListData[tabsType].isLoading = false
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.hideLoading()
        uni.$u.toast(err.message)
        return
      }
      if (res.code == 0) {
        this.articleListData[tabsType].totalCount = res.data.count
        // 追加新获取的数据
        this.articleListData[tabsType].list = this.articleListData[tabsType].list.concat(res.data.results)
        // 如果没有更多数据，设置isNoMoreData为true
        if (res.data.results.length < this.pageSize) {
          this.articleListData[tabsType].isNoMoreData = true
        }
      } else {
        uni.$u.toast(res.msg)
      }
    },
    clickArticlePath(item) {
      this.$miRouter.push({
        path: '/pages_health/article/article_details',
        query: {
          id: item.id
        }
      })
    },
    // dietaryManagementPath() {
    //   this.$miRouter.push('/pages_nutrition_circle/diet_recommended/index')
    // },
    // healthyEvaluationPath() {
    //   // if (this.userInfo.h5_context_display) {
    //   this.$miRouter.push('/pages_health/healthy/assessment/healthy_assessment_entrance')
    //   // } else {
    //   //   this.$miRouter.push('/pages_health/healthy/diet_guide/diet_guide')
    //   // }
    // },
    gotoIndexPage() {
      this.$miRouter.pushTab({
        path: '/pages/index/index'
      })
    },
    goToPage() {
      this.$miRouter.push('/pages_nutrition_circle/diet_recommended/recipe_list')
    },
    goToRecognition() {
      this.$miRouter.push('/pages/photo_recognition/photo_recognition')
    },
    goToIngredient() {
      this.$miRouter.push('/pages_food_ingredient/food_ingredient/foodIngredient')
    }
  },
  onReachBottom() {
    // 如果没有更多数据，则不再请求下一页的数据
    if (this.articleListData[this.tabsType].isNoMoreData || this.articleListData[this.tabsType].isLoading) {
      return
    }
    // 增加页码
    this.articleListData[this.tabsType].pageNum++
    // 请求下一页数据
    this.getArticleList(this.tabsType)
  }
}
</script>

<style lang="scss">
.nutrition-wrapper {
  // height: 100%;
  position: relative;
  .u-sticky {
    top: 0 !important;
  }
  .nutrition-circle {
    padding: 40rpx 40rpx calc(40rpx + env(safe-area-inset-bottom));
    .header-wrapper {
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      // .header-item {
      //   width: 48%;
      //   height: 236rpx;
      //   background-repeat: no-repeat;
      //   background-size: cover;
      //   padding: 30rpx;
      //   .title {
      //     font-weight: bold;
      //     font-size: 32rpx;
      //   }
      //   .text {
      //     margin-top: 8rpx;
      //     font-size: 24rpx;
      //   }
      //   .btn {
      //     margin-top: 50rpx;
      //     display: inline-block;
      //     padding: 10rpx 15rpx;
      //     font-size: 24rpx;
      //     border-radius: 50rpx;
      //     color: #ffffff;
      //   }
      // }
      // .diet-recommend {
      //   background-image: url('../../static/images/nutrition_diet_recommend.png');
      //   .text {
      //     color: #7ad2ab;
      //   }
      //   .btn {
      //     background-color: #18d39b;
      //   }
      // }
      // .health-assessment {
      //   background-image: url('../../static/images/nutrition_health_assessment.png');
      //   .text {
      //     color: #4896e2;
      //   }
      //   .btn {
      //     background-color: #3f79fc;
      //   }
      // }
      .header-wrapper-firstLabel {
        .u-col {
          view {
            color: #1D201E;
            padding: 30rpx;
            height: 184rpx;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin-bottom: 20rpx;
            line-height: 100%;
            text:first-child {
              font-weight: 600;
              font-size: 32rpx;
            }
            text:last-child {
              font-size: 20rpx;
              color:#1D201E66
            }
            .u-button {
              margin-left: 0rpx;
              margin-top: 24rpx;
              width: 50%;
            }
          }
          .breakfast {
            background-image: url($imgBasePath + '/images/yyqbg3.png');
          }
          .lunch {
            background-image: url($imgBasePath + '/images/yyqbg4.png');
          }
          .dinner {
            background-image: url($imgBasePath + '/images/yyqbg5.png');
          }
          .checkFood {
            background-image: url($imgBasePath + '/images/yyqbg1.png');
          }
          .recognizeFood {
            background-image: url($imgBasePath + '/images/yyqbg2.png');
          }
        }
      }
      .header-wrapper-secondLabel {
        margin-bottom: 20rpx;
        .recipeRecommendation {
          padding: 30rpx;
          height: 340rpx;
          border-radius: 15rpx;
          background-color: #ffffff;
          .recipeRecommendation-header {
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30rpx;
            .recipeRecommendation-header-title {
              color: #1D201E;
              font-size: 30rpx;
              font-weight: 600;
              line-height: 100%;
            }
            .recipeRecommendation-header-more {
              align-items: center;
              color: #8F9295;
              font-size: 24rpx;
              font-weight: 400;
              line-height: 100%;
            }
          }
          .recipeRecommendation-content {

            .recipe-plain-wrap-banner {
              height: 220rpx;
              border-radius: 20rpx;
              overflow: hidden;
              position: relative;
              .swiper-dot-wrapper {
                position: absolute;
                bottom: 10%;
                left: 50%;
                transform: translate(-50%, -50%);
                border-radius: 200rpx;
                .swiper-dot {
                  width: 35rpx;
                  height: 7rpx;
                  border-radius: 200rpx;
                  margin: 0 4rpx;
                  transition: transform 0.3s;
                  background-color: #e3e3e3;
                  &.active {
                    width: 35rpx;
                    background-color: $color-primary;
                  }
                }
              }
              .swiper-wrapper {
                height: 100%;
                .selected-item {
                  padding: 30rpx;
                  width: 670rpx;
                  height: 250rpx;
                  box-sizing: border-box;
                  background-repeat: no-repeat;
                  background-size: cover;
                  background-position: center;
                }
                .selected-wrapp {
                  width: 100%;
                  border-radius: 10rpx;
                  .title {
                    font-size: 42rpx;
                    font-weight: 500;
                    color: #1d201e;
                  }
                  .text {
                    color: #1d201e;
                  }
                }
              }
            }
          }
        }
      }
    }
    .article-list {
      border-radius: 15rpx;
      background-color: #ffffff;
      ::v-deep .u-tabs__wrapper__nav__item {
        padding: 0 30rpx;
      }
      .information-lists {
        background-color: #ffffff;
        border-radius: 15rpx;
        .information-item {
          margin: 0 20rpx;
          padding: 20rpx 0;
          &:not(:last-of-type) {
            border-bottom: $border-base;
          }
          .information-item-left {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          }
        }
      }
    }
  }
}
</style>
