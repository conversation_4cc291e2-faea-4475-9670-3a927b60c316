<template>
  <view :style="theme.style + `background-image: url(${themeImgPath.img_login_bg});`" class="login flex flex-col">
    <!-- #ifndef MP-ALIPAY -->
    <view class="header">
      <u-navbar v-if="platform === 'psbc'" title="朴食健康" bg-color="transparent" @leftClick="exitApp"></u-navbar>
      <u-navbar v-else title="朴食健康" bg-color="transparent" :leftIconSize="loginType !== 'setPasswordR' ? '40rpx': '0'" :auto-back="loginType !== 'setPasswordR'"></u-navbar>
    </view>
    <!-- #endif -->
    <!-- 手机号码登录 -->
    <view class="phone-login flex-1 flex flex-col">
      <view class="text-center">
        <!-- <image class="logo" src="themeImgPath.img_logo"></image> -->
        <image class="logo" :src="themeImgPath.img_logo"></image>
      </view>
      <view class="form-input">
        <view v-if="loginType==='forget'" class="text-center f-w-500 font-size-42 m-b-60">忘记密码</view>
        <view v-else-if="loginType==='verify'" class="text-center f-w-500 font-size-42 m-b-60">验证手机号</view>
        <view v-else-if="loginType==='setPasswordR' || loginType==='setPasswordF'" class="text-center f-w-500 font-size-42 m-b-60">设置密码</view>
        <view v-else-if="loginType==='tip'" class="text-center f-w-500 font-size-42 m-b-60">提示</view>
        <view v-else-if="loginType==='register'" class="text-center f-w-500 font-size-42 m-b-60">新用户注册</view>
        <view v-else-if="loginType==='changePassword'" class="text-center f-w-500 font-size-42 m-b-60">修改密码</view>
        <view class="login-type-tabs " v-else>
          <div :class="['tabs-type-btn', loginType==='password'?'active-btn':'']" @click="changeTabsType('password')">密码登录</div>
          <div :class="['tabs-type-btn', loginType==='code'?'active-btn':'']" @click="changeTabsType('code')">验证码登录</div>
        </view>
        <u-form :model="formData" :rules="rules" ref="uformRef" labelWidth="60rpx">
          <u-form-item
            prop="phone"
            borderBottom
            v-if="loginType==='password' || loginType==='code' || loginType==='forget' || loginType==='register' || loginType === 'verify'"
          >
            <u-input v-model="formData.phone" border="none" placeholder="请输入手机号">
              <image slot="prefix" class="icon m-r-20 img-filter"  :src="themeImgPath.img_phone"></image>
            </u-input>
          </u-form-item>
          <u-form-item prop="code" borderBottom v-if="loginType==='code' || loginType==='forget' || loginType==='register' || loginType === 'verify'">
            <u-input v-model="formData.code" border="none" maxlength="6" placeholder="请输入验证码">
              <image slot="prefix" class="icon m-r-20 img-filter" :src="themeImgPath.img_password"></image>
            </u-input>
            <view slot="right">
              <u-code :seconds="seconds" ref="uCodeRef" @change="codeChange"></u-code>
              <u-button @click="handlerRefreshCode" type="primary" :color="variables.colorPrimary" :customStyle="customBtnStyle" plain="" size="small">{{ tips }}</u-button>
            </view>
          </u-form-item>
          <u-form-item prop="password" borderBottom v-if="loginType==='password'">
            <u-input v-model="formData.password" :password="!isShowPassword" border="none" placeholder="请输入密码">
              <image slot="prefix" class="icon m-r-20 img-filter" :src="themeImgPath.img_password"></image>
            </u-input>
            <view slot="right">
              <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_eye_gray_open" v-if="isShowPassword" name="eye-fill" @click="isShowPassword = false"></u-image>
              <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_eye_gray_hide" v-if="!isShowPassword" name="eye-off" @click="isShowPassword = true"></u-image>
            </view>
          </u-form-item>
          <!-- <u-form-item prop="oldPassword" borderBottom v-if="loginType==='changePassword'">
            <u-input v-model="formData.oldPassword" :password="!isOldShowPassword" border="none" placeholder="请输入旧密码">
              <image slot="prefix" class="icon m-r-20 img-filter" :src="themeImgPath.img_old_password"></image>
            </u-input>
            <view slot="right">
              <u-icon v-if="isOldShowPassword" name="eye-fill" @click="isOldShowPassword = false"></u-icon>
              <u-icon v-if="!isOldShowPassword" name="eye-off" @click="isOldShowPassword = true"></u-icon>
            </view>
          </u-form-item> -->
          <u-form-item prop="newPassword" borderBottom v-if="loginType==='setPasswordR' || loginType==='setPasswordF' || loginType==='changePassword'">
            <u-input v-model="formData.newPassword" :password="!isNewShowPassword" border="none" placeholder="请输入新密码">
              <image slot="prefix" class="icon m-r-20 img-filter" :src="themeImgPath.img_password"></image>
            </u-input>
            <view slot="right">
              <u-icon v-if="isNewShowPassword" name="eye-fill" @click="isNewShowPassword = false"></u-icon>
              <u-icon v-if="!isNewShowPassword" name="eye-off" @click="isNewShowPassword = true"></u-icon>
            </view>
          </u-form-item>
          <u-form-item prop="confirmPassword" borderBottom v-if="loginType==='setPasswordR' || loginType==='setPasswordF' || loginType==='changePassword'">
            <u-input v-model="formData.confirmPassword" :password="!isConfirmShowPassword" border="none" placeholder="确认密码">
              <image slot="prefix" class="icon m-r-20 img-filter" :src="themeImgPath.img_confirm_password"></image>
            </u-input>
            <view slot="right">
              <u-icon v-if="isConfirmShowPassword" name="eye-fill" @click="isConfirmShowPassword = false"></u-icon>
              <u-icon v-if="!isConfirmShowPassword" name="eye-off" @click="isConfirmShowPassword = true"></u-icon>
            </view>
          </u-form-item>
        </u-form>
        <view v-if="loginType==='tip'" class="m-t-60 xs muted text-center">
          <view class="flex flex-center m-b-40">
            <u-image width="150rpx" height="150rpx" :src="themeImgPath.img_set_password_tip"></u-image>
          </view>
          <view class="m-b-10">为了保证您的帐号安全</view>
          <view >首次登陆需设置密码</view>
        </view>
        <view v-if="loginType==='setPasswordR' || loginType==='setPasswordF' || loginType==='changePassword'" class="m-t-20 mini muted">密码长度为8-16位，必须包含大小写字母和数字</view>
        <view v-if="loginType==='setPasswordR'" class="m-t-20 mini muted">为了您的账号安全，首次登录需设置密码</view>
        <view class="forget-password" v-if="loginType==='password'">
          <view class="small-btn xs m-t-20" @click="returnLogin('register')">点击注册</view>
          <view class="xs m-t-20 muted" @click="returnLogin('forget')">忘记密码？</view>
        </view>
        <view v-if="loginType==='password' || loginType==='code'" class="m-t-40">
          <view>
            <u-button shape="circle" :color="variables.bgLinearGradient1" @click="userlogin">登录</u-button>
          </view>
          <!-- <view class="small-btn xs m-t-30" @click="loginType='register'">新用户注册</view> -->
          <!-- #ifdef H5 -->
          <!-- <view class="small-btn xs m-t-30" v-if="allowVisitorlogin" @click="getWechatValidate">直接点餐</view> -->
          <!-- #endif -->
        </view>
        <view v-if="loginType==='forget' || loginType==='verify'" class="m-t-40">
          <u-button shape="circle" :color="variables.bgLinearGradient1" @click="checkData">下一步</u-button>
        </view>
        <view v-if="loginType==='setPasswordR' || loginType==='setPasswordF'" class="m-t-40">
          <u-button shape="circle" :color="variables.bgLinearGradient1" @click="checkData">完成</u-button>
        </view>
        <view v-if="loginType==='register'" class="m-t-40">
          <u-button shape="circle" :color="variables.bgLinearGradient1" @click="checkData">注册</u-button>
        </view>
        <view v-if="loginType==='tip'" class="m-t-40">
          <u-button shape="circle" :color="variables.bgLinearGradient1" @click="loginType='setPasswordR'">设置密码</u-button>
        </view>
        <view v-if="loginType==='changePassword'" class="m-t-40">
          <u-button shape="circle" :color="variables.bgLinearGradient1" @click="checkData">确认修改</u-button>
        </view>
        <view class="small-btn xs m-t-30" v-if="loginType==='forget'||loginType==='register'||loginType==='setPasswordR'||loginType==='setPasswordF'||loginType==='tip'" @click="returnLogin('password')">返回登录</view>
      </view>
      <view v-if="agreementList.length && (loginType==='password' || loginType==='code' || loginType==='register')" class="user-agreement muted mini checkbox flex flex-center">
        <u-checkbox-group v-model="readAgreement" size="25" @change="changeAgreement">
          <view class="checkbox-wrapper">
            <u-checkbox shape="circle" name="login" :activeColor="variables.colorPrimaryLight3"></u-checkbox>
            <text>我已阅读并同意</text>
            <text :style="{color: variables.colorPrimaryLight3}" v-for="(agreement, index) in agreementList" :key="agreement.agreement_type" @click="pathAgreement(agreement, index)">《{{ agreement.agreement_type_alias }}》<text  class="muted" v-if="index !=agreementList.length-1"> 和 </text></text>
          </view>
        </u-checkbox-group>
      </view>
      <!-- #ifndef H5 -->
      <view class="flex-1"></view>
      <view class="other-login">
        <u-divider text="其他方式登录"></u-divider>
        <view class="flex flex-center">
          <!-- #ifdef MP-WEIXIN -->
          <router-link to="/pages/login/wx_login" nav-type="replace">
            <view class="wx-login flex flex-center">
              <u-icon :color="variables.colorPrimary" name="weixin-fill" size="50rpx"></u-icon>
            </view>
          </router-link>
          <!-- #endif -->
          <!-- #ifdef MP-ALIPAY -->
          <router-link to="/pages/login/mp_login" nav-type="replace">
            <view class="wx-login flex flex-center">
              <u-icon :color="variables.colorPrimary" name="zhifubao" size="50rpx"></u-icon>
            </view>
          </router-link>
          <!-- #endif -->
        </view>
      </view>
      <!-- #endif -->
      <!--新增直接点餐-->
      <!-- #ifdef H5 -->
      <view class="btn-bottom-dining flex flex-col flex-center" v-if="allowVisitorlogin">
           <view class="tip-dining-txt lg">不登录直接点餐</view>
           <view class="m-t-30 p-b-30" @click="getWechatValidate"><u-image width="108rpx" height="108rpx" :src="themeImgPath.img_login_green_dining"></u-image></view>
      </view>
      <!-- #endif -->
    </view>
    <u-modal
      :show="showAgreementTips"
      title="请阅读并同意以下条款"
      cancelText="取消"
      cancelColor="#8d8f92"
      confirmText="同意并继续"
      showCancelButton
      :confirmColor="variables.colorPrimary"
      @cancel="clickCancelButton"
      @confirm="clickConfirmButton"
    >
      <view slot="default" class="lg">
        <text>登录程序，请先阅读并同意</text>
        <text class="primary-light-3" v-for="(agreement, index) in agreementList" :key="agreement.agreement_type" @click="pathAgreement(agreement, index)">《{{ agreement.agreement_type_alias }}》</text>
      </view>
    </u-modal>
    <u-popup :show="showPopupAgreementTips" mode="bottom" :closeOnClickOverlay="false" :round="10">
      <view class="container p-40">
        <view class="text-center f-w-500 font-size-38 p-b-20">温馨提示</view>
        <view>
          <p class="p-b-20">
            亲爱的用户，我们依据相关法律法规制定了朴食服务条款
            <text class="primary" @click="pathAgreement('policy')">《用户服务协议》</text>
            和
            <text class="primary" @click="pathAgreement('policy')">《隐私政策》</text>
            ，请您在同意之前仔细阅读并充分理解相关条款，其中的重点条款已为您标注，方便您了解自己的权利。
          </p>
          <p class="normal p-b-20">
            我们将通过
            <text class="primary" @click="pathAgreement('policy')">《XX政策》</text>
            向您说明：
          </p>
          <p class="normal p-b-20">1、向您提供便捷的服务，我们会根据您的授权，收集和使用对应的必要信息；</p>
          <p class="normal p-b-20">2、为了提升您使用时的流畅性，您需要授权我们获取存储权限；</p>
          <p class="normal p-b-20">3、为了方便您查询附近门项目点，您需要授权我们获取位置权限；</p>
          <p class="normal p-b-20">4、您可以对以上信息进行访问、修改、删除以及注销账号。</p>
          <p class="normal p-b-20">5、未经您的授权同意，我们不会将以上信息共享给第三方或您未授权的其他用途。</p>
          <p class="normal p-b-20">6、如您是不满14周岁未成年人，请确保您已征得父母或其他监护人的同意。</p>
        </view>
        <view class="flex row-around">
          <u-button
            hover-class="none"
            :custom-style="customStyleBack"
            text="再想想"
            shape="circle"
            @click="showPopupAgreementTips = false"
          ></u-button>
          <u-button
            :custom-style="customStyleTo"
            text="我已阅读,本人同意"
            shape="circle"
            color="linear-gradient(90deg,  #11E69E 0%, #11E6C5 100%)"
            @click="clickPopupConfirmButton"
          ></u-button>
        </view>
      </view>
    </u-popup>
    <!-- setting -->
    <setting-popup></setting-popup>

    <!-- 客服弹窗 -->
    <CustomerServicePopup></CustomerServicePopup>
    <verify-code ref="verifyCode" :is-number="true" @success="verifyCodeSuccess" @refresh="verifyCodeRefresh"></verify-code>
  </view>
</template>

<script>
import {
  apiLoginPhone, apiSmsSend, getApiBindProjectCardUserList,setApiChangeProjectPoint, getLoginAgreementApi, 
  updateAgreementApi, apiGetWechatValidate, apiGetVisitorConfig, apiLoginAccount, apiPhoneRegister, 
  apiSetUserPassword, apiForgetPasswordValidate, apiChangeUserPassword, apiForgetSetUserPassword, getLoginVerifyCode } from '@/api/app'
import { mapActions, mapGetters, mapMutations } from 'vuex'
import Cache from '@/utils/cache'
import { encodeQuery, deepClone } from '@/utils/util'
import { exitApp } from '@/utils/psbc'
import { LOGINAGREEMENT } from '@/constants/agreement'
import CustomerServicePopup from '@/components/customer-service-popup/customer-service-popup.vue'
import md5 from 'js-md5'
import Base64 from 'base-64'
import { getDemoCompany, deleteDemoCompany } from '@/utils/demo'

export default {
  components:{
    CustomerServicePopup
  },
  data() {
    return {
      formDataCopy: {},
      formData: {
        phone: '',
        code: '',
        password: '',
        // oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      userinfo: {},
      rules: {
        phone: [
          {
            required: true,
            message: '请输入手机号码',
            trigger: ['blur', 'change']
          }
        ],
        code: [
          {
            required: true,
            message: '请输入验证码',
            trigger: ['blur', 'change']
          }
        ],
        password: [
          {
            required: true,
            message: '请输入密码',
            trigger: ['blur']
          }
        ],
        // oldPassword: [
        //   {
        //     required: true,
        //     message: '请输入旧密码',
        //     trigger: ['blur']
        //   }
        // ],
        confirmPassword: [
          {
            required: true,
            message: '请确认密码',
            trigger: ['blur']
          }
        ],
        newPassword: [
          {
            required: true,
            // message: '请输入新密码',
            validator: (rule, value, callback) => {
              let reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[a-zA-Z])(.{8,16})$/
              if (reg.test(value)) {
                callback()
              } else {
                callback(new Error('密码格式不正确'))
              }
            },
            trigger: ['blur']
          }
        ],
      },
      tips: '',
      seconds: 60,
      readAgreement: [],
      showAgreementTips: false,
      showPopupAgreementTips: false,
      customStyleTo: {
        backgroundColor: '#52da98',
        color: '#fff',
        width: '55%',
        height: '65rpx'
      },
      customStyleBack: {
        width: '30%',
        height: '65rpx',
        color: '#52da98',
        border: '1px solid #52da98'
      },
      customBtnStyle: {
        minWidth: '180rpx',
        height: '60rpx',
        lineHeight: '60rpx',
        padding: '0 10rpx'
      },
      agreementList: deepClone(LOGINAGREEMENT), // 协议列表
      allowVisitorlogin: false,
      clickVisitorlogin: false,
      loginType: '', // password密码登录，code验证码登录，forget忘记密码，setPasswordF忘记密码设置密码，register注册，setPasswordR注册设置密码，tip提示，changePassword修改密码
      isShowPassword: false,
      isOldShowPassword: false,
      isNewShowPassword: false,
      isConfirmShowPassword: false,
      resultTxtList: []
    }
  },
  computed: {
    ...mapGetters(['platform', 'codeInfo', 'isAddressVisitor']),
  },
  async onLoad(option) {
    this.userinfo = Cache.get('userInfo')
    if (option.loginType) {
      this.loginType = option.loginType
      this.formData.phone = this.userinfo.phone
    } else {
      this.loginType = 'password'
    }
    // this.showPopupAgreementTips = true
    if (this.codeInfo) {
      this.getVisitorSetting()
    }
    // #ifdef MP-WEIXIN
    this.$nextTick(() => {
      this.$refs.uformRef.setRules(this.rules);
    });
    // #endif
  },
  methods: {
    ...mapMutations(['SETCOOKIE', 'USERINFO', 'SET_SELECT']),
    ...mapActions({
      setUserInfo: 'setUserInfo',
      setProjectCardUserList: 'setProjectCardUserList',
      setMembershipRights: 'setMembershipRights',
      setCodeInfo: 'setCodeInfo',
      setAddressVisitor: 'setAddressVisitor',
      setOrganizationAppPermission: 'setOrganizationAppPermission',
      setMealVisitor: 'setMealVisitor'
    }),
    codeChange(text) {
      this.tips = text
    },
    getCode() {
      this.$refs.uformRef.validateField('phone', res => {
        if (res.length) return
        if (!this.$refs.uCodeRef.canGetCode) return
        let value = {
          value1: this.resultTxtList[0],
          value2: this.resultTxtList[1]
        }
        let keys = Base64.encode(JSON.stringify(value))
        console.log("JSON.stringify(value)", JSON.stringify(value), keys, this.resultTxtList);
        let params = {
          phone: this.formData.phone,
          code: keys
        }
        if (this.loginType === 'forget' || this.loginType === 'verify') {
          params.sms_type = 1
        } else if (this.loginType === 'register') {
          params.sms_type = 2
        }
        apiSmsSend(params).then(res => {
          this.$refs.uCodeRef.start()
        })
      })
    },
    // 设置项目点
    setChangeProjectPoint(parmas) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      setApiChangeProjectPoint(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.$u.toast('成功')
            uni.hideLoading()
            let memberCenterChannelId = Cache.get('memberCenterChannelId') || ''
              if (memberCenterChannelId) {
                this.$miRouter.replaceAll({
                  path: `/pages_member/member_center/VIP_page`
                })
              }else {
                this.$miRouter.replaceAll({
                  path: `/pages/index/index`
                })
              }
            // this.$miRouter.push({
            //   path: '/pages_info/userinfo/perfect_userinfo',
            //   query: {
            //     company_id: parmas.company_id,
            //     // user_id: parmas.user_id,
            //     person_no: parmas.person_no
            //   }
            // })
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 获取已录入相同手机号的项目点用户列表
    getBindProjectCardUserList(parmas) {
      this.$showLoading({
        title: '获取项目点....',
        mask: true
      })
      getApiBindProjectCardUserList(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.hideLoading()
            if (!res.data.length) {
              // 没有项目点直接进入绑定页面
              // this.$miRouter.push('/pages_info/userinfo/bind_items')
              // 判断用户是否从扫码点餐进来
              var codeInfo = this.codeInfo || {}
              // 有项目点公司Id
              if (codeInfo && Reflect.has(codeInfo, "codeCompanyId")) {
                this.$miRouter.push('/pages_bundle/switch_items/add_items')
              } else {
                this.$miRouter.push('/pages_info/userinfo/bind_items')
              }
            } else if (res.data && res.data.length == 1) {
              // 只有一个项目点 直接绑定 进入标签选择页面
              // 还有调一个绑定接口
              let formData = {
                company_id: res.data[0].company_id,
                person_no: res.data[0].person_no,
                name: res.data[0].name
              }
              this.setChangeProjectPoint(formData)
            } else {
              // 多个项目点 进入选择项目点页面
              this.setProjectCardUserList(res.data)
              this.$miRouter.push('/pages_info/userinfo/choice_items')
            }
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 抽出登录成功后的处理逻辑
    userLoginResult(res) {
      if (res.code == 0) {
        this.updateFaceAgreement(res.data)
        // 小程序特有的cookie处理
        // #ifdef MP-WEIXIN || MP-ALIPAY
        // let cookie = res.header['Set-Cookie']
        // this.SETCOOKIE(cookie)
        // #endif
        this.USERINFO(res.data)
        // 获取权限
        this.setMembershipRights(res.data.user_id)
        if (res.data.person_no) {
          uni.setStorageSync('companyId', res.data.company_id)
          let memberCenterChannelId = Cache.get('memberCenterChannelId') || ''
          if (memberCenterChannelId) {
            this.$miRouter.replaceAll({
              path: `/pages_member/member_center/VIP_page`
            })
          }else {
            this.$miRouter.replaceAll({
              path: `/pages/index/index`
            })
          }
        } else {
          let params = {
            company_id: res.data.company_id,
            user_id: res.data.user_id
          }
          // 获取该手机下的列表
          this.getBindProjectCardUserList(params)
        }
        // 登录成功需要清下
        deleteDemoCompany()
        // this.setUserInfo(res.data)
      } else if (res.code == 500001) {
        // 去掉cookie避免刷新自动登录
        if (this.$store.getters.cookie) {
          this.$store.dispatch('setLogOut')
        }
        uni.$u.toast(res.msg)
        this.formData.code = ''
        this.loginType='setPasswordR'
        // 去掉tip页面直接去设置密码页
        // setTimeout(() => {
        //   this.loginType='tip'
        // }, 500)
      } else {
        uni.$u.toast(res.msg)
      }
    },
    userlogin(autoLogin = false) {
      // this.$miRouter.push({
      // 	path: '/pages_info/userinfo/bind_items'
      // })
      if (this.$store.getters.cookie) {
        this.$store.dispatch('setLogOut')
      }

      if (this.isAddressVisitor) {
        this.SET_SELECT({
          key: 'address_info',
          data: {}
        })
        this.setAddressVisitor(0) // 清除游客缓存
        this.setMealVisitor(0) // 清除访客餐游客缓存
        uni.removeStorageSync('codeOpenid') // 清除游客openid
        uni.removeStorageSync('appidVisitor') // 清除游客appid
      }
      
      if(autoLogin) { // 主动登录
        let api = apiLoginAccount
        let params = {
          phone: this.formDataCopy.phone,
          password: md5(this.formDataCopy.newPassword)
        }
        
        // 添加演示使用company数据
        let demoCompanyId = getDemoCompany()
        if (demoCompanyId) {
          params.demo_company_id = +demoCompanyId
        }
        
        api(params).then((res)=> {
          this.userLoginResult(res)
        }).catch(errors => {
          uni.$u.toast(errors)
        })
      } else {
        this.$refs.uformRef
          .validate()
          .then(res => {
            if (this.agreementList.length && !this.readAgreement.length) {
              // return uni.$u.toast('请先阅读并同意相关协议')
              this.showAgreementTips = true
              return
            }
            let api
            let params = {
              phone: this.formData.phone
            }
            if (this.loginType === 'code') {
              api = apiLoginPhone
              params.code = this.formData.code
            } else if (this.loginType === 'password') {
              api = apiLoginAccount
              params.password = md5(this.formData.password)
            }
            
            // 添加演示使用company数据
            let demoCompanyId = getDemoCompany()
            if (demoCompanyId) {
              params.demo_company_id = +demoCompanyId
            }
            
            return api(params)
          })
          .then(res => {
            this.userLoginResult(res)
          })
          .catch(errors => {
            console.log(errors)
            // uni.$u.toast(errors)
          })
      }
    },
    clickCancelButton() {
      this.showAgreementTips = false
      this.clickVisitorlogin = false
    },
    clickConfirmButton() {
      this.readAgreement = ['login']
      this.showAgreementTips = false
      if (this.clickVisitorlogin) {
        this.getWechatValidate()
      }else if (this.loginType==='password' || this.loginType==='code') {
        this.userlogin()
      } else {
        this.checkData()
      }
    },
    pathAgreement(row) {
      this.$miRouter.push({
        path: '/pages_info/agreement/detail',
        query: {
          type: row.agreement_type,
          key: 'AGREEMENTLIST',
          title: row.agreement_type_alias
        }
      })
    },
    clickPopupConfirmButton() {
      this.readAgreement = ['login']
      this.showPopupAgreementTips = false
    },
    exitApp,
    // 更新协议
    updateFaceAgreement(info) {
      let params = {
        agreement_types: []
      }
      if (info.user_id) params.user_id = info.user_id
      this.agreementList.map(v => {
        params.agreement_types.push(v.agreement_type)
      })
      if (params.agreement_types.length) {
        updateAgreementApi(params)
      }
    },
    changeAgreement(e) {
      Cache.set('READAGREEMENT', e)
    },
    async getVisitorSetting() {
      await apiGetVisitorConfig({
        addr_id: this.codeInfo.codeNo
      })
        .then(res => {
          if (res.code == 0) {
            if (res.data.support_reservation) {
              this.allowVisitorlogin = true
            } else {
              this.allowVisitorlogin = false
            }
          } else {
            this.setCodeInfo(null)
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    async getWechatValidate() {
      this.clickVisitorlogin = true
      if (this.agreementList.length && !this.readAgreement.length) {
        // return uni.$u.toast('请先阅读并同意相关协议')
        this.showAgreementTips = true
        return
      }
      await apiGetWechatValidate({
        company_id: this.codeInfo.codeCompanyId
      })
        .then(res => {
          if (res.code == 0) {
            uni.setStorageSync('appidVisitor', res.data.appid)
            let redirectUrl = encodeQuery(`${res.data.redirect_uri}/pages_bundle/select/select_diner`) 
            window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.data.appid}&redirect_uri=${redirectUrl}&response_type=code&scope=snsapi_base&state=snsapi_base_get_openid#wechat_redirect`
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    changeTabsType(type) {
      this.loginType = type
    },
    checkData() {
      this.$refs.uformRef
        .validate()
        .then(res => {
          if (this.agreementList.length && !this.readAgreement.length && this.loginType==='register') {
            // return uni.$u.toast('请先阅读并同意相关协议')
            this.showAgreementTips = true
            return
          }
          let api
          let params
          if (this.loginType==='register') {
            api = apiPhoneRegister
            params = {
              phone: this.formData.phone,
              code: this.formData.code
            }
          } else if (this.loginType==='forget' || this.loginType==='verify') {
            api = apiForgetPasswordValidate
            params = {
              phone: this.formData.phone,
              code: this.formData.code
            }
          } else if (this.loginType==='setPasswordR' || this.loginType==='setPasswordF') {
            if (this.formData.newPassword !== this.formData.confirmPassword) {
              return uni.$u.toast('密码输入不一致')
            }
            api = this.loginType==='setPasswordR' ? apiSetUserPassword : apiForgetSetUserPassword
            params = {
              password: md5(this.formData.newPassword),
              password_confirm: md5(this.formData.confirmPassword),
              set_password_type: this.loginType==='setPasswordR' ? 2 : 1 // 1忘记密码 设置密码 ,2注册设置密码
            }
          } else if (this.loginType==='changePassword') {
            // if (this.formData.newPassword === this.formData.oldPassword) {
            //   return uni.$u.toast('新旧密码不能一致')
            // }
            if (this.formData.newPassword !== this.formData.confirmPassword) {
              return uni.$u.toast('新密码输入不一致')
            }
            api = apiChangeUserPassword
            params = {
              password: md5(this.formData.newPassword),
              password_confirm: md5(this.formData.confirmPassword),
              // old_password: md5(this.formData.oldPassword)
            }
          }
          return api(params)
        })
        .then(res => {
          if (res.code == 0) {
            // 保留新的账号密码来主动调起登录
            this.formDataCopy = uni.$u.deepClone(this.formData)
            
            this.formData.password = ''
            this.formData.oldPassword = ''
            this.formData.newPassword = ''
            this.formData.confirmPassword = ''
            
            if (this.loginType==='setPasswordR' || this.loginType==='setPasswordF') {
              uni.$u.toast('密码设置成功，正在登录')
              // this.loginType = 'password'
              // 主动 重新登录 进入首页
              setTimeout(()=> {
                this.userlogin(true)
              },500)
            } else if (this.loginType === 'forget') {
              this.loginType = 'setPasswordF'
            } else if (this.loginType==='changePassword') {
              uni.$u.toast('密码已修改成功, 正在登录')
              // 主动 重新登录 进入首页
              // this.loginType = 'password'
              setTimeout(()=> {
                this.userlogin(true)
              },500)
            } else if(this.loginType==='verify') {
              this.loginType = 'changePassword'
            }
          } else if (res.code == 500001) {
            // 去掉cookie避免刷新自动登录
            if (this.$store.getters.cookie) {
              this.$store.dispatch('setLogOut')
            }
            this.formData.code = ''
            // this.loginType='tip'
            this.loginType='setPasswordR'
          } else if (res.code == 1) {
            if (this.loginType==='register') {
              uni.$u.toast(res.msg)
              // this.loginType = 'password'
            } else if (this.loginType==='changePassword') {
              uni.$u.toast(res.msg)
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(errors => {
          console.log(errors)
          // uni.$u.toast(errors)
        })
    },
    // 获取图形验证码
    getVerCode(phone, flag) {
      console.log("phone", phone);
      if (!phone || !/^1[3456789]\d{9}$/.test(phone)) {
        return this.$u.toast('请输入正确的手机号码')
      }
      getLoginVerifyCode({ phone }).then(res => {
        if (res && res.code == 0) {
          let data = res.data || ''
          if (data) {
            let keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ''
            console.log("getLoginVerifyCode", keys);
            this.resultTxtList = []
            if (keys && typeof keys === 'object') {
              for (let keyName in keys) {
                this.resultTxtList.push(keys[keyName])
              }
            }
            if (this.$refs.verifyCode) {
              this.$refs.verifyCode.setResultTxt(this.resultTxtList)
              if (flag) {
                this.$refs.verifyCode.init()
              } else {
                this.$refs.verifyCode.open()
              }

            }
          }
        } else {
          uni.$u.toast(res.msg)
        }
      }).catch(err => { })
    },
    // 更新验证码啊
    handlerRefreshCode() {
      if (!this.$refs.uCodeRef.canGetCode) return
      this.getVerCode(this.formData.phone, false)
    },
    // 返回登录
    returnLogin(type) {
      this.loginType = type
    },
    verifyCodeSuccess(value) {
      console.log("verifyCodeSuccess", value);
      this.getCode()
    },
    verifyCodeRefresh() {
      this.getVerCode(this.formData.phone, true)
    }
  }
}
</script>

<style lang="scss">
page {
  padding: 0;
}

.login {
  height: 100vh;
  // background-image: url("https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/h5/fe6ad7c9869bb5f34906b7e579e029581675755680792.png");
  background-size: 100% 785rpx;
  background-repeat: no-repeat;

  .phone-login {
    .logo {
      width: 186rpx;
      height: 186rpx;
      margin-top: 44rpx;
    }

    .form-input {
      margin: 44rpx 40rpx 0;
      padding: 60rpx 40rpx 50rpx;
      border-radius: 20rpx;
      background-color: #fff;
    }

    .login-type-tabs{
      display: flex;
      margin-bottom: 30rpx;
      .tabs-type-btn{
        margin-right: 40rpx;
        color: #ADAEB0;
        font-size: 36rpx;
      }
      .active-btn{
        color: #000;
        font-weight: bold;
        position: relative;
        z-index: 88;
        &::after{
          content: '';
          position: absolute;
          bottom: 8rpx;
          left: 50%;
          transform: translate(-50%);
          display: inline-block;
          width: 70%;
          height: 8rpx;
          background: $color-primary;
          z-index: -1;
        }
      }
    }

    .other-login {
      padding: 0 40rpx calc(env(safe-area-inset-bottom) + 40rpx);

      .wx-login {
        width: 78rpx;
        height: 78rpx;
        background: #fff;
        border-radius: 50%;
      }
    }

    .small-btn{
      color: $color-primary;
      text-align: center;
      position: relative;
      z-index: 88;

    }
    .forget-password{
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .user-agreement{
      margin: 40rpx 40rpx 10rpx;
      // position: fixed;
      // bottom: 10rpx;
      // font-size: 28rpx;
    }
  }
  .checkbox {
    .checkbox-wrapper{
      display: flex;
      flex-wrap: wrap;
    }
  }
  .btn-bottom-dining {
    margin-top: 190rpx;
    .tip-dining-txt {
      color: #101010;
      width: 650rpx;
      display: flex;
    }
    .tip-dining-txt::before,
    .tip-dining-txt::after {
      content: "";
      flex:1 1;
      border-bottom: 1px solid #dde2e5;
      margin:auto;
    }
  }
 .vrcode {
  width: 120rpx;
  height: 56rpx;
 }
}
</style>
