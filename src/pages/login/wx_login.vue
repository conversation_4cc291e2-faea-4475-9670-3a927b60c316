<template>
  <view :style="theme.style" class="login flex flex-col">
    <!-- 微信登录 -->
    <view class="wx-login flex flex-col col-center">
      <view class="avatar">
        <open-data type="userAvatarUrl"></open-data>
      </view>
      <view class="user-name mt20">
        <open-data type="userNickName"></open-data>
      </view>
      
      <view class="login-btn">
        <u-button
          v-if="checkedAgreement.length"
          :key="buttonKey"
          text="一键登录"
          shape="circle"
          :color="variables.bgLinearGradient1"
          open-type="getPhoneNumber"
          @getphonenumber="getPhoneNumber"
        ></u-button>
        <u-button
          v-else
          text="一键登录"
          shape="circle"
          :color="variables.bgLinearGradient1"
          @click="showAgreementTips = true"
        ></u-button>
        <view class="primary m-t-30 text-center">
          <router-link to="/pages/login/login" nav-type="replace">
            <text>手机号登录</text>
          </router-link>
        </view>
        <!-- <button open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">一键登录</button> -->
      </view>
      <view class="flex flex-center m-t-40 muted xs checkbox">
        <u-checkbox-group v-model="checkedAgreement" @change="changeAgreement" size="25">
          <view class="checkbox-wrapper">
            <u-checkbox shape="circle" name="login" :activeColor="variables.colorPrimaryLight3"></u-checkbox>
            <text>我已阅读并同意</text>
            <text v-for="(agreement, index) in agreementList" class="primary-light-3" @click="pathAgreement(agreement, index)" :key="agreement.agreement_type" >《{{ agreement.agreement_type_alias }}》</text>
            <!-- <text style="color: #6ae3a4" @click="pathAgreement('platform_privacy')">《隐私政策》</text>
            <text style="color: #6ae3a4" @click="pathAgreement('disclaimer')">《免责声明》</text> -->
          </view>
        </u-checkbox-group>
      </view>
    </view>
    <u-modal
      :show="showAgreementTips"
      title="请阅读并同意以下条款"
      cancelText="取消"
      cancelColor="#8d8f92"
      confirmText="同意并继续"
      showCancelButton
      confirmColor="#11e69e"
      @cancel="showAgreementTips = false"
      @confirm="clickConfirmButton"
    >
      <view slot="default" class="lg">
        <text>登录程序，请先阅读并同意</text>
        <text class="primary-light-3" v-for="(agreement, index) in agreementList" :key="agreement.agreement_type" @click="pathAgreement(agreement, index)">《{{ agreement.agreement_type_alias }}》</text>
      </view>
    </u-modal>
  </view>
</template>

<script>
import { apiWechatAuth, apiWechatPhoneLogin, apiAlipayAuth, apiAlipayLoginPhone, getApiBindProjectCardUserList, setUserBindProjectPoint, getLoginAgreementApi, updateAgreementApi } from '@/api/app'
import { mapMutations, mapActions } from 'vuex'
import { LOGINAGREEMENT } from '@/constants/agreement'
import { deepClone } from '@/utils/util'
import Cache from '@/utils/cache'

export default {
  data() {
    return {
      checkedAgreement: [],
      agreementList: deepClone(LOGINAGREEMENT), // 协议列表
      showAgreementTips: false
    }
  },
  methods: {
    ...mapMutations(['SETCOOKIE', 'USERINFO', 'setProjectCardUserList']),
    // 授权码登录
    getAuthCode(params) {
      uni.getProvider({
        service: 'oauth',
        success: function (res) {
          uni.login({
            ...params,
            provider: res.provider[0],
            success: res => {
              const code = res.code
                apiWechatAuth({
                  code
                }).then(result => {
                  if (result.code !== 0) {
                    uni.$u.toast(result.msg)
                  }
                  // 获取cookie
                  // let cookie = res.header['Set-Cookie']
                  // this.SETCOOKIE(cookie)
                  // let userInfo = res.data
                  // if (userInfo.company.length) {
                  // 	userInfo.company_id = userInfo.company[0]
                  // }
                  // this.USERINFO(userInfo)
                })
            }
          })
        }
      })
    },
    // 获取一键登陆的code
    getPhoneNumber(e) {
      if (!this.checkedAgreement.length) {
        // return (this.showAgreementTips = true)
        return uni.$u.toast('请先阅读并同意相关协议')
      }
      if (e.detail && e.detail.code) {
        this.wechatPLogin(e.detail.code)
      } else {
        uni.$u.toast('获取手机号失败')
      }
    },

    // 微信一键登陆请求
    wechatPLogin(code) {
      this.$showLoading({
        title: '加载中...',
        mask: true
      })
      apiWechatPhoneLogin({
        code
      })
        .then(res => {
          uni.hideLoading()
          this.loginHandle(res)
        })
        .finally(res => {
          uni.hideLoading()
        })
    },

    // 登录结果处理
    loginHandle(res) {
      // uni.hideLoading()
      // if (res.data.company.length) {
      // 	return this.$miRouter.replaceAll({
      // 		path: '/pages/index/index'
      // 	})
      // }
      let userInfo = res.data
      this.updateFaceAgreement(res.data)
      // if (userInfo.company.length) {
      // 	userInfo.company_id = userInfo.company[0]
      // }
      this.USERINFO(userInfo)
      // this.$miRouter.replace({
      //   path: '/pages_info/userinfo/bind_items'
      // })
      this.checkInfo(res)
    },
    checkInfo(res) {
      if (res.data.person_no) {
        this.$miRouter.replaceAll({
          path: '/pages/index/index'
        })
      } else {
        let params = {
          company_id: res.data.company_id,
          user_id: res.data.user_id
        }
        // 获取该手机下的列表
        this.getBindProjectCardUserList(params)
      }
    },
    // 获取已录入相同手机号的项目点用户列表
    getBindProjectCardUserList(parmas) {
      this.$showLoading({
        title: '获取项目点....',
        mask: true
      })
      getApiBindProjectCardUserList(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.hideLoading()
            if (!res.data.length) {
              // 没有项目点直接进入绑定页面
              this.$miRouter.replace('/pages_info/userinfo/bind_items')
            } else if (res.data && res.data.length == 1) {
              // 只有一个项目点 直接绑定 进入标签选择页面
              // 还有调一个绑定接口
              let formData = {
                company_id: res.data[0].company_id,
                person_no: res.data[0].person_no,
                name: res.data[0].name
              }
              this.setUserProjectList(formData)
            } else {
              // 多个项目点 进入选择项目点页面
              this.setProjectCardUserList(res.data)
              this.$miRouter.replace('/pages_info/userinfo/choice_items')
            }
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 设置项目点
    setUserProjectList(parmas) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      setUserBindProjectPoint(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.$u.toast('成功')
            uni.hideLoading()
						this.$miRouter.replaceAll({ path: '/pages/index/index' })
            // this.$miRouter.replace({
            //   path: '/pages_info/userinfo/perfect_userinfo',
            //   query: {
            //     company_id: parmas.company_id,
            //     // user_id: parmas.user_id,
            //     person_no: parmas.person_no
            //   }
            // })
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    changeAgreement(e) {
      Cache.set('READAGREEMENT', e)
    },
    // 更新协议
    updateFaceAgreement(info) {
      let params = {
        agreement_types: []
      }
      if (info.user_id) params.user_id = info.user_id
      this.agreementList.map(v => {
        params.agreement_types.push(v.agreement_type)
      })
      if (params.agreement_types.length) {
        updateAgreementApi(params)
      }
    },
    pathAgreement(row, index) {
      // this.$miRouter.push(`/pages_info/agreement/detail?type=${type}`)
      this.$miRouter.push({
        path: '/pages_info/agreement/detail',
        query: {
          type: row.agreement_type,
          key: 'AGREEMENTLIST',
          title: row.agreement_type_alias
        }
      })
    },
    clickConfirmButton() {
      this.checkedAgreement = ['login']
      this.showAgreementTips = false
    },
  },
  onLoad() {
    let params = {}
    this.getAuthCode(params)
  }
}
</script>

<style lang="scss">
page {
  padding: 0;
}

.login {
  background-color: #fff;
  height: 100vh;
  background-size: 100% 785rpx;
  background-repeat: no-repeat;

  .wx-login {
    padding-top: 100rpx;

    .avatar {
      display: inline-block;
      width: 200rpx;
      height: 200rpx;
      border-radius: 50%;
      border: 1px solid #eee;
      overflow: hidden;
    }

    .user-name {
      margin-top: 10rpx;
      height: 60rpx;
      font-size: 42rpx;
    }

    .login-btn {
      width: 100%;
      padding: 80rpx 80rpx 20rpx;
    }
    .alipay-login{
      background-color: #11E69E;
      border-color: #11E69E;
    }
  }
  .checkbox {
    padding: 0 80rpx;
    .u-checkbox-group--row{
      flex: 1;
    }
    .checkbox-wrapper{
      display: flex;
      flex-wrap: wrap;
    }
  }
}
</style>
