<template>
  <view :style="theme.style" class="login flex flex-col">
    <!-- 微信登录 -->
    <view class="wx-login flex flex-col col-center">
      <!-- #ifdef MP-WEIXIN -->
      <view class="avatar">
        <open-data type="userAvatarUrl"></open-data>
      </view>
      <view class="user-name mt20">
        <open-data type="userNickName"></open-data>
      </view>
      
      <view class="login-btn">
        <u-button
          text="一键登录"
          shape="circle"
          :color="variables.bgLinearGradient1"
          open-type="getPhoneNumber"
          @getphonenumber="getPhoneNumber"
        ></u-button>
        <!-- <button open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">一键登录</button> -->
        <view class="primary m-t-30 text-center">
          <router-link to="/pages/login/login" nav-type="replace">
            <text>手机号登录</text>
          </router-link>
        </view>
      </view>
      <!-- #endif -->

      <!-- #ifdef MP-ALIPAY -->
      <view class="avatar">
        <u-icon v-if="!hasAuthCode" :color="variables.colorPrimary" name="account-fill" size="200rpx"></u-icon>
        <u-image v-else width="200rpx" height="200rpx" shape="circle" :src="authInfo.avatar"></u-image>
      </view>
      <view class="user-name mt20 text-center">
        <text v-if="!hasAuthCode">支付宝用户</text>
        <text v-else>{{ authInfo.nick_name }}</text>
      </view>
      <view class="login-btn">
        <button
          v-if="hasAuthCode && canIUseAuthButton"
          open-type="getAuthorize"
          @getAuthorize="getAuthorizeHandle"
          @error="onAuthError"
          scope='phoneNumber'
          type="primary"
          class="alipay-login"
        >
          获取手机号登录
        </button>
        <button
          v-if="!hasAuthCode"
          scope='phoneNumber'
          type="primary"
          class="alipay-login"
          @click="getAuthCode"
        >
          确定授权
        </button>
        <view v-if="!hasAuthCode" class="primary m-t-30 text-center">
          <router-link to="/pages/index/index" nav-type="replace">
            <text>暂不授权</text>
          </router-link>
        </view>
        <view v-if="hasAuthCode" class="primary m-t-30 text-center">
          <router-link to="/pages/login/login" nav-type="replace">
            <text>手机号登录</text>
          </router-link>
        </view>
      </view>
      <view class="flex flex-center m-t-40 muted nr checkbox">
        <u-checkbox-group v-model="checkedAgreement" @change="changeAgreement" size="25">
          <view class="checkbox-wrapper">
            <u-checkbox shape="circle" name="login" :activeColor="variables.colorPrimaryLight3"></u-checkbox>
            <text>我已阅读并同意</text>
            <text v-for="(agreement, index) in agreementList" class="primary-light-3" @click="pathAgreement(agreement, index)" :key="agreement.agreement_type" >《{{ agreement.agreement_type_alias }}》</text>
          </view>
        </u-checkbox-group>
      </view>
      <u-popup :show="showPopupAgreementTips" mode="bottom" :closeOnClickOverlay="false" :round="10">
        <view class="container p-40">
          <view class="text-center f-w-500 font-size-38 p-b-20">温馨提示</view>
          <view>
            <p class="p-b-20">
              亲爱的用户，我们依据相关法律法规制定了朴食服务条款
              <text class="primary" @click="pathAgreement('policy')">《XX政策》</text>
              和
              <text class="primary" @click="pathAgreement('policy')">《XX规则》</text>
              ，请您在同意之前仔细阅读并充分理解相关条款，其中的重点条款已为您标注，方便您了解自己的权利。
            </p>
            <p class="normal p-b-20">
              我们将通过
              <text class="primary" @click="pathAgreement('policy')">《XX政策》</text>
              向您说明：
            </p>
            <p class="normal p-b-20">1、向您提供便捷的服务，我们会根据您的授权，收集和使用对应的必要信息；</p>
            <p class="normal p-b-20">2、为了提升您使用时的流畅性，您需要授权我们获取存储权限；</p>
            <p class="normal p-b-20">3、为了方便您查询附近门项目点，您需要授权我们获取位置权限；</p>
            <p class="normal p-b-20">4、您可以对以上信息进行访问、修改、删除以及注销账号。</p>
            <p class="normal p-b-20">5、未经您的授权同意，我们不会将以上信息共享给第三方或您未授权的其他用途。</p>
            <p class="normal p-b-20">6、如您是不满14周岁未成年人，请确保您已征得父母或其他监护人的同意。</p>
          </view>
          <view class="flex row-around">
            <u-button
              hover-class="none"
              :custom-style="customStyleBack"
              text="再想想"
              shape="circle"
              @click="showPopupAgreementTips = false"
            ></u-button>
            <u-button
              :custom-style="customStyleTo"
              text="我已阅读,本人同意"
              shape="circle"
              :color="variables.bgLinearGradient1"
              @click="clickPopupConfirmButton"
            ></u-button>
          </view>
        </view>
      </u-popup>
      <u-modal
        :show="showAgreementTips"
        title="请阅读并同意以下条款"
        cancelText="取消"
        cancelColor="#8d8f92"
        confirmText="同意并继续"
        showCancelButton
        :confirmColor="variables.colorPrimary"
        @cancel="showAgreementTips = false"
        @confirm="clickConfirmButton"
      >
        <view slot="default" class="lg">
          <text>登录程序，请先阅读并同意</text>
          <text class="primary-light-3" v-for="(agreement, index) in agreementList" :key="agreement.agreement_type" @click="pathAgreement(agreement, index)">《{{ agreement.agreement_type_alias }}》</text>
        </view>
      </u-modal>
      <!-- #endif -->
    </view>
  </view>
</template>

<script>
import { apiWechatAuth, apiWechatPhoneLogin, apiAlipayAuth, apiAlipayLoginPhone, getApiBindProjectCardUserList, setUserBindProjectPoint, getLoginAgreementApi, updateAgreementApi } from '@/api/app'
import { mapMutations, mapActions } from 'vuex'
import Cache from '@/utils/cache'
import { LOGINAGREEMENT } from '@/constants/agreement'
import { deepClone } from '@/utils/util'

export default {
  data() {
    return {
      hasAuthCode: false, // 是否授权登录过
      authInfo: {}, // 拿到的授权信息
      canIUseAuthButton: false,
      checkedAgreement: [],
      showAgreementTips: false,
      showPopupAgreementTips: false,
      customStyleTo: {
        backgroundColor: '#52da98',
        color: '#fff',
        width: '55%',
        height: '65rpx'
      },
      customStyleBack: {
        width: '30%',
        height: '65rpx',
        color: '#52da98',
        border: '1px solid #52da98'
      },
      agreementList: deepClone(LOGINAGREEMENT) // 协议列表
    }
  },
  methods: {
    ...mapMutations(['SETCOOKIE', 'USERINFO', 'setProjectCardUserList']),
    // 授权码登录
    getAuthCode() {
      this.$showLoading({
        title: '加载中...',
        mask: true
      })
      let params = {}
      // #ifdef MP-ALIPAY
      params.scopes = ['auth_paycode_getuid','auth_ecard','auth_user']
      // #endif
      let _self = this
      uni.getProvider({
        service: 'oauth',
        success: function (res) {
          uni.login({
            ...params,
            provider: res.provider[0],
            success: res => {
              
              const code = res.code
              // #ifdef MP-WEIXIN
                apiWechatAuth({
                  code
                }).then(result => {
                  uni.hideLoading()
                  if (result.code === 0) {
                    _self.hasAuthCode = true
                    _self.authInfo = result.data
                  } else {
                    uni.$u.toast(result.msg)
                  }
                }).catch(err => {
                  uni.hideLoading()
                })
                // #endif

                // #ifdef MP-ALIPAY
                apiAlipayAuth({
                  auth_code: code
                }).then(result => {
                  uni.hideLoading()
                  if (result.code === 0) {
                    _self.hasAuthCode = true
                    _self.authInfo = result.data
                  } else {
                    uni.$u.toast(result.msg)
                  }
                }).catch(err => {
                  uni.hideLoading()
                })
                // #endif
            },
            fail: (err) => {
              uni.hideLoading()
            }
          })
        }
      })
    },
    // 获取一键登陆的code
    getPhoneNumber(e) {
      if (!this.checkedAgreement.length) {
        return (this.showAgreementTips = true)
        // return uni.$u.toast('请先阅读并同意相关协议')
      }
      this.wechatPLogin(e.detail.code)
    },

    // 微信一键登陆请求
    wechatPLogin(code) {
      apiWechatPhoneLogin({
        code
      })
        .then(res => {
          this.loginHandle(res)
        })
        .finally(res => {
          // uni.hideLoading()
        })
    },

    // 登录结果处理
    loginHandle(res) {
      // uni.hideLoading()
      // if (res.data.company.length) {
      // 	return this.$miRouter.replaceAll({
      // 		path: '/pages/index/index'
      // 	})
      // }
      let userInfo = res.data
      // if (userInfo.company.length) {
      // 	userInfo.company_id = userInfo.company[0]
      // }
      this.USERINFO(userInfo)
      this.$miRouter.replace({
        path: '/pages_info/userinfo/bind_items'
      })
    },
    // alipay登录
    getAuthorizeHandle() {
      if (!this.checkedAgreement.length) {
        return (this.showAgreementTips = true)
        // return uni.$u.toast('请先阅读并同意相关协议')
      }
      my.getPhoneNumber({
        success: (res) => {
          this.$showLoading({
            title: '加载中....',
            mask: true
          })
          let encryptedData = JSON.parse(res.response).response;
          apiAlipayLoginPhone({
            encrypted_data: encryptedData
            }).then(res => {
              uni.hideLoading()
              if (res.code === 0) {
              let userInfo = res.data
              this.updateFaceAgreement(res.data)
              this.USERINFO(userInfo)
              // this.$miRouter.replace({
              //   path: '/pages/index/index'
              // })
              this.checkInfo(res)
            } else {
              uni.$u.toast(res.msg)
            }
          }).catch(err => {
            uni.hideLoading()
            console.log(err)
          })
        },
        fail: (res) => {
          console.log('getPhoneNumber_fail', res);
        },
      });
    },
    checkInfo(res) {
      if (res.data.person_no) {
        this.$miRouter.replaceAll({
          path: '/pages/index/index'
        })
      } else {
        let params = {
          company_id: res.data.company_id,
          user_id: res.data.user_id
        }
        // 获取该手机下的列表
        this.getBindProjectCardUserList(params)
      }
    },
    // 获取已录入相同手机号的项目点用户列表
    getBindProjectCardUserList(parmas) {
      this.$showLoading({
        title: '获取项目点....',
        mask: true
      })
      getApiBindProjectCardUserList(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.hideLoading()
            if (!res.data.length) {
              // 没有项目点直接进入绑定页面
              this.$miRouter.replace('/pages_info/userinfo/bind_items')
            } else if (res.data && res.data.length == 1) {
              // 只有一个项目点 直接绑定 进入标签选择页面
              // 还有调一个绑定接口
              let formData = {
                company_id: res.data[0].company_id,
                person_no: res.data[0].person_no,
                name: res.data[0].name
              }
              this.setUserProjectList(formData)
            } else {
              // 多个项目点 进入选择项目点页面
              this.setProjectCardUserList(res.data)
              this.$miRouter.replace('/pages_info/userinfo/choice_items')
            }
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    // 设置项目点
    setUserProjectList(parmas) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      setUserBindProjectPoint(parmas)
        .then(res => {
          if (res.code == 0) {
            uni.$u.toast('成功')
            uni.hideLoading()
						this.$miRouter.replaceAll({ path: '/pages/index/index' })
            // this.$miRouter.replace({
            //   path: '/pages_info/userinfo/perfect_userinfo',
            //   query: {
            //     company_id: parmas.company_id,
            //     // user_id: parmas.user_id,
            //     person_no: parmas.person_no
            //   }
            // })
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    onAuthError(err) {
      console.log('getAuthorize_fail', err)
    },
    clickConfirmButton() {
      this.checkedAgreement = ['login']
      this.showAgreementTips = false
    },
    pathAgreement(row, index) {
      this.$miRouter.push({
        path: '/pages_info/agreement/detail',
        query: {
          type: row.agreement_type,
          key: 'AGREEMENTLIST',
          title: row.agreement_type_alias
        }
      })
    },
    // 更新协议
    updateFaceAgreement(info) {
      let params = {
        agreement_types: []
      }
      if (info.user_id) params.user_id = info.user_id
      this.agreementList.map(v => {
        params.agreement_types.push(v.agreement_type)
      })
      if (params.agreement_types.length) {
        updateAgreementApi(params)
      }
    },
    changeAgreement(e) {
      Cache.set('READAGREEMENT', e)
    },
    clickPopupConfirmButton() {
      this.checkedAgreement = ['login']
      this.showPopupAgreementTips = false
    }
  },
  onLoad() {
    // #ifdef MP-ALIPAY
    this.canIUseAuthButton = my.canIUse('button.open-type.getAuthorize')
    // #endif
    // this.showPopupAgreementTips = true

    this.getAuthCode()
  }
}
</script>

<style lang="scss">
page {
  padding: 0;
}

.login {
  background-color: #fff;
  height: 100vh;
  background-size: 100% 785rpx;
  background-repeat: no-repeat;

  .wx-login {
    padding-top: 100rpx;

    .avatar {
      display: inline-block;
      width: 200rpx;
      height: 200rpx;
      border-radius: 50%;
      border: 1px solid #eee;
      overflow: hidden;
    }

    .user-name {
      margin-top: 10rpx;
      height: 60rpx;
      font-size: 42rpx;
    }

    .login-btn {
      width: 100%;
      padding: 80rpx 80rpx 40rpx;
    }
    .alipay-login{
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 200rpx;
      font-size: 28rpx;
      background-color: #11E69E;
      border-color: #11E69E;
      &:active{
        color: #fff;
        opacity: .7;
      }
    }
  }
  .checkbox {
    padding: 0 80rpx;
    .u-checkbox-group--row{
      flex: 1;
    }
    .checkbox-wrapper{
      display: flex;
      flex-wrap: wrap;
    }
  }
}
</style>
