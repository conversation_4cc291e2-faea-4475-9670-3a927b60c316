<template>
	<view :style="theme.style" :class="['camera-box', platform === 'psbc' ? 'p-t-60' : '', 'flex']">
		<!-- #ifndef MP-ALIPAY -->
    <view class="header">
      <u-navbar v-if="platform === 'psbc'" title="菜品识别" bg-color="transparent" @leftClick="gotoCirclePage"></u-navbar>
      <u-navbar v-else title="菜品识别" bg-color="transparent" auto-back></u-navbar>
    </view>
    <!-- #endif -->
		<!-- <view class="title">欢迎使用拍照记录饮食</view> -->
		<!-- 		<view class="image-box">
			<image
				class="img"
				src="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/80dd26566ca5b22cc7ea4685dc8ea8dd1671883929795.png"
			></image>
		</view> -->
		<!-- Footer Start -->
		<view class="footer">
			<u-button
				:text="item.name"
				v-for="(item, index) in tabsData"
				:key="index"
				:color="index === 0 ? variables.colorPrimaryLight13:variables.colorPrimary"
				shape="circle"
				@click="select(item)"
				>
			</u-button>
			<!-- <view class=" tabs">
				<block v-for="(item, index) in tabsData" :key="index">
					<view class="footer-content flex row-between col-center p-r-40 p-l-40 m-b-20" @click="select(item)">
						<view class="flex col-center">
							<image :src="item.img"></image>
							<view class="p-l-20" style="font-size:36rpx">{{ item.name }}</view>
						</view>
						<u-icon name="arrow-right" color="#8f9295" size="28"></u-icon>
					</view>
				</block>
				<view class="record-text flex col-center row-center" @click="clickManualRecord">
					<text>手动记录</text>
					<u-icon name="arrow-right" color="#fff" size="28"></u-icon>
				</view>
			</view> -->
		</view>
		<!-- #ifdef MP-WEIXIN || MP-ALIPAY -->
		<!-- <u-action-sheet
			:actions="photoList"
			:closeOnClickOverlay="true"
			:closeOnClickAction="true"
			@close="photoListShow = false"
			cancelText="取消"
			:show="photoListShow"
			safeAreaInsetBottom
			@select="selectPhotoList"
		></u-action-sheet> -->
		<!-- #endif -->

		<!-- Footer End -->
		<!-- <custom-tabbar type="photo_recognition"></custom-tabbar> -->
	</view>
</template>

<script>
import Cache from '@/utils/cache'
import { compressImage } from '@/utils/file.js'
import { mapGetters } from 'vuex'
// #ifdef H5
import jweixin from 'jweixin-module'
// #endif
import { getApiWechatCongfigGet } from '@/api/app.js'

export default {
	data() {
		return {
			photoListShow: false,
			// photoList: [
			// 	{
			// 		name: '拍照',
			// 		type: 'camera'
			// 	},
			// 	{
			// 		name: '从手机相册选择',
			// 		type: 'album'
			// 	}
			// ],
			clsData: {},
			userinfo: {},
			src: '', //拍照后图像路径(临时路径)
			tabsData: [
				// {
				// 	name: '菜品识别',
				// 	// img: require('@/static/icons/canteen_caipu.png'),
				// 	img: this.$imgPath.img_canteen_caipu,
				// 	cls: 'food'
				// },
				// {
				// 	name: '果蔬识别',
				// 	// img: require('@/static/icons/photho1.png'),
				// 	img: this.$imgPath.img_photho1,
				// 	cls: 'fruit'
				// },
				// {
				// 	name: '零食识别',
				// 	// img: require('@/static/icons/photho2.png'),
				// 	img: this.$imgPath.img_photho2,
				// 	cls: 'snack'
				// }
				{
					name: '菜品识别',
					cls: 'food',
					type: ['album', 'camera']
				},
				// {
				// 	name: '拍菜品图',
				// 	cls: 'food',
				// 	type: 'camera'
				// }
			]
		}
	},
	computed: {
		...mapGetters(['platform'])
	},
	onReady() {
		// uni.hideTabBar()
	},
	onShow() {},
	onLoad() {
		// uni.hideTabBar()
		// #ifdef H5
		// this.$nextTick(_ => {
		// 	document.getElementsByTagName('uni-page-head')[0].style.display = 'none'
		// })
		// #endif
		this.$getUserInfo()
		this.userinfo = Cache.get('userInfo')
		this.getWechatCongfigGet()
	},
	methods: {
		async getWechatCongfigGet() {
			let params = {
				appid: uni.getStorageSync('appid') || Cache.get('userInfo').appid,
				company_id: Cache.get('userInfo').company_id,
				url: window.location.href.split('#')[0]
			}
			let res = null
			try {
				res = await getApiWechatCongfigGet(params)
				uni.hideLoading()
				if (res.code == 0) {
					if (res.data) {
						// #ifdef H5
						jweixin.config({
							beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
							debug: false,
							appId: res.data.appid,
							timestamp: res.data.timestamp,
							nonceStr: res.data.noncestr,
							signature: res.data.signature,
							jsApiList: ['chooseImage', 'previewImage', 'uploadImage', 'downLoadImage'] // 初始化
						})
						jweixin.checkJsApi({
							jsApiList: ['chooseImage'], // 需要检测的JS接口列表，所有JS接口列表见附录2,
							success: function(res) {
							}
						});
						jweixin.error(function (res) {
						});
						// #endif
					}
				} else {
					uni.$u.toast(res.msg)
				}
		   } catch (error) {
				uni.hideLoading()
				uni.$u.toast(error.message)
			}
		},
		select(item) {
			this.clsData = item
			// // #ifdef H5
			this.takePhoto(item.type)
			// // #endif
			// //  #ifdef MP-WEIXIN || MP-ALIPAY
			// this.photoListShow = true
			// // #endif
		},
		selectPhotoList(data) {
			this.takePhoto(data.type)
		},
		// 拍照按钮
		takePhoto(type) {
			const that = this
			// let sourceType = []
			// switch (type) {
			// 	case 'album':
			// 		sourceType.push('album')
			// 		break
			// 	case 'camera':
			// 		sourceType.push('camera')
			// 		break
			// 	default:
			// 		break
			// }
			// if (this.platform === 'wechat') {
			// 	jweixin.ready(() => {
			// 		jweixin.chooseImage({
			// 			count: 1, // 默认9
			// 			sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
			// 			sourceType: sourceType, // 可以指定来源是相册还是相机，默认二者都有
			// 			success: function (data) {
			// 				console.log('res', data)
			// 				if (data.localIds && data.localIds.length) {
			// 					that.src = data.localIds[0] //tempImagePath为api返回的照片路径
			// 					that.$miRouter.push({
			// 						path: '/pages/photo_recognition/result',
			// 						query: {
			// 							src: that.src,
			// 							cls: that.clsData.cls,
			// 							//  #ifdef MP-WEIXIN || MP-ALIPAY
			// 							photoType: 'MPWXALI'
			// 							// #endif
			// 						}
			// 					})
			// 				}
			// 			},
			// 			fail:function(error){
			// 				console.log("error",error);
			// 			},
						
			// 		})
			// 	})
			// } else {
				
			// }
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: type,
				success(data) {
					// let resSize = res.tempFiles[0].size
					// if (resSize > 5242880) return uni.$u.toast('上传的图片大小不超过5M')
					if (data.tempFilePaths && data.tempFilePaths.length) {
						that.src = data.tempFilePaths[0] //tempImagePath为api返回的照片路径
						Cache.set('photoRecognition', {
							src: that.src,
							cls: that.clsData.cls,
							//  #ifdef MP-WEIXIN || MP-ALIPAY
							photoType: 'MPWXALI'
							// #endif
						})
						that.$miRouter.push({
							path: '/pages/photo_recognition/result'
						})
					}
				}
			})
		},
		clickManualRecord(){
			this.$miRouter.push({
				path: '/pages_health/record_food/manual_record'
			})
		},
		gotoCirclePage() {
			this.$miRouter.pushTab({
				path: '/pages/nutrition_circle/nutrition_circle'
			})
		}
	}
}
</script>

<style lang="scss" scope>
page {
	height: 100%;
	background-image: url($imgBasePath + '/images/recognizeBg1.png');
	background-repeat: no-repeat;
	background-position: center -100rpx;
	background-size: 100% auto;
}
.camera-box {
	// position: relative;
	/* #ifdef H5 */
	height: calc(100vh - (100rpx + env(safe-area-inset-bottom)));
	/* #endif */
	/* #ifndef H5 */
	height: calc(50rpx - env(safe-area-inset-bottom));
	/* #endif */
	// height: 100vh;
	flex-direction: column;
	justify-content: space-between;
	.title {
		padding-top: 90rpx;
		font-size: 48rpx;
		color: #fff;
		text-align: center;
	}
	.image-box {
		.img {
			width: 100%;
			/* #ifdef H5 */
			height: 53vh;
			// height: calc(100vh - (400rpx + 90rpx + env(safe-area-inset-bottom)));
			/* #endif */
			/* #ifndef H5 */
			height: 838rpx;
			// height:  calc(100vh - (400rpx + 50rpx + env(safe-area-inset-bottom)));
			/* #endif */
		}
	}
	// 底部
	.footer {
		position: fixed;
		bottom: 12%;
		width: 100%;
		padding: 0rpx 40rpx;
		// box-sizing: border-box;
		border-radius: 20rpx 20rpx 0 0;
		.u-button {
			margin-bottom: 48rpx;
		}
		.u-button:last-child {
			margin-bottom:  0rpx;
		}
		
	}
}
</style>
