<template>
	<view :style="theme.style" class="result">
		<!-- #ifndef MP-ALIPAY -->
		<view class="header">
			<u-navbar v-if="platform === 'psbc'" title="识别结果" bg-color="transparent" @leftClick="$miRouter.back()"></u-navbar>
			<u-navbar v-else title="识别结果" bg-color="transparent" @leftClick="$miRouter.back()"></u-navbar>
		</view>
		<!-- #endif -->
		<!-- 识别中的页面 -->
		<template v-if="!src&&pageLoading!==0||pageLoading === 4">
			<view class="wrappre-img-box">
				<!-- :style="{ left: '50%', top: navStyle.top + 'px' }" -->
				<!-- height="450rpx" width="750rpx" -->
				<div class="wrappre-img" :style="{ backgroundImage: 'url(' + recognitionImgBase64 + ')', backgroundSize: '175% 100%', height: '100vh', backgroundRepeat: 'no-repeat', backgroundPositionX: '50%' }"></div>
				<!-- <div class="img-box-wrapp">
					<u-image class="img" height="300rpx" width="330rpx" :src="recognitionImgBase64"></u-image>
				</div> -->
				<!-- Loading -->
				<view class="pageLoading flex" v-if="pageLoading === 1">
					<u-image class="flex flex-center m-b-20" width="300rpx" height="300rpx" src="https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/icons/recognizeLoading.png"></u-image>
					<u-loading-icon size="100"></u-loading-icon>
				</view>
				<view class="pageLoading flex" v-else-if="pageLoading === 2">
					<u-popup :show="true" mode="center" @close="close" :round="20">
						<view class="tip flex">
							<view class="tip-icon flex row-center">
								<u-image width="140rpx" height="140rpx" :src="themeImgPath.img_recognizeTip"></u-image>
							</view>
							<view class="tip-content flex row-center">
								<text>正在努力识别中</text>
								<text>识别太久，可换个角度重新拍</text>
							</view>
							<view class="tip-button">
								<u-button type="primary" shape="circle" text="返回" @click="$miRouter.back()"></u-button>
							</view>
						</view>
					</u-popup>
				</view>
				<view class="flex" v-else-if="pageLoading === 3">
					<u-popup :show="true" mode="bottom" :round="20">
						<view class="fail-view p-30 flex flex-col col-center">
							<view class="lg m-b-40">
								无法识别
							</view>
							<u-image width="300rpx" height="300rpx" :src="themeImgPath.img_re_identify" class="m-b-40"></u-image>
							<view class="xs muted m-b-40">请聚焦食物或换个角度重新上传试试</view>
							<view class="footer m-t-30 flex row-around">
							<view><u-button @click="goToPhotoRecognition" text="返回" shape="circle" :plain="true" type="primary"></u-button></view>
							<view>
								<u-button
									:text="item.name"
									v-for="(item, index) in tabsData"
									:key="index"
									:color="index === 0 ? variables.colorPrimaryLight13:variables.colorPrimary"
									shape="circle"
									@click="select(item)"
									>
								</u-button>
							</view>
						</view>
						</view>
					</u-popup>
				</view>
			</view>
		</template>
		<!-- 识别后的页面 -->
		<template v-if="pageLoading === 0">
			
			<view class="swiper-part">
				<!-- 轮播图（新） -->
				<swiper
					class="swiper-bg"
					:indicator-dots="true"
					:style="{height: screenHeight}"
					:indicator-active-color="variables.colorPrimary"
					@change="swiperChange">
					<swiper-item
						v-for="(item, index) in resultFood.slice (0, 5)" :key="index"
						:style="{ backgroundImage: 'url(' + item.img_url + ')', backgroundSize: '148% 65%', backgroundRepeat: 'no-repeat', backgroundPositionX: '50%', }">
					</swiper-item>
				</swiper>
				<view v-if="sequence === 4" class="feedback flex row-center bg-white" @click="goToFeedBack">
					<text class="nr">纠正反馈</text>
					<u-icon name="arrow-right" size="12"></u-icon>
				</view>
				<view class="swiper-bottom flex flex-col" :style="{bottom: 0}">
					<view class="swiper-img flex row-center col-center m-b-60">
						<u-image :showLoading="true" shape="circle" :src="resultFood[sequence].img_url" width="112rpx" height="112rpx"></u-image>
						<text class="xxl"> {{ getResultFood(resultFood[sequence])  }}</text>
					</view>
					<view class="swiper-details flex flex-col col-center bg-white p-30">
						<view class="m-b-40 lg">
							每100克营养含量
						</view>
						<view class="detail-content flex">
							<u-row justify="space-between" gutter="10">
								<u-col span="3">
									<view class="detail-item flex flex-col col-center">
										<u-image class="m-t-10 m-b-10" shape="circle" :src="themeImgPath.img_heat" width="136rpx" height="136rpx"></u-image>
										<text class="nr m-t-10">{{ nutritionInfo.heat }}kcal</text>
										<text class="xs m-t-10">能量</text>
									</view>
								</u-col>
								<u-col span="3">
									<view class="detail-item flex flex-col col-center">
										<circle-progress
											:active-color="'#99DBFF'"
											:inactiveColor="'#EBF8FF'"
											width="150"
											:border-width="7"
											:percent="nutritionInfo.proteinPercentage"
											:subtitle="`${nutritionInfo.proteinPercentage}%`"
											:bgShow="false"
										></circle-progress>
										<text class="nr m-t-10">{{ nutritionInfo.proteinPercentageValue }}g</text>
										<text class="xs m-t-10">蛋白质</text>
									</view>
								</u-col>
								<u-col span="3">
									<view class="detail-item flex flex-col col-center">
										<circle-progress
											:active-color="'#FCA17A'"
											:inactiveColor="'#FEECE4'"
											width="150"
											:border-width="7"
											:percent="nutritionInfo.fatPercentage"
											:subtitle="`${nutritionInfo.fatPercentage}%`"
											:bgShow="false"
										></circle-progress>
										<text class="nr m-t-10">{{ nutritionInfo.fatPercentageValue }}g</text>
										<text class="xs m-t-10">脂肪</text>
									</view>
								</u-col>
								<u-col span="3">
									<view class="detail-item flex flex-col col-center">
										<circle-progress
											:active-color="'#B09CFF'"
											:inactiveColor="'#EFEBFF'"
											width="150"
											:border-width="7"
											:percent="nutritionInfo.carbohydratePercentage"
											:subtitle="`${nutritionInfo.carbohydratePercentage}%`"
											:bgShow="false"
										></circle-progress>
										<text class="nr m-t-10">{{ nutritionInfo.carbohydratePercentageValue }}g</text>
										<text class="xs m-t-10">碳水化合物</text>
									</view>
								</u-col>
							</u-row>
						</view>
						<view class="footer m-t-80 flex row-around m-b-60">
							<view><u-button @click="takePhoto(['album', 'camera'])" text="重新上传" shape="circle" :plain="true" type="primary"></u-button></view>
							<view><u-button @click="clickDietRecord" text="饮食记录" shape="circle" :color="variables.colorPrimaryLight3"></u-button></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 轮播图 -->
			<!-- <view class="swiper-3d" :style="{ backgroundImage: 'url(' + resultFood[sequence].img_url + ')', backgroundSize: '175% 100%', height: '100vh', backgroundRepeat: 'no-repeat', backgroundPositionX: '50%' }" v-if="false">
				<swiper
					class="s-container"
					:indicator-dots="true"
					:previous-margin="mpStatus ? '' : '140rpx'"
					:next-margin="mpStatus ? '' : '140rpx'"
					:current="swiperCurrent"
					:duration="500"
					indicator-active-color="#6be0a7"
					@change="swiperChange"
				>
					<swiper-card class="swiper-item" v-for="(item, index) in resultFood" :key="index">
						<image
							:class="{ 'item-img': true, 'img-error': item.type == 'error' }"
							v-if="resultFood.length"
							:src="item.img_url"
							ref="image"
						></image>
						<view class="img-error-text" v-if="item.type == 'error'">
							<div>以上结果</div>
							<div>都不是</div>
							<view class="p-t-20 flex flex-center">
								<u-button
									@click="errorCorrectionShow = true"
									shape="circle"
									text="纠错反馈"
									hover-class="none"
									:custom-style="customStyleErrorCorrection"
								></u-button>
							</view>
						</view>
						<view class="img-error-text" v-if="item.type == 'padding'">
							<view class="flex flex-center m-b-20"><u-icon name="more-dot-fill" color="#51db98" size="40px"></u-icon></view>
							识别中...
						</view>
						<view class="text line-1 m-b-20" v-if="item.type == 'error'">{{ item['食品中文名'] }}</view>
					</swiper-card>
				</swiper>
			</view> -->
			<!-- 识别结果 -->
			<!-- <view class="result-wrapper-box" v-if="false">
				<view class="result-wrapper">
					<view class="result" v-if="imgType !== 'error' && resultFood && resultFood.length >= 2">
						<view class="icon-box flex flex-center"><u-icon name="arrow-up" color="#51db98"></u-icon></view>
						<view class="result-title">识别结果</view>
						<view class="result-box flex row-between">
							<view class="result-content p-r-10">
								<view class="result-title" style="padding: 20rpx 0 10rpx 0">{{ nutritionInfo.foodName }}</view>
								<view class="category">
									食物类型：
									<text class="text">{{ nutritionInfo.foodType }}</text>
								</view>
								<view class="category">
									食物别名：
									<text class="text">{{ nutritionInfo.foodAlias }}</text>
								</view>
							</view>
							<view class="result-img-box flex flex-center">
								<view class="img-box">
									<image class="img" v-if="recognitionImgBase64" :src="recognitionImgBase64" ref="image"></image>
									<text class="tips">原图</text>
								</view>
								<view class="contrast">VS</view>
								<view class="img-box">
									<image class="img" v-if="nutritionInfo.img_url" :src="nutritionInfo.img_url" ref="image"></image>
									<text class="tips tips-result">识别结果</text>
								</view>
							</view>
						</view>
					</view>

					<view class="result" v-else-if="imgType == 'error'? false : false">
						<view class="result-title m-b-20">无法识别</view>
						<view class="result-box">
							<view class="xs muted mr-10 m-t-20 m-b-20">请聚焦物体换个角度拍照试试,或者点击纠错帮助我们完善识别引擎</view>
							<u-button
								text="重新拍照"
								shape="circle"
								@click="$miRouter.back()"
								:color="$primaryBtn"
							></u-button>
						</view>
					</view>
				</view>
				<view class="nutrition-wrap" v-if="resultFood && resultFood.length >= 2 && imgType !== 'error'">
					<view class="nutrition-title">营养元素</view>
					<view class="nutrition-box">
						<view class="nutrition-item flex flex-col flex-center">
							<view class="nutrition-c">
								<text class="item">{{ nutritionInfo.heat }}</text>
								<text class="unit">大卡</text>
							</view>
							<view class="tips">每100克(可有效提供的热量)</view>
						</view>
					</view>
					<view class="circle-progress-box flex row-around">
						<view class="circle-progress flex flex-col flex-center">
							<circle-progress
								active-color="#51da98"
								width="180"
								:border-width="7"
								:percent="nutritionInfo.carbohydratePercentage"
								:subtitle="`${nutritionInfo.carbohydratePercentage}%`"
							></circle-progress>
							<view class="tips">
								碳水化合物
								<text style="color: #adb2b5">{{ nutritionInfo.carbohydratePercentageValue }}克</text>
							</view>
						</view>
						<view class="circle-progress flex flex-col flex-center">
							<circle-progress
								active-color="#51da98"
								width="180"
								:border-width="7"
								:percent="nutritionInfo.fatPercentage"
								:subtitle="`${nutritionInfo.fatPercentage}%`"
							></circle-progress>
							<view class="tips">
								脂肪
								<text style="color: #adb2b5">{{ nutritionInfo.fatPercentageValue }}克</text>
							</view>
						</view>
						<view class="circle-progress flex flex-col flex-center">
							<circle-progress
								active-color="#51da98"
								width="180"
								:border-width="7"
								:percent="nutritionInfo.proteinPercentage"
								:subtitle="`${nutritionInfo.proteinPercentage}%`"
							></circle-progress>
							<view class="tips">
								蛋白质
								<text style="color: #adb2b5">{{ nutritionInfo.proteinPercentageValue }}克</text>
							</view>
						</view>
						<view class="circle-progress flex flex-col flex-center">
							<circle-progress
								active-color="#51da98"
								width="180"
								:border-width="7"
								:percent="nutritionInfo.sodiumPercentage"
								:subtitle="`${nutritionInfo.sodiumPercentage}%`"
							></circle-progress>
							<view class="tips">
								钠
								<text style="color: #adb2b5">{{ nutritionInfo.sodiumPercentageValue }}毫克</text>
							</view>
						</view>
					</view>

					<view class="footer m-t-60 flex row-around">
						<view><u-button @click="$miRouter.back()" text="重新拍" shape="circle" :plain="true" color="#6ae3a3"></u-button></view>
						<view><u-button @click="clickDietRecord" text="饮食记录" shape="circle" color="#6ae3c4"></u-button></view>
					</view>
				</view>
			</view> -->
		</template>

		<u-overlay :show="errorCorrectionShow" @click="goBack">
			<view class="warp flex flex-center">
				<view class="rect" @tap.stop>
					<view class="title">纠错反馈</view>
					<u-form :model="modelForm" ref="uformRef" :rules="rules" labelPosition="top">
						<u-form-item label="食物名称：" prop="imgName" label-width="200">
							<u-input type="text" placeholder="请输入你认为的正确名称" v-model="modelForm.imgName" :customStyle="inputStyle" />
						</u-form-item>
					</u-form>
					<view class="text">你的反馈对我们很重要。</view>
					<view class="mask-btn flex row-around">
						<view>
							<u-button type="primary" shape="circle" @click="submitCorrect" :loading="false" :custom-style="customStyle">确定</u-button>
						</view>
					</view>
				</view>
			</view>
		</u-overlay>

		<u-picker
			:show="selectShow"
			keyName="label"
			:columns="classifyList"
			confirmColor="#18e6bb"
			:closeOnClickOverlay="true"
			@cancel="selectShow = false"
			@confirm="selectConfirm"
		></u-picker>
		<kpsImageCutter
			v-if="photoType !== 'MPWXALI'"
			@ok="onok"
			:fixed="true"
			@cancel="oncancel"
			:url="src"
			:referenceUrl="src"
			:blod="false"
			:height="kpsImageCutterWindoHW"
			:width="kpsImageCutterWindoHW"
		></kpsImageCutter>
		<!-- 饮食记录 -->
		<u-popup v-if="dietRecordShow" :show="dietRecordShow" closeable :round="40" @close="selectMealShow ?  '' : dietRecordShow = false">
			<view class="popup-meal-box">
				<view class="select-meal-box" @click="clickMealShow">
					<!-- 餐段 -->
					<view class="select-meal-text m-t-20">
						<text>{{ mealLabel }}</text>
						<text class="iconfont icon-arrow-down-filling"></text>
					</view>
				</view>
				<view class="p-20 flex row-center">
					<view class="flex p-b-15">
						<!-- <u-image width="100rpx" height="100rpx" :radius="10" mode="scaleToFill" :src="nutritionInfo.img_url"></u-image> -->
						<view class="flex flex-col">
							<view class="flex col-center row-center">
								<view class="name food-name lg">
									<text>{{ nutritionInfo.foodName }}</text>
								</view>
							</view>
							<view class="m-t-10 col-center row-center flex">
								<text class="muted xs">均为可食部重量</text>
							</view>
						</view>
					</view>
				</view>
				<!-- <view class="diet-content-wrapp">
					<view class="diet-content-box">
						<view class="content-left">
							<text class="content">{{ scrollWeight }}</text>
							<text class="muted sm p-l-15">g</text>
						</view>
						<view class="line"></view>
						<view class="content-right">
							<text class="muted sm p-r-15">含</text>
							<text class="content">{{ dictEnergyKcal }}</text>
							<text class="muted sm p-l-15">kcal</text>
						</view>
					</view>
				</view> -->
				<view class="diet-content flex flex-col row-center col-center">
					<view class="content-view flex row-center col-center m-b-10 xxxl">
						<text class="p-r-15">含:</text>
						<text class="content">{{ dictEnergyKcal }}</text>
						<text class="p-l-15">kcal</text>
					</view>
					<u-row
						justify="space-between"
						gutter="40"
						class="button-grop p-30 m-b-20"
					>
						<u-col span="3">
							<u-button type="info" text="50g" @click="getInnerText(50)" :class="[isClick === 50 ? 'select' : '']"></u-button>
						</u-col>
						<u-col span="3">
							<u-button type="info" text="100g" @click="getInnerText(100)" :class="[isClick === 100 ? 'select' : '']"></u-button>
						</u-col>
						<u-col span="3">
							<u-button type="info" text="150g" @click="getInnerText(150)" :class="[isClick === 150 ? 'select' : '']"></u-button>
						</u-col>
						<u-col span="3">
							<u-button type="info" text="200g" @click="getInnerText(200)" :class="[isClick === 200 ? 'select' : '']"></u-button>
						</u-col>
					</u-row>
					<view class="weightShow flex row-center">
						<text class="content">{{ scrollWeight }}</text>
						<text class="sm p-l-15">g</text>
					</view>
				</view>
				<!-- 刻度 -->
				<scroll-choose :key="rerender" @scroll="scroll" :scrollStart="0" :scrollEnd="1000" :scrollLeft="scrollLeft" :maginL="5"></scroll-choose>
				<u-button
					shape="circle"
					hover-class="none"
					type="primary"
					:custom-style="customStyleCropper"
					@click="clickPopupCetermine"
					:loading="!isOk"
					loadingText="正在获取信息"
				>
					确定
				</u-button>
			</view>
		</u-popup>
		<u-picker
			:show="selectMealShow"
			:defaultIndex="[2]"
			keyName="label"
			:columns="mealList"
			itemHeight="62"
			:closeOnClickOverlay="true"
			@cancel="selectMealShow = false"
			@confirm="selectMealConfirm"
			:confirmColor="variables.color-primary"
			:pickerType="'meal'"
		></u-picker>
	</view>
</template>

<script>
import { getApiNutritionPhoto, getApiNutritionCorrectPhoto } from '@/api/photo.js'
import kpsImageCutter from '@/pages/photo_recognition/components/ksp-image-cutter'
import { mapActions, mapGetters, mapMutations } from 'vuex'
// import SimpleCrop from '@/pages/photo_recognition/components/simple-crop/simple-crop.vue'
import { checkClient } from '@/utils/util.js'
import scrollChoose from '@/components/scroll-choose/scroll-choose'
import { getApiMealTypeIntakeAdd } from '@/api/healthy.js'
import { dataURLtoFile, convertBase64UrlToBlob } from '@/utils/uploadFaceImg.js'
import { uploadApiUrl, uploadApiCommon } from '@/api/upload.js'
// #ifdef H5
import jweixin from 'jweixin-module'
// #endif
import { getApiWechatCongfigGet } from '@/api/app.js'
import Cache from '@/utils/cache'
export default {
	data() {
		return {
			tabsData: [
				{
					name: '重新上传',
					cls: 'food',
					type: ['album', 'camera']
				},
			],
			imgPath: this.$imgPath,
			sequence: 0,
			rerender: 0, // 使用key重新渲染组件
			isClick: 0, // 按钮是否点击
			photoType: '',
			kpsImageCutterWindoHW: uni.getSystemInfoSync().windowWidth, //裁剪框 高宽度
			// show: true,
			src: '', //传进来的url bold
			cls: 'food',
			recognitionImgBase64: '', // 处理过的baser64 传给后端
			deepSrc: '', //克隆一层原图片，展示
			swiperCurrent: 0,
			resultFood: [],
			resultImg: '', //changeSwiper Img
			imgType: '', //changeSwiper type
			customStyleErrorCorrection: {
				width: '200rpx',
				height: '60rpx',
				backgroundColor: this.$variables.colorPrimaryLight3,
				marginTop: '20rpx',
				marginBottom: '20rpx',
				color: '#fff',
				border: 'none'
			},
			modelForm: {
				classify: '',
				imgName: '',
				cls: ''
			},
			classifyList: [
				[
					{
						label: '菜品',
						cls: 'food'
					},
					{
						label: '果蔬',
						cls: 'fruit'
					},
					{
						label: '零食',
						cls: 'snack'
					}
				]
			],
			nutritionInfo: {
				foodName: '',
				foodType: '',
				foodAlias: '',
				foodImg: '',
				heat: 0,
				carbohydratePercentageValue: 0,
				fatPercentageValue: 0,
				proteinPercentageValue: 0,
				sodiumPercentageValue: 0,
				carbohydratePercentage: 0,
				fatPercentage: 0, //脂肪百分比
				proteinPercentage: 0, //蛋白质百分比
				sodiumPercentage: 0 //钠百分比
			},
			errorCorrectionShow: false,
			selectShow: false,
			rules: {
				classify: [
					{
						required: true,
						message: '请选择分类',
						trigger: ['blur', 'change']
					}
				],
				imgName: [
					{
						required: true,
						message: '请输入物品名称',
						trigger: ['blur', 'change']
					}
				]
			},
			platform: checkClient(), // 平台， 微信or支付宝
			mpStatus: false,
			dietRecordShow: false, // 饮食记录
			selectMealShow: false,
			mealType: '',
			mealLabel: '餐段',
			mealList: [
				[
					{
						label: '早餐',
						cls: 'breakfast'
					},
					{
						label: '午餐',
						cls: 'lunch'
					},
					{
						label: '晚餐',
						cls: 'dinner'
					},
					{
						label: '上午加餐',
						cls: 'morning'
					},
					{
						label: '下午加餐',
						cls: 'afternoon'
					},
					{
						label: '晚上加餐',
						cls: 'supper'
					}
				]
			],
			nutritionFoodInfoNumber:0,
			scrollWeight: 0, //页面显示
			scrollLeft: 100, //初始值
			customStyleCropper: {
				width: '670rpx',
				marginTop: '20rpx',
				marginBottom: '20rpx',
				color: '#fff',
				border: 'none'
			},
			// 纠错反馈输入框样式
			inputStyle: {
				backgroundColor: '#F0F3F5',
			},
			pageLoading: 1, // 页面根据pageLoading状态切换页面 0为识别成功 1为Loading 2为Loading过长 3为无法识别 4为纠正弹窗
			timer: {},
			img_public_url: '',
			isOk: false,
			img_base64: '',
		}
	},
	components: {
		// SimpleCrop,
		kpsImageCutter,
		scrollChoose
	},
	computed: {
		screenHeight() {
			return uni.getSystemInfoSync().windowHeight-44+'px'
		},
		toTop() {
			return (uni.getSystemInfoSync().windowHeight-44)*(1-0.54)+'px'
		},
		dictEnergyKcal() {
			let totle = this.nutritionInfo.heat / 100
			return (this.scrollWeight * Number(totle)).toFixed(1)
		},
		...mapGetters(['userInfo', 'cookie'])
		// itemHeight() {
		// 	console.log('item',uni.getSystemInfoSync().screenHeight/2)
		// 	return uni.getSystemInfoSync().screenHeight/2
		// }
	},
	methods: {
		goToPhotoRecognition() {
			this.$miRouter.back()
		},
		select(item) {
			this.clsData = item
			this.takePhoto(item.type)
		},
		// 拍照按钮
		takePhoto(type) {
			const that = this
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				sourceType: type,
				success(data) {
					if (data.tempFilePaths && data.tempFilePaths.length) {
						that.src = data.tempFilePaths[0] 
						Cache.set('photoRecognition', {
							src: that.src,
							cls: that.clsData.cls,
							//  #ifdef MP-WEIXIN || MP-ALIPAY
							photoType: 'MPWXALI'
							// #endif
						})
						that.$miRouter.push({
							path: '/pages/photo_recognition/result'
						})
					}
				}
			})
		},
		onok(ev) {
			let that = this
			this.recognitionImgBase64 = ev.path
			// this.recognitionImgBase64 = ev.resultSrc
			// this.resultFood.push({
			// 	img_url: this.recognitionImgBase64,
			// 	type: 'padding'
			// })
			this.timer = setTimeout(() => {
				that.pageLoading = 2
			}, 5000);
			this.getNutritionData()
			// // 隐藏裁剪
			this.src = ''
		},
		// 识别
		getNutritionData() {
			this.pageLoading = 1
			let that = this
			// this.$showLoading({
			// 	title: '加载中...',
			// 	mask: true
			// })
			getApiNutritionPhoto({
				image: that.recognitionImgBase64,
				top_n: 10,
				img_cls: that.cls
			})
				.then(res => {
					// uni.hideLoading()
					this.resultFood = []
					if (res.code == 0) {
						try {
							that.resultFood = res.data[0].nutrition_info.map(v => {
								v.img_url = `https://model-files.oss-cn-shenzhen.aliyuncs.com/example/${v.food_id}.jpg`
								v.type = 'success'
								return v
							})
						} catch (e) {
							setTimeout(() => {
								that.imgType = 'error'
							}, 200)
							//TODO handle the exception
						}
						that.initNutritionInfo(0)
						// if (that.uploadImgType == 'first') {
						// 	that.resultFood.push({
						// 		img_url: that.recognitionImgBase64,
						// 		type: 'error'
						// 	})
						// } else {
						// 	that.resultFood.splice(that.resultFood.length - 1, 1, {
						// 		img_url: that.recognitionImgBase64,
						// 		type: 'error'
						// 	})
						// }
					} else {
						// 识别失败
						this.imgType = 'error'
						// this.resultFood.push({
						// 	img_url: this.recognitionImgBase64,
						// 	type: 'error'
						// })
						// uni.$u.toast(res.msg)
						clearTimeout(this.timer)
						return this.pageLoading = 3
					}
					this.pageLoading = 0
					clearTimeout(this.timer)
				})
				.catch(err => {
					// uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		isNull(str) {
			if (str === null || str === undefined || str.length === 0) {
				return true
			}
			return false
		},
		initNutritionInfo(number) {
			try {
				this.nutritionFoodInfoNumber = number
				let carbohydrate = this.isNull(this.resultFood[number]['碳水化合物(g/100g)']) ? 0 : Number(this.resultFood[number]['碳水化合物(g/100g)'].toFixed(2))
				let protein = this.isNull(this.resultFood[number]['蛋白质(g/100g)']) ? 0 : Number(this.resultFood[number]['蛋白质(g/100g)'].toFixed(2))
				let fat = this.isNull(this.resultFood[number]['脂肪(g/100g)']) ? 0 : Number(this.resultFood[number]['脂肪(g/100g)'].toFixed(2))
				let total = carbohydrate + protein + fat
				this.nutritionInfo = {
					foodId: this.resultFood[number].food_id,
					foodName: this.resultFood[number]['食品中文名'],
					foodType: this.resultFood[number]['食物类型'],
					foodAlias: this.resultFood[number]['别名'],
					img_url: `https://model-files.oss-cn-shenzhen.aliyuncs.com/example/${this.resultFood[number].food_id}.jpg`,
					heat: this.resultFood[number]['热量(大卡/100g)'] ? this.resultFood[number]['热量(大卡/100g)'].toFixed(1) : 0,
					carbohydratePercentageValue: this.resultFood[number]['碳水化合物(g/100g)']
						? this.resultFood[number]['碳水化合物(g/100g)'].toFixed(1)
						: 0,
					fatPercentageValue: this.resultFood[number]['脂肪(g/100g)'] ? this.resultFood[number]['脂肪(g/100g)'].toFixed(1) : 0,
					proteinPercentageValue: this.resultFood[number]['蛋白质(g/100g)'] ? this.resultFood[number]['蛋白质(g/100g)'].toFixed(1) : 0,
					// 百分比计算碳水用100去减
					// 判断脂肪和蛋白质是否为零或空
					carbohydratePercentage: (this.isNull(this.resultFood[number]['脂肪(g/100g)'])||this.resultFood[number]['脂肪(g/100g)']===0)&&(this.isNull(this.resultFood[number]['蛋白质(g/100g)'])||this.resultFood[number]['蛋白质(g/100g)']===0) ? 0
					: 100 - Math.round(Number(this.resultFood[number]['脂肪(g/100g)'].toFixed(2)) / total * 100) - Math.round(Number(this.resultFood[number]['蛋白质(g/100g)'].toFixed(2)) / total * 100),
					fatPercentage:
						total !== 0 && this.resultFood[number]['脂肪(g/100g)'] !== 0
						? Math.round(Number(this.resultFood[number]['脂肪(g/100g)'].toFixed(2)) / total * 100)
						: 0,
					proteinPercentage:
						total !== 0 && this.resultFood[number]['蛋白质(g/100g)'] !== 0
						? Math.round(Number(this.resultFood[number]['蛋白质(g/100g)'].toFixed(2)) / total * 100)
						: 0,
				}
				this.imgType = this.resultFood[number].type
			} catch (e) {
				console.log(e)
				//TODO handle the exception
			}
		},
		// 记录时按钮事件
		getInnerText(e) {
			this.isClick = e
			this.scrollLeft = e
			this.scrollWeight = e
			this.rerender += 1
		},
		// 切换轮播
		swiperChange(e) {
			this.initNutritionInfo(e.detail.current)
			this.sequence = e.detail.current
		},
		// 换回识别时的bg
		goToFeedBack() {
			this.pageLoading = 4
			this.errorCorrectionShow = true
		},
		// 点击纠错弹窗遮罩返回上一层
		goBack() {
			this.pageLoading = 0
			this.errorCorrectionShow = false
		},
		dataURLtoBlob(dataurl) {
			var arr = dataurl.split(","),
				mime = arr[0].match(/:(.*?);/)[1],
				bstr = atob(arr[1]),
				n = bstr.length,
				u8arr = new Uint8Array(n);
			while (n--) {
				u8arr[n] = bstr.charCodeAt(n);
			}
			return new Blob([u8arr], {
				type: mime,
			});
		},
		// 调用接口上传
    uploadFilePromise(url) {
      return new Promise((resolve, reject) => {
        uni.uploadFile({
          url: uploadApiUrl, // 'http://**************:9999/upload',
          filePath: url,
          fileType: 'image',
          name: 'file',
          formData: {
            key:  new Date().getTime() + Math.floor(Math.random() * 150),
            prefix: 'photo_recognition',
          },
          header: {
            CONTRACT: this.cookie
          },
          success: (res) => {
            resolve(JSON.parse(res.data))
          }
        });
      })
    },
		// 饮食记录
		async clickDietRecord() {
			this.mealType = ''
			this.mealLabel = '餐段'
			this.dietRecordShow = true
		},
		// 饮食记录-选择餐段
		clickMealShow() {
			this.selectMealShow = true
		},
		// 选择餐段确定
		selectMealConfirm(e) {
			this.mealType = e.value[0].cls
			this.mealLabel = e.value[0].label
			this.selectMealShow = false
		},
		/**
		 * 滑动时触发
		 */
		scroll(val) {
			this.scrollWeight = val
		},
		// 选择类型
		selectConfirm(e) {
			this.modelForm.classify = ''
			this.modelForm.classify = e.value[0].label
			this.modelForm.cls = 'food'
			this.selectShow = false
		},

		// 提交纠错
		submitCorrect(value) {
			console.log('this.modelForm', this.modelForm)
			this.$refs.uformRef.validate().then(res => {
				return this.getNutritionCorrect()
			})
		},
		getNutritionCorrect() {
			let that = this
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiNutritionCorrectPhoto({
				image: this.recognitionImgBase64,
				img_cls: this.$Route.query.cls,
				img_name: this.modelForm.imgName
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						uni.$u.toast('感谢你的反馈')
						that.modelForm.classify = ''
						that.modelForm.imgName = ''
						that.errorCorrectionShow = false
					} else {
						uni.$u.toast(res.msg)
					}
					setTimeout(() => {
						that.$miRouter.back(2)
					},1000)
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		clickPopupCetermine() {
			if (!this.mealType) return uni.$u.toast('请选择餐段')
			let params = {
				image:this.img_public_url,
				axunge:this.nutritionInfo.fatPercentageValue,
				carbohydrate:this.nutritionInfo.carbohydratePercentageValue,
				date:uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd'),
				protein:this.nutritionInfo.proteinPercentageValue,
				meal_type: this.mealType, //餐段类型
				name: this.nutritionInfo.foodName, //名字
				weight: this.scrollWeight, //重量
				energy_kcal: Number(this.dictEnergyKcal)
			}
			console.log(params)
			this.getMealTypeIntakeAdd(params)
		},
		// 饮食记录
		getMealTypeIntakeAdd(params) {
			uni.showLoading({
				title: '记录中...',
				mask: true
			})
			getApiMealTypeIntakeAdd(params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						uni.$u.toast('记录成功')
						this.dietRecordShow = false
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		// 使用canvas创建一个图片并转成base64
		getBase64Image(img) {
			var canvas = document.createElement('canvas')
			canvas.width = img.width
			canvas.height = img.height
			var ctx = canvas.getContext('2d')
			ctx.drawImage(img, 0, 0, img.width, img.height)
			var ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase()
			var dataURL = canvas.toDataURL('image/' + ext)
			return dataURL
		},
		getResultFood(data) {
			if(!data) {
				return ''
			}
			return data.食品中文名 || ''
		},
		// 图片格式化
		async getImgUrl() {
			// 将图片转化格式
			console.log("this.$Route.query.src", this.src);
			let res= ''
			// 判断是否是微信平台
			if (this.platform === 'wechat') {
				res = await this.uploadFilePromise(this.img_base64)
			} else {
				res = await this.uploadFilePromise(this.src)
			}
			console.log('getImgUrl',res)
			var data = res.data  || {}
			this.img_public_url = data.public_url || ""
			this.isOk = true
		},
		async getWechatCongfigGet() {
			let params = {
				appid: uni.getStorageSync('appid') || Cache.get('userInfo').appid,
				company_id: Cache.get('userInfo').company_id,
				url: window.location.href.split('#')[0]
			}
			let res = null
			try {
				res = await getApiWechatCongfigGet(params)
				uni.hideLoading()
				if (res.code == 0) {
					if (res.data) {
						// #ifdef H5
						jweixin.config({
							beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
							debug: false,
							appId: res.data.appid,
							timestamp: res.data.timestamp,
							nonceStr: res.data.noncestr,
							signature: res.data.signature,
							jsApiList: ['chooseImage', 'previewImage', 'uploadImage', 'downLoadImage', 'getLocalImgData'] // 初始化
						})
						jweixin.checkJsApi({
							jsApiList: ['chooseImage', 'uploadImage', 'getLocalImgData'], // 需要检测的JS接口列表，所有JS接口列表见附录2,
							success: function(res) {
							}
						});
						jweixin.error(function (res) {
						});
						// #endif
					}
				} else {
					uni.$u.toast(res.msg)
				}
		   } catch (error) {
				uni.hideLoading()
				uni.$u.toast(error.message)
			}
		},
	},
	onLoad() {
		// 判断是否是支付宝
		this.mpStatus = this.platform === 'mp-alipay' ? true : false
		let photoRecognition = Cache.get('photoRecognition') || {}
		this.src = photoRecognition.src || ''
		this.cls = photoRecognition.cls
		// 如果小程序 就不需要裁剪
		this.photoType = photoRecognition.photoType || ''
		this.getWechatCongfigGet()
		let that = this
		// #ifdef MP-WEIXIN || MP-ALIPAY
		const fs = wx.getFileSystemManager()
		fs.readFile({
			filePath: this.src,
			encoding: 'base64',
			position: 0,
			success(res) {
				that.src = 'data:image/jpeg;base64,' + res.data
				that.deepSrc = uni.$u.deepClone(that.src)
				that.recognitionImgBase64 = that.src
				// that.resultFood.push({
				// 	img_url: that.recognitionImgBase64,
				// 	type: 'padding'
				// })
				that.getNutritionData()
				that.src = ''
			},
			fail(res) {
				console.error(res)
			}
			// })
		})
		// #endif
		// #ifdef H5
		// console.log('是H5')
		// if (this.platform === 'wechat') {
		// 	jweixin.getLocalImgData({
		// 		localId: this.src + '?_=' + new Date().getTime(),
		// 		success: function(data) {
		// 			console.log('返回的base64', data)
		// 			that.img_base64 = data.localData
		// 			that.src = data.localData
		// 			that.deepSrc = uni.$u.deepClone(that.src)
		// 			console.log('赋值的内容', that.img_base64, that.src, that.deepSrc)
		// 		},
		// 		fail: function(res) {
		// 			console.log('出错咯',res)
		// 		}
		// 	})
		// } else {
			
		// }
		let image = new Image()
		// 因为刷新页面image.onload 不会触发，加上时间搓
		image.src = this.src + '?_=' + new Date().getTime()
		image.onload = () => {
			that.src = this.getBase64Image(image)
			that.deepSrc = uni.$u.deepClone(that.src)
		}
		// #endif
		this.getImgUrl()
	}
}
</script>

<style lang="scss">
.result {
	padding-bottom: constant(safe-area-inset-bottom);
	/* 兼容 iOS<11.2 */
	padding-bottom: env(safe-area-inset-bottom);
	/* 兼容iOS>= 11.2 */
	.wrappre-img-box {
		position: relative;
		.pageLoading {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			z-index: 99;
			.u-loading-icon {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				z-index: 100;
			}
			.tip {
				width: 640rpx;
				height: 410rpx;
				flex-direction: column;
				justify-content: space-around;
				border-radius: 20rpx;
				background: linear-gradient($color-primary-light-15 0%, #fff 20%, #fff 100%);
				.tip-icon {
					margin-top: -100rpx;
				}
				.tip-content {
					flex-direction: column;
					justify-content: center;
					align-items: center;
					text {
						color: #1D201E;
						text-align: center;
						line-height: 100%;
						margin-bottom: 20rpx;
					}
					text:first-child {
						font-size: 36rpx;
					}
					text:last-child {
						font-size: 32rpx;
					}
				}
				.tip-button {
					padding: 0rpx 180rpx;
				}
			}
			.fail-view {
				width: 100%;
				height: 50%;
				background-color: #fff;

			}
		}
		.wrappre-img {
			width: 750rpx;
			height: 450rpx;
			background-repeat: no-repeat;
			background-size: cover;
			// filter: blur(5px);
			z-index: 0;
		}
		// .img-box-wrapp {
		// 	position: absolute;
		// 	top: 50%;
		// 	left: 50%;
		// 	transform: translate(-50%, -50%);
		// 	border: 2px solid #fff;
		// 	z-index: 99;
		// }
	}
	.swiper-part {
		position: relative;
		.swiper-bg {
			height: 100vh;
			::v-deep .uni-swiper-dots {
				position: absolute;
				bottom: 630rpx;
				.uni-swiper-dot {
					background-color: #FFFFFFCC;
				}
				.uni-swiper-dot-active {
					width: 48rpx;
					border-radius: 10rpx;
				}
			}
		}
		.feedback {
			position: absolute;
			top: 3%;
			left: 73%;
			width: 180rpx;
			height: 60rpx;
			border-radius: 60rpx;
			align-items: center;
		}
		.swiper-bottom {
			position: absolute;
			width: 100%;
			.swiper-img {
				::v-deep .u-image {
					z-index: 100;
					.u-image__image {
						border: 4rpx solid #fff;
					}
				}
				text {
					margin-left: -40rpx;
					background-color: #1D201ECC;
					width: 50%;
					color: #fff;
					text-align: center;
					line-height: 72rpx;
					border-radius: 50rpx;
				}
			}
			.swiper-details {
				border-radius: 40rpx 40rpx 0rpx 0rpx;
				.detail-content {
					.detail-item {
						text:first-child {
							color: #1D201E;
						}
						text:last-child {
							color: #8F9295;
						}
					}
				}
			}
		}
	}
	// .swiper-3d {
	// 	padding: 0upx 20rpx;
	// 	// 如果不是支付宝就拿
	// 	/* #ifndef MP-ALIPAY */
	// 	height: 570rpx;
	// 	/* #endif */
	// 	// 是支付宝
	// 	/* #ifdef MP-ALIPAY */
	// 	height: 680rpx;
	// 	/* #endif */
	// 	.s-container {
	// 		height: 667rpx !important;
	// 		width: 100%;
	// 		.swiper-item {
	// 			// height: 90%;
	// 			padding: 0upx 20rpx;
	// 			box-sizing: border-box;
	// 			// position: relative;
	// 			position: absolute;
	// 			text-align: center;
	// 		}

	// 		.img-error-text {
	// 			width: 290rpx;
	// 			word-wrap: break-word;
	// 			border-radius: 50%;
	// 			position: absolute;
	// 			left: 50%;
	// 			top: 40%;
	// 			transform: translate(-50%, -50%);
	// 			z-index: 10;
	// 			font-size: 46rpx;
	// 			font-weight: bold;
	// 			color: #3ad992;
	// 		}

	// 		.item-img {
	// 			margin-top: 30upx;
	// 			// 如果不是支付宝就拿
	// 			/* #ifndef MP-ALIPAY */
	// 			width: 98%;
	// 			height: 62%;
	// 			border-radius: 50%;
	// 			/* #endif */

	// 			z-index: 5;
	// 			border: 6px solid #fff;
	// 			margin-bottom: 10rpx;
	// 		}

	// 		.img-error {
	// 			opacity: 0.8;
	// 		}

	// 		.text {
	// 			font-size: 36rpx;
	// 			color: #2e2d2d;
	// 		}

	// 		.active {
	// 			opacity: 1;
	// 			z-index: 10;
	// 			height: 90%;
	// 			top: -3%;
	// 			transition: all 0.1s ease-in 0s;
	// 			transform: translateY(0);
	// 		}
	// 	}
	// }
	.result-wrapper-box {
		margin-top: -80rpx;
		.result-wrapper {
			background-color: #fff;

			.result {
				padding: 50rpx;
				position: relative;

				// .icon-box {
				// 	width: 50rpx;
				// 	height: 50rpx;
				// 	line-height: 50rpx;
				// 	text-align: center;
				// 	background-color: #ffffff;
				// 	box-shadow: 0px 0px 4px 0px #e0f2e9;
				// 	border-radius: 50%;
				// 	position: absolute;
				// 	top: -20rpx;
				// 	left: 45%;
				// }

				// .result-title {
				// 	font-size: 30rpx;
				// 	font-weight: bold;
				// 	color: #2e2d2d;
				// }

				// .result-box {
				// 	.result-content {
				// 		width: 50%;

				// 		.category {
				// 			font-size: 24rpx;
				// 			color: #93989e;
				// 			padding-bottom: 5px;

				// 			.text {
				// 				font-size: 24rpx;
				// 				color: #2e2d2d;
				// 			}
				// 		}
				// 	}

				// 	// .result-img-box {
				// 	// 	width: 50%;
				// 	// 	position: relative;

				// 	// 	.contrast {
				// 	// 		width: 60rpx;
				// 	// 		height: 60rpx;
				// 	// 		line-height: 60rpx;
				// 	// 		font-size: 24rpx;
				// 	// 		text-align: center;
				// 	// 		position: absolute;
				// 	// 		left: 39%;
				// 	// 		top: 40%;
				// 	// 		z-index: 10;
				// 	// 		border-radius: 50%;
				// 	// 		color: #fff;
				// 	// 		background-color: #51db98;
				// 	// 	}

				// 	// 	.img-box {
				// 	// 		position: relative;
				// 	// 		.img {
				// 	// 			width: 140rpx;
				// 	// 			height: 140rpx;
				// 	// 			margin-right: 20rpx;
				// 	// 			border-radius: 10rpx;
				// 	// 		}
				// 	// 		.tips {
				// 	// 			position: absolute;
				// 	// 			left: 0;
				// 	// 			bottom: 11rpx;
				// 	// 			width: 140rpx;
				// 	// 			height: 37rpx;
				// 	// 			font-size: 24rpx;
				// 	// 			text-align: center;
				// 	// 			background-color: #51db98;
				// 	// 			color: #fff;
				// 	// 			border-radius: 0px 0px 12rpx 12rpx;
				// 	// 			opacity: 0.7;
				// 	// 		}

				// 	// 		.tips-result {
				// 	// 			background-color: #37c5e3;
				// 	// 		}
				// 	// 	}
				// 	// }
				// }
			}
		}

		// .nutrition-wrap {
		// 	background-color: #fff;
		// 	padding: 20rpx 30rpx;
		// 	margin-top: 20rpx;
		// 	border-radius: 25rpx;

		// 	.nutrition-title {
		// 		font-size: 30rpx;
		// 		font-weight: bold;
		// 	}

		// 	.nutrition-box {
		// 		display: flex;
		// 		align-items: center;
		// 		justify-content: space-between;
		// 		padding: 20rpx;

		// 		.nutrition-item {
		// 			.nutrition-c {
		// 				text-align: center;

		// 				.item {
		// 					font-size: 54rpx;
		// 					color: #51db98;
		// 				}
		// 			}

		// 			.tips {
		// 				font-size: 24rpx;
		// 				color: #93989e;
		// 			}
		// 		}
		// 	}

		// 	.circle-progress-box {
		// 		flex-wrap: wrap;

		// 		.circle-progress {
		// 			width: 50%;
		// 			text-align: center;

		// 			.tips {
		// 				padding: 20rpx;
		// 			}
		// 		}
		// 	}
		// }
	}
	.footer {
		width: 100%;
		> view {
			width: 40%;
		}
	}

	.warp {
		height: 100%;
	}

	.rect {
		width: 600rpx;
		// height: 650rpx;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;

		.title {
			text-align: center;
			font-size: 36rpx;
			font-weight: bold;
			padding-bottom: 15rpx;
		}

		.text {
			font-size: 28rpx;
			color: #93989e;
		}
	}

	.mask-btn {
		padding-top: 20rpx;

		> view {
			width: 40%;
		}
	}
	.popup-meal-box {
		.select-meal-box {
			display: flex;
			justify-content: center;
			align-items: center;
			.select-meal-text {
				display: flex;
				padding: 3rpx 20rpx 3rpx 40rpx;
				background-color: #f6f8f8;
				border-radius: 25rpx;
				text-align: center;
				line-height: 50rpx;
				.icon-arrow-down-filling {
					padding-left: 20rpx;
					color: #a8acac;
				}
			}
		}
		.food-name {
			display: flex;
			align-items: center;
			font-weight: bold;
		}
		.food-name-before {
			display: inline-block;
			width: 12rpx;
			height: 12rpx;
			border-radius: 12rpx;
			background-color: #ffd737;
			margin-right: 15rpx;
		}
		.diet-content {
			.content-view {
				border-radius: 44rpx;
				background-color: #F0F3F5;
				width: 386rpx;
				height: 88rpx;
				color: #1D201E;
			}
			.button-grop {
				.select {
					border-color: $color-primary;
					background-color: $color-primary-light-9;
					color: $color-primary;
				}
			}
			.weightShow {
				color: $color-primary;
				font-size: 64rpx;
				align-items: baseline;
			}
		}
		// .diet-content-wrapp {
		// 	display: flex;
		// 	justify-content: center;
		// 	align-items: center;
		// 	margin-bottom: 40rpx;
		// 	.diet-content-box {
		// 		display: flex;
		// 		justify-content: space-between;
		// 		align-items: center;
		// 		width: 670rpx;
		// 		height: 140rpx;
		// 		background-color: #f6f7fb;
		// 		border-radius: 20rpx;
		// 		.content-left {
		// 			padding-left: 70rpx;
		// 			.content {
		// 				font-size: 60rpx;
		// 			}
		// 		}
		// 		.line {
		// 			width: 2rpx;
		// 			height: 80rpx;
		// 			background-color: #e9eaf1;
		// 		}
		// 		.content-right {
		// 			padding-right: 70rpx;
		// 			.content {
		// 				font-size: 60rpx;
		// 			}
		// 		}
		// 	}
		// }
	}
}
</style>
