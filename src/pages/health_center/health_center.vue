<template>
	<view :style="theme.style" :class="['health-wrapper', platform === 'psbc' ? 'p-t-60' : '']">
		<view class="health-center">
			<view v-if="userinfo.healthy_info" class="p-t-40">
				<!-- 选择人员 -->
				<u-sticky :customStyle="stickyStyle">
					<view :class="[isScroll ? 'userInfo-isScroll' : 'userInfo', 'col-center', 'm-b-20', 'p-30', 'bg-white']">
						<view class="flex col-center row-between">
							<view class="flex">
								<u-avatar
									:src="userinfo.card_healthy.head_img_url ? userinfo.card_healthy.head_img_url : themeImgPath.img_avatar"
								></u-avatar>
								<!-- 新增 -->
								<view class="userInfo_text m-l-20">
									<text class="lg f-w-600 m-b-6">{{ nickName }}</text>
									<view>
										<text class="userInfo_tag mini primary text-center" style="border-radius: 10rpx;">{{ healthyGoal }}</text>
									</view>
								</view>
							</view>
							<view class="flex flex-center m-l-200" @click="clickHealthArchives({type:'healthy_archives'})">
								<text class="grey">健康档案</text>
								<u-icon class="m-l-10" name="arrow-right" color="#8f9295" size="16"></u-icon>
							</view>
							<!-- <text v-if="healthRecordsList.length" class="iconfont icon-arrow-down-filling icon-down p-l-20"></text> -->
						</view>
						<!-- <text class="line-title"></text> -->
						<!-- 固定滑动 -->
						<!-- <view style="width:430rpx;">
							<u-scroll-list :indicator="false">
								<view class="flex col-center" v-for="(row, i) in titleIconList" :key="i" @click="clickHealthArchives(row)">
									<view class="flex flex-col row-center col-center" style="width:108rpx;">
										<u-image width="60rpx" height="60rpx" :src="row.icon"></u-image>
										<view class="m-t-10 muted xs">{{ row.name }}</view>
									</view>
								</view>
							</u-scroll-list>
						</view> -->
						
					</view>
				</u-sticky>
				<!-- Header 头部 Start -->
				<!-- <div>
					<health-weight-score-charts
						:healthyIndexData="healthyIndexData"
						@confirmGetHealthyIndex="confirmGetHealthyIndex"
					></health-weight-score-charts>
				</div> -->
				<!-- <view class="dietary-tips flex col-center muted m-b-20 m-t-20 " @click="pathDietHealthy">
						<view class="icon-tips">
							<u-image
								src="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/052eca9ff33f2ca51cab542524539a571671603413759.png"
								width="70rpx"
								height="90rpx"
								mode="aspectFit"
							></u-image>
						</view>
						<view
							class="p-l-40"
							style="width: 600rpx;"
							v-if="healthyIndexData.healthy_tips && Number(healthyIndexData.healthy_tips.length)"
						>
							<u-notice-bar
								:text="healthyIndexData.healthy_tips"
								icon=" "
								bgColor="#fff"
								mode="link"
								color="#424242"
								direction="column"
							>
						</u-notice-bar>
						</view>
						<span class="p-l-40 xs" v-else>合理搭配是膳食营养的重要保证</span>
				</view> -->
				<!-- Header 头部 End -->
				<!-- Section 主体 Start -->
				<view class="m-t-20 p-l-40 p-r-40">
					<!-- 今日摄入 -->
					<intake-today :intakeData="intakeData" :key="refresh" />
					<!-- 饮水推荐 -->
					<drinking-water-recommendation :waterIntake="waterIntake" :waterSuggestion="waterSuggestion" />
					<!-- 健康分图表 -->
					<!-- <health-score-line :healthyIndexData="healthyIndexData"></health-score-line> -->
					<!-- 订单以及数据 -->
					<!-- 汇总餐段 不知道需不需要 -->
					<!-- <ingestion-meal-food :healthyIndexData="healthyIndexData"></ingestion-meal-food> -->
					<!-- <nutrition-ratio :healthyIndexData="healthyIndexData" v-show="healthyIndexData"></nutrition-ratio> -->
					<!-- 运动 -->
					<sports :healthyIndexData="healthyIndexData" @confirmGetHealthyIndex="confirmGetHealthyIndex" @refresh="getHealthyIndex"></sports>
					<!-- 习惯养成 -->
					<habit-formation :healthyIndexData="healthyIndexData" @confirmGetHealthyIndex="confirmGetHealthyIndex" />
				</view>
			</view>

			<view class="health-center-no" v-else>
				<view class="no-archives-bg">
				<view class="no-archives-wrapper" >
					<view class="no-archives-title">
						<view class="title">Hello,</view>
						<view class="title-text">开启朴食健康的精彩之旅</view>
					</view>
					<view class="complete-archives-wrapper" >
					<view class="complete-archives" >
						<view class="complete-archives-text" >
								<view>完善健康档案</view>
								<view class="m-t-10">领取会员 <text class="day">3</text>天有效期</view>
						</view>
						<view>
						<u-button
							text="创建档案"
							iconColor="#fff"
							shape="circle"
							size="small"
							@click="pathHealth"
							:custom-style="{width:'180rpx'}"
						></u-button>
					</view>
					</view>
					<view class="complete-archives-bottom">
						档案创建后自动发放，每个手机号仅可领取一次。
					</view>
				</view>
					<view class="no-archives-content">
							<view class="content-title" >档案管理</view>
							<view class="content-text">专业的团队，有序构建全面的健康档案管理体系和流程。</view>
							<view class="no-archives-content-img" >
								<image :src="themeImgPath.img_no_archives_1" class="image"></image>
							</view>
					</view>
					<view class="no-archives-content">
							<view class="content-title" >营养推荐及摄入监控</view>
							<view class="content-text">基于科学和健康的模式，有效保持健康的饮食习惯和生活方式。</view>
							<view class="no-archives-content-img" >
								<image :src="themeImgPath.img_no_archives_2" class="image"></image>
							</view>
					</view>
					<view class="no-archives-content">
							<view class="content-title" >营养分析</view>
							<view class="content-text">通过记录结果，为您提供专业的膳食营养分析指导。</view>
							<view class="content-image-list"  >
								<view class="p-b-20" >
									<image class="image" :src="themeImgPath.img_no_archives_3" ></image>
								</view>
								<view class="p-b-20" >
									<image class="image" :src="themeImgPath.img_no_archives_4" ></image>
								</view>
								<view class="" >
									<image class="image" :src="themeImgPath.img_no_archives_5" ></image>
								</view>
								<view class="" >
									<image class="image" :src="themeImgPath.img_no_archives_6" ></image>
								</view>
							</view>
					</view>
				</view>
				<view class="p-l-20 p-r-20 m-b-40" @click="pathHealth">
						<u-button
							text="创建档案"
							iconColor="#fff"
							size="normal"
							shape="circle"
							:color="variables.bgLinearGradient1"
						></u-button>
					</view>
					<view class="swiper-bottom flex flex-center">
						<text class="grey">已有档案？</text>
						<view class="flex flex-center" @click="pathHealthBind">
							<text class="primary nr">立即绑定</text>
							<u-icon name="arrow-right" :color="variables.colorPrimary" size="16"></u-icon>
						</view>
					</view>
			</view>
			</view>
		</view>
		<u-modal :show="switchArchivesShow" :showConfirmButton="false" closeOnClickOverlay @close="switchArchivesShow = false">
			<view class="switch-archives-box">
				<block v-for="(item, index) in healthRecordsList" :key="index">
					<view class="flex row-between col-center m-b-20" @click="changeRecords(item)">
						<view class="flex col-center">
							<u-avatar :src="item.face_url ? item.face_url : themeImgPath.img_avatar"></u-avatar>
							<text class="p-l-40">{{ item.name }}</text>
						</view>
						<u-icon v-if="userinfo.use_card === item.id" name="checkmark-circle-fill" color="#6ae3a3" size="32"></u-icon>
					</view>
				</block>
			</view>
		</u-modal>
		<non-member :showMember="showMember" :content="modalContent" :show-confirm-button="showConfirmbtn" @setShowMember="setShowMember"></non-member>
		<custom-tabbar type="health_center"></custom-tabbar>
	</view>
</template>

<script>
import { getApiUserGetProjectCardUserList, setApiChangeProjectPoint } from '@/api/app'
import { apiQueryUserinfo } from '@/api/user.js'
import { getApiHealthyIndex, getApiHealthyHealthyRecord } from '@/api/healthy.js'
import healthWeightScoreCharts from '@/components/health_center/healthWeightScoreCharts'
import healthScoreLine from '@/components/health_center/healthScoreLine'
import nutritionRatio from '@/components/health_center/nutritionRatio'
import intakeToday from '@/components/health_center/intakeToday'
import drinkingWaterRecommendation from '@/components/health_center/drinkingWaterRecommendation'
import sports from '@/components/health_center/sports'
import habitFormation from '@/components/health_center/habitFormation'
import Cache from '@/utils/cache'
import checkMember from '@/mixins/checkMember.js'
import { mapActions, mapGetters } from 'vuex'
export default {
	mixins: [checkMember],
	data() {
		return {
			isScroll: false,
			healthyData: {},
			imgPath: this.$imgPath,
			titleIconList: [
				{
					// icon: require('@/static/icons/icona.png'),
					icon: this.$imgPath.img_icona,
					name: '健康档案',
					name: '健康档案',
					type: 'healthy_archives'
				},
				{
					// icon: require('@/static/icons/canteen_caipu.png'),
					icon: this.$imgPath.img_canteen_caipu,
					name: '饮食档案',
					type: 'diet_archives'
				},
				{
					// icon: require('@/static/icons/canteen_dingdan.png'),
					icon: this.$imgPath.img_canteen_dingdan,
					name: '运动档案',
					type: 'motion_archives'
				},
				{
					// icon: require('@/static/icons/icond.png'),
					icon: this.$imgPath.img_icond,
					name: '体检报告',
					type: 'examination_report'
				}
			],
			healthyIndexData: {}, //所有的数据
			userinfo: {},
			switchArchivesShow: false, // 头像切换档案
			healthRecordsList: [], //健康档案
			healthRecordsInfo: {}, // 当前的健康档案
			// bgList: [
			// 	{
			// 		img_url: this.$imgPath.img_health_center_bg1,
			// 		title: '饮食分析',
			// 		text: '通过记录结果为健康人群提供膳食营养分析指导'
			// 	},
			// 	{
			// 		img_url: this.$imgPath.img_health_center_bg2,
			// 		title: '饮食记录',
			// 		text: '记录每日主餐和加餐摄入的食物信息'
			// 	},
			// 	{
			// 		img_url: this.$imgPath.img_health_center_bg3,
			// 		title: '健康&有效',
			// 		text: '基于科学和健康的方案，有效保持健康的饮食习惯和生活方式'
			// 	}
			// ],
			intakeData: {
				ingested: 0,
				intakeRange: [],
				actual: {
					protein: 0,
					fat: 0,
					carbohydrate: 0
				},
				should: {
					Protein: [],
					Fat: [],
					Carbohydrate: []
				}
			},
			stickyStyle: {
				padding: '0rpx 40rpx'
			},
			waterIntake: 0,
			waterSuggestion: "起床后大概半小时左右喝，可以喝一杯温开水，有利于加速促进肠道与血管内废物排出，提高身体的新陈代谢能力，同时补充晚上消耗的水分。",
			refresh: 0
		}
	},
	components: {
		healthWeightScoreCharts,
		healthScoreLine,
		nutritionRatio,
		sports,
		habitFormation,
		intakeToday,
		drinkingWaterRecommendation
	},
	computed: {
		...mapGetters(['platform', 'noPermissions']),
		healthyGoal() {
			if (this.healthyData.weight > this.healthyData.weight_target) {
				return '减重'
			} else if (this.healthyData.weight < this.healthyData.weight_target) {
				return '增重'
			} else {
				return '营养均衡'
			}
		},
		nickName() {
			if (JSON.stringify(this.healthRecordsInfo) !== '{}') {
				return this.healthRecordsInfo.healthy_nickname.nickname !=='' ? this.healthRecordsInfo.healthy_nickname.nickname : this.healthRecordsInfo.name
			}
		},
	},
	onPageScroll(e) {
		if (e.scrollTop >= 100) {
			this.isScroll = true
			this.stickyStyle = {}
		} else {
			this.isScroll = false
			this.stickyStyle = {
				padding: '0rpx 40rpx'
			}
		}
	},
	onReady() {
		uni.hideTabBar()
	},
  onLoad() {
		uni.hideTabBar()
		// #ifdef H5
		// this.$nextTick(_ => {
		// 	document.getElementsByTagName('uni-page-head')[0].style.display = 'none'
		// })
		// #endif
	},
	watch: {
		noPermissions(newVal) {
      if (!this.noPermissions && this.$Route.path === '/pages/health_center/health_center' ) {
        this.isPermissions()
      }
    }
	},
	onShow() {
		this.userinfo = Cache.get('userInfo')
		// this.getHealthyIndex()
		this.queryUserinfo()
		this.refresh++
	},
	destroyed(){
    uni.$off('routerChange')
  },
	methods: {
		// 获取健康信息
		getHealthyMsg() {
			getApiHealthyHealthyRecord()
			.then(async res => {
				uni.hideLoading()
				if (res.code == 0) {
					// 健康档案
					this.healthyData = res.data.healthy_data
					this.getHealthyIndex()
				} else {
					// uni.$u.toast(res.msg)
				}
			})
			.catch(err => {
				uni.$u.toast(err)
			})
		},
		...mapActions({
			setUserInfo: 'setUserInfo'
		}),
		// 获取项目点
		getUserProjectList() {
			let params = {
				company_id: this.userinfo.company_id,
				is_healthy: true
			}
			getApiUserGetProjectCardUserList(params)
				.then(res => {
					if (res.code == 0) {
						res.data.sort((a, b) => {
							if (this.userinfo.use_card === a.id) {
								return -1
							} else {
								return 0
							}
						})
						this.healthRecordsList = res.data
						this.healthRecordsInfo = res.data[0]
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err.message)
				})
		},
		// 设置项目点
		setChangeProjectPoint(parmas) {
			this.$showLoading({
				title: '切换中....',
				mask: true
			})
			setApiChangeProjectPoint(parmas)
				.then(res => {
					if (res.code == 0) {
						this.switchArchivesShow = false
						this.queryUserinfo()
					} else {
						uni.hideLoading()
					}
				})
				.catch(err => {
					uni.$u.toast(err.message)
				})
		},
		// 设置项目点
		queryUserinfo() {
			this.$showLoading({
				title: '加载中....',
				mask: true
			})
			apiQueryUserinfo()
				.then(res => {
					if (res.code == 0) {
						this.setUserInfo(res.data)
						this.userinfo = res.data
						this.getHealthyMsg()
						if (this.userinfo.healthy_info && this.userinfo.company_id) {
							this.getUserProjectList()
						}
					} else {
						uni.hideLoading()
					}
				})
				.catch(err => {
					uni.$u.toast(err.message)
				})
		},
		changeRecords(data) {
			if (data.id === this.userinfo.use_card) return
			let formData = {
				company_id: data.company_id,
				company_name: data.company_name,
				person_no: data.person_no,
				name: data.name
			}
			this.setChangeProjectPoint(formData)
		},
		async clickHealthArchives(row) {
			switch (row.type) {
				case 'healthy_archives':
					this.$miRouter.push({
						path: '/pages_health_pomr/healthy_archives'
					})
					break
				case 'diet_archives':
					// let hasPermissions = await this.checkMemberPermissions('diet_archives', true)
					if (hasPermissions) {
						this.$miRouter.push({
							path: '/pages_health_pomr/diet_archives/index'
						})
					}
					break
				case 'motion_archives':
					this.$miRouter.push({
						path: '/pages_health_pomr/motion_archives/index'
					})
					break
				case 'examination_report':
					// uni.$u.toast('正在建设中')
					this.$miRouter.push({
						path: '/pages_health_pomr/examination_report/index'
					})
					break

				default:
					break
			}
		},
		// 修改用户信息
		// getHealthyModify(params) {
		// 	this.$showLoading({
		// 		title: '加载中....',
		// 		mask: true
		// 	})
		// 	getApiHealthyModify(params)
		// 		.then(res => {
		// 			if (res.code == 0) {
		// 				uni.$u.toast('修改成功')
		// 				this.getHealthyIndex()
		// 				uni.hideLoading()
		// 			} else {
		// 				uni.$u.toast(res.msg)
		// 				uni.hideLoading()
		// 			}
		// 		})
		// 		.catch(err => {
		// 			uni.hideLoading()
		// 			console.log('获取用户信息err', err)
		// 		})
		// },
		// modifyWeight(weight) {
		// 	let params = {
		// 		healthy_info_type: 'card', //暂时不知道是card 还是user
		// 		weight: weight
		// 	}
		// 	this.getHealthyModify(params)
		// },
		// 健康页面数据
		getHealthyIndex() {
			getApiHealthyIndex()
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.healthyIndexData = res.data
						// 蛋白质
						this.intakeData.actual.protein = res.data.nutrient.protein
						this.intakeData.should.Protein = res.data.nutrient.protein_range
						// 油脂
						this.intakeData.actual.fat = res.data.nutrient.axunge
						this.intakeData.should.Fat = res.data.nutrient.axunge_range
						// 碳水
						this.intakeData.actual.carbohydrate = res.data.nutrient.carbohydrate
						this.intakeData.should.Carbohydrate = res.data.nutrient.carbohydrate_range
						// 能量
						this.intakeData.ingested = res.data.nutrient.energy_kcal
						this.intakeData.intakeRange = res.data.nutrient.energy_kcal_range
						// 饮水推荐
						this.waterIntake = res.data.water_recommend_text.water_ml
						this.waterSuggestion = res.data.water_recommend_text.water_tips_data

					} else {
						// uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					// uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		changeArchives() {
			if(!this.healthRecordsList.length) return
			this.switchArchivesShow = true
		},
		pathHealth() {
			Cache.remove('PERFECT_USERINFO')
			this.$miRouter.push({
				path: '/pages_info/userinfo/perfect_userinfo'
			})
		},
		pathHealthBind() {
			this.$miRouter.push({
				path: '/pages_health/bind_healthy/bind_healthy'
			})
		},
		gotoIndexPage() {
			this.$miRouter.pushTab({
				path: '/pages/index/index'
			})
		},
		async pathDietHealthy() {
			let hasPermissions = await this.checkMemberPermissions('nutrition_analysis', true)
			if (hasPermissions) {
				this.$miRouter.push({
					path: '/pages_health/healthy/diet_healthy/nutritionalAnalysis',
					query: {
						meal_type: this.healthyIndexData.healthy_meal_type
					}
				})
			}
		},
		// 重新加载接口
		confirmGetHealthyIndex(){
			this.getHealthyIndex()
		}
	}
}
</script>

<style lang="scss">
.health-wrapper {
	// height: 100%;
	position: relative;
	.u-sticky {
		top: 0 !important;
	}
	.health-center {
		padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
		// height: 100%;
		.userInfo {
			border-radius: 20rpx;
		}
		.userInfo_text {
			.userInfo_tag {
				padding: 4rpx 10rpx;
				background-color: $color-primary-light-9;
			}
		}
		.userInfo-isScroll {
			border-radius: 0rpx 0rpx 20rpx 20rpx;
			> view {
				padding: 0rpx 40rpx;
			}
		}
		.select-box {
			.icon-down {
				color: #c8c8c8;
			}
			.line-title {
				width: 1rpx;
				height: 90rpx;
				background-color: #e6e6e6;
			}
		}
		.dot {
			display: inline-block;
			width: 15rpx;
			height: 15rpx;
			border-radius: 10rpx;
		}
		.health-center-no {
			.no-archives-bg {
				width: 100%;
				background-image: url($imgBasePath + '/health/no_archives_bg_3.png');
				background-repeat: no-repeat;
				background-size: 100%;
			}
			.no-archives-wrapper {
				padding: 45rpx 40rpx 40rpx 40rpx;
				width: 100%;
				.no-archives-title {
					.title {
						font-size: 64rpx;
					}
					.title-text {
						font-size: 30rpx;
						font-weight: bold;
					}
				}
				.complete-archives-wrapper {
					.complete-archives {
						display: flex;
						justify-content: space-between;
						align-items: center;
						padding: 30rpx 30rpx 20rpx 30rpx;
						margin-top: 30rpx;
						background-image: url($imgBasePath + '/health/no_archives_bg_1.png');
						background-repeat: no-repeat;
						background-size: 100%;
						.complete-archives-text {
							font-size: 30rpx;
							color: #fff;
							.day {
								margin: 0 15rpx;
								display: inline-block;
								background-image: url($imgBasePath + '/health/no_archives_bg_2.png');
								background-repeat: no-repeat;
								background-size: 100%;
								width: 44rpx;
								height: 44rpx;
								text-align: center;
								line-height: 44rpx;
								font-weight: bold;
								color: #262626;
								font-size: 36rpx;
							}
						}
					}
					.complete-archives-bottom {
						border-bottom-left-radius: 20rpx;
						border-bottom-right-radius: 20rpx;
						padding: 15rpx 30rpx;
						font-size: 24rpx;
						color: #fff;
						background-color: #4e4f4b;
					}
				}
				.no-archives-content {
					padding-top: 30rpx;
					.content-title {
						font-size: 30rpx;
						font-weight: bold
					}
					.content-text {
						padding: 10rpx 0 20rpx 0;
						font-size: 24rpx;
						color: #8F9295;
					}
					.content-image-list {
						display: flex;
						flex-wrap: wrap;
						justify-content: space-between;
						.image {
							width: 325rpx;
							height: 268rpx;
						}
					}
					.no-archives-content-img {
						.image {
							width: 100%;
							height: 320rpx;
						}
					}
				}
			}
		}
		.u-scroll-list {
			padding-bottom: 0rpx;
		}
		.dietary-tips {
			padding-left: 50rpx;
			padding-right: 30rpx;
			height: 80rpx;
			background-color: #ffffff;
			border-radius: 40rpx;
			position: relative;
			.icon-tips {
				position: absolute;
				top: -15rpx;
				left: 10rpx;
			}
		}
	}
	.switch-archives-box {
		width: 100%;
		max-height: 300rpx;
		overflow: auto;
	}
	// ::v-deep.u-modal__content {
	// 	padding-bottom: 0rpx;
	// }
}
</style>
