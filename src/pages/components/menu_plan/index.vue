<template>
	<view :style="theme.style" class="recipe-plain-wrap m-t-20" v-if="indexMealPlanList.is_menu_plan || (indexMealPlanList.data && indexMealPlanList.data.length)">
		<view class="recipe-plain" v-if="indexMealPlanList.is_menu_plan">
			<view class="flex row-between">
				<view class="card-title flex flex-center">
					<text class="p-r-10 f-w-600 md">食谱计划</text>
					<u-icon name="question-circle" color="#999" size="32rpx" @click="showRecipePlainTips = true"></u-icon>
				</view>
				<view class="xs muted flex flex-center" @click="clickMenuPlan">
					<text>查看详情</text>
					<u-icon color="inherit" name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
			<view class="flex row-between plain-info main-card">
				<view>
					{{ nameFormat(indexMealPlanList.menu_plan_name, 6) }}
					<text v-if="indexMealPlanList.days == indexMealPlanList.end_days" class="muted mini">(即将结束)</text>
				</view>
				<view>
					第
					<text class="num">{{ indexMealPlanList.days }}</text>
					天
				</view>
			</view>
			<view class="plain-list">
				<view class="plain-item" v-for="(item, index) in foodData" :key="index">
					<view class="flex row-between">
						<view class="meal-text">
							{{ item.name }}
							<text class="time" v-if="item.mealTime">{{ item.mealTime }}</text>
						</view>
						<view class="nr muted">{{ item.totalKcal }} kcal</view>
					</view>
					<view class="food-list main-card">
						<u-checkbox-group placement="column" v-model="item.checkedFoodId" :activeColor="variables.colorPrimary" style="width: 100%">
							<u-checkbox
								:name="foodItem.food_id"
								v-for="(foodItem, foodIndex) in item.list"
								:key="foodIndex"
								@change="changeCheckboxFood(item, foodItem)"
							>
								<view class="food-item m-b-20">
									<u-image
										width="80rpx"
										height="80rpx"
										class="food-img"
										:src="foodItem.image ? foodItem.image : themeImgPath.img_default"
									></u-image>

									<view class="food-info">
										<view class="food-info-left">
											<view>{{ nameFormat(foodItem.name, 6) }}</view>
											<view class="flex col-center">
												<view class="muted mini m-r-20">
													约
													<text class="m-l-6 m-r-6">{{ foodItem.weight }}</text>
													g
												</view>
												<view class="muted mini">{{ foodItem.energy_kcal }} kcal</view>
											</view>
										</view>
									</view>
								</view>
							</u-checkbox>
						</u-checkbox-group>
					</view>
				</view>
			</view>
			<view class="" v-if="foodData.length">
				<view class="" v-if="!allFinishPlanStatus">
					<view class="recipe-btn" @click="clickAllAddMealPlanFood">一键记录饮食</view>
					<view class="muted text-center mini m-t-20">记录饮食查看更多营养信息</view>
				</view>
				<view class="primary flex flex-center m-t-20" v-else @click="gotoNutritionalAnalysis">
					查看营养摄入
					<u-icon color="primary" name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
		</view>
		<view class="recipe-plain-wrap-banner" v-if="false">
			<swiper class="swiper-wrapper" :indicator-dots="false" :autoplay="true" @change="changeSwiper">
				<swiper-item v-for="(item, i) in indexMealPlanList.data" :key="i">
					<view
						:style="{ backgroundImage: 'url(' + item.image + ')' }"
						class="selected-wrapp selected-item flex row-center flex-col m-b-20"
						@click="clickMenuPlanDetails(item)"
					>
						<view class="title m-b-20">{{ item.name }}</view>
						<view class="text">{{ item.use_count ? item.use_count : 0 }}人正在使用</view>
					</view>
				</swiper-item>
			</swiper>
			<!-- 指示器 -->
			<view class="swiper-dot-wrapper flex flex-center">
				<view
					v-for="(j, k) in indexMealPlanList.data"
					:key="k"
					:class="['swiper-dot', currentSwiper === k ? 'active' : '']"
				></view>
			</view>
		</view>
		<u-modal :show="showRecipePlainTips" title="规律进餐" @confirm="showRecipePlainTips = false" :confirmColor="variables.colorPrimary">
			<view class="slot-content">
				<view>规律进餐是实现平衡膳食、合理营养的前提。进餐不规律会引起代谢紊乱，增加肥胖、糖尿病等疾病的发生风险。</view>
				<view>通常情况下，上班上学时间相对稳定，综合考虑消化系统生理特点和日常生活习惯，进餐时间建议如下。</view>
				<view>早餐: 6:30一8:30，用餐时间15一20分钟。</view>
				<view>午餐: 11:30一13:30，用餐时间20一30分钟。</view>
				<view>晚餐: 18:00一20:00，至少在睡前2小时完成进餐，用餐时间20一30分钟。</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
import {
	getApiHealthyIndexMealPlan,
	getApiAddMealPlanFood,
	getApiDeleteMealPlanFood,
	getApiAddAllMealPlanFood
} from '@/api/healthy.js'
import { deepClone, plus } from '@/utils/util'
export default {
	props: {},
	data() {
		return {
			indexMealPlanList: {}, // 数据列表
			showRecipePlainTips: false,
			checked: [],
			currentSwiper: 0,
			foodData: [],
			allFinishPlanStatus: false
		}
	},
	created() {
		// this.getHealthyIndexMealPlan()
	},
	onShow() {},
	mounted() {},
	methods: {
		getHealthyIndexMealPlan() {
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
			getApiHealthyIndexMealPlan()
				.then(res => {
					uni.hideLoading()
					if (res.code === 0) {
						this.indexMealPlanList = res.data.results
						if (res.data.results.is_menu_plan) {
							this.menuPlanDetailData(res.data.results)
						}
					} else {
						uni.hideLoading()
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		menuPlanDetailData(data) {
			let meal = {
				breakfast: {
					name: '早餐',
					totalKcal: 0,
					mealTime: '6:30-8:30',
					meal_type: 'breakfast',
					checkedFoodId: []
				},
				lunch: {
					name: '午餐',
					totalKcal: 0,
					mealTime: '11:30-13:30',
					meal_type: 'lunch',
					checkedFoodId: []
				},
				dinner: {
					name: '晚餐',
					totalKcal: 0,
					mealTime: '18:00-20:00',
					meal_type: 'dinner',
					checkedFoodId: []
				}
			}
			this.foodData = []
			Object.keys(meal).forEach((v, index) => {
				if (data[v + '_main'] && data[v + '_main'].length) {
					let item = deepClone(meal[v])
					item.totalKcal = data[v + '_main'].reduce((prev, curr) => {
						//  使用 Array.prototype.reduce() 方法遍历数组并提取满足条件的 food_id
						if (curr.finish_plan) {
							item.checkedFoodId.push(curr.food_id)
						}
						return plus(prev, curr.energy_kcal) // 计算 营养合计
					}, 0)
					item.list = data[v + '_main']
					item.food_type = 'main'
					this.foodData.push(item)
				}
				if (data[v + '_sec'] && data[v + '_sec'].length) {
					let item = deepClone(meal[v])
					item.totalKcal = data[v + '_sec'].reduce((prev, curr) => {
						//  使用 Array.prototype.reduce() 方法遍历数组并提取满足条件的 food_id
						if (curr.finish_plan) {
							item.checkedFoodId.push(curr.food_id)
						}
						return plus(prev, curr.energy_kcal) // 计算 营养合计
					}, 0)
					item.list = data[v + '_sec']
					item.name += '加餐'
					item.food_type = 'sec'
					item.mealTime = ''
					this.foodData.push(item)
				}
			})
			// 所有数据finish_plan 为true的时候返回
			this.allFinishPlanStatus = this.foodData.every(item => item.list.every(food => food.finish_plan))
		},
		changeCheckboxFood(item, foodItem) {
			let params = {
				meal_type: item.meal_type,
				food_type: item.food_type
			}
			// 如果已经记录 就删除记录
			if (foodItem.finish_plan) {
				this.getDeleteMealPlanFood({ ...params, payment_food_id: foodItem.payment_food_id })
			} else {
				this.getAddMealPlanFood({ ...params, food_id: foodItem.food_id })
			}
		},
		// 单个记录菜品
		getAddMealPlanFood(params) {
			// this.$showLoading({
			// 	title: '记录中....',
			// 	mask: true
			// })
			getApiAddMealPlanFood(params)
				.then(res => {
					// uni.hideLoading()
					if (res.code === 0) {
						this.getHealthyIndexMealPlan()
					} else {
						// uni.hideLoading()
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					// uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		// 删除记录菜品
		getDeleteMealPlanFood(params) {
			// this.$showLoading({
			// 	title: '记录中....',
			// 	mask: true
			// })
			getApiDeleteMealPlanFood(params)
				.then(res => {
					// uni.hideLoading()
					if (res.code === 0) {
						this.getHealthyIndexMealPlan()
					} else {
						// uni.hideLoading()
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					// uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		// 一键记录菜品
		getAddAllMealPlanFood(params) {
			// this.$showLoading({
			// 	title: '记录中....',
			// 	mask: true
			// })
			getApiAddAllMealPlanFood(params)
				.then(res => {
					// uni.hideLoading()
					if (res.code === 0) {
						this.getHealthyIndexMealPlan()
					} else {
						// uni.hideLoading()
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					// uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},

		clickAllAddMealPlanFood() {
			let params = {
				add_food_data: []
			}
			this.foodData.forEach(v => {
				let foodData = {
					food_ids: [],
					meal_type: v.meal_type,
					food_type: v.food_type
				}
				for (let i = 0; i < v.list.length; i++) {
					let foodItem = v.list[i]
					if (!foodItem.finish_plan) {
						foodData.food_ids.push(foodItem.food_id)
					}
				}
				params.add_food_data.push(foodData)
			})
			this.getAddAllMealPlanFood(params)
		},
		changeSwiper(e) {
			this.currentSwiper = e.detail.current
		},
		clickRecipeBanner() {},
		clickMenuPlanDetails(item) {
			this.$miRouter.push({
				path: '/pages_health_pomr/menu_plan/detail',
				query: {
					id: item.id // 餐段
				}
			})
		},
		clickMenuPlan() {
			this.$miRouter.push({
				path: '/pages_health_pomr/menu_plan/index',
				query: {}
			})
		},
		gotoNutritionalAnalysis() {
			// let meal = ['breakfast', 'lunch', 'dinner']
			// let meal_type = ''
			// meal.forEach((v, index) => {
			// 	// 切割餐段
			// 	if (this.indexMealPlanList[v + '_main'] || this.indexMealPlanList[v + '_sec']) {
			// 		meal_type = v
			// 	}
			// })
			this.$miRouter.push({
				path: '/pages_health_pomr/nutritional_analysis/index',
				// query: {
				// 	meal_type: meal_type
				// }
			})
		},
		// 格式化文字 超过多少显示...
		nameFormat(name, number) {
			if (!name) return
			let subStr = name.slice(0, number)
			subStr = subStr + (name.length > number ? '...' : '')
			return subStr
		}
	}
}
</script>

<style lang="scss">
.recipe-plain-wrap {
	.recipe-plain {
		background: #ffffff;
		border-radius: 20rpx;
		padding: 30rpx;
		.main-card {
			background-color: #f6f7fb;
			border-radius: 20rpx;
			padding: 20rpx 30rpx;
			margin: 20rpx 0;
		}
		.plain-info {
			.num {
				background-color: $color-primary;
				color: #ffffff;
				padding: 4rpx 18rpx;
				border-radius: 20rpx;
				margin: 0 10rpx;
				font-size: 24rpx;
			}
		}
		.plain-list {
			margin-bottom: 30rpx;
			.plain-item {
				.meal-text {
					font-weight: bold;
					.time {
						background-color: $color-primary;
						color: #ffffff;
						padding: 4rpx 10rpx;
						border-radius: 10rpx;
						margin: 0 10rpx;
						font-size: 24rpx;
						font-weight: bold;
					}
				}
				.food-list {
					.food-item {
						display: flex;
						justify-content: space-between;
						width: 100%;
						.food-img {
							border-radius: 14rpx;
							overflow: hidden;
							margin-left: 10rpx;
						}
						.food-info {
							width: 100%;
							margin-left: 20rpx;
							display: flex;
							justify-content: space-between;
							align-items: center;
							.food-info-left {
								display: flex;
								flex-direction: column;
								justify-content: space-between;
								height: 100%;
							}
						}
					}
				}
			}
		}
		.recipe-btn {
			margin: auto;
			width: 220rpx;
			background-color: $color-primary;;
			color: #ffffff;
			border-radius: 40rpx;
			text-align: center;
			padding: 10rpx 0;
		}
	}
	.recipe-plain-wrap-banner {
		height: 220rpx;
		border-radius: 20rpx;
		overflow: hidden;
		position: relative;
		.swiper-dot-wrapper {
			position: absolute;
			bottom: 10%;
			left: 50%;
			transform: translate(-50%, -50%);
			border-radius: 200rpx;
			.swiper-dot {
				width: 35rpx;
				height: 7rpx;
				border-radius: 200rpx;
				margin: 0 4rpx;
				transition: transform 0.3s;
				background-color: #e3e3e3;
				&.active {
					width: 35rpx;
					background-color: $color-primary;
				}
			}
		}
		.swiper-wrapper {
			height: 100%;
			.selected-item {
				padding: 30rpx;
				width: 670rpx;
				height: 250rpx;
				box-sizing: border-box;
				background-repeat: no-repeat;
				background-size: cover;
				background-position: center;
			}
			.selected-wrapp {
				width: 100%;
				border-radius: 10rpx;
				.title {
					font-size: 42rpx;
					font-weight: 500;
					color: #1d201e;
				}
				.text {
					color: #1d201e;
				}
			}
		}
	}
	.u-checkbox {
		width: 100%;
	}
}
</style>
