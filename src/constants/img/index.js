import { isObject } from "@/utils/type"

import { defaultImgs } from "./default"
import { redImgs } from "./red"


/**
 * @description 匹配下路径是否是远程地址
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|:\/\/)/.test(path)
}

/**
 * @description 根据主题生成相关图片的地址
 * @param {String} url 
 * @param {Object} obj 
 * @returns {Object}
 */
export function generateImgList(url, obj) {
  let imgObj = {}
  if (!isObject(obj)) {
    return imgObj
  }
  for (const key in obj) {
    if (Object.hasOwnProperty.call(obj, key)) {
      const item = obj[key];
      // 变量当前的图片地址，如果是的带http或://的则不需要拼接了
      if (isExternal(item)) {
        imgObj[key] = item
      } else {
        imgObj[key] = `${url}/${item}`
      }
    }
  }
  return imgObj
}

// 主题图片地址
export const imgPathList = {
  default: defaultImgs,
  red: redImgs
}