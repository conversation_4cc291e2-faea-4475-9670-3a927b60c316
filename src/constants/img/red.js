
import { imgPrefix } from './prefix'
import { commonImg } from "./common"

// 图片配置文件
const imgIndexIcon = imgPrefix.imgIndexIcon
const imgIndexImages = imgPrefix.imgIndexImages
const imgIndexSource = imgPrefix.imgIndexSource
const imgIndexInfo = imgPrefix.imgIndexInfo
const imgIndexPomr = imgPrefix.imgIndexPomr
const imgIndexBundle = imgPrefix.imgIndexBundle
const imgIndexThird = imgPrefix.imgIndexThird
const imgIndexCommon = imgPrefix.imgIndexCommon
const imgIndexMember = imgPrefix.imgIndexMember
const imgHealth = imgPrefix.imgHealth

// 不同主题的图标，需要根据主题切换的图标
const redImgList = {
  // tabbar图标
  img_tab_home_s : imgIndexIcon + 'tab_home_s.png',
  img_tab_user_s : imgIndexIcon + 'tab_user_s.png',
  img_tab_healthy_s : imgIndexIcon + 'tab_healthy_s.png',
  img_avatar : imgIndexImages + 'avatar.png',
  img_login_bg : imgIndexImages + 'login_bg.png',
  img_logo : imgIndexImages + 'logo.png',
  img_login_green_dining: imgIndexIcon +'ic_login_green_dining.png',
  img_jiaofei_bg : imgIndexCommon + 'jiaofei_icons/' + 'jiaofei_bg.png',
  img_dianfei : imgIndexCommon + 'jiaofei_icons/' +'dianfei.png',
  img_xuezafei : imgIndexCommon + 'jiaofei_icons/' +'xuezafei.png',
  img_huoshifei : imgIndexCommon + 'jiaofei_icons/' +'huoshifei.png',
  img_qitashoufei : imgIndexCommon + 'jiaofei_icons/' +'qitashoufei.png',
  img_car_warn : imgIndexCommon + 'car_admin_icons/' +'car_warn.png',
  img_healthy_no_diet: imgHealth + 'healthy_no_diet.png',
  img_recognizeBg : imgIndexImages +'recognizeBg1.png',
  img_recognizeTip : imgIndexIcon +'recognizeTip.png',
  img_bundle_coupon_not : imgIndexBundle + 'images/' + 'coupon_not.png',
  img_weekly_report_icon: imgIndexMember + 'reportIcon.png',
  img_servicenotice: imgIndexMember +'icon/serviceNotice.png',
  system_notification: imgIndexMember + 'newVIP/system_notification.png',
  img_male: imgIndexIcon + 'male.png',
  img_female: imgIndexIcon + 'female.png',
  img_male_select: imgIndexIcon + 'male_select.png',
  img_female_select: imgIndexIcon + 'female_select.png',
  img_question_icon: imgIndexCommon + 'question/' +'icon.png',
  img_question_top_bg: imgIndexCommon + 'question/' +'top_bg.png'
}

export const redImgs = Object.assign({}, commonImg, redImgList)
