
// icon 可以是为imgPath中定义的，可以随是require('@/static/icons/canteen_invoice_record.png')，也可以是远程图片http://等方式
// 因为要做主题切换，所有图标最好为imgPath中定义的，这样可以动态切换图标
// 针对有些情况主题图标可以使用css更换颜色，但个别不需要更换颜色的可以使用noFilter: true

export const allMenuList = [
  {
    // icon: '@/static/icons/canteen_chongzhi.png',
    icon: 'img_canteen_chongzhi',
    name: '充值',
    path: '/pages_bundle/recharge/recharge',
    type: 'recharge',
    permission: 'charge',
    // noFilter: true
  },
  {
    // icon: '@/static/icons/canteen_dingdan.png',
    icon: 'img_canteen_dingdan',
    name: '订单',
    path: '/pages_order/order/order_lists?mode=items',
    permission: 'order'
  },
  {
    // icon: '@/static/icons/canteen_yuyue.png',
    icon: 'img_canteen_yuyue',
    name: '预约点餐',
    path: '/pages_bundle/appoint/appoint_order',
    type: 'reservation',
    permission: 'reservation'
  },
  {
    // icon: '@/static/icons/canteen_baocan.png',
    icon: 'img_canteen_baocan',
    name: '报餐',
    path: '/pages_bundle/meal_report/user_meal_report',
    type: 'report_meal',
    permission: 'report'
  },
  // {
  // 	icon: "@/static/icons/canteen_shenqing.png",
  // 	name: "就餐申请",
  // 	path: "/pages_bundle/dining_apply/dining_status"
  // },
  // {
  // 	icon: "@/static/icons/canteen_zahngdan.png",
  // 	name: "账单",
  // 	path: "/pages_info/wallet/bill"
  // },
  {
    icon: 'img_bundle_coupon',
    name: '卡劵中心',
    path: '/pages_bundle/coupon/index',
    type: 'coupon',
    permission: 'coupon'
  },
  {
    // icon: '@/static/icons/canteen_caipu.png',
    icon: 'img_canteen_caipu',
    name: '意向菜谱',
    path: '/pages_bundle/intention_food/intention_food',
    orgPath: '/pages_bundle/intention_food/choice_org',
    type: 'intention_menu',
    permission: 'intent_food'
  },
  // {
  // 	icon: "@/static/icons/canteen_youhui.png",
  //  icon: imgPath.img_canteen_youhui,
  // 	name: "优惠中心",
  // 	path: "/pages_bundle/discount_center/discount_center",
  //   no_permission: true
  // },
  {
    // icon: '@/static/icons/canteen_zahnghu.png',
    icon: 'img_canteen_zahnghu',
    name: '账户信息',
    path: '/pages_info/account_info/account_info',
    type: 'account_info',
    permission: 'account_info'
  },
  // #ifdef H5
  {
    // icon: '@/static/icons/buffet.png',
    icon: 'img_buffet',
    name: '托盘绑定',
    path: '',
    type: 'bind_buffet',
    permission: 'tray_bind'
  },
  {
    // icon: '@/static/icons/face_collect.png',
    icon: 'img_face_collect',
    name: '人脸采集',
    path: '/pages_info/face_gather/face_gather',
    type: 'face_collect',
    permission: 'face_collect'
  },
  // #endif
  {
    // icon: '@/static/icons/my_appoint.png',
    icon: 'img_my_appoint',
    name: '我的预约',
    path: '/pages_bundle/appoint/user_appoint',
    type: 'myReservation',
    permission: 'reservation'
  },
  {
    // icon: '@/static/icons/jiaofei.png',
    icon: 'img_jiaofei',
    name: '缴费中心',
    path: '/pages_common/jiaofei_center/jiaofei_center',
    type: 'jiaofei',
    permission: 'jiaofei'
  },
  {
    // icon: '@/static/icons/control_attendance.png',
    icon: 'img_control_attendance',
    name: '门禁考勤',
    path: '/pages_common/control_attendance/control_attendance',
    type: 'control_attendance',
    permission: 'attendance'
  },
  {
    // icon: '@/static/icons/control_attendance.png',
    icon: 'img_control_attendance',
    name: '后勤门禁',
    // path: '/pages_common/access_attendance/access_attendance_main', // 后续做这个
    path: '/pages_common/access_attendance/access_record', // 先跳这里
    type: 'myAttendance',
    // permission: 'attendance'
    // 与产品确认过  不要与门禁考勤相同权限  所以更换permission
    permission: 'myAttendance'
  },
  {
    // icon: '@/static/icons/icon_review.png',
    icon: 'img_icon_review',
    name: '审核查询',
    path: '/pages_order/review/index',
    type: 'order_review',
    permission: 'order_review'
  },
  {
    // icon: '@/static/icons/sign_icon.png',
    icon: 'img_sign_icon',
    name: '免密支付',
    path: '/pages_info/user_config/free_payment_setting',
    type: 'free_payment_setting',
    permission: 'free_payment_setting'
  },
  {
    icon: 'img_smart_campus',
    name: '智慧校园',
    path: '',
    type: 'zhxy',
    permission: 'zhxy'
  },
  // {
  //   icon: require('@/static/icons/feedback.png'),
  //   name: '食堂建议',
  //   path: '/pages_info/feedback/proposal_complaint',
  //   type: 'shop_feedback',
  //   permission: 'shop_feedback'
  // },
  {
    // icon: '@/static/icons/car_admin.png',
    icon: 'img_car_admin',
    name: '车辆管理',
    path: '/pages_common/car_admin/car_order',
    type: 'car_travel',
    permission: 'car_travel',
  },
  {
    icon: 'img_canteen_advise',
    name: '食堂建议',
    path: '/pages_info/feedback/canteen_advise',
    type: 'shop_feedback',
    permission: 'shop_feedback'
  },
  {
    icon: 'img_canteen_notice',
    name: '消息通知',
    path: '/pages_info/news/project_news',
    permission: 'marketing_notice'
  },
  {
    icon: 'img_canteen_order_evaluation',
    name: '我的评价',
    path: '/pages_info/evaluate/user_evaluate',
    permission: 'order_evaluation'
  },
  {
    icon: 'img_canteen_invoice_record',
    name: '开票记录',
    path: '/pages_order/invoice/list',
    permission: 'invoice_record'
  },
  {
    icon: 'img_abc_ebank',
    name: '电子账户',
    path: '',
    type: 'ebank',
    permission: 'ebank'
  },
  {
    icon: 'img_meal_apply',
    name: '访客餐',
    path: '/pages_common/meal_apply/apply_list',
    permission: 'approve_order'
  },
  {
    icon: 'img_face_pay',
    name: '一脸通行',
    path: '/pages_info/user_config/alipay_face/sign_apply',
    type: 'facepass',
    permission: 'facepass'
  },
  {
    icon: 'img_meal_subsidy',
    name: '餐补使用',
    path: '/pages_info/user_config/meal_subsidy/sign_apply',
    type: 'facepass_cb',
    permission: 'facepass_cb'
  },
  {
    icon: 'img_alipay_qy_code',
    name: '企业码',
    path: '/pages_info/user_config/qy_code/sign_apply',
    type: 'alipay_qycode',
    permission: 'alipay_qycode'
  },
  {
    path: '/pages_member/member_center/VIP_page',
    type: 'member',
    permission: 'member'
  },
  {
    icon: 'img_face_collect',
    name: '洗衣中心',
    path: '',
    type: 'zk_laundry',
    permission: 'zk_laundry'
  },
  {
    icon: 'img_third_shop',
    name: '商城',
    path: '',
    type: 'third_shop',
    permission: 'third_shop'
  },
  {
    icon: 'img_meituan',
    name: '美团',
    path: '',
    type: 'mei_tuan',
    noFilter: true,
    permission: 'mei_tuan'
  },
  {
    icon: 'img_voip',
    name: '视频公话',
    path: '/pages_third/voip/index',
    type: 'voip',
    permission: 'voip'
  },
  {
    icon: 'img_meal_package',
    name: '我的餐包',
    path: '/pages_common/meal_package/meal_package_list',
    type: 'meal_pack',
    permission: 'meal_pack'
  }
  // {
  //   icon: '@/static/icons/canteen_more.png',
  //   icon: imgPath.img_canteen_more,
  //   name: '拓展',
  //   path: '',
  //   no_permission: true
  // }
]
