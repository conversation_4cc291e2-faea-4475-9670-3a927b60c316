<template>
  <view :style="theme.style" class="dining-status">
    <view class="header">
      就餐状态
      <view class="status-select">
        全部
        <trigonometry />
      </view>
    </view>
    <view class="main-scroll">
      <mescroll-uni
        ref="mescrollRef"
        :fixed="false"
        :safearea="false"
        :bottom="120"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
      >
        <view class="dining-lists">
          <view class="dining-item">
            <view class="item-header flex row-between">
              <view class="lg">12月01日就餐申请</view>
              <view class="muted">未审批</view>
            </view>
            <view class="item-info sm">
              <view class="flex m-b-20">
                <view class="info-label">餐段：</view>
                <view>午餐</view>
              </view>
              <view class="flex m-b-20">
                <view class="info-label">就餐人数：</view>
                <view>2</view>
              </view>
              <view class="flex m-b-20">
                <view class="info-label">餐桌：</view>
                <view>A201</view>
              </view>
            </view>
            <view class="item-footer flex row-right">
              <view class="m-l-20">
                <u-button size="small" type="primary" plain text="取消申请"></u-button>
              </view>
              <!-- <view class="m-l-20">
								<u-button type="primary" size="small" text="查看详情"></u-button>
							</view> -->
            </view>
          </view>

          <view class="dining-item">
            <view class="item-header flex row-between">
              <view class="lg">12月01日就餐申请</view>
              <view class="warning">待用餐</view>
            </view>
            <view class="item-info sm">
              <view class="flex m-b-20">
                <view class="info-label">餐段：</view>
                <view>早餐</view>
              </view>
              <view class="flex m-b-20">
                <view class="info-label">就餐人数：</view>
                <view>4</view>
              </view>
              <view class="flex m-b-20">
                <view class="info-label">餐桌：</view>
                <view>A202</view>
              </view>
            </view>
            <view class="item-footer flex row-right">
              <view class="m-l-20">
                <u-button size="small" type="primary" plain text="取消申请"></u-button>
              </view>
              <!-- <view class="m-l-20">
                    			<u-button type="primary" size="small" text="查看详情"></u-button>
                    		</view> -->
            </view>
          </view>

          <view class="dining-item">
            <view class="item-header flex row-between">
              <view class="lg">11月31日就餐申请</view>
              <view class="u-error">已拒绝</view>
            </view>
            <view class="item-info sm">
              <view class="flex">
                <view class="info-label">餐段：</view>
                <view>早餐</view>
              </view>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>
    <view class="apply-btn">
      <router-link to="/pages_bundle/dining_apply/dining_apply">
        <u-button text="申请用餐" type="primary"></u-button>
      </router-link>
    </view>
  </view>
</template>

<script>
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
export default {
  mixins: [MescrollMixin],
  data() {
    return {}
  },
  methods: {
    upCallback() {
      this.mescroll.endBySize(1, 1)
    }
  }
}
</script>

<style lang="scss">
page {
  padding: 0;
}
.dining-status {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 90rpx;
    padding: 0 40rpx;
    background-color: #fff;
    border-top: $border-base;
    .status-select {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: $font-size-sm;
      margin-left: 20rpx;
      width: 140rpx;
      padding: 10rpx;
      border: 1px solid $color-primary;
      border-radius: 10rpx;
    }
  }
  .main-scroll {
    min-height: 0;
    flex: 1;
    .dining-lists {
      padding: 35rpx 40rpx 0;
      .dining-item {
        background: #ffffff;
        border-radius: 20rpx;
        padding: 0 30rpx;
        margin-bottom: 24px;
        .item-header {
          padding: 30rpx;
        }
        .item-info {
          border-top: $border-base;
          border-bottom: $border-base;
          padding: 30rpx;
          .info-label {
            width: 140rpx;
            color: $color-text-regular;
          }
        }
        .item-footer {
          padding: 20rpx 0;
        }
      }
    }
  }
  .apply-btn {
    position: fixed;
    left: 0;
    bottom: 40rpx;
    width: 100%;
    padding: 0 40rpx;
    height: 80rpx;
  }
}
</style>
