<template>
  <view :style="theme.style" class="dining-apply">
    <view class="content">
      <u-form :model="formData" ref="uFormRef" label-width="162">
        <u-form-item label="就餐食堂：" prop="name">
          <select-lay v-model="formData.value" :showplaceholder="false" :options="options" />
        </u-form-item>
        <u-form-item label="就餐时间：" prop="name"></u-form-item>

        <calendar></calendar>
        <u-form-item label="餐段：" prop="name">
          <u-radio-group v-model="formData.radiovalue" @change="groupChange" :active-color="variables.colorPrimary">
            <u-radio
              :customStyle="{ marginTop: '30rpx' }"
              label-size="24"
              v-for="(item, index) in radiolist"
              :key="index"
              :label="item.name"
              :name="item.name"
              @change="radioChange"
            ></u-radio>
          </u-radio-group>
        </u-form-item>
        <u-form-item label="就餐人数：" prop="name">
          <view class="flex col-center">
            <u-input v-model="formData.name" :customStyle="{ maxWidth: '100rpx' }" />
            <text class="xs m-l-16">人</text>
          </view>
        </u-form-item>
        <u-form-item label="餐桌：" prop="name">
          <view style="width: 150rpx">
            <select-lay v-model="formData.value1" :showplaceholder="false" :options="options1" />
          </view>
        </u-form-item>
        <u-form-item label="备注：" prop="name">
          <u-textarea v-model="formData.value3" placeholder="请输入内容"></u-textarea>
        </u-form-item>
      </u-form>
      <view class="m-t-20">
        <u-button type="primary" :custom-style="{ width: '360rpx' }" text="提交申请"></u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        radiovalue: '早餐',
        value: 1,
        value1: 1
      },
      radiolist: [
        {
          name: '早餐'
        },
        {
          name: '午餐'
        },
        {
          name: '下午茶'
        },
        {
          name: '晚餐'
        },
        {
          name: '宵夜'
        },
        {
          name: '凌晨餐'
        }
      ],
      options: [
        {
          label: '朴食科技智慧食堂',
          value: 1
        }
      ],
      options1: [
        {
          label: '餐桌',
          value: 1
        }
      ]
    }
  },
  methods: {}
}
</script>

<style lang="scss">
.dining-apply {
  padding: 20rpx 34rpx;

  .content {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx 36rpx;
  }
}
</style>
