<!-- 授权钱包 -->
<template>
  <view :style="theme.style" class="authorization-wallet">
    <view class="">
      <view class="m-b-26 md f-w-500">朴食科技智慧食堂</view>
      <view class="authorization-wallet-item nr">
        <view class="flex row-between col-center p-t-25 p-b-25 item">
          <view class="">储值钱包</view>
          <view class="">
            <u-switch :activeColor="variables.colorPrimary" v-model="isOpenList.isOpen1" @change="switchChange" :size="34"></u-switch>
          </view>
        </view>

        <view class="flex row-between col-center p-t-25 p-b-25 item">
          <view class="">农行电子钱包</view>
          <view class="">
            <u-switch :activeColor="variables.colorPrimary" v-model="isOpenList.isOpen2" @change="switchChange" :size="34"></u-switch>
          </view>
        </view>
      </view>
    </view>

    <view class="">
      <view class="m-b-26 md f-w-500">黄豆科技智哈食堂</view>
      <view class="authorization-wallet-item nr">
        <view class="flex row-between col-center p-t-25 p-b-25 item">
          <view class="">储值钱包</view>
          <view class="">
            <u-switch :activeColor="variables.colorPrimary" v-model="isOpenList.isOpen3" @change="switchChange" :size="34"></u-switch>
          </view>
        </view>

        <view class="flex row-between col-center p-t-25 p-b-25 item">
          <view class="">农行电子钱包</view>
          <view class="">
            <u-switch :activeColor="variables.colorPrimary" v-model="isOpenList.isOpen4" @change="switchChange" :size="34"></u-switch>
          </view>
        </view>
      </view>
    </view>

    <view class="footer bg-white">
      <view class="footer--warpper flex flex-center">
        <u-button
          text="确认"
          :color="variables.bgLinearGradient1"
          @click="confirm"
        ></u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isOpenList: {
        isOpen1: true,
        isOpen2: false,
        isOpen3: true,
        isOpen4: false
      }
    }
  },

  methods: {
    switchChange() {},

    // 确定
    confirm() {
      this.$miRouter.back()
    }
  },

  onLoad() {}
}
</script>

<style lang="scss">
.authorization-wallet {
  padding: 40rpx;

  .authorization-wallet-item {
    border-radius: 20rpx;
    background: #ffffff;
    padding: 30rpx;
    margin-bottom: 56rpx;

    .item:not(:last-of-type) {
      border-bottom: 1rpx solid $border-color-base;
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 190rpx;
    }
  }
}
</style>
