<template>
  <view :style="theme.style" class="related_personnel">
    <!-- Section 主体 Start -->
    <view class="section bg-white">
      <view class="item" @click="toSearchItem">
        <view class="black nr m-b-20">项目名称</view>
        <u-input placeholder="选择绑定对象所在的项目" border="surround" fontSize="26rpx" v-model="formData.item_name">
          <u-icon slot="suffix" color="#8F9295" name="arrow-right" size="24rpx"></u-icon>
        </u-input>
      </view>

      <view class="item">
        <view class="black nr m-b-20">姓名</view>
        <u-input placeholder="输入你要绑定的姓名" border="surround" fontSize="26rpx" v-model="formData.name"></u-input>
      </view>

      <view class="item">
        <view class="black nr m-b-20">用户编号</view>
        <u-input placeholder="输入他的人员编号" border="surround" fontSize="26rpx" v-model="formData.sn"></u-input>
      </view>

      <view class="item" @click="toAuthorizationWallet">
        <view class="black nr m-b-20">添加授权钱包</view>
        <u-input placeholder="选择他可使用的钱包" border="surround" fontSize="26rpx" v-model="formData.wallet">
          <u-icon slot="suffix" color="#8F9295" name="arrow-right" size="24rpx"></u-icon>
        </u-input>
      </view>
    </view>
    <!-- Section End -->

    <!-- Footer 底部 Start -->
    <view class="footer bg-white">
      <view class="footer--warpper flex flex-center">
        <u-button
          text="确认"
          :color="variables.bgLinearGradient1"
          @click="confirmAddItemsFunc"
        ></u-button>
      </view>
    </view>
    <!-- Footer End -->

    <!-- Components Modal Start -->

    <!-- 弹窗-余额上限 -->
    <u-modal :show="isError" :title="'添加失败'" confirmColor="#5A6080">
      <view class="xl text-center" v-html="content"></view>
    </u-modal>

    <!-- Components Modal End -->
  </view>
</template>

<script>
export default {
  // Data Start
  data() {
    return {
      value: '',
      isError: false,
      // 弹窗提示文字
      content: '‘黄小二’已被绑定，<span>请确认信息是否正确 </span>或 <span>先解除绑定再进行添加</span>',

      formData: {
        item_name: '',
        name: '',
        sn: '',
        wallet: ''
      }
    }
  },
  // Data End

  // Methods Start
  methods: {
    // 切换项目
    toSearchItem() {
      this.$miRouter.push('/pages_bundle/switch_items/switch_items')
    },

    // 去到钱包授权
    toAuthorizationWallet() {
      this.$miRouter.push('/pages_bundle/related_personnel/authorization_wallet')
    },

    // 确定按钮
    confirmAddItemsFunc() {
      this.$miRouter.back()
    }
  },
  // Methods End

  // Life Cycle Start
  onLoad() {}
  // Life Cycle End
}
</script>

<style lang="scss">
.related_personnel {
  .ls-card {
    border-radius: 20rpx;
  }

  .section {
    padding: 40rpx;
    margin-bottom: calc(100rpx + env(safe-area-inset-bottom));
    border-top: $border-base;

    .item {
      margin-bottom: 30rpx;
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 190rpx;
    }
  }
}
</style>
