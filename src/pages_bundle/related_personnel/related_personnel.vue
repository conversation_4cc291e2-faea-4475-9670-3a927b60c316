<!-- 关联人员 -->
<template>
  <view :style="theme.style" class="related-personnel">
    <view class="top flex row-between col-center">
      <view class="nr">关联人员</view>
      <view class="add-btn flex flex-center" @click="goToAddPersonnel">添加</view>
    </view>

    <view class="related-personnel-item m-b-30">
      <view class="flex row-between col-center m-b-22">
        <view class="nr f-w-500">朴食科技智慧食堂</view>
        <view class="sm muted" @click="goToUserInfo">人员信息</view>
      </view>

      <view class="flex p-t-22 p-b-22 item">
        <view class="sm muted">姓名：</view>
        <view class="sm lighter">黄小二</view>
      </view>

      <view class="flex p-t-22 p-b-22 item">
        <view class="sm muted">用户编号：</view>
        <view class="sm lighter">20211221</view>
      </view>

      <view class="flex p-t-22 p-b-22 item">
        <view class="sm muted">关联钱包：</view>
        <view class="sm lighter">食堂-储值钱包，农行电子钱包</view>
      </view>

      <view class="flex flex-1 row-right m-t-26">
        <view class="cancel-btn flex flex-center m-r-20" @click="isShow = true">取消关联</view>
        <view class="info-btn flex flex-center">消费信息</view>
      </view>
    </view>

    <view class="related-personnel-item">
      <view class="flex row-between col-center m-b-22">
        <view class="nr f-w-500">黄豆科技智哈食堂</view>
        <view class="sm muted" @click="goToUserInfo">人员信息</view>
      </view>

      <view class="flex p-t-22 p-b-22 item">
        <view class="sm muted">姓名：</view>
        <view class="sm lighter">黄小六</view>
      </view>

      <view class="flex p-t-22 p-b-22 item">
        <view class="sm muted">用户编号：</view>
        <view class="sm lighter">20211221</view>
      </view>

      <view class="flex p-t-22 p-b-22 item">
        <view class="sm muted">关联钱包：</view>
        <view class="sm lighter">食堂-储值钱包，农行电子钱包</view>
      </view>

      <view class="flex flex-1 row-right m-t-26">
        <view class="cancel-btn flex flex-center m-r-20" @click="isShow = true">取消关联</view>
        <view class="info-btn flex flex-center">消费信息</view>
      </view>
    </view>

    <u-modal
      :show="isShow"
      :title="'提示'"
      confirmColor="#5A6080"
      confirmText="确定"
      @confirm="isShow = false"
      :showCancelButton="true"
      cancelText="我再想想"
      @cancel="isShow = !isShow"
    >
      <view class="xl text-center" v-html="content"></view>
    </u-modal>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isShow: false,
      content: '取消关联后，授权使用的钱包对方 将不可用，确定要取消关联？'
    }
  },

  methods: {
    // 去到人员信息
    goToUserInfo() {
      this.$miRouter.push({
        path: '/pages_info/account_info/detail',
        query: {
          type: 'basic_info'
        }
      })
    },

    // 去添加关联人员
    goToAddPersonnel() {
      this.$miRouter.push({
        path: '/pages_bundle/related_personnel/add_personnel'
      })
    }
  }
}
</script>

<style lang="scss">
.related-personnel {
  padding: 100rpx 40rpx 0;
  position: relative;

  .top {
    padding: 0 40rpx;
    background: #ffffff;
    position: fixed;
    left: 0;
    /* #ifdef MP-WEIXIN */
    top: 0;
    /* #endif */
    /* #ifdef H5 */
    top: 40px;
    /* #endif */
    width: 100%;
    height: 72rpx;

    .add-btn {
      width: 104rpx;
      height: 44rpx;
      background: $color-primary;
      border-radius: 10rpx;

      font-size: 24rpx;
      color: #ffffff;
    }
  }

  .related-personnel-item {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;

    .item {
      border-bottom: 1rpx solid #efefef;
    }

    .cancel-btn {
      width: 140rpx;
      height: 54rpx;
      color: $color-primary;
      font-size: 24rpx;
      border-radius: 10rpx;
      border: 1rpx solid $color-primary;
    }

    .info-btn {
      width: 140rpx;
      height: 54rpx;
      background: $color-primary;
      border-radius: 10rpx;
      color: #ffffff;
      font-size: 24rpx;
    }
  }
}
</style>
