<template>
  <view :style="theme.style" class="appointment-detail">
    <view class="appointment-header">总订单号：{{ tradeId }}</view>
    <view class=" white-bg flex row-around" v-if="isMealReportDetail">
      <view class="text-center p-b-20">
        <view class="muted">已取餐</view>
        <view class="order-info-text">{{mealReportCount.take_out_total}}</view>
      </view>
      <view class="text-center p-b-20">
        <view class="muted">未取餐</view>
        <view class="order-info-text">{{mealReportCount.no_take_total}}</view>
      </view>
      <view class="text-center p-b-20">
        <view class="muted">已退款</view>
        <view class="order-info-text">{{mealReportCount.refund_total}}</view>
      </view>
      <view class="text-center p-b-20">
        <view class="muted">总预定</view>
        <view class="order-info-text">{{mealReportCount.total}}</view>
      </view>
    </view>
    <view class="tabs-content flex-1" v-if="tradeId">
      <appointment-lists :tradeId="tradeId" :dateValue="dateValue" :order-type="orderType" :is-meal-report-detail="isMealReportDetail" type="now"></appointment-lists>
    </view>
    <view class="meal-report-detail-btn flex row-around" v-if="isMealReportDetail && !orderInfo.pack_settings_name">
      <view class="flex-1" @click="gotoApply">
        <u-button text="批量取消" shape="circle" :color="variables.colorPrimary"></u-button>
      </view>
    </view>
  </view>
</template>

<script>
import appointmentLists from '../components/appointment/lists.vue'
import { getApiReportMealOrderInfoCount } from '@/api/report_meal.js'
import Cache from '@/utils/cache'
export default {
  components: {
    appointmentLists
  },
  data() {
    return {
      orderInfo: {},
      tradeId: '',
      orderType: '',
			dateValue:"2022-07-21",
      isMealReportDetail: false,
      mealReportCount: {}
    }
  },
  methods: {
    gotoApply() {
      this.$miRouter.push({
      	path: '/pages_order/review/apply/select_meal',
				query: {
					type: this.orderType,
          data: this.orderInfo,
          isMealReportDetail: this.isMealReportDetail
				}
      })
    },
    async getMealReportDetail() {
      try {
        const res = await getApiReportMealOrderInfoCount({
          unified_trade_no: this.tradeId,
          company_id: Cache.get('userInfo').company_id
        })
        if(res.code === 0) {
          this.mealReportCount = res.data
        } else {
          uni.$u.toast(res.msg)
        }
      } catch (error) {
        uni.$u.toast(error.message)
      }
    },
  },
  onShow() {
		this.dateValue = this.$Route.query.date
    this.orderType = this.$Route.query.order_type
    let title
    if (this.orderType === 'reservation') {
      this.tradeId = this.$Route.query.id
      title = '预约详情'
      this.isMealReportDetail = false
    } else if (this.orderType === 'report_meal') {
      title = '报餐详情'
      this.orderInfo = this.$Route.query.data
      this.tradeId = this.$Route.query.data.trade_no
      this.isMealReportDetail = true
      this.getMealReportDetail()
      console.log(this.orderInfo, 'orderInfo')
    }
    uni.setNavigationBarTitle({
      title
    })
  },
  onLoad() {}
}
</script>

<style lang="scss">
.appointment-detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .appointment-header {
    background-color: #ffffff;
    padding: 30rpx 40rpx;
    font-size: 30rpx;
    font-weight: 500;
  }
  .tabs-content {
    min-height: 0;
  }
  .white-bg{
    background-color: #ffffff;
    border-bottom: 1px solid #E4E4E4;
  }
  .order-info-text{
    font-size: 28px;
  }
  .meal-report-detail-btn {
    position: fixed;
    left: 0;
    bottom: 0rpx;
    width: 100%;
    padding: 20rpx 40rpx;
    height: 120rpx;
    background-color: #FFF;
    .flex-1{
      flex: 1;
      padding: 0 10rpx;
    }
  }
}
</style>
