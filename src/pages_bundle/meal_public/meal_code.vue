<template>
  <view :style="theme.style" class="meal-code">
    <view class="container ls-card">
      <!-- Header 头部 Start -->
      <view class="header xxl f-w-500 white flex flex-center">取餐码</view>
      <!-- Header End -->

      <!-- Section 主体 Start -->
      <view class="section bg-white flex flex-col flex-center">
        <view class="md normal m-b-15">向商家展示取餐码进行核销</view>

        <view class="code">
          <uqrcode v-if="qrcode" ref="uQRCode" :text="qrcode" :size="185" />
        </view>

        <view class="font-size-42 m-t-30">{{ qrcodeShow }}</view>
      </view>
      <!-- Section End -->
    </view>
    <view v-if="orderInfo.take_meal_type === 'cupboard'" class="fixed-bottom">
      <u-button text="一键开柜" :color="variables.colorPrimary" @click="openPopupHandle" :loading="isLoading"></u-button>
    </view>
    <!-- 弹窗 start -->
    <popup-layout
      :show.sync="showPopup"
      :content="popupTips"
      button-shape="square"
      confirm-text="立即开柜"
      :loading="isLoading"
      @confirm="confirmPopupHandle"
      @cancel="cancelPopupHandle"
      :confirm-style="popupConfirmButStyle"
      :cancel-style="popupButStyle"
    >
    </popup-layout>
    <!-- 弹窗 end -->
  </view>
</template>

<script>
import popupLayout from '@/components/popup/popup-layout'
import { apiGetCupboardCell, apiOpenCupboardCell } from '@/api/cupboard.js'

export default {
  // Data Start
  components: { popupLayout },
  data() {
    return {
      // 是否已经失效
      isInvalid: false,
      qrcode: '',
			qrcodeShow:'',
      showPopup: false,
      isLoading: false,
      loading: false,
      popupButStyle: {
        color: '#919191',
        maxWidth: '42%',
        height: '65rpx',
        fontSize: '12px'
      },
      popupConfirmButStyle: {
        backgroundColor: this.$variables.colorPrimaryLight3,
        color: '#fff',
        maxWidth: '42%',
        height: '65rpx',
        fontSize: '12px'
      },
      popupTips: '',
      orderInfo: {},
      cupboardInfo: null
    }
  },
  // Data End

  // Methods Start
  methods: {},
  // Methods End
  onShow() {
    // this.qrcode = this.$Route.query.data
  },
  // Life Cycle Start
  onLoad() {
    console.log(this.$Route.query)
		let data = this.$decodeQuery(this.$Route.query.data)
    this.orderInfo = data
    this.qrcode = data.meal_number_qrcode
		this.qrcodeShow = data.meal_number
    // uni.getScreenBrightness({
    // 	success: (res) => {
    // 		this.screenBrightness = res.value
    // 	}
    // });
    // 初始化时设置手机屏幕亮度至最高
    // uni.setScreenBrightness({
    // 	value: 1,
    // });
  },
  methods: {
    async openPopupHandle() {
      if (this.isLoading) return
      this.isLoading = true
      const [err, res] = await this.$to(apiGetCupboardCell({
        trade_no: this.orderInfo.trade_no
      }))
      this.isLoading = false
      if (err) {
        uni.$u.toast(err.message)
        return
      }
      console.log(res)
      if (res.code === 0) {
        this.cupboardInfo = res.data
        this.popupTips = `您的订单存放在${res.data.address}，是否立即开柜？`
        this.showPopup = true
      } else {
        uni.$u.toast(res.msg)
      }
    },
    async confirmPopupHandle(e) {
      if (!this.cupboardInfo) return uni.$u.toast('暂无取餐柜信息！')
      if (this.loading) return
      this.loading = true
      const [err, res] = await this.$to(apiOpenCupboardCell({
        trade_no: this.cupboardInfo.trade_no
      }))
      this.loading = false
      if (err) {
        uni.$u.toast(err.message)
        return
      }
      console.log(res)
      if (res.code === 0) {
        this.cupboardInfo = null
        this.showPopup = false
        this.popupTips = ``
        uni.showToast({
          title: '开柜成功',
          icon: 'success'
        })
      } else {
        uni.$u.toast(res.msg)
      }
    },
    cancelPopupHandle(e) {},
  },
  onUnload() {
    // uni.setScreenBrightness({
    // 	value: this.screenBrightness,
    // });
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.meal-code {
  padding: 40rpx;

  .ls-card {
    border-radius: 20rpx;
  }

  .container {
    width: 100%;
    height: 760rpx;
    overflow: hidden;

    .header {
      height: 108rpx;
      letter-spacing: 4rpx;
      background: #38ccc6 $bg-linear-gradient-1;
    }

    .section {
      height: 652rpx;
    }
  }
  .fixed-bottom{
    position: absolute;
    left: 40rpx;
    right: 40rpx;
    bottom: 80rpx;
  }
}
</style>
