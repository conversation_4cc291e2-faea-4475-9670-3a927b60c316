<template>
  <view class="multi-selector mul-picker-item"
    :style="{ height: height }">
    <picker-view :value="pickerValue"
      :indicator-style="indicatorStyle"
      :style="{ height: height }"
      @change="handleChange"
      @pickstart="pickstart"
      @pickend="pickend"
      >
      <picker-view-column v-for="(column, index) in pickerColumns"
        :key="index">
        <!-- #ifdef H5 -->
        <view v-for="(item, i) in column || []"
          :class="[
            'mul-picker-column',
            item[props.value] === selectValue[index]
              ? 'mul-picker-column-active'
              : ''
          ]"
          :key="i"
          :data-item="pressEnable ? JSON.stringify(item) : ''"
          @touchstart="touchstart"
          @touchmove="touchmove"
          @touchend="touchend">
          <!-- #endif -->
          <!-- #ifndef H5 -->
          <view v-for="(item, i) in column || []"
            :class="[
            'mul-picker-column',
            item[props.value] === selectValue[index]
              ? 'mul-picker-column-active'
              : ''
          ]"
            :key="i"
            :data-item="item"
            @touchstart="touchstart"
            @touchmove="touchmove"
            @touchend="touchend">
            <!-- #endif -->
            <!-- #ifdef APP-PLUS || H5 -->
            <text :class="[
              'mul-picker-column-label',
              `mul-picker-column-label-${align}`
            ]"
              :style="[
              item[props.value] === selectValue[index]
              ? activeColumnStyle
              : columnStyle
            ]">{{ getLabel(item, i, index) }}</text>
            <!-- #endif -->

            <!-- #ifndef APP-PLUS || H5 -->
            <text :class="[
              'mul-picker-column-label',
              `mul-picker-column-label-${align}`
            ]">{{ item[props.label] || item }}</text>
            <!-- #endif -->
          </view>
      </picker-view-column>
    </picker-view>
  </view>
</template>

<script>
import { getColumns, isFunction } from './utils'
export default {
  props: {
    value: Array,
    list: Array,
    mode: {
      type: String,
      default: 'multiSelector'
    },
    props: {
      type: Object,
      default() {
        return {
          children: 'children',
          value: 'id',
          label: 'name'
        }
      }
    },
    level: {
      type: Number,
      default: 5
    },
    visible: Boolean,
    height: {
      type: String,
      default: '280rpx'
    },
    columnStyle: Object,
    activeColumnStyle: Object,
    align: {
      type: String,
      default: 'center'
    },
    pressEnable: Boolean,
    pressTime: {
      type: String,
      default: 'YYYY-MM-DD'
    },
    formatter: {
      type: String,
      default: 'YYYY-MM-DD'
    },
  },
  data () {
    return {
      pickerValue: [],
      pickerColumns: [],
      selectValue: [],
      selectItem: [],
      isConfirmChange: false,
      indicatorStyle: `height: 33px`,
      pressTimeout: null,
      moving: false,
    }
  },
  watch: {
    value () {
      // if (!this.isConfirmChange) {
      //   this.init('value')
      // }
    },
    list () {
      this.init('list')
    }
  },
  created () {
    this.init('init')
  },
  methods: {
    pickerContentHeight () {
      return 34 * 5 + 'px'
    },
    // 标识滑动开始，只有微信小程序才有这样的事件
		pickstart() {
			// #ifdef MP-WEIXIN
			this.moving = true;
      this.$emit('moving',this.moving)
			// #endif
		},
		// 标识滑动结束
		pickend() {
			// #ifdef MP-WEIXIN
			this.moving = false;
      this.$emit('moving',this.moving)
			// #endif
		},
    init (changeType) {
      if (this.list && this.list.length) {
        const column = getColumns({
          value: this.value,
          list: this.list,
          mode: this.mode,
          props: this.props,
          level: this.level
        })
        const { columns, value, item, index } = column
        this.selectValue = value
        this.selectItem = item
        this.pickerColumns = columns
        this.pickerValue = index
        this.$emit('change', {
          value: this.selectValue,
          item: this.selectItem,
          index: this.pickerValue,
          change: changeType
        })
      }
    },
    touchstart (e) {
      if (!this.pressEnable) return
      clearTimeout(this.pressTimeout)
      this.pressTimeout = setTimeout(() => {
        let item = {}
        let toastTitle = ''
        // #ifdef APP-NVUE
        item = e.target.dataset.item
        // #endif

        // #ifdef H5
        item = JSON.parse(e.currentTarget.dataset.item)
        // #endif

        // #ifndef APP-NVUE || H5
        item = e.currentTarget.dataset.item
        // #endif

        // #ifdef APP-PLUS || H5
        toastTitle = this.getLabel(item)
        // #endif

        // #ifndef APP-PLUS || H5
        toastTitle = item[this.props.label] || item
        // #endif
        uni.showToast({
          title: toastTitle,
          icon: 'none'
        })
      }, this.pressTime)
    },
    touchmove () {
      if (!this.pressEnable) return
      clearTimeout(this.pressTimeout)
    },
    touchend () {
      if (!this.pressEnable) return
      clearTimeout(this.pressTimeout)
    },
    getLabel (item, rowIndex, columnIndex) {
      if (this.formatter && isFunction(this.formatter)) {
        return this.formatter({ item, rowIndex, columnIndex })
      } else {
        return item[this.props.label] || item
      }
    },
    handleChange (item) {
      const pickerValue = item.detail.value
      const columnIndex = pickerValue.findIndex(
        (item, i) => item !== this.pickerValue[i]
      )
      const valueIndex = pickerValue[columnIndex]
      this.setPickerChange(pickerValue, valueIndex, columnIndex)
    },
    setPickerChange (pickerValue, valueIndex, columnIndex) {
      for (let i = 0; i < this.level; i++) {
        if (i > columnIndex) {
          pickerValue[i] = 0
          const column =
            this.pickerColumns[i - 1][valueIndex] ||
            this.pickerColumns[i - 1][0]
          this.$set(this.pickerColumns, i, column[this.props.children] || [])
          valueIndex = 0
        }
        this.$set(this.pickerValue, i, pickerValue[i])
        const selectItem = this.pickerColumns[i][pickerValue[i]]
        if (selectItem) {
          this.selectItem[i] = selectItem
          this.selectValue[i] = selectItem[this.props.value]
        } else {
          const spliceNum = this.level - i
          this.pickerValue.splice(i, spliceNum)
          this.selectValue.splice(i, spliceNum)
          this.selectItem.splice(i, spliceNum)
          this.pickerColumns.splice(i, spliceNum)
          break
        }
      }
      this.$emit('change', {
        value: this.selectValue,
        item: this.selectItem,
        index: this.pickerValue,
        change: 'scroll'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mul-picker-column {
  height: 64rpx;
  padding: 0 10rpx;
  /* #ifndef APP-NVUE */
  display: flex;
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  /* #endif */
  flex-direction: row;
  align-items: center;
}

.mul-picker-column-label {
  font-size: 31rpx;
  text-align: center;
  flex: 1;
  /* #ifdef APP-NVUE */
  lines: 1;
  /* #endif */
  text-overflow: ellipsis;
  transition-property: color;
  transition-duration: 0.3s;
  /* #ifndef APP-NVUE */
  overflow: hidden;
  white-space: nowrap;
  /* #endif */
}

.mul-picker-column-label-left {
  text-align: left;
}

.mul-picker-column-label-center {
  text-align: center;
  color: #2e2d2d;
}
.mul-picker-column-active{
  .mul-picker-column-label-center{
    color: $color-primary-light-3;
  }
}

.mul-picker-column-label-right {
  text-align: right;
}
</style>
