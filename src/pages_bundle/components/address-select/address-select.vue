<template>
	<view>
		<u-popup :show="visible" mode="bottom" round="14" @close="closePopup">
		  <view class="address-popup">
		    <view class="title">选择收货地址</view>
        <view class="select-address">已选择：{{selectAdressStr}}</view>
        <view class="u-search-box">
          <u-search
            placeholder="请输入地址"
            v-model="addrName"
            :showAction="false"
            height="70"
            @change="$u.debounce(searchAddressList, 500)"
          ></u-search>
        </view>
		    
		    <view class="picker">
		      <multi-selector-picker
						v-if="showAddressPopup && addressList.length"
						:value="defaultAddressValue"
						:props="propsAddress"
						:height="pickerHeight"
						:list="addressList"
						:level="level"
		        @change="handleChange">
		      </multi-selector-picker>
          <view v-else class="no-address">暂无数据</view>
		    </view>
		    <u-button class="u-picker-btn" type="success" @click="confirmAddress()">确认</u-button>
		  </view>
		</u-popup>
	</view>
</template>

<script>
	import MultiSelectorPicker from '../multi-picker/multi-picker.vue'
  import { getApiGetAddrList } from '@/api/reservation'
  import { getTreeDeepArr, formateVisitorParams } from "@/utils/util.js"
	export default {
		components: {
			MultiSelectorPicker
		},
		props: {
			showAddressPopup: {
				type: Boolean,
				default: false
			},
			addrId: [Number, String],
			org: Number,
			rsvOrgs: Array
		},
		data() {
			return {
        allAddressList: [], // 保存所有地址
        addressList: [], // 页面显示的地址
				propsAddress: {
					children: 'children_list',
					value: 'addr_center_id',
					label: 'addr_name'
				},
				level: 5, // 设置显示的层级最多几个，当前后台最多只能5个
				pickerHeight: '370rpx',
				selectAdressStr: '',
				selectAdress: '',
        defaultAddressValue: [],
        addrName: ''
			}
		},
    created() {
      if (this.org) {
        this.getAllAddressList()
      }
    },
    watch: {
      org() {
				this.addressList = []
				this.allAddressList = []
				this.selectAdressStr = ''
				this.selectAdress = ''
				this.defaultAddressValue = []
				this.addrName = ''
        this.getAllAddressList()
      },
      addrId() {
        if (!this.addressList.length) return
        this.defaultAddressValue = getTreeDeepArr(this.addressList, this.addrId)
      }
    },
    computed: {
      visible: {
        get() {
          return this.showAddressPopup
        },
        set(val) {
          this.$emit('update:showAddressPopup', val)
        }
      }
    },
		methods: {
      // 获取地址列表
      getAllAddressList() {
        let params = {
          organization_id: this.org
        }
        if (this.rsvOrgs) {
          params.rsv_org_ids = this.rsvOrgs
        }
      	getApiGetAddrList(formateVisitorParams(params))
          .then(res => {
            if (res.code == 0) {
							if (this.org !== params.organization_id) {
								return
							}
      				this.allAddressList = res.data // 保存所有地址，防止数据过多加载慢
      				this.addressList = res.data
      				if (this.addrId) {
      					this.defaultAddressValue = getTreeDeepArr(this.addressList, this.addrId)
      				}
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            console.log(err)
          })
      },
      // 地址搜索单独用这个方法，防止数据过多加载慢
      searchAddressList() {
        if (!this.addrName) {
          this.addressList = this.allAddressList
          return
        }
        let params = {
          organization_id: this.org,
          addr_name: this.addrName
        }
        if (this.rsvOrgs) {
          params.rsv_org_ids = this.rsvOrgs
        }
      	getApiGetAddrList(formateVisitorParams(params))
          .then(res => {
            if (res.code == 0) {
              this.addressList = res.data
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            console.log(err)
          })
      },
			handleChange(e) {
			  this.selectAdress = e
			  let nameList = []
			  e.item.map(v => {
			    nameList.push(v.addr_name)
			  })
			  this.selectAdressStr = nameList.join('-')
			},
			confirmAddress() {
				this.$emit('confirm', {
					value: this.selectAdress.value,
					full_addr_name: this.selectAdressStr
				})
			},
      closePopup() {
        this.visible = false
      }
		},
	}
</script>

<style lang="scss">
	.address-popup {
		position: relative;
		width: 100%;
		height: 100%;
		// padding: 30rpx;

		.title {
			padding: 25rpx;
			text-align: center;
			font-size: 32rpx;
		}

		.picker {
      height: 420rpx;
			padding: 0 10rpx 10rpx;
		}
    
    .no-address{
      text-align: center;
      padding-top: 160rpx;
      color: #a2a2a2;
    }

		.address-select {
			margin: 10rpx;
			display: flex;
			height: 300rpx;
			flex-direction: row;

			::-webkit-scrollbar {
				width: 0;
				height: 0;
				color: transparent;
			}

			.address-area {
				width: 100%;
				text-align: center;

				.address-area-item {
					margin: 20rpx 0;
				}

				.area-item-color {
					color: $color-primary-light-3;
				}
			}
		}

		.u-picker-btn {
			// position: absolute;
			// left: 0;
			// right: 0;
			// bottom: 0;
			width: 100%;
			border: none;
			color: #FFF;
			text-align: center;
			border-radius: 0;
			background-color: $color-primary-light-3;
		}

		.u-btn {
			border: none;
			border-radius: 0;
			background-color: $color-primary-light-3;

			&::after {
				border: none;
				border-radius: 0;
			}
		}
		.select-address{
			padding: 0 20rpx;
			line-height: 30rpx;
      text-align: center;
			font-size: 28rpx;
			color: #999999;
		}
    .u-search-box{
      padding: 20rpx;
    }
	}
</style>