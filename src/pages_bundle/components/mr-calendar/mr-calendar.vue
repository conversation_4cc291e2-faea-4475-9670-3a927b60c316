<template>
	<view class="date bg-white">
		<view class="head" v-if="!hiddenHeader">
			<view class="prev" @click="switch_month_week('prev', true)"><u-icon color="#484848" name="arrow-left"></u-icon></view>
			<view class="title" @click="showPicker = true">
				<text class="lg black m-r-4">{{ `${nowYear}.${('0' + nowMonth).slice(-2)}` }}</text>
				<trigonometry />
			</view>
			<view class="next" @click="switch_month_week('next', true)"><u-icon color="#484848" name="arrow-right"></u-icon></view>
		</view>
		<view class="date-lists">
			<view class="date-item week" v-for="(item, index) in week" :key="index">{{ item }}</view>
		</view>
		<swiper
			:style="{ height: (retract ? 2 * 50 : week_list.length * 100) + 40 + 'rpx' }"
			:current="current"
			circular
			@change="change_date"
		>
			<swiper-item>
				<view :style="{ height: (retract ? 2 * 50 : week_list.length * 100) + 40 + 'rpx' }">
					<view
						class="date-lists"
						v-show="!retract || index == to_prev_week_index"
						v-for="(item, index) in week_list_prev_co"
						:key="index"
					>
						<view class="date-item" @click="item_click(vo, index, key)" v-for="(vo, key) in item" :key="key">
							<view
								class="num"
								:class="[vo.selected && (vo.type == 'month' || retract) ? 'selected' : '', vo.allow ? 'month' : 'disabled']"
							>
								<text v-if="retract || vo.type == 'month'">{{ vo.day }}</text>
							</view>
							<view v-show="vo.dot && (vo.type == 'month' || retract)" class="dot">{{ dotText }}</view>
						</view>
					</view>
				</view>
				<view class="retract flex row-center">
					<view class="" @click="open" style="display: inline-block;">
						<u-icon color="#484848" :name="retract ? 'arrow-down' : 'arrow-up'"></u-icon>
					</view>
					<view class="select-all-box">
						<u-checkbox-group v-model="selectAllChecked" @change="selectAllChange">
							<u-checkbox :activeColor="variables.colorPrimary" shape="square" :disabled="selectDisabledAll" name="all">本月全选</u-checkbox>
						</u-checkbox-group>
					</view>
				</view>
			</swiper-item>
			<swiper-item>
				<view class="date-lists" v-show="!retract || index == to_week_index" v-for="(item, index) in week_list" :key="index">
					<view class="date-item" @click="item_click(vo, index, key)" v-for="(vo, key) in item" :key="key">
						<view
							class="num"
							:class="[vo.selected && (vo.type == 'month' || retract) ? 'selected' : '', vo.allow ? 'month' : 'disabled']"
						>
							<text v-if="retract || vo.type == 'month'">{{ vo.day }}</text>
						</view>
						<view v-show="vo.dot && (vo.type == 'month' || retract)" class="dot">{{ dotText }}</view>
					</view>
				</view>

				<view class="retract flex row-center">
					<view class="" @click="open" style="display: inline-block;">
						<u-icon color="#484848" :name="retract ? 'arrow-down' : 'arrow-up'"></u-icon>
					</view>
					<view class="select-all-box">
						<u-checkbox-group v-model="selectAllChecked" @change="selectAllChange">
							<u-checkbox :activeColor="variables.colorPrimary" shape="square" :disabled="selectDisabledAll" name="all">本月全选</u-checkbox>
						</u-checkbox-group>
					</view>
				</view>
			</swiper-item>
			<swiper-item>
				<view
					class="date-lists"
					v-show="!retract || index == to_next_week_index"
					v-for="(item, index) in week_list_next_co"
					:key="index"
				>
					<view class="date-item" @click="item_click(vo, index, key)" v-for="(vo, key) in item" :key="key">
						<view
							class="num"
							:class="[vo.selected && (vo.type == 'month' || retract) ? 'selected' : '', vo.allow ? 'month' : 'disabled']"
						>
							<!-- {{vo.day}} -->
							<text v-if="retract || vo.type == 'month'">{{ vo.day }}</text>
						</view>
						<view v-show="vo.dot && (vo.type == 'month' || retract)" class="dot">{{ dotText }}</view>
					</view>
				</view>
				<view class="retract flex row-center">
					<view class="" @click="open" style="display: inline-block;">
						<u-icon color="#484848" :name="retract ? 'arrow-down' : 'arrow-up'"></u-icon>
					</view>
					<view class="select-all-box">
						<u-checkbox-group v-model="selectAllChecked" disabled="" @change="selectAllChange">
							<u-checkbox :activeColor="variables.colorPrimary" shape="square" :disabled="selectDisabledAll" name="all">本月全选</u-checkbox>
						</u-checkbox-group>
					</view>
				</view>
			</swiper-item>
		</swiper>
		<u-datetime-picker
			:show="showPicker"
			v-model="selectYearMonth"
			:minDate="minDate"
			mode="year-month"
			@confirm="confirmSelect"
			@cancel="closePicker"
		></u-datetime-picker>
	</view>
</template>

<script>
export default {
	props: {
		value: {
			type: [String, Number],
			default: ''
		},
		dotLists: {
			type: Array,
			default: () => {
				return []
			}
		},
		dotText: {
			type: String,
			default: ''
		},
		hiddenHeader: {
			type: Boolean,
			default: false
		},
		// 接收已经被选择的日期
		selectDateList: {
			type: Array,
			default: () => {
				return []
			}
		},
		// 接收允许被选择的日期，即允许点餐的日期
		allowDateList: {
			type: Array,
			default: () => {
				return []
			}
		}
	},
	data() {
		return {
			showPicker: false,
			selectYearMonth: Number(new Date()),
			minDate: Number(new Date()),
			week: ['日', '一', '二', '三', '四', '五', '六'],
			week_list: [],
			week_list_prev: [],
			week_list_prev_week: [],
			week_list_next: [],
			week_list_next_week: [],
			now_date: '',
			start_date: '',
			end_date: '',
			prev_date: '',
			next_date: '',
			nowYear: '',
			nowMonth: '',
			nowDay: '',
			retract: true, //默认week
			to_week_index: 0,
			to_prev_week_index: 0,
			to_next_week_index: 0,
			nowTime: 0,
			dot_list: [],
			select_date_list: [],
			allow_date_list: [],
			current: 1,
			date: '',
			selectAllChecked: [],
			selectDisabledAll: false
		}
	},
	watch: {
		value(value) {
			this.get_date(this.date_parse(value))
		},
		dotLists: {
			immediate: true,
			handler(value) {
				this.dot_list = uni.$u.deepClone(value)
				this.set_doc_lists_update()
				this.allMonthBtn()
			}
		},
		selectDateList: {
			immediate: true,
			handler(value) {
				this.select_date_list = uni.$u.deepClone(value)
				this.set_selected_date_all()
				this.allMonthBtn()
			}
		},
		allowDateList: {
			immediate: true,
			handler(value) {
				this.allow_date_list = uni.$u.deepClone(value)
				this.set_allow_date_all()
				this.allMonthBtn()
			}
		},
		showPicker(value) {
			// $eventbus 跨组件间通信
			this.$eventbus.$emit('changeCalendarPicker', value)
		}
	},
	computed: {
		week_list_prev_co() {
			return this.retract ? this.week_list_prev_week : this.week_list_prev
		},
		week_list_next_co() {
			return this.retract ? this.week_list_next_week : this.week_list_next
		}
	},
	created() {
		this.init()
	},
	methods: {
		isSameMonth(data) {
			let nowDate = uni.$u.timeFormat(new Date(this.date).getTime(), 'yyyy-mm')
			let currentMonth = []
			data.forEach(v => {
				if (nowDate == uni.$u.timeFormat(new Date(v).getTime(), 'yyyy-mm')) {
					if (!this.dot_list.includes(v)) {
						currentMonth.push(v)
					}
				}
			})
			return currentMonth
		},
		// 月全选
		allMonthBtn() {
			// 排序好的日期
			let selectDateListSort = this.select_date_list.sort(function(a, b) {
					//已经点的日期
					return a > b ? 1 : -1
				}),
				currentMonth = this.isSameMonth(this.allow_date_list).sort(function(a, b) {
					return a > b ? 1 : -1
				}) //当前月
			let selectDateListMonth = []
			selectDateListSort.forEach(v => {
				if (currentMonth.includes(v)) {
					selectDateListMonth.push(v)
				}
			})
			if (
				selectDateListMonth.length &&
				currentMonth.length &&
				JSON.stringify(selectDateListMonth) === JSON.stringify(currentMonth)
			) {
				this.selectAllChecked = ['all']
			} else {
				this.selectAllChecked = []
			}
		},
		selectAllChange(value) {
			this.selectAllChecked = value
			let obj = {
				value: value,
				currentMonth: this.isSameMonth(this.allow_date_list)
			}
			let monthList = this.isSameMonth(this.allow_date_list)
			let aloneMonth = []
			monthList.forEach(v => {
				if (!this.select_date_list.includes(v)) {
					aloneMonth.push(v)
				}
			})
			if (aloneMonth.length) {
				obj.currentMonth = aloneMonth
			} else {
				obj.currentMonth = this.isSameMonth(this.allow_date_list)
			}
			this.$emit('selectAllChange', obj)
		},
		confirmSelect({ value }) {
			this.get_date(value)
			this.set_allow_date_all()
			this.closePicker()
			this.change()
		},
		closePicker() {
			this.showPicker = false
		},
		change() {
			let value = {
				time: this.nowTime,
				fulldate: this.date.replace(/-(\d)(?!\d)/g, '-0$1')
			}
			this.$emit('change', value)
		},
		init() {
			if (this.value) {
				this.get_date(this.date_parse(this.value))
			} else {
				this.get_date()
			}
			this.doc_list_update()
			this.update_month()
			this.set_selected_date_all()
			this.set_allow_date_all()
			this.change()
		},
		open() {
			this.retract = !this.retract
			// this.set_to_day('week_list_prev')
			// this.set_to_day('week_list_next')

			this.change_week()

			if (this.retract) {
				this.update_swiper_item('week')
			} else {
				this.update_swiper_item('month')
			}
			this.get_date(this.nowTime)
			this.set_doc_lists_update()
			this.set_selected_date_all()
			this.set_allow_date_all()
		},
		change_week() {
			if (this.to_week_index < this.week_list.length - 1) {
				this.to_next_week_index = this.to_week_index + 1
				this.week_list_next_week = this.week_list
			} else {
				this.to_next_week_index = 0
				this.week_list_next_week = this.week_list_next
			}

			if (this.to_week_index == 0) {
				this.update_month()

				// if(){
				let next_day = this.week_list_prev[this.week_list_prev.length - 1][6].day

				// }
				this.to_prev_week_index = this.week_list_prev.length - 1 - Math.ceil(next_day / 7)

				this.week_list_prev_week = JSON.parse(JSON.stringify(this.week_list_prev))
			} else {
				this.to_prev_week_index = this.to_week_index - 1
				this.week_list_prev_week = this.week_list
			}

			// if(this.current == 1){

			// }
			// let to_week_index = this.to_week_index;
			// if(this.current == 2){
			// 	this.to_next_week_index = this.to_week_index;
			// 	this.to_week_index = this.to_week_index - 1;
			// 	this.to_prev_week_index =  this.to_next_week_index + 1;
			// }else if(this.current == 0){
			// 	this.to_next_week_index = this.to_week_index;
			// 	this.to_week_index = this.to_week_index - 1;
			// 	this.to_prev_week_index =  this.to_next_week_index + 1;
			// }
		},
		change_date_week(type) {
			let week_list = this.week_list
			let to_week_index = this.to_week_index
			if (type == 'prev') {
				this.to_week_index = this.to_prev_week_index
				this.to_prev_week_index = this.to_next_week_index
				this.to_next_week_index = to_week_index

				this.week_list = this.week_list_prev_week
				this.week_list_prev_week = this.week_list_next_week
				this.week_list_next_week = week_list
			} else if (type == 'next') {
				this.to_week_index = this.to_next_week_index
				this.to_next_week_index = this.to_prev_week_index
				this.to_prev_week_index = to_week_index

				this.week_list = this.week_list_next_week
				this.week_list_next_week = this.week_list_prev_week
				this.week_list_prev_week = week_list
			}

			// this.set_to_day_all();
			this.set_selected_date_all()
			this.set_allow_date_all()
		},
		change_date_month(type) {
			let week_list = this.week_list
			if (type == 'prev') {
				this.week_list = this.week_list_prev
				this.week_list_prev = this.week_list_next
				this.week_list_next = week_list
			} else if (type == 'next') {
				this.week_list = this.week_list_next
				this.week_list_next = this.week_list_prev
				this.week_list_prev = week_list
			}
		},
		change_date(e) {
			let primary_current = this.current
			let current = e.detail.current

			this.current = current

			if (primary_current - current == -1 || primary_current - current == 2) {
				if (this.retract) {
					this.switch_month_week('next')
					this.change_week()
					if (primary_current - current == -1 && current != 1) {
						this.change_date_week('prev')
					} else if (primary_current - current == 2) {
						this.change_date_week('next')
					}
				} else {
					this.get_date(this.get_month('next'))
					this.update_month()
					if (primary_current - current == -1 && current != 1) {
						this.change_date_month('prev')
					} else if (primary_current - current == 2) {
						this.change_date_month('next')
					}
				}
			} else {
				if (this.retract) {
					this.switch_month_week('prev')
					this.change_week()
					if (primary_current - current == 1 && current != 1) {
						this.change_date_week('next')
					} else if (primary_current - current == -2) {
						this.change_date_week('prev')
					}
				} else {
					this.get_date(this.get_month('prev'))
					this.update_month()
					if (primary_current - current == 1 && current != 1) {
						this.change_date_month('next')
					} else if (primary_current - current == -2) {
						this.change_date_month('prev')
					}
				}
			}
			//滑动之后，重新设置所有的日期的today
			// this.set_to_day_all();
			this.get_date(this.nowTime)
			//滑动之后，重新设置所有的doc
			this.set_doc_lists_update()
			//滑动之后，重新设置所有被选中的日期
			this.set_selected_date_all()
			//滑动之后，重新设置所有允许被选择的日期
			this.set_allow_date_all()
			this.change()
			this.allMonthBtn()
		},
		update_month() {
			this.get_date(this.get_month('prev'), 'prev')
			this.get_date(this.get_month('next'), 'next')
		},
		set_doc_lists_update() {
			this.doc_list_update('week_list')
			this.doc_list_update('week_list_prev')
			this.doc_list_update('week_list_next')
			this.doc_list_update('week_list_prev_week')
			this.doc_list_update('week_list_next_week')
		},
		doc_list_update(week_list = 'week_list') {
			let list = []

			this[week_list].map((item, index) => {
				list.push(
					item.map((vo, key) => {
						if (this.dot_list.indexOf(vo.date) > -1 || this.dot_list.indexOf(vo.date.replace(/-(\d)(?!\d)/g, '-0$1')) > -1) {
							vo.dot = true
						} else {
							vo.dot = false
						}
						return {
							...vo
						}
					})
				)
			})
			this[week_list] = list
		},
		set_to_day(type) {
			let list = []

			this[type].map((item, index) => {
				list.push(
					item.map((vo, key) => {
						if (vo.date == `${this.date}`) {
							vo.today = true
						} else {
							vo.today = false
						}
						return {
							...vo
						}
					})
				)
			})
			this[type] = list
		},
		set_selected_date_all() {
			this.set_selected_date('week_list')
			this.set_selected_date('week_list_prev')
			this.set_selected_date('week_list_next')
			this.set_selected_date('week_list_prev_week')
			this.set_selected_date('week_list_next_week')
			// this.selectDisabledAll = true
			// setTimeout(()=> {
			// }, 300);
		},
		set_selected_date(type) {
			let list = []

			this[type].map((item, index) => {
				list.push(
					item.map((vo, key) => {
						if (
							this.select_date_list.indexOf(vo.date) > -1 ||
							this.select_date_list.indexOf(vo.date.replace(/-(\d)(?!\d)/g, '-0$1')) > -1
						) {
							vo.selected = true
						} else {
							vo.selected = false
						}
						return {
							...vo
						}
					})
				)
			})
			this[type] = list
		},
		set_allow_date_all() {
			this.set_allow_date('week_list')
			this.set_allow_date('week_list_prev')
			this.set_allow_date('week_list_next')
			this.set_allow_date('week_list_prev_week')
			this.set_allow_date('week_list_next_week')
		},
		set_allow_date(type) {
			let list = []
			this[type].map((item, index) => {
				list.push(
					item.map((vo, key) => {
						if (
							this.allow_date_list.indexOf(vo.date) > -1 ||
							this.allow_date_list.indexOf(vo.date.replace(/-(\d)(?!\d)/g, '-0$1')) > -1
						) {
							//控制 禁止点击颜色
							vo.allow = true
						} else {
							vo.allow = false
						}
						return {
							...vo
						}
					})
				)
			})
			this[type] = list
		},
		item_click(item, item_index = -1) {
			// allow=fasle的日期不能点击
			if (!item.allow) {
				return uni.$u.toast('当前日期不支持报餐')
			}
			if (!this.retract && item.type !== 'month') {
				return false
			}
			this.date = item.date
			if (item.type == 'month') {
				this.nowDay = item.day
				if (item_index >= 0) this.to_week_index = item_index
			} else if (this.retract) {
				this.nowDay = item.day
			}

			let now_arr = item.date.split('-')
			this.nowYear = now_arr[0]
			this.nowMonth = now_arr[1]
			this.nowDay = now_arr[2]

			// this.set_to_day_all(item_index);
			let selectIndex = this.select_date_list.indexOf(this.date)
			let type
			if (selectIndex > -1) {
				type = 'del'
			} else {
				type = 'add'
			}
			this.set_selected_date_all()
			// 改变父组件的值
			let value = {
				type,
				date: this.date,
				already: item.dot
			}
			this.$emit('select', value)

			this.nowTime = this.date_parse(`${item.date}`)

			this.change()
			this.set_doc_lists_update()
		},
		// set_to_day_all(item_index) {
		// 	this.set_to_day('week_list')
		// 	this.set_to_day('week_list_prev')
		// 	this.set_to_day('week_list_next')
		// 	this.set_to_day('week_list_prev_week')
		// 	this.set_to_day('week_list_next_week')

		// },
		get_month(type) {
			let nowMonth = this.nowMonth
			let nowYear = this.nowYear
			let nowDay = this.nowDay

			if (type == 'prev') {
				if (nowMonth == 1) {
					nowMonth = 12
					nowYear = parseInt(nowYear) - 1
				} else {
					nowMonth--
				}
			} else if (type == 'next') {
				if (nowMonth == 12) {
					nowMonth = 1
					nowYear = parseInt(nowYear) + 1
				} else {
					nowMonth++
				}
			}

			let days = this.get_month_days(nowMonth, nowYear)
			if (nowDay > days) {
				nowDay = days
			}

			return this.date_parse(`${nowYear}-${nowMonth}-${nowDay}`)
		},

		date_parse(str) {
			return Date.parse(str.replace(/-(\d)(?!\d)/g, '-0$1'))
		},
		switch_month_week(type = 'next', update_week = false) {
			if (this.retract) {
				if (type == 'prev') {
					this.get_date(this.nowTime - 86400 * 7 * 1000)
				} else if (type == 'next') {
					this.get_date(this.nowTime + 86401 * 7 * 1000)
				}
				if (update_week) {
					this.update_swiper_item('week')
					this.set_doc_lists_update()
				}
			} else {
				this.get_date(this.get_month(type))
				this.update_swiper_item('month')
			}
			this.set_doc_lists_update()

			// this.set_to_day_all();
			this.set_selected_date_all()
			this.set_allow_date_all()

			if (update_week) {
				this.change()
			}
		},
		update_swiper_item(type = 'month') {
			if (type == 'month') {
				if (this.current == 0) {
					this.change_date_month('next')
				} else if (this.current == 2) {
					this.change_date_month('prev')
				}
			} else if (type == 'week') {
				if (this.current == 0) {
					this.change_date_week('next')
				} else if (this.current == 2) {
					this.change_date_week('prev')
				}
			}
		},
		next() {
			this.get_date(this.next_date)
		},
		get_date(value = '', type = 'same') {
			let date = new Date()
			if (value) {
				date = new Date(value)
			}
			let nowMonth = date.getMonth() + 1,
				nowYear = date.getFullYear(),
				nowDay = date.getDate(),
				nowTime = date.getTime(),
				nowWeek = date.getDay()

			let days = this.get_month_days(nowMonth, nowYear)
			let start_date = new Date(nowYear, nowMonth - 1, 1)
			let end_date = new Date(nowYear, nowMonth - 1, days)
			let prev_date = new Date(start_date.getTime() - 1)
			let prev_date_days = prev_date.getDate()
			let next_date = new Date(end_date.getTime() + 86401 * 1000)
			let next_date_days = next_date.getDate()
			let start_week = start_date.getDay()
			let date_arrs = []

			let week_list = []
			let count_days = 35

			for (let i = prev_date_days - start_week + 1; i <= prev_date_days; i++) {
				date_arrs.push({
					day: i,
					type: 'prev',
					date: `${prev_date.getFullYear()}-${this.formatNum(prev_date.getMonth() + 1)}-${this.formatNum(i)}`
				})
			}
			for (let i = 1; i <= days; i++) {
				date_arrs.push({
					day: i,
					type: 'month',
					today: i == nowDay ? true : false,
					date: `${nowYear}-${this.formatNum(nowMonth)}-${this.formatNum(i)}`
				})
				if (i == nowDay && type == 'same') {
					this.date = `${nowYear}-${this.formatNum(nowMonth)}-${this.formatNum(i)}`
				}
			}
			let date_arrs_length = date_arrs.length
			if (date_arrs_length > 35) {
				count_days = 42
			}
			for (let i = 1; i <= count_days - date_arrs_length; i++) {
				date_arrs.push({
					day: i,
					type: 'next',
					date: `${next_date.getFullYear()}-${this.formatNum(next_date.getMonth() + 1)}-${this.formatNum(i)}`
				})
			}
			for (let i = 0; i < date_arrs.length / 7; i++) {
				let arr = []
				for (let j = 0; j < 7; j++) {
					if (date_arrs[i * 7 + j].today) {
						if (type == 'same') {
							this.to_week_index = i
						}
					}
					arr.push(date_arrs[i * 7 + j])
				}
				week_list.push(arr)
			}

			if (type == 'same') {
				this.week_list = week_list
				this.nowYear = nowYear
				this.nowMonth = nowMonth
				this.nowDay = nowDay
				this.nowTime = nowTime
				this.start_date = start_date
				this.end_date = end_date
				this.prev_date = prev_date
				this.next_date = next_date
			} else if (type == 'prev') {
				this.week_list_prev = week_list
			} else if (type == 'next') {
				this.week_list_next = week_list
			}
		},
		get_month_days(nowMonth, nowYear) {
			let month_arr = [1, 3, 5, 7, 8, 10, 12]
			let days = 0
			if (nowMonth == 2) {
				if (nowYear % 4 == 0) {
					days = 29
				} else {
					days = 28
				}
			} else if (month_arr.indexOf(nowMonth) >= 0) {
				days = 31
			} else {
				days = 30
			}
			return days
		},
		// 日期不足两位数补零
		formatNum(value) {
			return value.toString().padStart(2, 0)
		},
		// 设置全选是否可点
		setDisabledAll(value) {
			this.selectDisabledAll = value
		}
	}
}
</script>

<style lang="scss">
.date {
	.head {
		display: flex;
		align-items: center;
		height: 100rpx;
		justify-content: space-between;
		border-bottom: 1rpx solid $border-color-base;
		color: $color-text-primary;
		padding: 0 40rpx;

		.title {
			width: 200rpx;
			font-size: 30rpx;
			padding-left: 4rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 50rpx;
			border-radius: 60rpx;
			background-color: $background-color;
		}
	}
}

.retract {
	position: absolute;
	width: 100%;
	bottom: 0;
	// display: flex;
	// justify-content: center;
	// align-items: center;
	// height: 50rpx;
	margin-bottom: 20rpx;
	.select-all-box {
		position: absolute;
		right: 10rpx;
	}
}

.date-lists {
	display: flex;
	width: 100%;

	.date-item {
		flex: 1;
		text-align: center;
		height: 100rpx;
		font-size: $font-size-xl;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;

		&.week {
			height: 60rpx;
			color: $color-text-secondary;
			font-size: $font-size-xs;
			justify-content: center;
		}

		.num {
			width: 60rpx;
			height: 60rpx;
			border-radius: 50%;
			line-height: 60rpx;

			&.disabled {
				color: $color-disabled;
			}

			&.month {
				color: $color-text-primary;
			}

			&.selected {
				background: $color-primary;
				color: #fff;
			}
		}

		.dot {
			font-size: $font-size-mini;
			color: $color-primary;
			margin-top: 5rpx;
			min-width: 10rpx;
			min-height: 10rpx;
			background: red;
			border-radius: 10rpx;
		}
	}
}
</style>
