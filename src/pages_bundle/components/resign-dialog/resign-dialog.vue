<template>
  <div :style="theme.style">
    <!-- 评价弹窗 -->
    <u-modal :show="visible" :confirmColor="variables.colorPrimary" class="resign-dialog" closeOnClickOverlay
      @close="handlerClose" title="提示" :showConfirmButton="false" :showCancelButton="false">
      <view class="resign-dialog-content" slot="default">
        <view class="resign-dialog-content-title">
          检测到您的用户（{{ personInfo.person_no }}{{ personInfo.name }}）已签约授权代扣，是否使用同一银行卡号继续签约</view>
        <view class="resign-dialog-content-btn m-t-40">
          <u-button type="primary" plain @click="handlerNext('continue_sign')">继续签约</u-button>
          <u-button type="default" plain @click="handlerNext('other_card_sign')">其他银行卡号签约</u-button>
          <u-button type="default" plain @click="handlerClose">再想想</u-button>
        </view>
      </view>
    </u-modal>
  </div>
</template>

<script>
export default {
  props: {
    visible: { // 是否显示
      type: Boolean,
      value: false
    },
    personInfo: { // 用户信息
      type: Object,
      value: {}
    },
    payInfo: { // 支付信息
      type: Object,
      value: {}
    },
    reSignPrice: { // 价格
      type: Number,
      value: 0
    }
  },
  data() {
    return {

    }
  },
  mounted() {},
  methods: {
    // 关闭
    handlerClose() {
      this.$emit("close", true)
    },
    // 继续签约
    handlerNext(type) {
      this.$emit('nextSign', type, this.reSignPrice)
    }
  }
}
</script>

<style lang="scss" scoped>
.resign-dialog {
  .resign-dialog-content-title {
    padding: 0 50rpx;
  }

  ::v-deep .u-modal__content {
    padding: 24rpx 0 !important;
  }
}
</style>
