<template>
  <view class="appoint-wrap">
    <!-- 头部 -->
    <view class="header">
      <!-- #ifndef MP-ALIPAY -->
      <u-navbar bg-color="transparent" left-icon-color="#fff" @leftClick="topLeftClick">
        <view class="white lg f-w-500" slot="center">{{ title }}</view>
      </u-navbar>
      <!-- #endif -->
      <view class="canteen img-filter" @click="gotoPathCanteen()">
        <!-- <router-link class="inline" to="/pages_bundle/select/select_diner"> -->
        <view class="flex col-center xxl">
          <u-icon name="map" color="#fff" size="38rpx"></u-icon>
          <text class="bold white line-2">{{ org.org_name }}</text>
          <u-icon name="arrow-right" color="#fff" size="28rpx"></u-icon>
        </view>
        <!-- </router-link> -->
      </view>
    </view>
    <!-- 日期 -->
    <!-- 日历 -->
    <view class="calendar">
      <calendar :disabled-all="loading" :hidden-header="true" :beforeDateDisabled="calendarDisabled" :reservationDocData="docData" @change="debounceDate"></calendar>
    </view>
    <!-- 预约列表 -->

    <!-- 餐别 -->
    <view class="meal-type flex">
      <view class="month-day lg f-w-500">{{ currentDay }}</view>
      <scroll-view class="meal-scroll" :scroll-x="true">
        <view class="meal-lists flex">
          <block v-for="(item, index) in mealTypeInfo" :key="index">
            <view
              class="meal-item flex flex-center"
              :class="{ active: nowMealTypeIndex == index }"
              @click="clickMealType(item, index)"
            >
              {{ item.meal_type_alias }}
            </view>
          </block>
        </view>
      </scroll-view>
		</view>
		<!-- 营养推荐 -->
		<!-- <view class="flex row-between col-center p-20" style="background-color: #f0f3f5;" v-if="nutrientMealType.is_healthy_info">
			<view class="flex flex-1">
        <view class="flex-1 text-right p-r-10">
					<view class="flex flex-1 col-center p-b-10">
								<text class="muted xs min-w">摄入量</text>
								<view class="p-l-10 p-r-10" style="width: 120rpx;">
									<u-line-progress
										:percentage="formatePercent(nutrientMealType.energy_kcal,nutrientMealType.need_energy_kcal)"
										height="8"
										:showText="false"
										:activeColor="formateColor(nutrientMealType.energy_kcal,nutrientMealType.need_energy_kcal)"
									></u-line-progress>
								</view>
								<text class="xs">{{nutrientMealType.energy_kcal}} <text class="muted p-l-10">kcal</text></text>
					</view>
					<view class="flex flex-1 col-center p-b-10">
								<text class="muted xs min-w">碳水</text>
								<view class="p-l-10 p-r-10" style="width: 120rpx;">
									<u-line-progress
									 :percentage="formatePercent(nutrientMealType.carbohydrate,nutrientMealType.need_carbohydrate)"
									 height="8"
									 :showText="false"
									 :activeColor="formateColor(nutrientMealType.carbohydrate,nutrientMealType.need_carbohydrate)">
									</u-line-progress>
								</view>
								<text class="xs">{{nutrientMealType.carbohydrate}}<text class="muted p-l-10">g</text></text>
					</view>
          </view>
          <view class="flex-1 text-right p-r-10">
					<view class="flex flex-1 col-center p-b-10">
							<text class="muted xs min-w">蛋白质</text>
							<view class="p-l-10 p-r-10" style="width: 120rpx;">
								<u-line-progress
                 :percentage="formatePercent(nutrientMealType.protein,nutrientMealType.need_protein)"
                 height="8"
                 :showText="false"
                 :activeColor="formateColor(nutrientMealType.protein,nutrientMealType.need_protein)">
                </u-line-progress>
							</view>
							<text class="xs">{{nutrientMealType.protein}} <text class="muted p-l-10">g</text></text>
					</view>
					<view class="flex flex-1 col-center p-b-10">
							<text class="muted xs min-w">脂肪</text>
							<view class="p-l-10 p-r-10" style="width: 120rpx;">
								<u-line-progress
                 :percentage="formatePercent(nutrientMealType.axunge,nutrientMealType.need_axunge)"
                 height="8"
                 :showText="false"
                 :activeColor="formateColor(nutrientMealType.axunge,nutrientMealType.need_axunge)">
                </u-line-progress>
							</view>
							<text class="xs">{{nutrientMealType.axunge}} <text class="muted p-l-10">g</text></text>
					</view>
          </view>
				</view>
			<view class="" @click="gotoNutritionalAnalysis">
				<u-image width="100rpx" height="80rpx" src="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/962f371523d20ff66d3af4d27b07b0b51671614797397.jpg" mode="aspectFit"></u-image>
			</view>
		</view> -->
    <!-- 点餐区 -->
    <view class="meal flex-1 flex">
      <view class="loading flex-1 flex row-center col-center" style="height: 100%" v-if="loading">
        <u-loading-icon></u-loading-icon>
      </view>
      <template v-else>
        <template v-if="getFoodLists && getFoodLists.length">
          <view class="meal-classify">
            <scroll-view :style="{ height : '100%' }" :scroll-y="true">
              <view class="classify-lists">
                <view v-for="(item, index) in getFoodLists" :key="index">
                  <view
                    class="classify-item black flex col-center"
                    :class="{ active: orgIndex == index }"
                    @click="handleClickItem(item, index)"
                  >
                    <text class="f-w-500 line-2">{{ item.org_name }}</text>
                    <u-icon
                      color="inherit"
                      :name="orgIndex == index && item.open ? 'arrow-up' : 'arrow-down'"
                      size="28rpx"
                    ></u-icon>
                  </view>
                  <view class="classify-children" :style="{ height: orgIndex == index && item.open ? 'auto' : 0 }">
                    <view
                      class="children-item muted flex col-center"
                      v-for="(fitem, findex) in item.food_data"
                      :index="findex"
                      :key="findex"
                      :class="{ active: cateIndex == findex }"
                      @click="handleClickCateItem(findex, fitem.category_id)"
                    >
                      <text class="xs line-2">{{ fitem.category }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view class="meal-goods" @touchstart="isTouch = true">
            <!-- 新增能量总和条 -->
            <view class="energy-card flex col-center row-between p-30" v-if="nutrientMealType">
              <view class="text flex flex-center">
                <text class="md">能量：</text>
                <text class="md">{{ nutrientMealType ? nutrientMealType.energy_kcal : 0 }} kcal</text>
                <u-icon v-if="showEnergyKcalIcon" :name="showArrowDown?'arrow-down-fill':'arrow-up-fill'" class="m-l-10" :color="showArrowDown?'#F8A73C':'#FE5858'"></u-icon>
              </view>
              <view class="instructions" @click="openInstructions">
                <u-icon name="question-circle"></u-icon>
              </view>
            </view>
            <scroll-view
              :style="{ height : '90%' }"
              :scroll-y="true"
              :scroll-into-view="currentView"
              scroll-with-animation="true"
              @scroll="handleScroll"
            >
              <view class="meal-goods-lists p-l-20 p-r-20">
                <slot></slot>
              </view>
            </scroll-view>
          </view>
        </template>
        <view class="flex-1" v-else>
          <slot name="empty"></slot>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
import { timeFormat } from '@/utils/date'
import { getRect } from '@/utils/util'
import { mapMutations, mapActions } from 'vuex'
export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    title: {
      type: String
    },
		// 从哪里来，目前预约点餐，意向菜谱
    entranceType: {
      type: String
    },
    loading: {
      type: Boolean,
      default: true
    },
		calendarDisabled: {
		  type: Boolean,
		  default: false
		},
    mealTypeInfo: {
      type: Array,
      default: () => []
    },
    nowMealType: {
      type: Object,
      default: () => {}
    },
		docData: {
		  type: Object,
		  default: () => {}
		},
		nutrientMealType: {
      type: Object,
      default: () => {}
		},
    totalCalories: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 可点餐天数
      currentId: '',
      currentDay: 0,
      currentDate: '',
      currentMealType: '',
      // timeSlot: [{
      // 	name: '早餐',
      // 	type: 'breakfast'
      // }, {
      // 	name: '午餐',
      // 	type: 'lunch'
      // }, {
      // 	name: '下午茶',
      // 	type: 'afternoon'
      // }, {
      // 	name: '晚餐',
      // 	type: 'dinner'
      // }, {
      // 	name: '夜宵',
      // 	type: 'supper'
      // }, {
      // 	name: '凌晨餐',
      // 	type: 'morning'
      // }],
      nowMealTypeIndex: 0, //早中晚时间段索引
      // 选中档口索引
      orgIndex: 0,
      // 选中分类
      selectItem: [],
      // 选中分类索引
      cateIndex: 0,
      cateTops: [],
      isTouch: false,
      currentView: '',
      showArrowDown: false
    }
  },
  methods: {
		...mapMutations(['SET_SELECT']),
		topLeftClick() {
			if (this.$store.state.appoint.select.address_info) { // 扫码进来，如果要返回，就返回到主页
				// 把点餐的address_info清空掉
				this.SET_SELECT({
				  key: 'address_info',
				  data: {}
				})
				this.$miRouter.replaceAll({
				  path: '/pages/index/index'
				})
			} else { // 其他情况返回到上一页
				this.$miRouter.back()
			}
		},
    handleClickItem(item, index) {
      this.orgIndex = index
      this.selectItem = item
      console.log('子组件的foodList', this.getFoodLists, typeof (this.getFoodLists))
			// this.getFoodLists.forEach(v=>{
			// 	if(v.org_id !== item.org_id){
			// 		v.open = false
			// 	}
			// })
			// console.log(item.open)
			// 是否 展开消费点菜品列表
      this.$set(item, 'open', item.open ? false : true)
      this.$emit('selectfood', this.selectItem)
    },
    handleClickCateItem(index, id) {
      this.isTouch = false
      this.cateIndex = index
      this.currentView = `category-item-${id}`
    },
    handleSelect(item, index) {
      this.orgIndex = index
    },
    // 加下防抖，刚进来会触发这
    debounceDate(e) {
      // 做了禁止日历点击的，暂时不需要防抖了
      // uni.$u.debounce(this.calendarChange.bind(this, e), 300)
      this.calendarChange(e)
    },
    calendarChange(e) {
      this.$emit('click', {
        type: 'date',
        value: e
      })
      this.currentDay = timeFormat(e.time, 'mm月dd日')
      this.currentDate = timeFormat(e.time, 'yyyy-mm-dd')
    },
    getFoodHeights() {
      setTimeout(() => {
        getRect('.meal-goods-item', true, this).then(res => {
          let height = 0
          this.cateTops = res.map(item => {
            const obj = {
              top: height
            }
            height += item.height

            return obj
          })
        })
      }, 200)
    },
    handleScroll(e) {
      if (!this.isTouch) return
      const { scrollTop } = e.detail
      for (let i = this.cateTops.length - 1; i >= 0; i--) {
        if (scrollTop >= this.cateTops[i].top) {
          this.cateIndex = i
          break
        }
      }
    },
    clickMealType(data, index) {
      if (this.loading) return console.warn('多次请求接口已拦截2！')// uni.$u.toast('操作过于频繁！')
      this.nowMealTypeIndex = index
      this.orgIndex = 0
      this.$emit('click', {
        type: 'meal_type',
        value: data.meal_type
      })
      this.currentMealType = data.meal_type
    },
    gotoPathCanteen() {
			if(this.entranceType === 'intentionMenu'){
				this.$miRouter.replace({
					  path: `/pages_bundle/intention_food/choice_org`
					})
			}else{
			this.$miRouter.replace({
				  path: `/pages_bundle/select/select_diner?type=${this.$store.state.appoint.select.payment_order_type}`
				})
			}

    },
    formatePercent(val, need) {
      let percentage = (val / need) * 100
      return percentage >= 100 ? 100 : percentage
    },
    formateColor(val, need) {
      let percentage = (val / need) * 100
      if (percentage >= 120) {
        return '#ff5f5f'
      } else if (percentage >= 80) {
        return '#11e69e'
      } else {
        return '#ff9656'
      }
    },
    gotoNutritionalAnalysis() {
      this.$miRouter.push({
        path: '/pages_health/healthy/diet_healthy/nutritionalAnalysis',
        query: {
          date: this.currentDate,
          meal_type: this.currentMealType
        }
      })
    },
    initMealType() {
      this.nowMealTypeIndex = this.mealTypeInfo.map(x => x.meal_type).indexOf(this.nowMealType.meal_type)
      this.currentMealType = this.nowMealType.meal_type
    },
    // 打开外部说明
    openInstructions() {
      console.log('打开父组件说明弹窗')
      this.$emit('openInstructions')
    }
  },
  computed: {
    getFoodLists: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    org() {
      const state = this.$store.state.appoint
      return this.entranceType === 'intentionMenu'? state.select.intent_org_id :state.select.org || {}
    },
    showEnergyKcalIcon() {
      let show = false
      if (!this.nutrientMealType || !this.nutrientMealType.energy_kcal) {
        show = false
        return show
      }
      if (this.nutrientMealType.energy_kcal_range && this.nutrientMealType.energy_kcal_range.length > 0) {
        if (this.nutrientMealType.energy_kcal > this.nutrientMealType.energy_kcal_range[1]) { // 超
          show = true
          this.showArrowDown = false
        } else if ((this.nutrientMealType.energy_kcal > this.nutrientMealType.energy_kcal_range[0] || this.nutrientMealType.energy_kcal == this.nutrientMealType.energy_kcal_range[0]) && (this.nutrientMealType.energy_kcal < this.nutrientMealType.energy_kcal_range[1] || this.nutrientMealType.energy_kcal == this.nutrientMealType.energy_kcal_range[1])) { // 刚好在范围
          show = false
        } else {
          show = true
          this.showArrowDown = true
        }
      } else {
        show = false
      }
      return show
    }
  },
  watch: {
    value(newVal) {
      if (newVal && newVal.length) {
        this.selectItem = newVal[this.orgIndex]
        this.$emit('selectfood', this.selectItem)
        // console.log(2222,newVal[this.orgIndex].food_data)
        this.$nextTick(function () {
          this.getFoodHeights()
        })
      }
    }
  },
  created() {
    // this.initMealType()
    // console.log('菜品', this.value)
    // console.log('传进来的餐段', this.mealTypeInfo)
  }
}
</script>

<style lang="scss">
$silde-width: 200rpx;

.appoint-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  .min-w{
    min-width: 76rpx;
  }
  .header {
    background: $color-primary-light-2 $bg-linear-gradient-1;

    .canteen {
      padding: 28rpx 36rpx;
      background-image: url($imgBasePath + '/images/canteen_r_bg.png');
      background-position: right bottom;
      background-size: 128rpx 82rpx;
      background-repeat: no-repeat;
    }
  }

  .date {
    padding: 20rpx 0 12rpx;

    .date-item {
      &.active {
        .day {
          color: #fff;
          background-color: $color-primary;
        }
      }

      .day {
        width: 64rpx;
        height: 64rpx;
        text-align: center;
        line-height: 64rpx;
        border-radius: 50%;
      }
    }
  }

  .meal-type {
    border-top: $border-base;
    border-bottom: $border-base;

    .month-day {
      flex: none;
      line-height: 80rpx;
      text-align: center;
      width: $silde-width;
      border-right: $border-base;
    }

    .meal-scroll {
      height: 80rpx;
      white-space: nowrap;
      min-width: 0;
      // touch-action: none;

      .meal-lists {
        display: inline-flex;
        height: 100%;

        .meal-item {
          flex: none;
          height: 100%;
          margin-right: 80rpx;
          border-bottom: 6rpx solid rgba(0, 0, 0, 0);

          &:first-of-type {
            margin-left: 80rpx;
          }

          &.active {
            border-color: $color-primary;
            font-weight: 500;
          }
        }
      }
    }
  }

  .meal {
    min-height: 0;

    .meal-classify {
      width: $silde-width;
      height: 100%;
      background-color: #f0f3f5;

      .classify-lists {
        .classify-item {
          min-height: 120rpx;
          padding: 20rpx 20rpx 20rpx 30rpx;

          &.active {
            color: $color-primary;
            background-color: #fff;
          }
        }

        .classify-children {
          height: auto;
          overflow: hidden;

          .children-item {
            position: relative;
            min-height: 120rpx;
            padding: 20rpx 24rpx;
            border-left: 6rpx solid transparent;

            &.active {
              background-color: $color-primary-light-9;
              color: $color-text-black;
              border-color: $color-primary;
            }
          }
        }
      }
    }

    .meal-goods {
      height: 100%;
      flex: 1;
      .energy-card {
        background-color: #F6F7FB;
        height: 10%;
      }
    }
  }
}
</style>
