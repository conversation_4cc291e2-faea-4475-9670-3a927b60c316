<template>
	<view :style="theme.style" class="appointment-item" :class="orderType">
		<view class="item-header">
			<view class="flex row-between">
				<view class="md f-w-500">总订单号：{{ info.trade_no }}</view>
				<!--20250326 根据特定项目点放开-->
				<view class="red" @click="cancelOrder" v-if="isShowCancelOrder">取消订单</view>
			</view>
			<view v-if="orderType !== 'report_meal'" class="md f-w-500 p-t-10">就餐人：{{ info.user_name }}</view>
		</view>
		<view class="p-t-20" v-for="(item, index) in info.order_payment_data" :key="index">
			<view class="flex row-between">
				<view class="muted">{{ item.reservation_date }} {{ item.take_meal_time_alias }}</view>
				<!-- <view class=" no_take">已取餐</view> -->
				<view :class="{ primary: item.take_meal_status == 'no_take' }">{{ item.take_meal_status_alias }}</view>
			</view>
			<view class="flex row-right p-t-20 p-b-20">
				<view class="flex">
					<view class="m-l-20">
						<u-button
							v-if="item.take_meal_status == 'no_take' && item.take_meal_status !== 'cancel' && item.take_meal_status !== 'take_out' && item.take_meal_status !== 'no_save'"
							type="primary"
							size="small"
							text="取餐码"
							:customStyle="customBtnStyle"
							:color="variables.colorPrimary"
							@click="toCode(info, item)"
						></u-button>
					</view>
					<view class="m-l-20" v-if="orderType !== 'report_meal'">
						<u-button
							type="primary"
							size="small"
							text="查看详情"
							:customStyle="customBtnStyleGrey"
							:color="variables.colorPrimary"
							@click="toDetail(info,item)"
						></u-button>
					</view>
				</view>
			</view>
		</view>
		<view v-if="orderType === 'report_meal'">
			<view v-if="info.pack_settings_name" class="flex row-between p-t-30">
				<view class="muted">餐包名称：</view>
				<view>{{ info.pack_settings_name }}</view>
			</view>
			<view class="flex row-between p-t-30">
				<view class="muted">下单时间：</view>
				<view>{{ info.create_time }}</view>
			</view>
			<view class="flex row-between p-t-30">
				<view class="muted">总预订数：</view>
				<view>{{ info.total }}</view>
			</view>
			<view class="flex row-between p-t-30">
				<view class="muted">已取餐：</view>
				<view>{{ info.take_out_total }}</view>
			</view>
			<view class="flex row-between p-t-30 p-b-30">
				<view class="muted">未取餐：</view>
				<view>{{ info.no_take_total }}</view>
			</view>
		</view>
		<view class="item-footer flex row-between col-center">
			<view v-if="orderType !== 'report_meal'" class="muted">{{ info.create_time }}</view>
			<view class="">
				<text class="muted m-r-8">合计</text>
				<price-format :price="info.total_fee" :size="36"></price-format>
			</view>
			<view v-if="orderType === 'report_meal'" class="muted flex" @click="gotoMealReportDetail">
				<view>详情</view>
				<u-icon name="arrow-right" size="24rpx"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
import { encodeQuery, unique } from '@/utils/util'
import Cache from '@/utils/cache'

export default {
	props: {
		info: Object,
		orderType: {
			type: String,
			default: 'reservation'
		},
	},
	data() {
		return {
			customBtnStyle: {
				minWidth: '110rpx',
				height: '50rpx',
				lineHeight: '60rpx'
			},
			customBtnStyleGrey: {
				minWidth: '110rpx',
				height: '50rpx',
				lineHeight: '60rpx',
				backgroundColor: '#f0f3f4',
				color: '#787a79',
				border: '#f0f3f4'
			},
			isShowCancelOrder: false
		}
	},
	onLoad(option) {	
	},
	created() {
		let userinfo = Cache.get('userInfo')
    if (userinfo) {
       let companyId = userinfo.company_id || -1
			 // 这里到时候正式会写死增城养老社区的进行测试，好了以后才会彻底放开
			 let id = location.origin.indexOf('debug') !== -1 ? 589 : 307
			 id = location.origin.indexOf('staging') !== -1 ? 3 : id
			 this.isShowCancelOrder = companyId ===  id
		}
	},
	onShow() {},

	mounted() {},
	methods: { 
		toDetail(data,orderPaymentDataInfo) {
			this.$miRouter.push({
				path: '/pages_bundle/meal_public/appoint_detail',
				query: {
					id: data.trade_no,
					order_type: this.orderType,
					date: orderPaymentDataInfo.reservation_date
				}
			})
		},
		gotoMealReportDetail() {
			this.$miRouter.push({
				path: '/pages_bundle/meal_public/appoint_detail',
				query: {
					order_type: this.orderType,
					data: this.info
				}
			})
		},
		cancelOrder() {
			let orderTypeText = ''
			if (this.orderType === 'reservation') {
				orderTypeText = '预约点餐'
			} else if (this.orderType === 'report_meal') {
				orderTypeText = '报餐'
			}
			let modalTitle = `确定取消${this.info.create_time}的${orderTypeText}吗？`
			// if (Reflect.has(this.info, 'order_payment_data') && this.info.order_payment_data && this.info.order_payment_data.length > 0) {
			// 	const isSubsidyFeeOrder = this.info.order_payment_data.some(v => v.is_subsidy_fee_order)
			// 	if (isSubsidyFeeOrder) {
			// 		modalTitle = '如对应补贴已清零，退款后，相应金额也将被清零，是否继续？'
			// 	}
			// }
			// 20250328 杨海俊要求修改显示提示，原因由于大订单现在不包含子订单数据，所以只能这样提示。
			modalTitle = '如对应补贴已清零，退款后，相应金额也将被清零，是否继续？如未使用补贴，请忽略此提示!'
			let _this = this
			uni.showModal({
				title: '取消订单',
				content: modalTitle,
				// cancelColor: this.$vars.color_primary,
				// confirmColor: this.$vars.color_red,
				confirmText: '取消订单',
				cancelText: '我再想想',
				success: function(res) {
					if (res.confirm) {
						if (_this.info.set_can_review) { // 是否开启审核
							let reviewSearchTimes = []
							let reviewMealTypes = []
							let reviewOrderNos = []
							let cancelReview = false
							// uni.$u.timeFormat(_this.info.create_time, 'yyyy-mm-dd')
							_this.info.order_payment_data.forEach(v => {
								if (v.cancel_review) { // 当前订单是否满足审核规则
									reviewSearchTimes.push(v.reservation_date)
									reviewMealTypes.push(v.take_meal_time)
									reviewOrderNos.push(v.order_payment_trade_no)
									cancelReview = v.cancel_review
								}
							})
							if (!cancelReview) {
								uni.$u.toast('当前暂无可取消餐段！')
								return
							}
							reviewSearchTimes = unique(reviewSearchTimes)
							reviewMealTypes = unique(reviewMealTypes)
							_this.$miRouter.push({
								path: '/pages_order/review/apply/select_meal',
								query: {
									type: _this.orderType,
									trade_nos: encodeQuery(reviewOrderNos),
									date: encodeQuery(reviewSearchTimes), // 时间
									meal_types: encodeQuery(reviewMealTypes) // 餐段
								}
							})
							return
						}
						let tradeNo = _this.info.trade_no
						// _this.confirmRefund(tradeNo, true)
						_this.$emit('refundconfirm', tradeNo)
					} else if (res.cancel) {
						// console.log('用户点击取消')
					}
				}
			})
		},
		toCode(data, item) {
			// let code
			// if (data.take_meal_type === 'cupboard') {
			// 	code = String(item.take_meal_number)
			// } else {
			// 	code = item.order_payment_trade_no
			// }
			this.$miRouter.push({
				path: '/pages_bundle/meal_public/meal_code',
				query: {
					data: this.$encodeQuery({
						trade_no: item.trade_no,
						take_meal_type: item.take_meal_type,
						meal_number_qrcode: item.meal_number_qrcode,
						meal_number: item.meal_number
					})
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.report_meal::before, .report_meal::after{
	top: 100rpx!important;
}
.appointment-item {
	flex: 1;
	border-radius: 20rpx;
	background-color: #fff;
	position: relative;
	padding: 0 30rpx;
	margin-bottom: 30rpx;
	&::before,
	&::after {
		content: '';
		display: block;
		height: 30rpx;
		width: 30rpx;
		background-color: $background-color;
		position: absolute;
		border-radius: 50%;
		top: 150rpx;
		transform: translateY(-50%);
	}
	&::before {
		left: -15rpx;
	}
	&::after {
		right: -15rpx;
	}
	.item-header {
		border-bottom: 1px dashed $border-color-base;
		// height: 90rpx;
		padding: 30rpx 0rpx;
	}
	.item-info {
		padding: 30rpx 0;
	}
	.item-footer {
		border-top: $border-base;
		height: 100rpx;
	}
}
</style>
