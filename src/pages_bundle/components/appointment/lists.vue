<template>
	<view class="" style="height: 100%;">
		<!-- 日历 -->
		<view class="calendar" v-if="type == 'now'">
			<calendar :reservationDocData="docData" :value="dateValue" :appointType="orderType" @change="calendarChange"></calendar>
		</view>
		<mescroll-uni
			ref="mescrollRef"
			:fixed="false"
			:safearea="false"
			:bottom="bottom"
			@init="mescrollInit"
			:up="{ auto: false }"
			:down="{ auto: false }"
			@down="downCallback"
			@up="upCallback"
		>
			<view class="appointment">
				<view class="flex row-between m-b-30" v-if="type == 'now' && !isMealReportDetail">
					<view class="appointment-time xxl">{{ currentDate }}</view>
					<view class="primary" @click="cancelOrder">批量取消</view>
				</view>
				<view class="appointment-lists" v-if="orderList && orderList.length">
					<u-checkbox-group v-model="checkedOrder" @change="changeCheckOrder" placement="column" v-if="type == 'now'">
						<item
							:type="type"
							:order-type="orderType"
							:is-meal-report-detail="isMealReportDetail"
							@refundconfirm="getUserReservationOrderRefund"
							:mode="mode"
							:info="item"
							v-for="(item, index) in orderList"
							:key="index"
							:time="currentTime"
						/>
					</u-checkbox-group>
					<block v-if="type == 'all'">
						<user-appoint-order-all
							:info="item"
							:order-type="orderType"
							@refundconfirm="getUserReservationOrderRefund"
							v-for="(item, index) in orderList"
							:key="index"
						></user-appoint-order-all>
					</block>
				</view>
			</view>
		</mescroll-uni>
	</view>
</template>

<script>
import { apiMonthReservationOrderDetailInfo, apiCollectionReservationOrder, apiMonthReservationOrder } from '@/api/user.js'
import {
	reportMealMonthOrder,
	reportMealCollectionOrder,
	reportMealOrderDetailInfo,
	reportMealOrderRefund
} from '@/api/report_meal.js'
import Cache from '@/utils/cache'
import { encodeQuery, unique } from '@/utils/util'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
import item from './item.vue'
import userAppointOrderAll from './user_appoint_order_all.vue'
import { timeFormat } from '@/utils/date'
import { getApiUserReservationOrderRefund } from '@/api/reservation.js'
import { formateVisitorParams } from "@/utils/util.js"
export default {
	mixins: [MescrollMixin],
	components: {
		item,
		userAppointOrderAll
	},
	props: {
		type: {
			type: String,
			default: ''
		},
		orderType: {
			type: String,
			default: 'reservation'
		},
		bottom: {
			type: Number,
			default: 0
		},
		mode: {
			type: String,
			default: 'appoint'
		},
		tradeId: {
			type: [Number, String],
			default: ''
		},
		dateValue: {
			type: String,
			default: ''
		},
		isMealReportDetail: {
			type: Boolean,
			default: false
		},
	},
	data() {
		return {
			checkedOrder: [],
			docData: {},
			currentDate: '',
			currentTime: 0,
			formData: {
				company_id: '',
				// user_id: '',
				person_no: '',
				// month: '',
				unified_trade_no: '',
				// page: '',
				// page_size: '',
				day: ''
			},
			orderList: [],
			calendarValue: {},
			cancelReview: false, // 是否需要审核， 批量取消时拿其中一个判断
			reviewSearchTimes: [], // 审核需要的时间搜索条件
			reviewMealTypes: [] // 审核的餐段筛选
		}
	},

	methods: {
		initMescroll() {
			this.orderList = []
      this.$nextTick(() => {
        this.mescroll.resetUpScroll() // 重置列表数据为第一页
        this.mescroll.scrollTo(0, 0)
      })
    },
		// 上拉加载
		async upCallback(page) {
			let pageNum = page.num // 第几页
			let pageSize = page.size // 每页条数
			let type = this.type
			const params = {
				...this.formData,
				page: pageNum,
				page_size: pageSize
			}
			let apiPromise
			if (this.orderType === 'reservation') {
				//now为一个月内的某天，all是汇总
				apiPromise = type == 'now' ? apiMonthReservationOrderDetailInfo(formateVisitorParams(params)) : apiCollectionReservationOrder(formateVisitorParams(params))
			} else if (this.orderType === 'report_meal') {
				apiPromise = type == 'now' ? reportMealOrderDetailInfo(params) : reportMealCollectionOrder(params)
			}

			apiPromise
				.then(res => {
					const results = res.data.results
					const count = res.data.count
					let pageLength = 0
					if (res.data.results && res.data.results.length) {
						pageLength = res.data.results.length
					} else {
						pageLength = 0
					}

					// 如果是第一页需手动置空列表
					if (page.num == 1) this.orderList = []
					// 追加新数据
					this.orderList = [...this.orderList, ...results]
					//方法二(推荐): 后台接口有返回列表的总数据量 count, 判断是否有下一页
					this.mescroll.endBySize(pageLength, count)
				})
				.catch(() => {
					// 请求失败,隐藏加载状态
					this.mescroll.endErr()
				})
		},
		// 日历日期变化
		calendarChange(val) {
			this.calendarValue = val
			this.currentDate = timeFormat(val.time, 'yyyy-mm-dd 周w')
			this.formData.day = timeFormat(val.time, 'yyyy-mm-dd')
			// this.formData.month = timeFormat(val.time, 'yyyymm')
			this.currentTime = val.time
			this.getMonthOrder(val.range)
			this.$nextTick(() => {
				this.mescroll.resetUpScroll()
			})
		},
		changeCheckOrder(e) {
			this.cancelReview = false
			for (let index = 0; index < this.orderList.length; index++) {
				const order = this.orderList[index];
				if (e.includes(order.trade_no)) {
					if (order.set_can_review) { // 是否开启了审批, 数据有多条的情况，其中一条为true都走
						if (!this.cancelReview) this.cancelReview = order.set_can_review
						if (order.cancel_review) { // 是否满足审核规则
							this.reviewSearchTimes.push(order.report_date)
							this.reviewMealTypes.push(order.meal_type)
						}
					}
					// break
				}
			}
			// 去重
			this.reviewSearchTimes = unique(this.reviewSearchTimes)
			this.reviewMealTypes = unique(this.reviewMealTypes)
		},
		cancelOrder() {
			if (!this.checkedOrder.length) {
				return uni.$u.toast('请选择订单')
			}
			let _this = this
			let content
			let confirmText
			if (this.orderType === 'reservation') {
				content = '确定取消选中的预约点餐吗？'
				confirmText = '取消预约'
			} else if (this.orderType === 'report_meal') {
				content = '确定取消选中的报餐订单吗？'
				confirmText = '取消报餐'
			}
			// 判断是否存在补贴钱包支付的订单，
			this.orderList.some(v => {
				if (this.checkedOrder.includes(v.trade_no) && v.is_subsidy_fee_order) {
					content = '如对应补贴已清零，退款后，相应金额也将被清零，是否继续？'
					return true
				}
				return false
			})
			uni.showModal({
				title: '批量取消',
				content,
				// #ifndef MP-ALIPAY
				// cancelColor: this.$vars.color_primary,
				// confirmColor: this.$vars.color_red,
				// #endif
				confirmText,
				cancelText: '我再想想',
				success: (res) => {
					if (res.confirm) {
						if (this.cancelReview) { // 审批
							this.$miRouter.push({
								path: '/pages_order/review/apply/select_meal',
								query: {
									type: this.orderType,
									trade_nos: encodeQuery(this.checkedOrder),
									date: encodeQuery(this.reviewSearchTimes), // 时间
									meal_types: encodeQuery(this.reviewMealTypes) // 餐段
								}
							})
						} else {
							_this.getUserReservationOrderRefund(_this.checkedOrder)
						}
					} else if (res.cancel) {
						console.log('用户点击取消')
					}
				}
			})
		},
		// 取消订单
		getUserReservationOrderRefund(tradeNo) {
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
			let params = {
				payment_trade_nos: this.type == 'now' ? tradeNo : [], //当餐
				unified_trade_no: this.type == 'all' ? tradeNo : '', //总订单号
				remark: '',
				person_no: this.formData.person_no,
				company_id: this.formData.company_id
			}
			let apiPromise
			if (this.orderType === 'reservation') {
				apiPromise = getApiUserReservationOrderRefund(formateVisitorParams(params))
			} else if (this.orderType === 'report_meal') {
				apiPromise = reportMealOrderRefund(params)
			}

			apiPromise
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.checkedOrder = []
						uni.$u.toast('取消成功')
						if (this.orderType === 'reservation' && this.type == 'now') {
							this.getMonthOrder(this.calendarValue.range)
						}
						this.upCallback({ num: 1, size: 10 })
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.hideLoading()
					uni.$u.toast(error.message)
				})
		},
		// 获取当前月已经预约/报餐的日期,用于日历上dot-lists的显示
		getMonthOrder(time) {
			let params = {
				person_no: this.formData.person_no,
				// month: this.formData.month,
				company_id: this.formData.company_id,
				unified_trade_no: this.formData.unified_trade_no,
				start_date: time[0],
				end_date: time[1]
			}
			let apiPromise
			if (this.orderType === 'reservation') {
				apiPromise = apiMonthReservationOrder(formateVisitorParams(params))
			} else if (this.orderType === 'report_meal') {
				apiPromise = reportMealMonthOrder(params)
			}
			return new Promise((resolve, reject) => {
				apiPromise
					.then(res => {
						if (Object.keys(res.data) && Object.keys(res.data).length) {
							this.docData = res.data
						}
						resolve(res)
					})
					.catch(() => {
						reject()
					})
			})
		},
		getInitParams() {
			this.currentDate = timeFormat(Date.now(), 'yyyy-mm-dd 周w')
			this.currentTime = Date.now()

			this.formData.day = timeFormat(Date.now(), 'yyyy-mm-dd')
			// this.formData.month = timeFormat(Date.now(), 'yyyymm')
			// console.log(timeFormat(nowTime.getTime(), 'yyyymm'))
			// this.formData.user_id = Cache.get('userInfo').user_id || ''
			this.formData.person_no = Cache.get('userInfo').person_no || ''
			this.formData.company_id = Cache.get('userInfo').company_id || ''
			this.formData.unified_trade_no = this.tradeId
		}
	},
	onShow() {
	},
	created() {
		this.getInitParams()
	},
	mounted() {
		if (this.type == 'all') {
			this.mescroll.resetUpScroll()
		}
	}
}
</script>
<style lang="scss" scoped>
.appointment {
	padding: 40rpx 40rpx 20rpx;

	.appointment-time {
		display: flex;
		align-items: center;

		&::before {
			content: '';
			display: block;
			width: 6rpx;
			height: 28rpx;
			background: $color-primary;
			margin-right: 15rpx;
		}
	}

	.appointment-lists {
	}
}
</style>
