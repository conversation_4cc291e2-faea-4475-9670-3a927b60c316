<template>
  <view>
    <u-count-down :time="timeupSecond * 1000" format="HH:mm:ss" autoStart millisecond @change="onChange" @finish="finish">
      <view class="time">
        <view class="time__custom">
          <text class="time__custom__item">{{ timeData.hours > 9 ? timeData.hours : '0' + timeData.hours }}</text>
        </view>
        <text class="time__doc">:</text>
        <view class="time__custom">
          <text class="time__custom__item">{{ timeData.minutes > 9 ? timeData.minutes : '0' + timeData.minutes }}</text>
        </view>
        <text class="time__doc">:</text>
        <view class="time__custom">
          <text class="time__custom__item">{{ timeData.seconds > 9 ? timeData.seconds : '0' + timeData.seconds }}</text>
        </view>
      </view>
    </u-count-down>
  </view>
</template>

<script>
export default {
  props: {
    // 这里传入数字是多少就是多少秒倒计时
    timeupSecond: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      timeData: {
        hours: 0,
        minutes: 0,
        seconds: 0
      }
    }
  },
  methods: {
    finish() {
      this.$emit('finish')
    },
    onChange(e) {
      this.timeData = e
    }
  }
}
</script>

<style lang="scss" scoped>
.time {
  display: flex;
  align-items: center;

  &__custom {
    width: 40rpx;
    height: 40rpx;
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    justify-content: center;
    align-items: center;

    &__item {
      color: #333333;
      font-size: 32rpx;
      text-align: center;
      font-weight: 600;
    }
  }

  &__doc {
    color: #333333;
    padding: 0px 5rpx;
    font-weight: 600;
  }

  &__item {
    color: #606266;
    font-size: 15rpx;
    margin-right: 4rpx;
  }
}
</style>
