<template>
  <view :style="theme.style" class="my-coupon">
    <view class="tabs-wrapp">
      <u-tabs
        :scrollable="false"
        lineWidth="70"
        :activeStyle="{ color: '#1d201e' }"
        :inactiveStyle="{ color: '#8f9295' }"
        :list="couponTabs"
        @click="clickTabs"
        :lineColor="variables.colorPrimary"
      ></u-tabs>
    </view>
    <!-- style="opacity:0.7" -->
    <!-- <view v-show="couponManageReceiveList.length"> -->
    <mescroll-uni
      ref="mescrollRef"
      @init="mescrollInit"
      @down="downCallback"
      top="110"
      bottom="100"
      @up="upCallback"
      :up="upOption"
      @emptyclick="emptyClick"
    >
      <view
        class="list-setting-box"
        :style="{ opacity: item.use_status === 'USED' || item.use_status === 'EXPIRED' ? 0.7 : 1 }"
        v-for="(item, index) in couponManageReceiveList"
        :key="index"
      >
        <view class="line" :style="[lineStyle(item)]"></view>
        <view class="coupon-center">
          <view class="coupon p-b-20">
            <!-- 满减 -->
            <view class="coupon-price" v-if="item.coupon_type === 'FULL_DISCOUNT'">
              <view class="red">
                <text style="font-size: 36rpx">¥</text>
                <text style="font-size: 60rpx">{{ couponMoney(item, 'currentMoney') }}</text>
              </view>
              <view class="muted xxs">满{{ couponMoney(item, 'fullMoney') }}可用</view>
            </view>
            <!-- 立减 -->
            <view class="coupon-price" v-if="item.coupon_type === 'INSTANT_DISCOUNT'">
              <view class="red">
                <text style="font-size: 36rpx">¥</text>
                <text style="font-size: 60rpx">{{ couponMoney(item, 'currentMoney') }}</text>
              </view>
              <view class="muted xxs">无限制金额</view>
            </view>
            <!-- 折扣 -->
            <view class="coupon-price" v-if="item.coupon_type === 'DISCOUNT'">
              <view class="red">
                <text style="font-size: 60rpx">{{ discountNumber(item) }}</text>
                <text style="font-size: 36rpx">折</text>
              </view>
              <view class="muted xxs" v-if="discountIsMax(item)">最多优惠{{ discountMoney(item) }}</view>
            </view>
            <!-- 兑换券 -->
            <view class="coupon-price" v-if="item.coupon_type === 'EXCHANGE'">
              <view class="m-b-10">
                <u-image width="68rpx" height="54rpx" :src="themeImgPath.img_bundle_exchange_coupon"></u-image>
              </view>
              <view class="muted xxs" v-if="item.use_condition.is_satisfy">满{{ couponMoney(item, 'exchangeMoney') }}可用</view>
            </view>
            <u-line color="#ccc" dashed direction="col" length="85rpx"></u-line>
            <view class="coupon-title-wrapp flex-1 p-l-20 p-r-20">
              <view class="coupon-title p-b-10">【{{ item.coupon_type_alias }}】{{ item.name }}</view>
              <view class="muted xxs p-l-10" v-if="item.is_use_time">{{ item.use_start_time }} - {{ item.use_end_time }}</view>
              <view class="muted xxs p-l-20" v-else>无限制使用时间</view>
            </view>
          </view>
          <!-- <u-line color="#ccc" dashed  ></u-line> -->
          <view>
            <u-collapse accordion :border="false">
              <u-collapse-item :clickable="false" :border="false" :title="tipsUseOrg(item)">
                <text class="xxs muted">{{ tipsUseMeal(item) }}</text>
                <!-- <view class="xxs muted">备注：慢慢慢慢</view> -->
              </u-collapse-item>
            </u-collapse>
          </view>
        </view>
        <view :class="[{ 'status-active': item.use_status === 'USED' || item.use_status === 'EXPIRED' }, 'coupon-status']">
          {{ item.use_status_alias }}
        </view>
      </view>

      <view v-if="!couponManageReceiveList.length" class="flex flex-col col-center">
        <image class="image-null" :src="themeImgPath.img_bundle_coupon_not"></image>
        <text class="xxl m-t-40">暂无优惠券</text>
        <text class="nr muted m-t-20">当前暂无优惠券，可以去卡券中心看看</text>
      </view>
    </mescroll-uni>
    <view class="foot-fixed m-t-30 p-30" @click="clickCouponIndex">
      <u-button
        shape="circle"
        :color="variables.bgLinearGradient1"
      >
        卡券中心
      </u-button>
    </view>
  </view>
</template>

<script>
import { apiCouponList } from '@/api/coupon.js'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
import NP from '@/utils/np.js'
export default {
  computed: {},
  mixins: [MescrollMixin],
  data() {
    return {
      imgPath: this.$imgPath,
      couponTabs: [
        {
          name: '未使用（0）',
          type: 'UNUSED'
        },
        {
          name: '已使用（0）',
          type: 'USED'
        },
        {
          name: '已失效（0）',
          type: 'EXPIRED'
        }
      ],
      useStatus: 'UNUSED',
      canReset: false,
      upOption: {
        empty: {
          use: false
        }
      },
      couponManageReceiveList: []
    }
  },
  onLoad() {},
  onShow() {
    this.canReset && this.mescroll.resetUpScroll() // 重置列表数据为第一页
    this.canReset && this.mescroll.scrollTo(0, 0) // 重置列表数据为第一页时,建议把滚动条也重置到顶部,避免无法再次翻页的问题
    this.canReset = false
  },
  computed: {
    lineStyle() {
      return data => {
        let style = {
          backgroundColor: ''
        }
        // #bab9b9 失效
        // #f94141 使用 未使用
        // #fec055 兑换
        if (data.coupon_type === 'EXCHANGE' && data.use_status !== 'EXPIRED') {
          style.backgroundColor = '#fec055'
        } else if (data.use_status === 'EXPIRED') {
          style.backgroundColor = '#bab9b9'
        } else {
          style.backgroundColor = '#f94141'
        }

        return style
      }
    }
  },
  methods: {
    clickTabs(item) {
      this.mescroll.scrollTo(0, 0) // 重置列表数据为第一页时,建议把滚动条也重置到顶部,避免无法再次翻页的问题
      this.useStatus = this.couponTabs[item.index].type
      this.upCallback({ num: 1, size: 10 })
    },
    // 上拉加载更多
    upCallback(page) {
      this.getApiCouponList(page)
    },
    async getApiCouponList(page) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      const [err, res] = await this.$to(
        apiCouponList({
          page: page.num,
          page_size: page.size,
          use_status: this.useStatus
        })
      )
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        this.couponTabs = [
          {
            name: `未使用（${res.data.summary_data.unused_count}）`,
            type: 'UNUSED'
          },
          {
            name: `已使用（${res.data.summary_data.used_count}）`,
            type: 'USED'
          },
          {
            name: `已失效（${res.data.summary_data.expired_count}）`,
            type: 'EXPIRED'
          }
        ]
        const results = res.data.results
        const count = res.data.count
        let curPageLen = res.data.results.length
        // 如果是第一页需手动置空列表
        if (page.num === 1) this.couponManageReceiveList = []
        // 追加新数据
        this.couponManageReceiveList = [...this.couponManageReceiveList, ...results]
        console.log(this.couponManageReceiveList, 7777)
        // 方法二(推荐): 后台接口有返回列表的总数据量 count, 判断是否有下一页
        this.mescroll.endBySize(curPageLen, count)
        // 如果是第一页就默认最顶部
        if (page.num === 1) {
          this.mescroll.scrollTo(0, 0) // 重置列表数据为第一页时,建议把滚动条也重置到顶部,避免无法再次翻页的问题
        }
      } else {
        uni.$u.toast(res.msg)
      }
    },
    couponMoney(item, type) {
      let useCondition = item.use_condition
      console.log(useCondition)
      let money = 0
      switch (type) {
        case 'currentMoney':
          money = NP.divide(useCondition.reduce, 100)
          break
        case 'fullMoney':
          money = NP.divide(useCondition.full_money, 100)
          break
          case 'exchangeMoney':
          money = NP.divide(useCondition.satisfy_money, 100)
          break
        default:
          break
      }
      return money
    },
    discountNumber(item) {
      let useCondition = item.use_condition
      return useCondition.discount
    },
    discountIsMax(item) {
      let useCondition = item.use_condition
      return useCondition.is_max
    },
    discountMoney(item) {
      let useCondition = item.use_condition
      return NP.divide(useCondition.max_money, 100)
    },
    tipsUseOrg(item) {
      return `限使用组织：${item.use_organization_name.join(',')}`
    },
    tipsUseMeal(item) {
      return `限使用餐段：${item.meal_type_list_alias.join(',')}。`
    },
    clickCouponIndex() {
      this.$miRouter.replace({
        path: '/pages_bundle/coupon/index'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.my-coupon {
  .image-null {
    width: 240rpx;
    height: 176rpx;
    margin: 170rpx auto 0;
  }
  .tabs-wrapp {
    background-color: #fff;
  }
  // .my-coupon-wrapp {
  //   padding: 30rpx;

  .list-setting-box:last-child {
    margin-bottom: 100rpx;
  }
  .list-setting-box {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    margin-left: 30rpx;
    margin-right: 30rpx;
    .line {
      position: absolute;
      left: 0;
      width: 8rpx;
      height: 100%;
      background-color: #f94141;
    }
    .exchange-line-active {
      background-color: #fec055 !important;
    }
    .coupon-status {
      position: absolute;
      right: 0;
      top: 0;
      color: #f94141;
      padding: 4px 11px;
      font-size: 11px;
      background-color: #fef2f2;
      border-radius: 0px 0px 0px 11px;
    }
    .status-active {
      color: #fff !important;
      background-color: #d9d9d9 !important;
    }
    .coupon-status-lose {
      color: #fff !important;
      background-color: #d9d9d9 !important;
    }
    .coupon-center {
      padding: 30rpx 40rpx;
      width: 100%;

      .coupon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .coupon-price {
          width: 140rpx;
          display: flex;
          flex-direction: column;
          // justify-content: center;
          align-items: center;
          // padding-right: 30rpx
        }
        .coupon-title-wrapp {
          .coupon-title {
            font-weight: bold;
            font-size: 30rpx;
          }
        }
      }
    }

    ::v-deep .u-collapse {
      .u-cell__body {
        padding: 0;
      }
      .u-cell--clickable {
        background-color: #fff;
      }
      .u-cell__title-text {
        padding-top: 20rpx;
        line-height: normal;
        color: #999999;
        font-size: 22rpx;
      }
      .u-collapse-item__content__text {
        padding: 15rpx 0rpx 0rpx 0rpx;
      }
    }
  }
  .foot-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1002;
    padding-bottom: 20rpx;
  }
}
</style>
