<template>
  <view :style="theme.style" class="coupon-center-wrapp">
    <mescroll-uni
      ref="mescrollRef"
      @init="mescrollInit"
      bottom="100"
      @down="downCallback"
      top="20"
      @up="upCallback"
      :up="upOption"
      @emptyclick="emptyClick"
    >
      <view class="list-setting-box" v-for="(item, index) in couponManageReceiveList" :key="item.id">
        <view class="line" :style="{ backgroundColor: item.coupon_type === 'EXCHANGE' ? '#fec055' : '#f94141' }"></view>
        <view class="coupon-center">
          <view class="coupon">
            <!-- 满减 -->
            <view class="coupon-price" v-if="item.coupon_type === 'FULL_DISCOUNT'">
              <view class="red">
                <text style="font-size: 36rpx">¥</text>
                <text style="font-size: 60rpx">{{ couponMoney(item, 'currentMoney') }}</text>
              </view>
              <view class="muted xxs">满{{ couponMoney(item, 'fullMoney') }}可用</view>
            </view>
            <!-- 立减 -->
            <view class="coupon-price" v-if="item.coupon_type === 'INSTANT_DISCOUNT'">
              <view class="red">
                <text style="font-size: 36rpx">¥</text>
                <text style="font-size: 60rpx">{{ couponMoney(item, 'currentMoney') }}</text>
              </view>
              <view class="muted xxs">无限制金额</view>
            </view>
            <!-- 折扣 -->
            <view class="coupon-price" v-if="item.coupon_type === 'DISCOUNT'">
              <view class="red">
                <text style="font-size: 60rpx">{{ discountNumber(item) }}</text>
                <text style="font-size: 36rpx">折</text>
              </view>
              <view class="muted xxs" v-if="discountIsMax(item)">最多优惠{{ discountMoney(item) }}</view>
            </view>
            <!-- 兑换券 -->
            <view class="coupon-price" v-if="item.coupon_type === 'EXCHANGE'">
              <view class="m-b-10">
                <u-image width="68rpx" height="54rpx" :src="themeImgPath.img_bundle_exchange_coupon"></u-image>
              </view>
              <view class="muted xxs" v-if="item.use_condition.is_satisfy">满{{ couponMoney(item, 'exchangeMoney') }}可用</view>
            </view>
            <view class="coupon-title-wrapp flex-1 p-l-20 p-r-20">
              <view class="coupon-title p-b-10">【{{ item.coupon_type_alias }}】{{ item.name }}</view>
              <view class="muted xxs" v-if="item.is_use_time">{{ item.use_start_time }} - {{ item.use_end_time }}</view>
            </view>
            <view @click="clickClaimCoupon(item)">
              <u-button
                shape="circle"
                size="small"
                color="linear-gradient(90deg, #f54133 0%, #928849 0%, #2ecf5f 0%, #1bca50 100%), linear-gradient(#0cd893, #0cd893)"
              >
                领取
              </u-button>
            </view>
          </view>
          <view>
            <u-collapse accordion :border="false">
              <u-collapse-item :clickable="false" :border="false" :title="tipsUseOrg(item)">
                <text class="xxs muted">{{ tipsUseMeal(item) }}</text>
              </u-collapse-item>
            </u-collapse>
          </view>
        </view>
        <view class="coupon-status">待领取</view>
      </view>
      <view v-if="!couponManageReceiveList.length" class="flex flex-col col-center">
        <image class="image-null" :src="themeImgPath.img_bundle_coupon_not"></image>
        <text class="xxl m-t-40">暂无优惠券</text>
        <!-- <text class="nr muted m-t-20">当前暂无优惠券，可以去卡券中心看看</text> -->
        <!-- <view class="m-t-30 p-30">
          <u-button
            :custom-style="{ width: '260rpx' }"
            shape="circle"
            color="linear-gradient(90deg, #a9fed5 0%, #5df2ba 0%, #11e69e 0%, #11e6c5 100%), linear-gradient(#4887ff, #4887ff)"
          >
            去卡券中心
          </u-button>
        </view> -->
      </view>
    </mescroll-uni>

    <view class="foot-fixed m-t-30 p-30" @click="clickMyCoupon">
      <u-button
        shape="circle"
        :color="variables.bgLinearGradient1"
      >
        我的优惠券
      </u-button>
    </view>
  </view>
</template>

<script>
import { apiGetCouponManageReceiveList, apiCouponManageReceive } from '@/api/coupon.js'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
import NP from '@/utils/np.js'
export default {
  computed: {},
  mixins: [MescrollMixin],
  data() {
    return {
      imgPath: this.$imgPath,
      canReset: false,
      couponManageReceiveList: [],
      upOption: {
        page: {
          size: 10 // 每页数据的数量,默认10
        },
        empty: {
          use: false
        }
      }
    }
  },
  onLoad() {},
  onShow() {
    this.canReset && this.mescroll.resetUpScroll() // 重置列表数据为第一页
    this.canReset && this.mescroll.scrollTo(0, 0) // 重置列表数据为第一页时,建议把滚动条也重置到顶部,避免无法再次翻页的问题
    this.canReset = false
  },
  methods: {
    // 上拉加载更多
    upCallback(page) {
      this.getApiGetCouponManageReceiveList(page)
    },
    async getApiCouponManageReceive(data) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      const [err, res] = await this.$to(
        apiCouponManageReceive({
          id: data.id
        })
      )
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        uni.$u.toast('领取成功')
        await uni.$u.sleep(500)
        this.upCallback({ num: 1, size: 10 })
      } else {
        uni.$u.toast(res.msg)
      }
    },
    async getApiGetCouponManageReceiveList(page) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      const [err, res] = await this.$to(
        apiGetCouponManageReceiveList({
          page: page.num,
          page_size: page.size
        })
      )
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        const results = res.data.results
        const count = res.data.count
        let curPageLen = res.data.results.length
        // 如果是第一页需手动置空列表
        if (page.num === 1) this.couponManageReceiveList = []
        // 追加新数据
        this.couponManageReceiveList = [...this.couponManageReceiveList, ...results]
        // 方法二(推荐): 后台接口有返回列表的总数据量 count, 判断是否有下一页
        this.mescroll.endBySize(curPageLen, count)
      } else {
        uni.$u.toast(res.msg)
      }
    },
    clickClaimCoupon(item) {
      uni.$u.debounce(this.getApiCouponManageReceive(item), 500)
    },
    couponMoney(item, type) {
      let useCondition = item.use_condition
      let money = 0
      switch (type) {
        case 'currentMoney':
          money = NP.divide(useCondition.reduce, 100)
          break
        case 'fullMoney':
          money = NP.divide(useCondition.full_money, 100)
          break
        case 'exchangeMoney':
          money = NP.divide(useCondition.satisfy_money, 100)
          break
        default:
          break
      }
      return money
    },
    discountNumber(item) {
      let useCondition = item.use_condition
      return useCondition.discount
    },
    discountIsMax(item) {
      let useCondition = item.use_condition
      return useCondition.is_max
    },
    discountMoney(item) {
      let useCondition = item.use_condition
      return NP.divide(useCondition.max_money, 100)
    },
    tipsUseOrg(item) {
      return `每人可领${item.manual_receive_number}张。限使用组织：${item.use_organization_name.join(',')}`
    },
    tipsUseMeal(item) {
      return `限使用餐段：${item.meal_type_list_alias.join(',')}。`
    },
    clickMyCoupon() {
      this.$miRouter.push({
        path: '/pages_bundle/coupon/my_coupon'
        // query: {
        //   type: 'basic_info'
        // }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.coupon-center-wrapp {
  .image-null {
    width: 240rpx;
    height: 176rpx;
    margin: 170rpx auto 0;
  }
  .list-setting-box {
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    margin-bottom: 20rpx;
    border-radius: 20rpx;
    margin-left: 30rpx;
    margin-right: 30rpx;
    .line {
      position: absolute;
      left: 0;
      width: 8rpx;
      height: 100%;
      background-color: #f94141;
    }
    .coupon-status {
      position: absolute;
      right: 0;
      top: 0;
      // width: 90rpx;
      // height: 34rpx;
      color: #fff;
      padding: 8rpx 20rpx;
      font-size: 20rpx;
      background-image: linear-gradient(90deg, #f54133 0%, #fa5a37 0%, #ff723b 0%, #ff3737 100%),
        linear-gradient(#11e69e, #11e69e);
      background-blend-mode: normal, normal;
      border-radius: 0rpx 0rpx 0rpx 20rpx;
    }
    .coupon-center {
      padding: 30rpx 40rpx;
      width: 100%;

      .coupon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .coupon-price {
          text-align: center;
        }
        .coupon-title-wrapp {
          .coupon-title {
            font-weight: bold;
            font-size: 30rpx;
          }
        }
      }
    }
  }
  .foot-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1002;
    padding-bottom: 20rpx;
  }
  ::v-deep .u-collapse {
    .u-cell__body {
      padding: 0;
    }
    .u-cell--clickable {
      background-color: #fff;
    }
    .u-cell__title-text {
      padding-top: 20rpx;
      line-height: normal;
      // margin-bottom: 20rpx;
      color: #999999;
      font-size: 22rpx;
    }
    .u-collapse-item__content__text {
      padding: 15rpx 0rpx 0rpx 0rpx;
      // margin-bottom: 30rpx;
    }
  }
}
</style>
