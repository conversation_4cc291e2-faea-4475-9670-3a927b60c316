<template>
    <view class="choice-address">

        <!-- Section 主体 Start -->
        <view class="section bg-white">

            <view class="m-b-20">
                <input placeholder="请输入地址名称" border="surround" v-model="value" prefixIcon="search"
                    prefixIconStyle="font-size: 22px;color: #909399"></input>
            </view>

            <view class="item bb" hover-class="click" v-for="(item, index) in 4" :key="index" @click="selectCanteen(item)">
                保利世界贸易中心-C座
            </view>

        </view>
        <!-- Section End -->
		
		<view class="footer">
		    <view class="footer--warpper flex flex-center">
		        <u-button text="保存" color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)" @click="onSubmit()"></u-button>
		    </view>
		</view>
    </view>
</template>

<script>
    export default {

        // Data Start
        data() {
            return {
                pay_way: 1
            }
        },
        // Data End

        // Methods Start
        methods: {
            selectCanteen() {
            }
        },
        // Methods End

        // Life Cycle Start
        onLoad() {

        }
        // Life Cycle End
    }
</script>

<style lang="scss" scoped>
    .choice-address {
        .ls-card {
            border-radius: 20rpx;
        }

        .section {
            padding: 40rpx 40rpx 1rpx 40rpx;
            margin-bottom: calc(100rpx + env(safe-area-inset-bottom));
            border-top: $border-base;
            border-radius: 0 0 20rpx 20rpx;
            
            .bb {
                border-bottom: $border-base;
            }
            
            .bb:last-child {
                border-bottom: 0;
            }
            
            .item {
                padding: 20rpx 0;
                padding-left: 60rpx;
                // margin-bottom: 30rpx;
            }
            
            .click {
                background-color: $background-color;
            }
        
        }
		
		.footer {
		    width: 100%;
		    position: fixed;
		    bottom: 0;
		    left: 0;
		    padding: 0 40rpx;
		    padding-bottom: env(safe-area-inset-bottom);
		
		    &--warpper {
		        height: 190rpx;
		    }
		}
    }
</style>
