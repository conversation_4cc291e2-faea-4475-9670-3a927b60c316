<!-- 地址管理 -->
<template>
  <view class="address-manage">
    <view class="" v-for="(item, index) in address" :key="index" @click="changeAddress(index)">
      <view class="address-item flex row-between">
        <view class="flex flex-center">
          <!-- 选择按钮 -->
          <view class="m-r-30 address-btn flex flex-center" :class="index === selectIndex ? 'address-btn-select' : ''">
            <view class="address-btn-inside"></view>
          </view>
          <!-- 中间主要信息 -->
          <view class="address-content">
            <view class="xs f-w-500 m-b-25">
              {{ item.project }}
            </view>
            <view class="lg flex name-phone m-b-16">
              <span class="m-r-28 line-1 name">{{ item.people }}</span>
              <span>{{ item.phone }}</span>
            </view>
            <view class="line-2 xxs f-w-500 muted address-info">
              {{ item.address }}
            </view>
          </view>
        </view>
        <view class="xxs primary flex flex-center address-tips-mr m-l-40" v-if="item.type">
          <view class="address-tips">默认</view>
        </view>
        <!-- 编辑 -->
        <view class="m-l-30 nr primary address-edit flex flex-center" @click="goToPager('/pages_bundle/address/add_address')">
          <view class="">编辑</view>
        </view>
      </view>
    </view>

    <view class="footer">
      <view class="footer--warpper flex flex-center" @click="goToPager('/pages_bundle/address/add_address')">
        <u-button
          text="+新增地址"
          color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)"
        ></u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectIndex: 0, // 被选中的地址

      address: [
        {
          project: '所属项目：朴食科技智慧食堂',
          people: '黄小二',
          phone: '15803245487',
          address: '海珠区新港东路1000号保利世贸C座西塔2204',
          type: true
        },
        {
          project: '所属项目：灰大狼智哈食堂',
          people: '黄小五',
          phone: '13822023487',
          address: '海珠区新港东路1000号保利世贸E座西塔308',
          type: false
        }
      ]
    }
  },

  methods: {
    // 选择地址
    changeAddress(val) {
      this.selectIndex = val
    },

    // 页面跳转
    goToPager(url) {
      this.$miRouter.push(url)
    }
  }
}
</script>

<style lang="scss" scoped>
.address-manage {
  margin: 40rpx;

  .address-item {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx 30rpx 28rpx;
    margin-bottom: 20rpx;

    .address-edit {
      min-width: 60rpx;
    }

    .address-btn {
      min-width: 36rpx;
      height: 36rpx;
      width: 36rpx;
      border-radius: 50%;
      border: 2rpx solid #ededed;
    }

    .address-btn-select {
      background: $color-primary;
      border: 2rpx solid $color-primary;
    }

    .address-btn-inside {
      height: 18rpx;
      width: 18rpx;
      border-radius: 50%;
      background: #ffffff;
      z-index: 2;
    }

    .address-content {
      max-width: 334rpx;
      .address-info {
        line-height: 30rpx;
      }

      .name-phone {
        max-width: 420rpx;

        .name {
          max-width: 200rpx;
        }
      }
    }

    .address-tips-mr {
      min-width: 80rpx;
      .address-tips {
        padding: 5rpx 10rpx;
        background: rgba($color: #12e294, $alpha: 0.15);
        border-radius: 4rpx;
      }
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 10;

    &--warpper {
      height: 190rpx;
    }
  }
}
</style>
