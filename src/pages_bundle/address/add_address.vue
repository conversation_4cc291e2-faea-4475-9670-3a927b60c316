<!-- 增加地址 -->
<template>
  <view class="edit-address">
    <!-- 表单 -->
    <view class="main">
      <view class="form">
        <view class="form-item flex col-center">
          <view class="form-label require">姓名</view>

          <view class="form-input flex-1">
            <u-input v-model="formData.name"></u-input>
          </view>
        </view>
        <view class="form-item flex col-center">
          <view class="form-label require">手机号</view>
          <view class="form-input flex-1">
            <u-input v-model="formData.phone"></u-input>
          </view>
        </view>
        <view class="form-item flex col-center">
          <view class="form-label require">所属项目点</view>
          <view class="form-input flex-1">
            <select-lay v-model="formData.item" :showplaceholder="false" :options="options" />
          </view>
        </view>

        <view class="form-item flex col-center">
          <view class="form-label require">配送区域</view>
          <view class="form-input flex-1" @click="showArea = true">
            <u-input v-model="formData.area" placeholder="请选择"></u-input>
          </view>
        </view>

        <view class="form-item flex col-center">
          <view class="form-label require">具体地址</view>
          <view class="form-input flex-1" @click="choiceAddress">
            <u-input v-model="formData.jtArea" placeholder="去填写">
              <u-icon slot="suffix" color="#8F9295" name="arrow-right" size="20rpx"></u-icon>
            </u-input>
          </view>
        </view>

        <view class="form-item flex col-center">
          <view class="form-label require m-b-20">详细地址</view>
          <view class="form-input flex-1">
            <u-input v-model="formData.xxArea" placeholder="请输入详细地址，如xx栋xx门室"></u-input>
            <view class="mini m-t-12 tips-desc">*填写的地址需在配送范围之内</view>
          </view>
        </view>

        <view class="form-item flex col-center">
          <view class="form-label-mr require">设为默认收货地址</view>
          <view class="form-input flex-1 flex row-right">
            <u-switch v-model="formData.default" activeColor="#12E294" @change="change"></u-switch>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view class="footer--warpper flex flex-center">
        <u-button
          text="保存"
          color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)"
          @click="onSubmit()"
        ></u-button>
      </view>
      <view class="footer--warpper flex flex-center m-t-20">
        <u-button text="删除" type="error" :customStyle="{ border: 'none' }" :plain="true" @click="onDel()"></u-button>
      </view>
    </view>

    <!-- 保存、删除提示框 -->
    <u-modal :show="showInfo" :title="'提示'">
      <!-- Content -->
      <view class="xl text-center m-t-30 m-b-30" v-if="!isDelOrSubmit">您所填写的地址信息超出配送范围 请重新填写</view>
      <view class="xl text-center m-t-30 m-b-30" v-else>确定删除该地址信息吗？</view>

      <!-- Confirm Btn -->
      <view slot="confirmButton" class="flex flex-center row-around">
        <view class="confirm-btn">
          <u-button text="取消" shape="circle" color="#BDBDBD" @click="showInfo = false"></u-button>
        </view>
        <view class="confirm-btn">
          <u-button text="确定" shape="circle" color="#11E69E" @click="showInfo = false"></u-button>
        </view>
      </view>
    </u-modal>

    <!-- 配送区域选择 -->
    <u-popup :show="showArea" :closeable="true" @close="closeShowArea">
      <!-- Content -->
      <view class="show-area">
        <view class="md text-center m-b-50">请选择所在消费点</view>
        <view class="md show-area-content">
          <view class="m-b-34 content-item flex flex-center row-left" v-for="(item, index) in xfdData" :key="index">
            <view class="tips m-r-24" :class="index == 0 || index == 1 ? 'tips-select' : ''"></view>
            <view class="">
              {{ item.name }}
            </view>
          </view>
        </view>

        <view class="show-area-bottom">
          <view class="nr m-b-35">请选择三级组织</view>
          <view class="sm m-b-30" v-for="(item, index) in zzData" :key="index">
            {{ item.name }}
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        name: '',
        phone: '',
        item: '',
        area: '',
        jtArea: '',
        xxArea: '',
        default: true
      },

      options: [
        {
          label: '朴食科技智慧食堂',
          value: 1
        }
      ],

      showInfo: false, // 保存、删除提示框
      isDelOrSubmit: false, // true-保存提示框/false-删除提示框
      showArea: false, // 配送区域选择弹框

      xfdData: [
        {
          name: '朴食科技'
        },
        {
          name: '广州地区'
        },
        {
          name: '请选择'
        }
      ],
      zzData: [
        {
          name: '智慧食堂'
        },
        {
          name: '演示食堂'
        }
      ]
    }
  },
  methods: {
    // 设置为默认地址
    change(event) {
      this.formData.default = event
    },

    // 删除
    onDel() {
      ;(this.isDelOrSubmit = true), (this.showInfo = true)
    },

    // 确定
    onSubmit() {
      ;(this.isDelOrSubmit = false), (this.showInfo = true)
    },

    // 选择地址
    choiceAddress() {
      this.$miRouter.push('/pages_bundle/address/choice_address')
    },

    // 关闭配送区域选择弹框
    closeShowArea() {
      this.showArea = false
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-address {
  padding: 30rpx 40rpx;
  position: relative;

  .tips-desc {
    color: #ff4c4d;
  }

  .main {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx;

    .form {
      .form-item {
        margin-bottom: 40rpx;

        .form-label {
          width: 140rpx;
          text-align: left;
          margin-right: 20rpx;
          font-weight: 500;
        }

        .form-label-mr {
          text-align: left;
          margin-right: 20rpx;
          font-weight: 500;
        }
      }
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx 0;
    padding-bottom: calc(54rpx + env(safe-area-inset-bottom));
  }

  .confirm-btn {
    width: 36%;
  }

  .show-area {
    padding: 80rpx;

    .show-area-content {
      .content-item {
        .tips {
          border-radius: 50%;
          width: 20rpx;
          height: 20rpx;
          border: 2rpx solid #11e69e;
          color: #11e69e;

          &-select {
            color: #333333;
            background: #11e69e linear-gradient(90deg, #a9fed5 0%, #11e69e 0%, #11e69e 0%, #11e6c5 100%, #11e6c5 100%);
          }
        }
      }
    }

    .show-area-bottom {
      padding-top: 50rpx;
      border-top: 1rpx solid #cfcfcf;
    }
  }
}
</style>
