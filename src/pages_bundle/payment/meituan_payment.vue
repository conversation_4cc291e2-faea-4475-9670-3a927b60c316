<template>
  <view :style="theme.style" class="payment">
    <!-- Header 头部 Start -->
    <view class="header bg-primary lg white flex col-center row-center">
      <u-count-down :time="timecount" format="DD:HH:mm:ss" autoStart millisecond @change="onTimeChange">
        <view class="time md">
          <text class="time__item p-r-5">剩余</text>
          <text class="time__item">{{ timeData.minutes }}&nbsp;分</text>
          <text class="time__item">{{ timeData.seconds }}&nbsp;秒</text>
          <text class="time__item">&nbsp;自动关闭</text>
        </view>
      </u-count-down>
    </view>
    <view class="section">
      <pay-way-card
        ref="payWayCard"
        @select="selectPayWay"
        :showPay="showPay"
        @showPayClose="showPayClose"
        :paymentOrderType="paymentOrderType"
        :orderInfo="orderInfo"
      ></pay-way-card>
      <view class="list m-t-20 ls-card bg-white md">
        <view class="item bb flex row-between">
          <view class="md muted">订单金额</view>
          <price-format :price="orderInfo.pay_fee" :size="36"></price-format>
        </view>
        <view class="item bb flex row-between">
          <view class="md muted">优惠金额</view>
          <price-format :price="0" :size="36"></price-format>
        </view>
      </view>
    </view>
    <!-- Section End -->

    <!-- Footer 底部 Start -->
    <view class="footer bg-white">
      <view class="footer--warpper flex row-between">
        <view class="muted flex col-center">
          <text class="m-r-20 md">需支付</text>
          <view class="black"><price-format :weight="500" :price="orderInfo.pay_fee" :size="36"></price-format></view>
        </view>

        <view class="submit-btn white flex flex-center" @click="handlePrepay">立即支付</view>
      </view>
    </view>
    <!-- Footer End -->
    <u-modal :show="showTimeEnd" title="提示" @confirm="timeEndConfirm">
      <view class="" slots="default">支付时间已结束,请重新提交订单</view>
    </u-modal>
    <!-- 警告框-->
    <u-modal
      :show="isShowDiffDialog"
      title="提示"
      content="亲，你手机的当前时间与标准时间存在差异，将会影响你下单支付，请尽快到系统进行设置！"
      :showCancelButton="false"
      @confirm="isShowDiffDialog = false"
    ></u-modal>
  </view>
</template>

<script>
import { getDatetime } from '@/api/app'
import { mapMutations, mapActions, mapGetters } from 'vuex'
import Cache from '@/utils/cache'
import { replaceDate } from '@/utils/util.js'
import NP from '@/utils/np'
import { getApiMeituanOrderInfo, getApiMeituanOrderPay } from '@/api/order.js'
export default {
  // Data Start
  data() {
    return {
      paramsData: {},
      orderInfo: {},
      totalFee: 0,
      payWay: {},
      paymentOrderType: 'meituan',
      isLoading: false,
      showPay: false, //打开支付方式
      timeData: {},
      showTimeEnd: false,
      tradeNo: '',
      timecount: 5 * 60 * 1000,
      isDiffTime: false, // 是否用户时间跟订单时间不一样，相差2分钟以上
      isShowDiffDialog: false // 警告弹窗
    }
  },
  // Data End

  // Methods Start
  methods: {
    async getMeituanOrderInfo() {
      this.$showLoading({
        title: '获取详情中...',
        mask: true
      })
      const [err, res] = await this.$to(
        getApiMeituanOrderInfo({
          trade_no: this.tradeNo
        })
      )
      uni.hideLoading()
      if (err) {
        return
      }
      if (res.code === 0) {
        this.orderInfo = res.data
      } else {
        uni.$u.toast(res.msg)
      }
    },
    onTimeChange(e) {
      this.timeData = e
    },
    timeEndConfirm() {
      this.$miRouter.back()
    },
    handlePrepay() {
      if (this.isLoading) return uni.$u.toast('请不要多次点击！')
      // 显示支付结束时间 没什么用 据说来骗骗产品和用户
      if (!this.isDiffTime && !this.timeData.minutes && !this.timeData.seconds) return (this.showTimeEnd = true)
      // 如果只有一条数据 默认打开弹框 产品要求
      if (!this.payWay.payinfo_id || (!this.payWay.wallet_id && this.payWay.payway == 'PushiPay')) return (this.showPay = true)
      if (!this.payWay.payinfo_id) return uni.$u.toast('请选择支付方式')
      if (!this.payWay.wallet_id && this.payWay.payway == 'PushiPay') return uni.$u.toast('请选择钱包')
      this.isLoading = true
      let params = {
        balance_type: this.payWay.balance_type,
        payinfo_id: this.payWay.payinfo_id,
        payment_order_type: 'mei_tuan',
        company_id: Cache.get('userInfo').company_id,
        user_id: Cache.get('userInfo').user_id,
        person_no: Cache.get('userInfo').person_no,
        trade_no: this.tradeNo
      }
      if (this.payWay.payway === 'PushiPay') {
        params.wallet_id = this.payWay.wallet_id
      }
      this.getMeituanOrderPay(params)
    },
    async selectPayWay(value) {
      console.log('selectPayWay', value)
      this.payWay = value
      // 只有预约订单需要
      if (this.payWay.payway === 'PushiPay' && !this.payWay.wallet_id) {
        // 如果没有选钱包的时候别调用
        return
      }
    },
    showPayClose(value) {
      this.showPay = value
    },
    // 格式化文字 超过多少显示...
    nameFormat(name, number) {
      if (!name) return
      let subStr = name.slice(0, number)
      subStr = subStr + (name.length > number ? '...' : '')
      return subStr
    },
    // 对比时间
    checkDate(time) {
      let nowTime = new Date().getTime()
      let payTime = new Date(time).getTime()
      if (Math.abs(payTime - nowTime) > 5 * 60 * 1000) {
        this.isShowDiffDialog = true
        this.isDiffTime = true
      }
    },
    // 计算时间
    computedTime() {
      uni.showLoading()
      getDatetime()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (this.orderInfo?.pay_time) {
              let paytime = replaceDate(this.orderInfo.pay_time)
              let nowtime = replaceDate(res.data.now)
              this.timecount = new Date(paytime).getTime() + 5 * 60 * 1000 - new Date(nowtime).getTime()
              this.checkDate(paytime)
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          // console.log('获取付款码err', err)
          uni.$u.toast(err.data.msg)
        })
    },

    async getMeituanOrderPay(params) {
      this.$showLoading({
        title: '支付中...',
        mask: true
      })
      const [err, res] = await this.$to(getApiMeituanOrderPay(params))
      uni.hideLoading()
      if (err) {
        return
      }
      if (res.code === 0) {
        this.$miRouter.push({
          path: '/pages_bundle/payment/meituan_payment_result',
          query: {
            tradeNo: this.tradeNo
          }
        })
      } else {
        uni.$u.toast(res.msg)
      }
    }
  },
  // Methods End

  // Life Cycle Start
  computed: {},
  onShow() {},
  onLoad() {
    // 计算时间
    this.computedTime()
    if (this.$Route.query.tradeNo) {
      this.tradeNo = this.$Route.query.tradeNo
      this.getMeituanOrderInfo()
    }
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.payment {
  .ls-card {
    border-radius: 20rpx;
  }

  .header {
    height: 90rpx;
    padding: 0 40rpx;
  }

  .section {
    padding: 40rpx;
    padding-bottom: calc(100rpx + env(safe-area-inset-bottom));

    .bb {
      border-bottom: 1px solid $border-color-base;
    }

    .list {
      padding: 0 30rpx;

      .item {
        padding: 32rpx 0;
        box-sizing: border-box;
      }

      .offline {
        padding: 20rpx 0;

        .tips {
          color: #f8a63c;
        }
        .coupon-number {
          // width: 110rpx;
          // height: 44rpx;
          padding: 8rpx 15rpx;
          color: #fff;
          font-size: 24rpx;
          background-color: #fe5858;
          border-radius: 8rpx;
        }
      }
    }
  }
  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding-left: 38rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 100rpx;
    }

    .submit-btn {
      width: 200rpx;
      height: 100%;
      background-color: $color-primary;
    }
  }
  .table-box {
    max-height: 600rpx;
    overflow: auto;
  }
}
</style>
