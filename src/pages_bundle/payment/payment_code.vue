<template>
  <view :style="theme.style" class="payment_code">
    <view class="container ls-card">
      <!-- Header 头部 Start -->
      <!-- 20250321  应农行纪检要求隐藏朴食相关字眼-->
      <!-- <view class="header xxl f-w-500 white flex flex-center">付款码</view> -->
      <view class="header xxl f-w-500 white flex flex-center">向商家展示付款码进行支付</view>
      <!-- Header End -->

      <!-- Section 主体 Start -->
      <view class="section bg-white">
        <!-- <view class="md normal m-b-15">向商家展示付款码进行付款</view> -->
        <view class="flex flex-center flex-col">
          <paymentCodeCountdown :timeupSecond="timeupSecond" @finish="finishTimeup"></paymentCodeCountdown>
          <text class="downTime-tips">倒计时结束自动刷新</text>
        </view>
        <view class="code">
          <!--   <view class="invalid xxl"  v-if="!isInvalid">
                        <view>付款码已失效</view>
                        <view class="m-t-20">请刷新付款码</view>
                    </view> -->
          <uqrcode v-if="qrcode" ref="uQRCode" :text="qrcode" :size="185" />
          <!-- <u-image width="394rpx" height="394rpx"></u-image> -->
        </view>

        <view class="refresh nr p-t-20 flex flex-center">
          <u-button @click="finishTimeup()" type="primary" text="刷新付款码" size="small" style="width: 300rpx;"></u-button>
        </view>
      </view>
      <!-- Section End -->
    </view>
    <view class="tips">
      温馨提示：付款码用于支付,请保护好您的付款码。切勿随意向他人展示或截图分享付款码,以防信息泄露和造成资金损失。
    </view>
  </view>
</template>

<script>
import { apiGenerateEaccountCode, apiGetPaymentQrcodeRefresh } from '@/api/user.js'
import paymentCodeCountdown from '@/pages_bundle/components/payment-code-countdown/payment-code-countdown.vue'
import Cache from '@/utils/cache'
export default {
  components: {
    paymentCodeCountdown
  },
  // Data Start
  data() {
    return {
      imgPath: this.$imgPath,
      // 是否已经失效
      isInvalid: false,
      screenBrightness: '',
      userinfo: {},
      qrcode: '',
      timeupSecond: 0,
      paymentQrcodeRefresh: 4, // 以前默认4分钟有效时长
      timer: null
    }
  },
  // Data End
  // Methods Start
  methods: {
    /**
     * @method 获取付款码有效时长 
     * flag是否手动刷新二维码 
     * oldPaymentQrcodeRefreshInfo 上一次的配置有效时间和用户id
     */
    getPaymentQrcodeRefresh(flag = true, oldPaymentQrcodeRefreshInfo) {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      apiGetPaymentQrcodeRefresh({
        company_id: this.userinfo.company_id,
      })
      .then(res => {
        uni.hideLoading()
        if (res.code == 0) {
          const data = res.data || { payment_qrcode_refresh : 0 }
          this.paymentQrcodeRefresh = data.payment_qrcode_refresh
          const code = uni.getStorageSync('qcode') || ''
          uni.setStorageSync('oldPaymentQrcodeRefreshInfo', {
            user_id: this.userinfo.user_id,
            payment_qrcode_refresh: data.payment_qrcode_refresh,
          })
          const isTheSame = 
            (oldPaymentQrcodeRefreshInfo?.user_id === this.userinfo.user_id) && 
            (oldPaymentQrcodeRefreshInfo?.payment_qrcode_refresh === data.payment_qrcode_refresh)
          // 如果后台更新配置时间与上一次不一样(同个用户的情况下)，则刷新二维码
          if (isTheSame) {
            flag = false
          } else {
            flag = true
          }
          
          this.generateEaccountCode(code, data.payment_qrcode_refresh, flag)
        } else {
          uni.$u.toast(res.msg)
        }
      })
      .catch(err => {
        uni.hideLoading()
        uni.$u.toast(err.message)
      })
    },
    // 获取付款码
    generateEaccountCode(qcode, ts, op_update) {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      apiGenerateEaccountCode({
        company_id: this.userinfo.company_id,
        person_no: this.userinfo.person_no,
        qcode,
        ts,
        op_update
      })
        .then(res => {
          uni.hideLoading()
          if (res.code !== 0) {
            uni.$u.toast(res.msg)
            return
          }
          const data = res.data || {}
          this.qrcode = data?.card_user_account_code
          if( data?.ttl ){
            this.timeupSecond = data?.ttl
          }else {
            this.timeupSecond = this.paymentQrcodeRefresh * 60
          }
          uni.setStorageSync('qcode', this.qrcode)
          // 开始倒计时
          this.startCountdown()
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 倒计时完成重新刷新
    finishTimeup() {
      this.getPaymentQrcodeRefresh(true)
    },
    startCountdown() {
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.timeupSecond > 0) {
          this.timeupSecond--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    }
  },
  // Methods End
  // Life Cycle Start
  onLoad() {
    this.userinfo = Cache.get('userInfo')
    const oldPaymentQrcodeRefreshInfo = uni.getStorageSync('oldPaymentQrcodeRefreshInfo')
    this.getPaymentQrcodeRefresh(false, oldPaymentQrcodeRefreshInfo)
    // uni.getScreenBrightness({
    //     success: (res) => {
    //        this.screenBrightness = res.value
    //     }
    // });
    // // 初始化时设置手机屏幕亮度至最高
    // uni.setScreenBrightness({
    //     value: 1,
    // });
  },
  onUnload() {
    clearInterval(this.timer)
    // uni.setScreenBrightness({
    // 	value: 0,
    // });
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.payment_code {
  padding: 40rpx;

  .ls-card {
    border-radius: 20rpx;
  }

  .container {
    width: 100%;
    height: 760rpx;
    overflow: hidden;

    .header {
      height: 108rpx;
      letter-spacing: 4rpx;
      background: #38ccc6 $bg-linear-gradient-1;
    }

    .section {
      height: 652rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-evenly;

      .code {
        width: 185px;
        height: 185px;
        position: relative;

        // 已失效的样式
        .invalid {
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          z-index: 100;
          padding: 138rpx 86rpx;
          font-weight: bold;
          color: #57595c;
          position: absolute;
          opacity: 0.8;
          background-color: #ffffff;
        }
      }

      .refresh {
        image {
          width: 24rpx;
          height: 24rpx;
        }
      }
    }
  }

  .tips{
    // letter-spacing: 1rpx;
    width: 97%;
    margin: 0 auto;
    margin-top: 40rpx;
  }
  .downTime-tips{
    margin-top: 10rpx;
    color: $color-info;
  }
}
</style>
