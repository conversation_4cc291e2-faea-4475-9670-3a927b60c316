<template>
  <view :style="theme.style" class="meituan_payment payment-result" v-if="Object.keys(orderInfo).length">
    <view class="flex flex-col flex-center">
      <u-icon color="#11E69E" name="checkmark-circle-fill" :size="200" v-if="orderInfo.order_status === 'ORDER_SUCCESS'"></u-icon>
      <u-icon color="red" name="close-circle" :size="150" v-else-if="orderInfo.order_status === 'ORDER_FAILED'"></u-icon>
      <view class="font-size-42 m-b-20 f-w-500 primary" v-if="orderInfo.order_status === 'ORDER_SUCCESS'">支付成功</view>
      <view class="font-size-42 m-b-20 f-w-500 red" v-else-if="orderInfo.order_status === 'ORDER_FAILED'">支付失败</view>
      <price-format size="66" weight="600" :price="orderInfo.pay_fee"></price-format>
    </view>
    <view class="m-t-40 flex flex-center xxl">
      <view class="">总订单号：</view>
      <view>{{ orderInfo.trade_no ? orderInfo.trade_no : '' }}</view>
    </view>
    <view class="flex flex-center m-t-50">
      <view class="m-t-50" style="width: 50%">
        <u-button
          :color="variables.bgLinearGradient1"
          text="返回"
          @click="clickUrl"
        ></u-button>
      </view>
    </view>
  </view>
</template>

<script>
import { getApiMeituanOrderInfo } from '@/api/order.js'

export default {
  data() {
    return {
      tradeNo: '',
      orderInfo: {}
    }
  },
  onLoad(option) {
    if (this.$Route.query.tradeNo) {
      this.tradeNo = this.$Route.query.tradeNo
      this.getMeituanOrderInfo()
    }
  },
  mounted() {},
  methods: {
    async getMeituanOrderInfo() {
      this.$showLoading({
        title: '获取详情中...',
        mask: true
      })
      const [err, res] = await this.$to(
        getApiMeituanOrderInfo({
          trade_no: this.tradeNo
        })
      )
      uni.hideLoading()
      if (err) {
        return
      }
      if (res.code === 0) {
        this.orderInfo = res.data
      } else {
        uni.$u.toast(res.msg)
      }
    },
    clickUrl() {
      if (this.orderInfo.return_url) {
        // #ifdef H5
        window.location.href = this.orderInfo.return_url
        // #endif
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.payment-result {
  padding: 20rpx 70rpx;
  .payment-info {
    margin-bottom: 30rpx;
    .info-item {
      display: flex;
      justify-content: space-between;
      font-size: $font-size-md;
      padding: 30rpx 0;
      &:not(:last-of-type) {
        border-bottom: $border-base;
      }
    }
  }
}
</style>
