<template>
  <view :style="theme.style" class="select-coupon-wrapp">
    <u-radio-group v-model="selectCouponId" @change="changeRadio" placement="row" v-if="orderCouponList.length">
      <view class="list-setting-box" v-for="(item, index) in orderCouponList" :key="item.id">
        <view class="line" :style="{ backgroundColor: item.coupon_type === 'EXCHANGE' ? '#fec055' : '#f94141' }"></view>
        <view class="coupon-center">
          <view class="coupon">
            <!-- 满减 -->
            <view class="coupon-price" v-if="item.coupon_type === 'FULL_DISCOUNT'">
              <view class="red">
                <text style="font-size: 36rpx">¥</text>
                <text style="font-size: 60rpx">{{ couponMoney(item, 'currentMoney') }}</text>
              </view>
              <view class="muted xxs">满{{ couponMoney(item, 'fullMoney') }}可用</view>
            </view>
            <!-- 立减 -->
            <view class="coupon-price" v-if="item.coupon_type === 'INSTANT_DISCOUNT'">
              <view class="red">
                <text style="font-size: 36rpx">¥</text>
                <text style="font-size: 60rpx">{{ couponMoney(item, 'currentMoney') }}</text>
              </view>
              <view class="muted xxs">无限制金额</view>
            </view>
            <!-- 折扣 -->
            <view class="coupon-price" v-if="item.coupon_type === 'DISCOUNT'">
              <view class="red">
                <text style="font-size: 60rpx">{{ discountNumber(item) }}</text>
                <text style="font-size: 36rpx">折</text>
              </view>
              <view class="muted xxs" v-if="discountIsMax(item)">最多优惠{{ discountMoney(item) }}</view>
            </view>
            <!-- 兑换券 -->
            <view class="coupon-price" v-if="item.coupon_type === 'EXCHANGE'">
              <view class="m-b-10">
                <u-image width="68rpx" height="54rpx" :src="themeImgPath.img_bundle_exchange_coupon"></u-image>
              </view>
              <view class="muted xxs" v-if="item.use_condition.is_satisfy">满{{ couponMoney(item, 'exchangeMoney') }}可用</view>
            </view>
            <u-line color="#ccc" dashed direction="col" length="85rpx"></u-line>
            <view class="coupon-title-wrapp flex-1 p-l-20 p-r-20">
              <view class="coupon-title p-b-10">【{{ item.coupon_type_alias }}】{{ item.name }}</view>
              <view class="muted xxs p-l-10" v-if="item.is_use_time">{{ item.use_start_time }} - {{ item.use_end_time }}</view>
              <view class="muted xxs p-l-20" v-else>无限制使用时间</view>
            </view>
            <view>
              <u-radio shape="circle" :name="item.id" :disabled="!item.is_use" activeColor="#4aeaa8"></u-radio>
            </view>
          </view>
          <view>
            <u-collapse accordion :border="false">
              <u-collapse-item :clickable="false" :border="false" :title="tipsUseOrg(item)">
                <text class="xxs muted">{{ tipsUseMeal(item) }}</text>
                 <!-- <view class="xxs muted">备注：慢慢慢慢</view> -->
              </u-collapse-item>
            </u-collapse>
          </view>
        </view>
        <view class="coupon-status" v-if="item.is_use">待使用</view>
      </view>
    </u-radio-group>
    <view v-else class="flex flex-col col-center">
      <image class="image-null" :src="themeImgPath.img_bundle_coupon_not"></image>
      <text class="xxl m-t-40">暂无优惠券</text>
      <text class="nr muted m-t-20">当前暂无优惠券，可以去卡券中心看看</text>
      <view class="m-t-30 p-30">
        <u-button
          @click="clickCouponIndex"
          :custom-style="{ width: '260rpx' }"
          shape="circle"
          :color="variables.bgLinearGradient1"
        >
          去卡券中心
        </u-button>
      </view>
    </view>
    <!-- </mescroll-uni> -->

    <view class="foot-fixed m-t-30 p-30" v-if="orderCouponList.length" @click="clickMyCoupon">
      <u-button
        shape="circle"
        :color="variables.bgLinearGradient1"
      >
        确定
      </u-button>
    </view>
  </view>
</template>

<script>
import { apiOrderCouponList } from '@/api/coupon.js'
import NP from '@/utils/np.js'
import Cache from '@/utils/cache'
import { mapActions,mapGetters } from 'vuex'
export default {
  computed: {},
  data() {
    return {
      imgPath: this.$imgPath,
      canReset: false,
      orderCouponList: [],
      selectCouponId: '',
      prevSelectCouponId: '',
      userInfo: Cache.get('userInfo'),
      unifiedTradeNo: ''
    }
  },
	computed: {
		...mapGetters(['selectCouponItem']),
  },
  onLoad(option) {},
  onShow() {
    if (this.$Route.query.unifiedTradeNo) {
      this.unifiedTradeNo = this.$Route.query.unifiedTradeNo
      this.getApiOrderCouponList()
    }
  },
  methods: {
    ...mapActions({
      setSelectCouponItem: 'setSelectCouponItem',
      setRemoveCouponItem: 'setRemoveCouponItem'
    }),
    async getApiOrderCouponList() {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      const [err, res] = await this.$to(
        apiOrderCouponList({
          company_id: this.userInfo.company_id,
          user_id: this.userInfo.user_id,
          unified_trade_no: this.unifiedTradeNo
        })
      )
      uni.hideLoading()
      if (err) {
        // 请求失败,隐藏加载状态
        uni.$u.toast(err.message)
        return
      }
      if (res.code === 0) {
        this.orderCouponList = res.data.results
        console.log(this.selectCouponItem,8888)
        this.selectCouponId = this.selectCouponItem.id

      } else {
        uni.$u.toast(res.msg)
      }
    },
    couponMoney(item, type) {
      let useCondition = item.use_condition
      let money = 0
      switch (type) {
        case 'currentMoney':
          money = NP.divide(useCondition.reduce, 100)
          break
        case 'fullMoney':
          money = NP.divide(useCondition.full_money, 100)
          break
          case 'exchangeMoney':
          money = NP.divide(useCondition.satisfy_money, 100)
          break
        default:
          break
      }
      return money
    },
    discountNumber(item) {
      let useCondition = item.use_condition
      return useCondition.discount
    },
    discountIsMax(item) {
      let useCondition = item.use_condition
      return useCondition.is_max
    },
    discountMoney(item) {
      let useCondition = item.use_condition
      return  NP.divide(useCondition.max_money, 100)
    },
    tipsUseOrg(item) {
      return `限使用组织：${item.use_organization_name.join(',')}`
    },
    tipsUseMeal(item) {
      return `限使用餐段：${item.meal_type_list_alias.join(',')}。`
    },
    clickCouponIndex() {
      this.$miRouter.push({
        path: '/pages_bundle/coupon/index'
      })
    },
    async changeRadio(item) {
      // 单选取消 加个延时 --mtj
      await this.$sleep(50)
      if (item === this.prevSelectCouponId && this.prevSelectCouponId) {
        this.selectCouponId = ''
        this.prevSelectCouponId = ''
      } else {
        this.prevSelectCouponId = item
      }
    },
    clickMyCoupon() {
      if (this.selectCouponId) {
        let found = this.orderCouponList.find(obj => obj.id === Number(this.selectCouponId))
        this.setSelectCouponItem(found)
      } else {
        // 删除vuex 的优惠券数据
        this.setRemoveCouponItem()
      }
      this.$miRouter.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.select-coupon-wrapp {
  .image-null {
    width: 240rpx;
    height: 176rpx;
    margin: 170rpx auto 0;
  }
  .list-setting-box:last-child {
    margin-bottom: 130rpx;
  }
  .list-setting-box {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    margin-top: 20rpx;
    border-radius: 20rpx;
    margin-left: 30rpx;
    margin-right: 30rpx;
    .line {
      position: absolute;
      left: 0;
      width: 8rpx;
      height: 100%;
      background-color: #f94141;
    }
    .coupon-status {
      position: absolute;
      right: 0;
      top: 0;
      color: #f94141;
      padding: 8rpx 20rpx;
      font-size: 20rpx;
      background-color: #fff0f0;
      border-radius: 0rpx 0rpx 0rpx 20rpx;
    }
    .coupon-center {
      padding: 30rpx 40rpx;
      width: 100%;

      .coupon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .coupon-price {
          width: 140rpx;
          display: flex;
          flex-direction: column;
          // justify-content: center;
          align-items: center;
          // padding-right: 30rpx
        }
        .coupon-title-wrapp {
          .coupon-title {
            font-weight: bold;
            font-size: 30rpx;
          }
        }
      }
    }
    .u-radio {
      margin-right: 0;
    }
  }
  .foot-fixed {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1002;
    padding-bottom: 20rpx;
  }
  ::v-deep .u-collapse {
    .u-cell__body {
      padding: 0;
    }
    .u-cell--clickable {
      background-color: #fff;
    }
    .u-cell__title-text {
      padding-top: 20rpx;
      line-height: normal;
      // margin-bottom: 20rpx;
      color: #999999;
      font-size: 22rpx;
    }
    .u-collapse-item__content__text {
      padding: 15rpx 0rpx 0rpx 0rpx;
      // margin-bottom: 30rpx;
    }
  }
}
</style>
