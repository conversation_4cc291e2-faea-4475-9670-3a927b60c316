<template>
  <view :style="theme.style" class="user-coupon">
    <!-- Header 头部 Start -->
    <view class="header">
      <u-sticky bgColor="#FFF">
        <u-tabs
          :list="tabs"
          :current="current"
          line-color="#12E294"
          :active-style="{ fontWeight: 500, color: '#101010' }"
          :scrollable="false"
          @change="tabsChange"
        ></u-tabs>
      </u-sticky>
      <view class="flex p-t-30 p-l-60 p-b-20">
        <view class="m-r-20">
          <u-button
            type="primary"
            size="small"
            shape="circle"
            @click="current2 = 0"
            text="满减券"
            :color="current2 == 0 ? '#FF9C4B' : '#C2C2C2'"
          ></u-button>
        </view>
        <view class="m-r-20">
          <u-button
            type="primary"
            size="small"
            shape="circle"
            @click="current2 = 1"
            text="折扣"
            :color="current2 == 1 ? '#FF9C4B' : '#C2C2C2'"
          ></u-button>
        </view>
      </view>
    </view>
    <!-- Header 头部 End -->

    <!-- Section 主体 Start -->
    <view class="section" id="section">
      <block v-for="(item, index) in tabs" :key="index">
        <template v-if="index === current">
          <mescroll-uni
            ref="mescrollRef"
            :fixed="false"
            :height="height"
            @init="mescrollInit"
            @down="downCallback"
            @up="upCallback"
            :down="downOption"
            :up="upOption"
          >
            <view class="p-30">
              <block v-for="(item2, index2) in 5" :key="index2">
                <coupon-card
                  :button="{
                    show: current === 0,
                    name: '立即领取',
                    theme: 'primary'
                  }"
                  :type="current"
                ></coupon-card>
              </block>
            </view>
          </mescroll-uni>
        </template>
      </block>
    </view>
    <!-- Section 主体 End -->

    <!-- Footer 底部 Start -->
    <view class="footer bg-white">
      <view class="footer--warpper flex flex-center">
        <u-button text="使用券码领券" :color="variables.colorPrimary" @click="$miRouter.push('/pages_bundle/discount_center/exchange')"></u-button>
      </view>
    </view>
    <!-- Footer End -->
  </view>
</template>

<script>
import { getRect } from '@/utils/util.js'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
export default {
  mixins: [MescrollMixin],
  data() {
    return {
      height: '100%', //优惠券容器盒子总高度

      current: 0,
      tabs: [
        {
          name: '我的优惠券'
        },
        {
          name: '领券中心'
        },
        {
          name: '历史记录'
        }
      ],

      current2: 0
    }
  },
  methods: {
    tabsChange(event) {
      this.current = event.index
    }
  },
  created() {
    this.$getUserInfo()
  },

  async onLoad() {
    const rect = await getRect('#section', true, false)
    this.height = rect[0].height + 'px'
  }
}
</script>

<style lang="scss">
.section {
  height: calc(100vh - (390rpx + env(safe-area-inset-bottom)));
}

.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  padding: 0 40rpx;
  border-top: 1px solid #eaecee;
  padding-bottom: env(safe-area-inset-bottom);

  &--warpper {
    height: 190rpx;
  }
}
</style>
