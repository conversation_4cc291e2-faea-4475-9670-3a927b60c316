<template>
  <view :style="theme.style" class="exchange">
    <view class="bg-white p-t-20 p-b-20 p-l-20">
      <u-input placeholder="请输入内容" border="none" v-model="value" @change="change"></u-input>
    </view>

    <view class="m-t-60">
      <u-button
        text="立即领取"
        shape="circle"
        :color="variables.colorPrimary"
        @click="$toast({ title: '领取成功' })"
        :disabled="isDisabled"
      ></u-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
  created() {
    this.$getUserInfo()
  }
}
</script>

<style lang="scss">
.exchange {
  padding: 40rpx;

  > view:first-child {
    border-radius: 8rpx;
  }
}
</style>
