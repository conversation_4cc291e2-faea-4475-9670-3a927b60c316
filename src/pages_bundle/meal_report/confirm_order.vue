<template>
  <view :style="theme.style" class="confirm_order">
    <!-- Section 头部 Start -->
    <view class="section">
      <view v-for="(item, index) in shopCardList" :key="index" class="m-b-30">
        <view class="title lg f-w-500 black">
          {{ item.date }}
        </view>

        <view v-for="(stallItem, stallIndex) in item.stall" :key="stallIndex" class="order-list ls-card bg-white m-b-15">
          <!-- 标题 -->
          <view class="bb flex row-between f-w-500">
            <text class="black">{{ stallItem.meal_type_alias }}</text>
            <text class="black">{{ stallItem.stall_name }}</text>
          </view>

          <!-- 费用 Cost -->
          <view class="bb">
            <view class="flex row-between">
              <text class="muted">{{ stallItem.meal_type_alias }}价格：</text>
              <price-format :price="stallItem.total_report_fee" :size="30"></price-format>
            </view>
            <view class="m-t-24 flex row-between">
              <text class="muted">服务费：</text>
              <price-format :price="stallItem.fuwu_fee" :size="30"></price-format>
            </view>
          </view>

          <!-- 合计 -->
          <view class="bb flex row-between">
     <!--       <view class="flex flex-center">
              <text class="muted m-r-10">份数：</text>
              <number-box
                :value="stallItem.count"
                :key="numberKey"
                @plus="handlePlus(stallItem, item.date)"
                @minus="handleMinus(stallItem, item.date)"
              ></number-box>
            </view> -->

            <view>
              <text class="muted m-r-20">合计</text>
              <price-format :price="stallItem.one_stall_fee" :size="30"></price-format>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- Section End -->

    <!-- Footer 底部 Start -->
    <view class="footer bg-white">
      <view class="footer--warpper flex row-between">
        <view class="muted flex col-center">
          <text class="m-r-20 md">合计</text>
          <price-format :price="allFee" :size="40" color="#000000"></price-format>
        </view>

        <view class="submit-btn white flex flex-center" @click="onSubmitOrder">提交订单</view>
      </view>
    </view>
    <!-- Footer End -->
  </view>
</template>

<script>
import { reportShopcardList, reportShopcardAdd, reportShopcardDelete, reportMealOrderCreate } from '@/api/report_meal.js'
import Cache from '@/utils/cache'
export default {
  // Data Start
  data() {
    return {
      value: 1,
      paramsData: '',
      shopCardList: [],
      allFee: '',
      userId: '',
      companyId: '',
      mealTypeList: ['breakfast', 'lunch', 'afternoon', 'dinner', 'supper', 'morning'], //餐段列表
      limitCount: 0,
      numberKey: 0
    }
  },
  // Data End

  // Methods Start
  methods: {
    getShopcardList() {
      reportShopcardList({
        payment_order_type: this.paramsData.payment_order_type,
        take_meal_type: this.paramsData.take_meal_type
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
						// 没有数据跳回报餐页面
						if(!res.data.shopcard.length) return	this.$miRouter.back()
            this.shopCardList = []
            // 餐段排序
            res.data.shopcard.map(date => {
              let stallList = []
              this.mealTypeList.map(meal => {
                date.stall.map(stall => {
                  if (stall.meal_type === meal) {
                    stallList.push(stall)
                  }
                })
              })
              this.shopCardList.push({
                date: date.date,
                stall: stallList
              })
            })
            this.numberKey = Math.random()
            this.allFee = res.data.all_date_fee
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    shopcardParams(data, date) {
      let params = {
        payment_order_type: this.paramsData.payment_order_type,
        take_meal_type: this.paramsData.take_meal_type,
				date_list:[date],
        meal_type: data.meal_type,
        organization_id: data.stall_id,
        organization_alias: data.stall_name,
        report_fee: data.report_fee,
        fuwu_fee: data.fuwu_fee,
        consume_type: data.consume_type
      }
      return params
    },
    // 添加购物车
    addShopcard(data, date) {
      this.$showLoading({
        mask: true
      })
      reportShopcardAdd({
        limit_count: Number(this.limitCount),
        ...this.shopcardParams(data, date)
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.getShopcardList()
          } else if (res.code == 202) {
						 uni.$u.toast(res.msg)
            this.getShopcardList()
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    // 删除购物车
    delShopcard(data, date, clean_org_all) {
      this.$showLoading({
        mask: true
      })
      reportShopcardDelete({
        ...this.shopcardParams(data, date),
        clean_org_all
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.getShopcardList()
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          uni.$u.toast(error.message)
        })
    },
    // 加份数
    handlePlus(data, date) {
      this.addShopcard(data, date)
    },
    // 减份数
    handleMinus(data, date) {
      this.delShopcard(data, date, false)
    },
    onSubmitOrder() {
      if (this.loading) {
        return
      }
      this.$showLoading({
				title: '提交中...',
				mask: true
			})
      this.loading = true
      reportMealOrderCreate({
        person_no: this.paramsData.person.person_no,
        take_meal_type: this.paramsData.take_meal_type,
        payment_order_type: this.paramsData.payment_order_type,
        org_id: this.paramsData.org.org_id,
        company_id: this.companyId,
        user_id: this.userId
      })
        .then(res => {
          if (res.code == 0) {
            this.$miRouter.replace({
              path: '/pages_bundle/payment/payment',
              query: {
                // #ifdef MP-ALIPAY
                data: this.$encodeQuery(res.data),
                // #endif
                // #ifndef MP-ALIPAY
                data: res.data,
                // #endif
              }
            })
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.data.msg)
        })
        .finally(res => {
          this.loading = false
          uni.hideLoading()
        })
    }
  },
  // Methods End

  // Life Cycle Start
  onLoad() {
    this.paramsData = this.$store.state.appoint.select
    const userInfo = Cache.get('userInfo')
    this.companyId = userInfo.company_id
    this.userId = userInfo.user_id
    this.getShopcardList()
    this.limitCount = this.$Route.query.limit_count
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.confirm_order {
  padding: 40rpx;

  .title {
    position: relative;
    padding-left: 24rpx;
    padding-bottom: 20rpx;
  }

  .title::before {
    content: '';
    width: 6rpx;
    height: 30rpx;
    position: absolute;
    left: 0;
    top: 10%;
    background-color: $color-primary;
  }

  .ls-card {
    border-radius: 20rpx;
  }

  .section {
    padding-bottom: calc(100rpx + env(safe-area-inset-bottom));

    .order-list {
      padding: 0 36rpx;

      .bb {
        border-bottom: 1px solid $border-color-base;
      }

      > view {
        padding: 30rpx 0;
      }

      .food {
        > view:first-child {
          margin-top: 0;
        }

        .food-name {
          width: 260rpx;
        }
      }

      .minus,
      .plus {
        color: $color-primary;
        width: 32rpx;
        height: 32rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid $color-primary;
        border-radius: 4rpx;
      }

      .plus {
        background-color: $color-primary;
        color: #fff;
        border-color: $color-primary;
      }

      .input {
        width: 50rpx;
      }
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding-left: 38rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 100rpx;
    }

    .submit-btn {
      width: 200rpx;
      height: 100%;
      background-color: $color-primary;
    }
  }
}
</style>
