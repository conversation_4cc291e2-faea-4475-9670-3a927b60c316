<template>
	<view :style="theme.style" class="meal-report">
		<view class="header">
			<!-- #ifndef MP-ALIPAY -->
			<u-navbar bg-color="transparent" left-icon-color="#fff" auto-back>
				<view class="white lg f-w-500" slot="center">我的报餐</view>
			</u-navbar>
			<!-- #endif -->
			<view class="canteen flex col-center row-between white">
				<router-link to="/pages_bundle/select/select_diner?type=report_meal">
					<view class="flex col-center xxl">
						<u-icon name="map" color="#fff" size="38rpx"></u-icon>
						<text class="bold line-2">{{ org.org_name }}</text>
						<u-icon name="arrow-right" color="#fff" size="28rpx"></u-icon>
					</view>
				</router-link>
			</view>
		</view>
		<view class="bg-white meal-cantent-box">
      <view class="tabs">
				<view class="meal-type-tabs">
					<u-tabs
						:list="mealList"
						:current="mealIndex"
						keyName="meal_type_alias"
						:line-color="tabActiveColor"
						:active-style="{ fontWeight: 500, color: '#101010' }"
						@change="mealTypeChange"
					></u-tabs>
					<view @click="selectAllMeal" :class="['meal-select-all', isSelectAllMeal ? 'meal-select-all-active' : '']">全选</view>
      	</view>
			</view>

			<!-- 日历 -->
			<view class="calendar">
				<mr-calendar
					:select-date-list="selectDateList"
					:allow-date-list="allowDateList"
					:dot-lists="dotAlreadyList"
					@select="calendarSelect"
					@selectAllChange="selectAllMonth"
					ref="mrCalendar"
				></mr-calendar>
			</view>
			<!-- 左右联动 -->
			<view class="order-warapp flex">
				<!-- 左边 -->
				<scroll-view scroll-y="true" class="left-stall-box " :style="{ height: allShopCardList.length ? '1040rpx' : '1140rpx' }">
					<view
						class="p-20 flex row-center order-warapp-item"
						:class="{ stallActive: currentTab == stallIndex }"
						v-for="(stallItem, stallIndex) in stallTabs"
						:key="stallIndex"
						@click="stallTabsChange(stallItem, stallIndex)"
					>
						<text class="line-2">{{ stallItem.org_name }}</text>
					</view>
				</scroll-view>

				<!-- 右边 -->
				<scroll-view scroll-y="true" class="right-stall-box" :style="{ height: allShopCardList.length ? '1040rpx' : '845rpx' }">
					<!-- 全选餐段的显示跟单餐段不一样，分开两种写法，全选餐段要显示购物车[在某消费点]所有的数据 -->
					<view v-if="isSelectAllMeal">
						<view
							class="p-l-20 p-r-20  p-t-40"
							v-for="(dateItem, dateIndex) in selectAllMealCardList"
							:key="dateIndex"
						>
							<view class="lg">{{ dateItem.month }}月{{ dateItem.day }}日</view>
							<view
								class="flex row-between col-center p-t-30"
								v-for="(stallMealItem, stallMealIndex) in dateItem.currentStallList"
								:key="stallMealIndex">
								<view class="">{{stallMealItem.meal_type_alias}}</view>
								<number-box
									v-if="stallMealItem.count"
									v-model="stallMealItem.count"
									:async-change="true"
									@change="selectCart($event, stallMealItem)"
									:key="numberKey"
								></number-box>
							</view>
						</view>
					</view>
					<view v-else>
						<view
							class="flex row-between p-l-20 p-r-20  p-t-30"
							v-for="(stallMealItem, stallMealIndex) in stallMealList"
							:key="stallMealIndex"
						>
							<view class="lg">{{ stallMealItem.month }}月{{ stallMealItem.day }}日</view>
							<number-box
								v-if="stallMealItem.count"
								v-model="stallMealItem.count"
								:async-change="true"
								@change="selectCart($event, stallMealItem)"
								:key="numberKey"
							></number-box>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<view class="report-btns" v-if="allShopCardList.length">
			<!--  <view class="flex flex-center m-b-20">
        <text class="primary" @click="showDetails = true">查看报餐详情</text>
      </view> -->
			<view class="flex bottom-btn">
				<view class="flex-1">
					<u-button
						:customStyle="{
							'border-radius': 0
						}"
						type="primary"
						size="large"
						:color="variables.colorPrimaryLight8"
						@click="showDetails = true"
					>
						<text class="primary">查看报餐详情</text>
					</u-button>
				</view>
				<view class="flex-1">
					<view class="flex-1" @click="gotoOrder()">
						<u-button
							:customStyle="{
								'border-radius': 0
							}"
							type="primary"
							size="large"
							text="下一步"
						></u-button>
					</view>
				</view>
			</view>
		</view>
		<!-- 几个清空的提示 -->
		<view class="report-tips">
			<u-modal
				:show="isShowClear"
				width="450rpx"
				:showCancelButton="true"
				@confirm="confirmClear"
				@cancel="cancelClear"
				:async-close="true"
				z-index="99999"
			>
				<view class="slot-content">
					<text class="text-center md">{{ clearText }}</text>
				</view>
			</u-modal>
		</view>

		<!-- 购物车详情 -->
		<view class="report-details">
			<u-popup :show="showDetails" mode="bottom" :round="20" @close="showDetails = false">
				<view class="popup-content">
					<view class="details-header flex row-between">
						<text class="muted">报餐详情</text>
						<view class="muted flex flex-center" @click="openClearModal('all')">
							<u-icon name="trash" color="#999999" size="32"></u-icon>
							清空
						</view>
					</view>
					<scroll-view style="height: 800rpx" :scroll-y="true">
						<view class="details-lists">
							<view v-for="(meal, mealIndex) in detailShopCardList" :key="mealIndex" class="details-item">
								<view class="flex row-between">
									<text class="lg f-w-500">{{ meal.meal_type_alias }}</text>
									<text class="primary" @click="openClearModal('meal', meal.meal_type)">清空当前餐段选择</text>
								</view>
								<view v-for="(stall, stallIndex) in meal.mealAllStallList" :key="stallIndex">
									<view class="details-stall md f-w-500">{{ stall.org_name }}</view>
									<view class="flex row-between m-t-20" v-for="(item, index) in stall.stallAllDateList" :key="index">
										<view class="md">{{ item.date }}</view>
										<number-box
										v-if="item.count"
										v-model="item.count"
										:async-change="true"
										@change="selectCart($event, item)"
										:key="numberKey"
										></number-box>
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</u-popup>
		</view>
	</view>
</template>

<script>
import {
	getReportMealStall,
	reportShopcardAdd,
	reportShopcardDelete,
	reportShopcardList,
	reportMealOrgDelete,
	reportShopcardClean,
	reportMealOrderMonthDetail,
	getApiYearHoliday
} from '@/api/report_meal.js'
import Cache from '@/utils/cache'
import { timeFormat, dateLengthInfo } from '@/utils/date'
import mrCalendar from '@/pages_bundle/components/mr-calendar/mr-calendar'
export default {
	components: {
		mrCalendar
	},
	data() {
		return {
			mealReportStallList: [],
			stallTabs: [],
			currentTab: 0,
			allShopCardList: [], // 购物车所有数据
			detailShopCardList: [], // 查看详情里的购物车数据
			mealIndex: 0,
			// mealList: [
			// 	{
			// 		meal_type_alias: ' '
			// 	}
			// ],
			showDetails: false,
			paramsData: {},
			isShowClear: false, // 清空的提示
			clearType: '', // 清空的类型
			clearText: '', // 清空的类型
			clearMealType: '', // 清空的餐段
			mealTypeList: [
				//餐段列表
				{ label: '早餐', value: 'breakfast' },
				{ label: '午餐', value: 'lunch' },
				{ label: '下午茶', value: 'afternoon' },
				{ label: '晚餐', value: 'dinner' },
				{ label: '夜宵', value: 'supper' },
				{ label: '凌晨餐', value: 'morning' }
			],
			allowDateList: [], //允许点餐的日期
			numberKey: 0,
			alreadyOrderList: [], // 已经下过单的数据，用于判断全选餐段的状态下，如果该天所有餐段已经下过单，不支持再次报餐
			formatAlreadyOrderList: [], // 将已经下过单的数据格式化，用于如果该天该餐段已经下过单，不支持再次报餐
			isSelectAllMeal: false,
			tabActiveColor: this.$variables.colorPrimary, //'#12E294', // tabs必须有默认值，不然会报错，所以全选的时候只能手动改样式了
			mealDateList: [], // 每个餐段能点餐的开始日期
			holidayList: [] // 节假日
		}
	},
	computed: {
		org() {
			// 用户进入页面前选择的组织
			const state = this.$store.state.appoint
			return state.select.org || {}
		},
    mealList() {
      let mealList = []
			this.mealDateList = []
      this.mealReportStallList.map(item => {
        if (item.org_id === this.stallTabs[this.currentTab].org_id) {
					// 存在同一个消费点,不同餐段可能有多条规则的情况,把属于同一消费点的餐段concat到一起
          mealList = mealList.concat(item.meal_data)
					// 全选餐段的时候后端那边需要把每个餐段能点餐的开始日期传给他，也concat到一起:mealDateList
					for (let key in item.meal_type_date) {
						this.mealDateList.push({
							meal_type: key,
							date: item.meal_type_date[key],
							report_fee: item[key + '_fixed'],
							fuwu_fee: item.fuwu_fee,
							limit_count: item.limit_count
						})
					}
        }
      })
      // 再给餐段排个序
      let mealSortList = []
      this.mealTypeList.map(item => {
        mealList.map(meal => {
          if (item.value === meal.meal_type) {
            mealSortList.push(meal)
          }
        })
      })
      return mealSortList
    },
		mealType() {
			// 右上角选择的餐段
			if (this.mealList && this.mealList.length) {
				return this.mealList[this.mealIndex].meal_type
			}
			return ''
		},
		currentRule() { // 当前走的规则，要符合目前页面所选的[组织和餐段]
      let obj = {}
      this.mealReportStallList.map(item => {
        item.meal_data.map(mealItem => {
          if (item.org_id === this.stallTabs[this.currentTab].org_id && mealItem.meal_type === this.mealType) {
            obj = item
          }
        })
      })
      return obj
		},
		// [特定餐段、消费点下]的购物车列表，显示在右侧的
		stallMealList() {
			return this.allShopCardList.filter(item => item.stall_id === this.currentRule.org_id && item.meal_type === this.mealType)
		},
		// [特定消费点]所有加过购物车的日期
		allShopCardDateList () {
			let date = []
			this.allShopCardList.map(item => {
				if (item.stall_id === this.currentRule.org_id && date.indexOf(item.date) === -1) {
					date.push(item.date)
				}
			})
			return date
		},
		// [特定消费点]全选餐段时，右边显示的数据，也可以理解为某个消费点所有的购物车数据
		selectAllMealCardList() {
			let list = []
			this.allShopCardDateList.map(date => {
				let currentStallList = []
				this.allShopCardList.map(item => {
					if (item.date === date && item.stall_id === this.currentRule.org_id) {
						currentStallList.push(item)
					}
				})
				list.push({
					date,
					month: date.split('-')[1],
					day: date.split('-')[2],
					currentStallList
				})
			})
			return list
		},
		// 标识[特定消费点][该餐段/所有餐段]加在购物车的日期，标识这些日期为绿色背景色
		selectDateList() {
			let datalist = []
			if (this.isSelectAllMeal) { // 全选餐段
				datalist = this.allShopCardDateList
			} else { // 单个餐段
				this.stallMealList.map(item => datalist.push(item.date))
			}
			return datalist
		},
		// 标识[该餐段]/[所有餐段]已经下过单的日期，标识这些日期为红色小点
		dotAlreadyList() {
			let dates = []
			if (this.isSelectAllMeal) { // 全选餐段
				for (let key in this.alreadyOrderList) {
					let flag = true
					this.mealList.map(meal => {
						if (this.alreadyOrderList[key].indexOf(meal.meal_type) === -1) {
							flag = false
						}
					})
					if (flag) {
						dates.push(key)
					}
				}
			} else { // 单个餐段
				let dataList = this.formatAlreadyOrderList.filter(item => item.meal_type === this.mealType)
				dataList.map(item => {
					dates.push(item.date)
				})
			}
			
			return dates
		}
	},
	watch: {
		isSelectAllMeal() {
			if (this.isSelectAllMeal) {
				let date = this.mealDateList[0].date
				this.mealDateList.map(item => {
					if (new Date(item.date).getTime() <= new Date(date).getTime()) {
					  date = item.date
					}
				})
				// 全选餐段的时候，使用所有餐段中最早可以点餐的日期，后端那边会做过滤，比如早餐的点餐时间是2022-11-15，午餐是2022-11-16，取2022-11-15
				this.computeAllDate(date)
			} else {
				this.computeAllDate()
			}
		}
	},
	async onShow() {
		this.paramsData = this.$store.state.appoint.select
		await this.getYearHoliday()
		this.getStallList()
	},
	methods: {
		// 获取一年的节假日
    getYearHoliday(primaryDate) {
    	this.$showLoading({
    		mask: true
    	})
    	getApiYearHoliday({
    		date: new Date().getFullYear()
    	})
    		.then(res => {
    			uni.hideLoading()
    			if (res.code == 0) {
    				// 节假日
						this.holidayList = res.data.total_holiday
    			} else {
    				uni.$u.toast(res.msg)
    			}
    		})
    		.catch(error => {
    			uni.hideLoading()
    			uni.$u.toast(error.message)
    		})
    },
		// 获取报餐的规则[不同消费点不同餐段都可能有不同规则]
		getStallList() {
			getReportMealStall({
				person_no: this.paramsData.person.person_no,
				org_id: this.org.org_id,
				take_meal_type: this.paramsData.take_meal_type
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
            // 后台报餐设置的所有规则
						this.mealReportStallList = res.data
            // 针对同一组织不同餐段可能有多条规则的情况，所以stallTabs要过滤处理下
            this.stallTabs = []
            // 针对同一组织不同餐段可能有多条规则的情况，找到第一个组织所有的规则中的near_meal_type(当前规则在今天可点餐的第一个餐段)
            // near_meal_type可能有多个值，再找到排序在最前面的餐段，就是目前的餐段
            let org_id = this.mealReportStallList[0].org_id //第一次进入页面默认第一个组织
            let nearMealType = [] //所有的near_meal_type
            this.mealReportStallList.map(item => {
              let arr = this.stallTabs.filter(tab => tab.org_id === item.org_id)
              if (!arr.length) {
                this.stallTabs.push({
                  org_id: item.org_id,
                  org_name: item.org_name
                })
              }
              if (item.org_id === org_id && item.near_meal_type) {
                nearMealType.push(item.near_meal_type)
              }
            })
            if (nearMealType.length === 0) { // 没有可点餐段了
              this.mealIndex = 0 // 默认显示第一个餐段
            } else {
              // 找到排序在最前面的餐段，就是第一次进入页面要显示的餐段了
              let flag = true
              this.mealList.map((item, index) => {
                if (nearMealType.indexOf(item.meal_type) !== -1 && flag) {
                  this.mealIndex = index
                  flag = false
                }
              })
            }
            this.computeAllDate() // 获取允许点餐的日期
						this.getShopcardList()
						this.getMonthDetail()
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					this.resetMealData('stall')
					uni.$u.toast(error.message)
				})
		},
    // 计算所有能点餐的日期
    // 不同消费点不同餐段都可能有不同规则，所以初始化和每次change都要重新获取允许点餐的日期
    computeAllDate(date) {
			// 兼容处理
			if (!date && this.mealType) {
				date = this.currentRule.meal_type_date[this.mealType]
			}
			// 如果是零，至今置灰所有可点餐段
			console.log("date", date, this.currentRule);
			if (this.$refs.mrCalendar && Reflect.has(this.$refs.mrCalendar, 'setDisabledAll')) {
				this.$refs.mrCalendar.setDisabledAll(this.currentRule.can_report_day === 0)
			}
			if (Reflect.has(this.currentRule, 'can_report_day') && this.currentRule.can_report_day === 0) {
				this.allowDateList = []
				return
			}
			// this.isSelectAllMeal = this.currentRule.can_report_day === 0 ? false : true
      // can_report_day是后台设置能报餐的天数
      // 今天往后数can_report_day，就是总的能点餐的日期：allDate
      let allDate = dateLengthInfo(new Date().getTime(), this.currentRule.can_report_day)
      // meal_type_date是某餐段从哪天开始可以点餐（因为后台有个餐段结束前多少小时截止点餐的设置）
      // 要再判断下是否超过餐段可点餐的日期，超过了就不要
			// 单个餐段的时候，用默认值【date = this.currentRule.meal_type_date[this.mealType]】，获取到某餐段从哪天开始可以点餐
			// 全选餐段的时候，使用所有餐段中最早可以点餐的日期，后端那边会做过滤，比如早餐的点餐时间是2022-11-15，午餐是2022-11-16，取2022-11-15
      let allowMealDate = []
			allDate.map((item, index) => {
        if (new Date(item).getTime() >= new Date(date).getTime()) {
          allowMealDate.push(item)
        }
      })
      if (this.currentRule.skip_holiday) { // 是否跳过节假日
				this.allowDateList = this.restrictionDate(allowMealDate, this.holidayList)
      } else {
      	this.allowDateList = this.restrictionDate(allowMealDate)
      }
    },
		// 限制日期 Date 可点餐日期，holiday节假日
		restrictionDate(dateData, holiday) {
			let primaryDate = uni.$u.deepClone(dateData)
			// 1.开启跳过周末并且跳过节假日
			// 2.跳过周末不跳过节假日
			// 3.跳过节假日不跳过周末
			// 4.日期不跳过['2022-8-18','2022-8-19']
			// no_skip_days = []
			let dateList = []
			// 1.开启跳过周末并且跳过节假日
			if (this.currentRule.skip_holiday && this.currentRule.skip_weekends) {
				holiday.forEach(v => {
					if (primaryDate.indexOf(v) !== -1) {
						let index = primaryDate.indexOf(v)
						primaryDate.splice(index, 1)
					}
				})
				primaryDate.forEach((v, index) => {
					if (new Date(v).getDay() !== 6 && new Date(v).getDay() !== 0) {
						dateList.push(v)
					}
				})
				primaryDate = dateList
			} else if (this.currentRule.skip_holiday && !this.currentRule.skip_weekends) {
				//跳过节假日不跳过周末
				holiday.forEach(v => {
					if (primaryDate.indexOf(v) !== -1) {
						let index = primaryDate.indexOf(v)
						primaryDate.splice(index, 1)
					}
				})
			} else if (!this.currentRule.skip_holiday && this.currentRule.skip_weekends) {
				//跳过周末不跳过节假日
				primaryDate.forEach((v, index) => {
					if (new Date(v).getDay() !== 6 && new Date(v).getDay() !== 0) {
						dateList.push(v)
					}
					primaryDate = dateList
				})
			}
			// 跳过工作日
			let noWorkList = []
			if (this.currentRule.skip_work_days) {
				primaryDate.forEach((v, index) => {
					if (new Date(v).getDay() === 6 || new Date(v).getDay() === 0) {
						noWorkList.push(v)
					}
					primaryDate = noWorkList
				})
			}
			// 找到可点日期 再判断是否在可点范围内 跳过的日期
			if (this.currentRule.skip_days && this.currentRule.skip_days.length) {
				this.currentRule.skip_days.forEach((v,index) => {
					if (primaryDate.includes(v)) {
						primaryDate.splice(primaryDate.indexOf(v), 1)
					}
				})
			}
			// 找到可点日期 再判断是否在可点范围内 不跳过的日期
			if (this.currentRule.no_skip_days && this.currentRule.no_skip_days.length) {
				this.currentRule.no_skip_days.forEach(v => {
					if (dateData.includes(v)) {
						primaryDate.push(v)
					}
				})
			}
			// 排序一遍
			primaryDate = primaryDate.sort(function(a, b) {
				return a > b ? 1 : -1
			})
			return primaryDate
		},
		stallTabsChange(data, index) {
			this.currentTab = index
      this.mealIndex = 0
			this.allowDateList = []
			this.computeAllDate()
			this.getMonthDetail()
		},
		mealTypeChange(e) {
			this.mealIndex = e.index
			this.isSelectAllMeal = false
			this.tabActiveColor = this.$variables.colorPrimaryLight10
			this.computeAllDate()
		},
		selectAllMeal() {
			this.isSelectAllMeal = true
			this.tabActiveColor = '#12E29400'
		},
    // 报餐某月内的每餐详情_如果该天该餐段已经下过单，不支持再次报餐
    getMonthDetail() {
    	reportMealOrderMonthDetail({
    		person_no: this.paramsData.person.person_no,
    		company_id: Cache.get('userInfo').company_id,
    		org_id: this.currentRule.org_id,
    		month: '202205' // 要传但是没啥用，获取的数据是今天往后点过餐的数据
    	})
    		.then(res => {
    			uni.hideLoading()
    			if (res.code == 0) {
						this.alreadyOrderList = res.data
    				this.formatAlreadyOrderList = []
    				for (let key in res.data) {
    					res.data[key].map(item => {
    						this.formatAlreadyOrderList.push({
    							date: key,
    							meal_type: item
    						})
    					})
    				}
    			} else {
    				uni.$u.toast(res.msg)
    			}
    		})
    		.catch(error => {
    			uni.$u.toast(error.message)
    		})
    },
		selectAllMonth(e) { // 全选某月的日期加入购物车
			if (!e.currentMonth.length) return
			this.calendarSelect(e.currentMonth, e.value[0] ? e.value[0] : 'allDel')
		},
		// 点击某个日期报餐或全选报餐
    calendarSelect(e, type) {
			let data = {
				stall_id: this.currentRule.org_id,
				stall_name: this.currentRule.org_name,
				report_fee: this.currentRule[this.mealType + '_fixed'],
				fuwu_fee: this.currentRule.fuwu_fee,
				meal_type_date_list: []
			}
			// 要报餐的日期
			if (type == 'all' || type == 'allDel') { // 全选日期
				data.date_list = e
			} else if (e.type) { // 单个日期，类型不是all 需要拿里面的日期
				data.date_list = [e.date]
			}
			// 餐段的
			if (this.isSelectAllMeal) { // 全选餐段
				if (e.already) { // 点过餐了
					return uni.$u.toast('该天所有餐段都已下过单')
				}
				data.meal_type = "all"
				data.meal_type_date_list = this.mealDateList
			} else { // 单餐段
				if (e.already) { // 点过餐了
					return uni.$u.toast('同一天同一餐段仅支持下单一次')
				}
				data.meal_type = this.mealType
				data.limit_count = this.currentRule.limit_count
			}
			
			if (type == 'all' || e.type === 'add') {
				// 添加购物车
				this.addShopcard(data)
			} else {
				this.delShopcard(data, true)
			}
		},
    // 加减份数
    selectCart(val, data) {
			if (this.isSelectAllMeal) { // 全选餐段
				this.mealDateList.map(item => {
					if (data.meal_type === item.meal_type) {
						data.limit_count = item.limit_count
					}
				})
			} else {
				data.limit_count = this.currentRule.limit_count
			}
    	data.date_list = [data.date]
    	if (val > data.count) {
    		this.addShopcard(data)
    	}else{
    		this.delShopcard(data, false)
    	}
    },

    // 关于购物车start
    getShopcardList() {
			reportShopcardList({
				payment_order_type: this.paramsData.payment_order_type,
				take_meal_type: this.paramsData.take_meal_type
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.allShopCardList = []
						res.data.shopcard.map(dateItem => {
							let currentStallList = []
							dateItem.stall.map(stallItem => {
								this.allShopCardList.push({
									...stallItem,
									date: dateItem.date,
									month: dateItem.date.split('-')[1],
									day: dateItem.date.split('-')[2]
								})
							})
						})
						this.numberKey = Math.random()
            // 格式化数据，为了方便显示购物车详情
						this.formatDetailShopCardList()
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					this.resetMealData('cart')
					uni.$u.toast(error.message)
				})
		},
    // 格式化数据，为了方便显示购物车详情
    formatDetailShopCardList() {
    	this.detailShopCardList = []
    	this.mealTypeList.map(meal => {
    		let detailStallList = []
    		this.stallTabs.map(stall => {
    			let stallItemList = this.allShopCardList.filter(item => item.stall_id === stall.org_id && item.meal_type === meal.value)
    			if (stallItemList.length) {
    				detailStallList.push({
    					org_id: stall.org_id,
    					org_name: stall.org_name,
    					stallAllDateList: stallItemList
    				})
    			}
    		})
    		if (detailStallList.length) {
    			this.detailShopCardList.push({
    				meal_type: meal.value,
    				meal_type_alias: meal.label,
    				mealAllStallList: detailStallList
    			})
    		}
    	})
    },
    // 添加购物车
		addShopcard(data) {
			this.$showLoading({
				mask: true
			})
			reportShopcardAdd({
				payment_order_type: this.paramsData.payment_order_type,
				take_meal_type: this.paramsData.take_meal_type,
				date_list: data.date_list,
				meal_type: data.meal_type,
				organization_id: data.stall_id,
				organization_alias: data.stall_name,
				report_fee: data.report_fee,
				fuwu_fee: data.fuwu_fee,
				consume_type: this.currentRule.consume_type,
				limit_count: data.limit_count,
				meal_type_date_list: data.meal_type_date_list
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.getShopcardList()
					} else if (res.code == 202) {
						this.getShopcardList()
						uni.$u.toast(res.msg)
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.hideLoading()
					uni.$u.toast(error.message)
				})
		},
		// 删除购物车
		delShopcard(data, clean_org_all) {
			this.$showLoading({
				mask: true
			})
			reportShopcardDelete({
				payment_order_type: this.paramsData.payment_order_type,
				take_meal_type: this.paramsData.take_meal_type,
				date_list: data.date_list,
				meal_type: data.meal_type,
				organization_id: data.stall_id,
				organization_alias: data.stall_name,
				report_fee: data.report_fee,
				fuwu_fee: data.fuwu_fee,
				consume_type: this.currentRule.consume_type,
				clean_org_all,
				meal_type_date_list: data.meal_type_date_list
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.getShopcardList()
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		// 关于购物车end

    // 关于购物车的清空start
    // 打开清空提示
		openClearModal(type, meal_type) {
			this.clearType = type
			this.isShowClear = true
			if (this.clearType === 'mealOrg') {
				this.clearText = '确定清空？'
			} else if (this.clearType === 'all') {
				this.clearText = '是否重置购物车？'
			} else if (this.clearType === 'meal') {
				this.clearText = '是否清空当前餐段选择？'
				this.clearMealType = meal_type
			}
		},
		confirmClear() {
			if (this.clearType === 'mealOrg') {
				this.delMealOrg('mealOrg')
			} else if (this.clearType === 'all') {
				this.delAllShopCard()
			} else if (this.clearType === 'meal') {
				this.delMealOrg('meal')
			}
		},
		cancelClear() {
			this.isShowClear = false
		},
		// 删除某个餐段某个组织里购物车
		delMealOrg(type) {
			this.$showLoading({
				mask: true
			})
			let params = {
				payment_order_type: this.paramsData.payment_order_type,
				take_meal_type: this.paramsData.take_meal_type
			}
			if (type === 'mealOrg') {
				params.organization_id = this.currentRule.org_id
				params.meal_type = this.mealType
			} else if (type === 'meal') {
				params.meal_type = this.clearMealType
			}
			reportMealOrgDelete(params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.isShowClear = false
						this.getShopcardList()
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		// 清空全部购物车
		delAllShopCard() {
			this.$showLoading({
				mask: true
			})
			reportShopcardClean({
				payment_order_type: this.paramsData.payment_order_type,
				take_meal_type: this.paramsData.take_meal_type
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.isShowClear = false
						this.getShopcardList()
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		// 关于购物车的清空end

		gotoOrder() {
			this.$miRouter.push({
				path: `/pages_bundle/meal_report/confirm_order`
			})
		},
		// 重置数据
		resetMealData(key) {
			switch (key) {
				case 'stall':
					this.mealReportStallList = []
					this.stallTabs = []
					this.mealList = []
					break;
				case 'cart':
					this.allShopCardList = []
					this.detailShopCardList = []
					break;
				default:
					this.mealReportStallList = []
					this.stallTabs = []
					this.mealList = []
					this.allShopCardList = []
					this.detailShopCardList = []
					break;
			}
		}
	}
}
</script>

<style lang="scss">
.pages {
	background-color: #fff !important;
}
.meal-report {
	.meal-cantent-box {
		padding-bottom: 100rpx;
		.tabs{
			position: relative;
			.meal-type-tabs{
				padding-right: 140rpx;
			}
			.meal-select-all{
				width: 140rpx;
				height: 90rpx;
				font-size: 32rpx;
				line-height: 90rpx;
				text-align: center;
				position: absolute;
				right: 0;
				top: 0;
			}
			.meal-select-all-active{
				color: $color-primary;
			}
		}
		// height: calc(100vh - 180rpx);
	}
	.header {
		background: #38ccc6 $bg-linear-gradient-1;

		.canteen {
			padding: 28rpx 36rpx;
		}
	}
	.stallActive {
		color: $color-primary;
		background-color: #fff;
		animation: stallActiveFrames 1s 1;
	}
	@keyframes stallActiveFrames {
		0% {
			opacity: 0;
		}
		10% {
			opacity: 0.5;
		}
		100% {
			opacity: 1;
		}
	}
	.report-btns {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
		z-index: 100;
		.bottom-btn {
			background-color: #ffffff;
		}
	}
	.report-tips {
		position: relative;
		z-index: 99999;
	}
	.report-details {
		.popup-content {
			height: 900rpx;
			.details-header {
				padding: 30rpx 40rpx;
			}
			.details-lists {
				.details-item {
					padding: 20rpx 40rpx 40rpx;
					border-bottom: 10rpx solid #f0f3f5;

					.details-stall {
						padding: 35rpx 0 20rpx;
						border-bottom: $border-base;
					}
				}
			}
		}
	}
	.order-warapp {
		border-top: 1rpx solid #ccc;
		.left-stall-box {
			width: 260rpx;
			background-color: #f0f3f5;
			.order-warapp-item {
				display: flex;
				align-items: center;
				height: 120rpx;
			}
		}
		.right-stall-box {
			width: 100%;
		}
	}
}
</style>
