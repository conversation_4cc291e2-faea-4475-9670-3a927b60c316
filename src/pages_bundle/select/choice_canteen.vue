<!-- 选择食堂 -->
<template>
  <view :style="theme.style" class="choice-canteen">
    <u-sticky offset-top="0" bg-color="transparent">
      <u-navbar bg-color="transparent" left-icon-color="#fff" auto-back>
        <view class="white lg f-w-500" slot="center">选择食堂</view>
      </u-navbar>
    </u-sticky>

    <view class="choice-title">请选择要点餐的食堂</view>

    <!-- 各大食堂 -->
    <view class="" v-for="(item, index) in shopList">
      <view class="flex row-between flex-center choice-canteen-item" @click="gotoshop(item)">
        <view class="flex">
          <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_bundle_icon_canteen"></u-image>
          <view class="m-l-18 md f-w-500">
            {{ item.org_name }}
          </view>
        </view>
        <u-icon color="#999999" name="arrow-right" size="24rpx"></u-icon>
      </view>
    </view>
  </view>
</template>

<script>
import { getApiBookingUserGetCanteenList } from '@/api/reservation'
import Cache from '@/utils/cache'
import { mapMutations } from 'vuex'

export default {
  data() {
    return {
      imgPath: this.$imgPath,
      userinfo: {},
      shopList: []
    }
  },
  onLoad(option) {
  },
  onShow() {
    this.userinfo = Cache.get('userInfo')
    this.getBookingUserGetCanteenList()
  },

  methods: {
    ...mapMutations(['SET_SELECT']),
    // 获取食堂组织
    getBookingUserGetCanteenList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiBookingUserGetCanteenList({
        company_id: this.userinfo.company_id,
        user_id: this.userinfo.user_id
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.shopList = res.data
          } else {
            uni.hideLoading()
            uni.$u.toast(res.msg)
          }
        })
        .catch(error => {
          // console.log(error)
          uni.$u.toast(error.msg)
        })
    },

    gotoshop(item) {
      this.SET_SELECT({
        key: 'org',
        data: item
      })
      if (this.$Route.query.mode == 'back') {
        this.$miRouter.back()
        return
      }
      this.$miRouter.replace({
        path: '/pages_bundle/appoint/appoint_order'
      })
    }
  }
}
</script>

<style lang="scss">
.choice-canteen {
  background-image: $bg-linear-gradient-2;
  background-size: 750rpx 308rpx;
  background-repeat: no-repeat;

  .choice-title {
    margin: 0 0 40rpx 70rpx;
    padding-top: 40rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #ffffff;
    line-height: 36rpx;
  }

  .choice-canteen-item {
    margin: 0 40rpx 20rpx 40rpx;
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    background: #ffffff;
  }
}
</style>
