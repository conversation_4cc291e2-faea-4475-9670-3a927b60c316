<!-- 选择配送地址 -->
<template>
  <view :style="theme.style" class="choice-address">
    <view class="" v-for="(item, index) in 3" :key="index">
      <view class="address-item flex row-between">
        <view class="flex flex-center">
          <!-- 选择按钮 -->
          <view
            class="m-r-30 address-btn flex flex-center"
            :class="index === selectIndex ? 'address-btn-select' : ''"
            @click="changeAddress(index)"
          >
            <view class="address-btn-inside"></view>
          </view>
          <!-- 中间主要信息 -->
          <view class="address-content">
            <view class="m-b-26 line-2">
              <text class="xxs primary address-tips m-r-10" v-if="index === 0">默认</text>
              <text class="lg f-w-500 address-info">海珠区新港东路1000号保利世贸C座西塔2204</text>
            </view>
            <view class="nr muted flex name-phone">
              <text class="m-r-28 line-1 name">李子明</text>
              <text>17673268633</text>
            </view>
          </view>
        </view>

        <!-- 编辑 -->
        <view class="m-l-30 nr primary address-edit flex flex-center">
          <view class="">编辑</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      selectIndex: 0 // 被选中的地址
    }
  },

  methods: {
    // 选择地址
    changeAddress(val) {
      this.selectIndex = val
    }
  }
}
</script>

<style lang="scss" scoped>
.choice-address {
  margin: 40rpx;
  .address-item {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 20rpx;

    .address-edit {
      min-width: 60rpx;
    }

    .address-btn {
      min-width: 36rpx;
      height: 36rpx;
      width: 36rpx;
      border-radius: 50%;
      border: 2rpx solid #ededed;
    }
    .address-btn-select {
      background: $color-primary;
      border: 2rpx solid $color-primary;
    }

    .address-btn-inside {
      height: 18rpx;
      width: 18rpx;
      border-radius: 50%;
      background: #ffffff;
      z-index: 2;
    }

    .address-content {
      .address-tips {
        padding: 2rpx 10rpx;
        background: rgba($color: #12e294, $alpha: 0.15);
        border-radius: 4rpx;
        position: relative;
        top: -5rpx;
      }

      .address-info {
        line-height: 48rpx;
      }

      .name-phone {
        max-width: 420rpx;
        .name {
          max-width: 200rpx;
        }
      }
    }
  }
}
</style>
