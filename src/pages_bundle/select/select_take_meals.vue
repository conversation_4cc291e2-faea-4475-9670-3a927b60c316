<!-- 选择取餐方式 -->
<template>
  <view :style="theme.style" class="select-take-meals">
    <u-navbar bg-color="transparent" left-icon-color="#fff" auto-back>
      <view class="white lg f-w-500" slot="center">取餐方式</view>
    </u-navbar>

    <!-- all -->
    <view class="" v-if="type == 'all'">
      <view class="select-title">请选择任一取餐方式</view>

      <!-- 取餐方式 -->
      <view class="take-meals-all">
        <!-- 顶部选择 -->
        <radio-group @change="groupChange">
          <label class="" v-for="(item, index) in selectMeals" :key="item.type">
            <view class="flex take-meals-item" :class="selectMealsType == item.type ? 'take-meals-item-select' : ''">
              <radio :value="item.type" color="#12E294"></radio>
              <view class="m-l-18 lg f-w-500 line-1">
                {{ item.name }}
              </view>
            </view>
          </label>
        </radio-group>

        <!-- 地部选择 -->
        <!-- 				<view class="" v-if="selectMealsId == 1 || selectMealsId == 2">
					<view class="xs muted m-b-30 take-meals-func">
						{{ selectMealsId == 1 ? '请选择食堂取餐方式' : '请选择外卖配送方式'}}
					</view>
					<view class="flex">
						<view class="" v-for="(item, index) in selectBtn" :key="item.id">
							<view class="flex flex-center m-r-30 meals-func-item"
								:class="item.type == selectMealsDetailsId ?'meals-func-item-select':''"
								@click="changeDetails(item.type)">
								<u-image width="36rpx" height="36rpx"
									:src="selectMealsDetailsId == item.type ? item.iconSelect : item.icon"></u-image>
								<view class="lg f-w-500 m-l-12">
									{{ item.name }}
								</view>
							</view>
						</view>
					</view>
				</view> -->
      </view>
    </view>

    <!-- hallFood-单独堂食取餐页面 -->
    <view class="" v-if="type == 'hallFood'">
      <view class="select-title">请选择食堂取餐方式</view>
      <view class="hall-food-all">
        <view class="flex">
          <view class="" v-for="(item, index) in selectHallFood" :key="item.type">
            <view
              class="flex flex-col flex-center m-r-30 meals-func-item"
              :class="item.type == selectMealsType ? 'meals-func-item-select' : ''"
              @click="changeDetails(item.type)"
            >
              <u-image width="54rpx" height="54rpx" :src="selectMealsType == item.type ? item.iconSelect : item.icon"></u-image>
              <view class="lg f-w-500 m-t-20">
                {{ item.name }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapMutations } from 'vuex'
export default {
  data() {
    return {
      // 取餐方式type
      selectMealsType: '',
      // 具体取餐方式id
      // selectMealsDetailsId: '0',
      // 选中的取餐按钮列表
      // selectBtn: [],

      // 取餐方式列表
      selectMeals: [
        {
          type: 'on_scene',
          name: '堂食'
        },
        // {
        // 	type: 'bale',
        // 	name: '堂食自取',
        // },
        {
          type: 'waimai',
          name: '外卖配送'
        }
        // {
        // 	type: 'cupboard',
        // 	name: '取餐柜自取',
        // }
      ],

      // 具体取餐方式-堂食列表
      selectHallFood: [
        {
          id: '',
          name: '堂食',
          type: 'on_scene',
          icon: this.$imgPath.img_bundle_icon_hall_food,
          iconSelect: this.$imgPath.img_bundle_icon_hall_food_white
        },
        {
          id: '2',
          type: 'bale',
          name: '自提打包',
          icon: this.$imgPath.img_bundle_icon_take_out_food,
          iconSelect: this.$imgPath.img_bundle_icon_take_out_food_white
        }
      ],
      // 具体取餐方式-外卖列表
      // selecTakeOutFood: [{
      // 		id: '3',
      // 		name: '外卖配送',
      // 		type:"waimai",
      // 		icon: this.$imgPath.img_bundle_icon_take_out_food,
      // 		iconSelect: this.$imgPath.img_bundle_icon_take_out_food_white,
      // 	},
      // 	{
      // 		id: '4',
      // 		name: '取餐柜自取',
      // 		type:"cupboard",
      // 		icon: this.$imgPath.img_bundle_icon_dining_cabinet,
      //    iconSelect: this.$imgPath.img_bundle_icon_dining_cabinet_white,
      // 	}
      // ],
      type: 'all' // all-全部选择-默认，  hallFood-单独堂食取餐页面
    }
  },

  methods: {
    ...mapMutations(['SET_SELECT']),
    // 改变取餐方式id
    groupChange(e) {
      this.changeDetails(e.target.value)
      // if (e.target.value == 1) {
      // 	this.selectBtn = this.selectHallFood
      // } else {
      // 	this.selectBtn = this.selecTakeOutFood
      // }

      // console.log('change', e.target.value);
    },

    // 改变具体取餐方式id
    changeDetails(type) {
      this.selectMealsType = type
      this.SET_SELECT({
        key: 'take_meal_type',
        data: type
      })
      this.$miRouter.replace({
        path: '/pages_bundle/select/choice_canteen'
      })
    },
    handleConfirm() {
      // this.$miRouter.push({
      // 	path: this.type == 'all' ? '/pages_bundle/appoint/appoint_order' : '/pages_bundle/meal_report/meal_report'
      // })
    }
  },

  onLoad() {
    let type = this.$Route.query.type
    if (type) {
      this.type = type
    }
    // if (type == 'hallFood') {
    // 	this.selectBtn = this.selectHallFood
    // }
  }
}
</script>

<style lang="scss">
.select-take-meals {
  background-image: $bg-linear-gradient-2;
  background-size: 750rpx 308rpx;
  background-repeat: no-repeat;

  .select-title {
    margin: 0 0 40rpx 70rpx;
    padding-top: 40rpx;
    font-size: 36rpx;
    font-weight: bold;
    color: #ffffff;
    line-height: 36rpx;
  }

  .take-meals-all {
    width: 670rpx;
    height: 620rpx;
    background: #ffffff;
    border-radius: 20rpx;

    margin: 0 40rpx;
    padding: 40rpx 30rpx 36rpx 30rpx;

    .take-meals-item {
      margin-bottom: 20rpx;
      border-radius: 20rpx;
      padding: 40rpx 30rpx;
      background: #ffffff;
      border: 2rpx solid #dcdcdc;
    }

    .take-meals-item-select {
      color: $color-primary;
      border: 2rpx solid $color-primary;
    }

    .take-meals-func {
      margin-top: 108rpx;
    }

    .meals-func-item {
      width: 290rpx;
      height: 112rpx;
      border: 2rpx solid #dcdcdc;
      border-radius: 12rpx;

      &-select {
        color: #ffffff;
        background: $color-primary;
        border: 2rpx solid $color-primary;
      }
    }
  }

  .hall-food-all {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    margin: 0 40rpx;

    .meals-func-item {
      width: 290rpx;
      height: 200rpx;
      background: #ffffff;
      border-radius: 12rpx;
      border: 2rpx solid #e0e0e0;
    }

    .meals-func-item-select {
      width: 290rpx;
      height: 200rpx;
      background: $color-primary;
      border-radius: 12rpx;
      border: 2rpx solid $color-primary;
      color: #ffffff;
    }
  }

  .confirm-btn {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    padding-bottom: calc(54rpx + env(safe-area-inset-bottom));
  }
}
</style>
