<template>
  <view :style="theme.style" class="select_canteen">
    <!-- Main Start -->
    <scroll-view scroll-y="true" class="main">
      <view class="canteen" v-for="(item, index) in canteenData" :key="index">
        <view class="canteen-wallet">
          <block v-for="(walletItem, index2) in item.wallet_list" :key="index2">
            <view class="canteen-name xs">{{ walletItem.org_name}}<text class="muted">{{ '（' + walletItem.card_name+'）'}}</text></view>
            <view class="canteen-wallet-item white m-t-20 flex col-center row-between" @click="selectWalletFunc(walletItem)">
              <view class="flex">
                <text class="nr canteen-org-name">{{ walletItem.org_name }}</text>
                -
                <text class="nr">{{ walletItem.name }}</text>
              </view>
              <image class="m-r-10" :src="themeImgPath.img_icon_white_wallet"></image>
            </view>
          </block>
        </view>
      </view>
    </scroll-view>
    <!-- Main End -->
  </view>
</template>

<script>
import { checkClient } from '@/utils/util'
import { mapActions, mapGetters } from 'vuex'
import { getApiRechargeGetSettingsV2, getApiRechargeWalletList } from '@/api/user.js'
export default {
  data() {
    return {
      imgPath: this.$imgPath,
      // 食堂数据
      canteenData: [],
      routeType: '',
      personNo: '', // 人员编号， 用于区分用户是从首页进还是钱包中心进，多用户适配
      walletData: {},
      platform: checkClient()
    }
  },

  // Methods Start
  methods: {
    ...mapActions({
      setWallet: 'setWallet'
    }),
    selectWalletFunc(data) {
      this.setWallet(data)
      this.walletData = data
      if (this.platform === 'abc') {
        this.getRechargeGetSettings()
      } else {
        if (this.routeType == 'index') {
          this.$miRouter.replace('/pages_bundle/recharge/recharge')
        } else {
          this.$miRouter.back()
        }
      }
    },
    getRechargeWalletList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      let params = {}
      if (this.personNo) {
        params.person_no = this.personNo
      }
      getApiRechargeWalletList(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.canteenData = res.data
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    // 校验一下后台是否配置去农行缴费
    getRechargeGetSettings() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      let params = {
        org_id: this.walletData.org_id,
        person_no: this.personNo,
      }
      if (this.walletData.wallet_id) {
        params.wallet_id = this.walletData.wallet_id
      }
      getApiRechargeGetSettingsV2(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (this.routeType == 'index') {
              this.$miRouter.replace('/pages_bundle/recharge/recharge')
            } else {
              this.$miRouter.back()
            }
          } else if (res.code == 302) { // 302就去农行
            // #ifdef H5
            window.location.href = res.msg
            // #endif
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err)
        })
    }
  },
  // Methods End

  // Life Cycle Start
  onShow() {
    if (this.$Route.query.routeType) {
      this.routeType = this.$Route.query.routeType
    }
    this.personNo = this.$Route.query.person_no
    this.getRechargeWalletList()
  },
  onLoad() {
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.select_canteen {
  display: flex;
  height: calc(100vh - env(safe-area-inset-bottom));

  .aside {
    width: 262rpx;
    height: 100%;

    &-active {
      color: #1b1d1c;
      font-weight: 500;
      background-color: $background-color;
    }

    &-item {
      height: 70rpx;
      text-align: right;
      line-height: 70rpx;
      padding: 0 30rpx;
    }
  }

  .main {
    width: 100%;
    padding: 30rpx;

    .canteen {
      &-name {
        padding: 24rpx 0rpx;
        padding-bottom: 0;
      }

      &-wallet {
        width: 100%;
      }

      &-wallet-item {
        height: 84rpx;
        padding: 0 30rpx;
        border-radius: 20rpx;
        box-sizing: border-box;
        background: #ffffff $bg-linear-gradient-2;

        image {
          width: 58rpx;
          height: 58rpx;
        }
      }

      &-org-name {
        display: inline-block;
        max-width: 370rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        // color: red;
      }
    }
  }
}
</style>
