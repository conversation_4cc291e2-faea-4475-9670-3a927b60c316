<template>
  <view :style="theme.style" class="recharge_status">
    <!-- recharge progress 充值进度 Start -->
    <template v-if="progress">
      <view v-if="progressStatus === 1" class="recharge-progress flex flex-col flex-center">
        <!-- Icon -->
        <image class="icon-wallet" :src="themeImgPath.img_recharge_wallet"></image>
        <!-- Progress text -->
        <text class="xxl f-w-500 black m-t-26">已下单，充值中</text>
        <!-- steps -->
        <view class="steps flex m-t-44">
          <view class="steps--wrapper flex flex-col flex-center">
            <view class="steps--wrapper-dot flex flex-center">
              <view class="steps--start-item"></view>
            </view>
            <view class="m-t-22 xs">充值成功</view>
          </view>
          <view class="steps--wrapper flex flex-col flex-center">
            <view class="steps--wrapper-dot flex flex-center">
              <view class="steps--item"></view>
            </view>
            <view class="m-t-22 xs">充值中</view>
          </view>
          <view class="steps--wrapper flex flex-col flex-center">
            <view class="steps--wrapper-dot flex flex-center">
              <view class="steps--end-item"></view>
            </view>
            <view class="m-t-22 xs">充值到账</view>
          </view>
        </view>
      </view>
      <view v-else class="recharge-progress flex flex-col flex-center">
        <u-icon name="close-circle" color="red" size="42px"></u-icon>
        <!-- Progress text -->
        <text class="xxl f-w-500 black m-t-26">{{ progressText }}</text>
      </view>
    </template>
    <!-- recharge progress End -->

    <!-- recharge result 充值结果 Start -->
    <u-transition :show="result" mode="fade">
      <view class="result-container flex flex-col flex-center">
        <!-- Icon -->
        <image class="icon-success" :src="themeImgPath.img_reacharge_success"></image>
        <!-- Result Text -->
        <text class="black xxl f-w-500 m-t-32">充值到账</text>
        <!-- Money -->
        <text class="muted xxs m-t-32" v-if="money">食堂储值钱包已到账{{money}}元</text>
        <!-- Gift amount -->
        <text class="muted xxs m-t-4" v-if="complimentaryFee">食堂赠送钱包已到账{{complimentaryFee}}元</text>
        <!-- Confirm Btn-->
        <button class="confirm-btn md white bg-primary m-t-60" @click="confirm">确定</button>
      </view>
    </u-transition>
    <!-- recharge result End -->

    <view class="main-content m-p-20">
      <!-- 推广图 -->
      <generalization-map v-if="isGetGeneralizationMapShow&&hasGeneralizationMap" ref="generalizationMap" uiType="recharge" :key="refresh" class="m-t-20 m-b-20" @transfer="getList" :pageType="'index'"></generalization-map>
    </view>
    
     <!-- 满意度 -->
    <image v-if="isShowStatisfaction" :src="themeImgPath.img_banner_idea_big" style="width: 670rpx;height: 200rpx;display: block;margin: 0 auto; margin-top: 68rpx;" @click="onEditorSatisfaction" />
    <!-- 评价弹窗 -->
    <statisfaction-modal ref="statisfactionMoadlRef" :tips="statisfactionTips" :tags="tags" :satisfactionForm="satisfactionForm" :visible.sync="sModalVisible" @onModalSuccess="onModalSuccess" />
  </view>
</template>

<script>
import { getApiRechargeOrderQuery, queryOrderInfo } from '@/api/user.js'
import { divide, getCurrentQuarter } from '@/utils/util.js'
import generalizationMap from '@//components/generalization-map/generalization-map'
import Cache from '@/utils/cache'
import StatisfactionModal from '@/components/statisfaction-modal/statisfaction-modal.vue'
import appMixins from '@/mixins/app.js'
import {setStorage, getStorage} from '@/utils/storage'

export default {
  mixins: [appMixins],
  components: {generalizationMap, StatisfactionModal},
  // Data Start
  data() {
    return {
      // 满意度
      statisfactionTips: '',
      tags: [], // 不满意评价标签
      isShowStatisfaction: true,
      satisfactionForm: {
        is_satisfied: true,
        reason: '',
        module_key: 'charge'
      },
      sModalVisible: false,
      
      isGetGeneralizationMapShow: false,
      hasGeneralizationMap: true,
      imgPath: this.$imgPath,
      tradeNo: '', // 订单号
      money: '',
      complimentaryFee: '', // 赠送余额
      progress: true, //充值中，充值进度
      progressStatus: 1, // 1查询中，2成功，3失败
      progressText: '查询失败，请联系客服',
      result: false, // 充值完成
      queryHandle: null // 定时查询订单结果任务
    }
  },
  // Data End

  // Methods Start
  methods: {
    // 提交评价回调
    onModalSuccess() {
      console.log('onModalSuccess----');
    },
    onEditorSatisfaction() {
      this.sModalVisible = true
    },
    getList(e) {
      if (e && e.length !== 0) {
        this.hasGeneralizationMap = true
      } else {
        this.hasGeneralizationMap = false
      }
      // this.refresh++
    },
    queryResult() {
      this.$showLoading({
        title: '查询中....',
        mask: true
      })
      this.queryHandle = setInterval(() => {
        this.queryPayResult()
        // clearInterval(this.queryHandle);
      }, 1000)
    },
    // 查询支付结果
    queryPayResult() {
      if (this.tradeNo) {
        getApiRechargeOrderQuery({
          trade_no: this.tradeNo
        }).then(res => {
          if (res.code === 0) {
            if (res.data.order_status === 'ORDER_SUCCESS') {
              this.progress = false
              this.result = true
              this.money = divide(res.data.wallet_fee) // 充值
              this.complimentaryFee = divide(res.data.complimentary_fee) // 赠送
              uni.hideLoading()
              if (this.queryHandle) {
                clearInterval(this.queryHandle);
              }
            }
          } else {
            this.progressStatus = 3
            uni.hideLoading()
            if (this.queryHandle) {
              clearInterval(this.queryHandle);
            }
            uni.$u.toast(res.msg)
          }
        }).catch(err => {
          this.progressStatus = 3
          uni.hideLoading()
          if (this.queryHandle) {
            clearInterval(this.queryHandle);
          }
        })
      } else {
        uni.hideLoading()
        uni.$u.toast('订单号不能为空！')
        if (this.queryHandle) {
          clearInterval(this.queryHandle);
        }
      }
    },
    // 确定
    confirm() {
      this.$miRouter.replace({ path: '/pages/index/index' })
      // this.$miRouter.back()
    }
  },
  // Methods End

  // Life Cycle Start
  async onLoad(option) {
    // this.money = this.$Route.query.money;
    // 支付宝h5支付有out_trade_no和trade_no，先判断out_trade_no
    this.tradeNo = this.$Route.query.out_trade_no || this.$Route.query.trade_no;
    //五秒
    // setTimeout(() => {
    //   this.result = true
    //   this.progress = false
    // }, 3000)
    if (this.tradeNo) { // 当有订单号时使用定时器查询充值订单
      this.queryResult()
    } else {
      this.result = true
      this.progress = false
    }
    // 在此判断是否显示轮播图
    if (Cache.get('isVIP')) {
      this.isGetGeneralizationMapShow = true
    }
    // 判断是否显示评价弹窗
    const moduleKey = this.satisfactionForm.module_key // key
    const today = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd') // 获取当天时间
    const result = await this._appGetSatisfaction(moduleKey) // 获取记录

    if (!result) return

    const isEveryDay = result.is_every_day
    const storageKey = isEveryDay ? moduleKey : `${moduleKey}_quarter`
    const lastTime = getStorage(storageKey)
    const currentTime = isEveryDay ? today : getCurrentQuarter()
    
    if (lastTime && lastTime === currentTime) {
      this.sModalVisible = false
    } else {
      setStorage({ name: moduleKey, content: today }) // 记录当天弹窗时间
      if (!isEveryDay) {
        setStorage({ name: `${moduleKey}_quarter`, content: getCurrentQuarter() }) // 记录当前季度
      }
      this.sModalVisible = true
    }
    this.isShowStatisfaction = true
    this.statisfactionTips = result.tips
    this.tags = result.tags
    this.satisfactionForm.module_key = result.module_key
    // this.satisfactionForm.is_satisfied = result.is_satisfied
    this.satisfactionForm.reason = result.reason
  },
  // Life Cycle End
  onUnload() {
    if (this.queryHandle) {
      clearInterval(this.queryHandle);
    }
  }
}
</script>

<style lang="scss">
page {
  background-color: #ffffff;
}

.recharge_status {
  // 充值进度
  .recharge-progress {
    padding-top: 90rpx;

    .icon-wallet {
      width: 220rpx;
      height: 176rpx;
    }

    .steps {
      &--wrapper {
        width: 150rpx;
        height: 150rpx;
      }
      &--wrapper-dot {
        width: 55rpx;
        height: 55rpx;
      }
      &--start-item {
        width: 30rpx;
        height: 30rpx;

        border-radius: 50%;
        position: relative;
        background-color: $color-primary;
      }
      &--start-item::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 75rpx;
        height: 5rpx;
        transform: translate(0%, -50%);
        background-color: $color-primary;
      }
      &--item {
        width: 55rpx;
        height: 55rpx;
        position: relative;
        background-image: url($imgBasePath + '/images/reacharging.png');
        background-repeat: no-repeat;
        background-size: cover;
      }
      &--item::before {
        content: '';
        position: absolute;
        top: 50%;
        right: 100%;
        width: 50rpx;
        height: 5rpx;
        transform: translate(0%, -50%);
        background-color: $color-primary;
      }
      &--item::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 100%;
        width: 50rpx;
        height: 5rpx;
        transform: translate(0%, -50%);
        background-color: $color-primary;
      }
      &--end-item {
        width: 30rpx;
        height: 30rpx;
        border-radius: 50%;
        position: relative;
        background-color: #a3a6a8;
      }
      &--end-item::before {
        content: '';
        position: absolute;
        top: 50%;
        right: 50%;
        width: 75rpx;
        height: 5rpx;
        transform: translate(0%, -50%);
        background-color: #a3a6a8;
      }
    }
  }

  // 充值结果
  .result-container {
    padding-top: 90rpx;

    .icon-success {
      width: 170rpx;
      height: 170rpx;
    }

    .confirm-btn {
      width: 410rpx;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 45rpx;
    }
  }

  // 推广图
  .main-content {
    padding: 0 40rpx;
    margin-bottom: 60rpx;
    position: relative;
    z-index: 1;
  } 
}
</style>
