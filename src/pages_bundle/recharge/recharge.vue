<template>
  <view :style="theme.style" class="recharge">
    <!-- Header 头部 Start -->
    <view class="header ls-card" @click="toSelectWalletFunc">
      <view class="bb nr lighter">{{ walletData.org_name ? walletData.org_name : '请选择钱包' }}</view>
      <view class="flex col-center row-between">
        <text class="normal md f-w-500">{{ walletData.name ? walletData.name : '请选择充值钱包' }}</text>
        <u-icon name="arrow-right"></u-icon>
      </view>
    </view>
    <!-- Header End -->
    <!-- Section 主体 Start -->
    <view class="section ls-card" v-if="walletData.org_id">
      <!-- recharge template Start -->
      <view class="recharge-template">
        <view
          class="template flex flex-col flex-center"
          :class="{ active: index === currentIndex, disabled: disabledBtn }"
          v-for="(item, index) in rechargeAmountList"
          :key="index"
          @click="rechargeNowFunc(item, index)"
        >
          <view class="money">
            {{ item.money }}
            <text class="xxs">元</text>
          </view>
          <!-- 充值赠送 -->
          <view v-if="item.give_money && walletData.complimentary_on" class="desc xxs f-w-500">赠送{{item.give_money}}元</view>
        </view>
        <view
          class="template flex flex-col flex-center"
          :class="{ disabled: disabledBtn }"
          v-if="rechargeGetSettingsData.allow_custom_amount"
          @click="showCustomDialog"
        >
          <view class="normal xxs">自定义金额</view>
        </view>
      </view>
      <!-- recharge template End -->
      <!-- 新增充值提示 -->
      <view class="text-center xxs u-error m-b-10">充值活动仅对部分支付方式生效</view>
      <u-button
        :text="determinePrice + '立即充值'"
        shape="circle"
        :color="variables.colorPrimary"
        @click="confirmRechargeFunc(true, 'immediately')"
        :disabled="isDisabled"
      ></u-button>
      <!-- 允许充值日期 -->
      <view v-if="tips" class="text-center xxs u-error m-t-10">{{tips}}</view>
      <view v-if="rechargeGetSettingsData.cycle_limit_num > -1" class="text-center xxs u-error m-t-10">{{limitTips}}</view>
    </view>
    <!-- Section End -->

    <!-- Components Modal Start -->

    <!-- 弹窗-余额上限 -->
    <u-modal :show="isError" :title="'提示'" confirmColor="#5A6080">
      <view class="xl text-center" v-html="content"></view>
    </u-modal>

    <!-- 自定义金额充值 -->
    <u-modal
      :show="isCustom"
      :title="'自定义金额'"
      confirmColor="#5A6080"
      confirmText="充值"
      @confirm="confirmRechargeFunc(true, 'custom')"
      :showCancelButton="true"
      cancelText="我再想想"
      @cancel="cancelRecharge"
    >
      <view class="flex-col">
        <input class="custom-money normal md" placeholder="请输入充值的金额" v-model="rechargePrice" @input="changeRechargePrice" type="number" :key="refresh" />
        <view v-if="customTip" class="m-t-4 nr">赠送{{ customTip }}元</view>
      </view>
    </u-modal>
    <!-- 支付方式 -->
    <!-- <u-modal
      :show="rechargeSettingShow"
      :title="'支付方式'"
      confirmColor="#5A6080"
      confirmText="充值"
      @confirm="confirmRechargeFunc(false, 'mode')"
      :showCancelButton="true"
      cancelText="取消"
      @cancel="rechargeSettingShow = false"
    >
      <u-radio-group placement="column" v-model="radioPayinfoId" size="35" iconPlacement="right">
        <u-radio
          :customStyle="{ marginBottom: '16px' }"
          v-for="(item, index) in payinfosList"
          @change="radioRechargeChange(item)"
          :key="index"
          :activeColor="$colorPrimary"
          :name="item.id"
        >
            <view class="flex flex-wrap"><text class="p-r-25 p-b-10">{{item.payway_alias}}</text> <text class="yellow" v-if="item.rate_fee">手续费：{{item.rate_fee}}</text></view>
        </u-radio>
      </u-radio-group>
    </u-modal> -->
    <!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
    <!-- Components Modal End -->
    <!--支付方式底部弹窗 20230427改成这种弹窗 start-->
    <view>
      <u-action-sheet  :closeOnClickOverlay="true" :closeOnClickAction="true"  :title="'支付方式'" :show="rechargeSettingShow" :round="10" @close="closePopSettingShow" >
       <!--选择支付方式-->
       <scroll-view scroll-y show-scrollbar :style="scrollViewStyle">
        <view class ='m-40' v-for="(itemPay,indexPay) in payInfoAllList" :key="indexPay">
          <view class="lg m-b-20 m-l-20 m-t-20 text-left ">{{itemPay.name}}</view>
            <view
              v-for="(item, index) in itemPay.childrenList"
              @click="radioRechargeChange(item,index,indexPay,showRateFee(item,false))"
              :key="index"
              :class="['template-pay flex row-between col-center',item.checked?'template-active':'']"
            >  
            <view>
              <view class="flex flex-wrap flex-col">
              <view class="lg p-r-25 p-b-10 text-left">{{item.sub_payway_alias}}</view> 
              <view class="flex text-left" >
                <view class="m-r-20 mini " v-if="showCouponFee(item,true)">赠送金额：¥ <text class="xs">{{showCouponFee(item,false)}}</text></view>
                <view class="m-r-20 mini " v-if="showRateFee(item,true)">手续费：¥ <text class="xs">{{mathAbsFee(showRateFee(item,false))}}</text></view>
              </view>
            </view>
            </view>
            <!-- <u-icon v-if='item.checked' name="checkmark-circle" color="#11E69E" size="35rpx"></u-icon> -->
          </view>
      </view>
    </scroll-view>
       <!--确认支付-->
        <!-- <u-button
          :text="'充值'"
          shape="circle"
          color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)"
          @click="confirmRechargeFunc(false, 'mode')"
          :disabled="isPayDisabled"
          :customStyle="customBtnStyle"
        ></u-button> -->
        
      </u-action-sheet>
    </view>
    <!--支付方式底部弹窗 20230427改成这种弹窗 end-->
    
    <!-- 满意度 -->
    <image v-if="isShowStatisfaction" :src="themeImgPath.img_banner_idea_big" style="width: 670rpx;height: 200rpx;display: block;margin: 0 auto; margin-top: 68rpx;" @click="onEditorSatisfaction" />
    <!-- 评价弹窗 -->
    <statisfaction-modal ref="statisfactionMoadlRef" :tips="statisfactionTips" :tags="tags" :satisfactionForm="satisfactionForm" :visible.sync="sModalVisible" @onModalSuccess="onModalSuccess" />
    <resign-dialog :visible.sync="showResignDialog" :person-info="personInfo" :re-sign-price="reSignPrice" @close="closeResignDialog" @nextSign="onNextSign"></resign-dialog>
  </view>
</template>

<script>
// #ifdef H5
var jweixin = require('jweixin-module')
// #endif
import { getApiRechargeGetSettings, getApiRechargeOrderCreate, getApiRechargeRateFee,apiBackgroundMarketingRechargeGetRechargePayinfoPost, apibookingRechargeGetRechargeRateDiscountPost } from '@/api/user.js'
import { addSatisfactionRecord } from '@/api/app.js'
import store from '@/store'
import Cache from '@/utils/cache'
import { mapActions, mapGetters } from 'vuex'
import { payMpRequest, checkWxBridgeReady, setWxJssdkConfig, payWxJssdkRequest } from '@/utils/payment'
import { getQueryObject, getMonthLength, deepClone, divide, times, getBaseUrl,plus, minus, checkClient } from '@/utils/util.js'
import { getApiWechatCongfigGet, getQywechatConfigGet } from '@/api/app'
import { apiWingPayCheckSign } from '@/api/sign'
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'
import { abcJSBridgeReady } from '@/utils/abcJsBridge'
import { createForm } from '@/utils/create_element'
import StatisfactionModal from '@/components/statisfaction-modal/statisfaction-modal.vue'
import appMixins from '@/mixins/app.js'
import ResignDialog from "../components/resign-dialog/resign-dialog.vue"
// import {setStorage, getStorage} from '@/utils/storage'
export default {
  mixins: [appMixins],
  // Data Start
  components: { FloatingPopup, StatisfactionModal, ResignDialog },
  data() {
    return {
      // 满意度
      statisfactionTips: '',
      tags: [],
      isShowStatisfaction: false,
      satisfactionForm: {
        is_satisfied: true,
        reason: '',
        module_key: 'charge'
      },
      sModalVisible: false,
      radioPayinfoId: '', // 选择的支付方式id
      walletData: {
        // title: '朴食科技知乎食堂',
        // name: '储值钱包',
        // id: 0
      },
      rechargeAmountList: [], //金额
      rechargeGetSettingsData: {}, //充值配置
      rechargeModeSelectData: {}, // 充值选择方式
      payinfosList: [], //充值方式数据
      rechargeSettingShow: false, // 打开支付选择方式
      currentIndex: 0,
      priceValue: 0, //充值的金额
      determinePrice: '',
      rechargePrice: '', //自定义金额
      isError: false, //是否充值不成功
      isCustom: false,
      customTip: '', // 自定义充值金额提示语
      isDisabled: false, //是否禁止点击充值按钮
      tips: '', //按钮下方提示信息 *每月1号、15号可进行充值
      limitTips: '', // 充值次数限制提示

      // 弹窗提示文字
      content: '超过钱包累计<span class="u-error">余额上限￥10000.00 </span>请重新选择',
      tradeNo: '', // 当前充值创建的订单号
      isLoading: false,
			rechargeRateFee: {} ,// 手续费返回的信息
      isPayDisabled:false,//是否允许点击支付
      customBtnStyle: { //支付按钮样式，小程序不这样写会不生效
				width: '620rpx',
				height: '60rpx',
				lineHeight: '60rpx',
        margin: "20rpx auto"
			},
      pushBtnStyle: {
        width: '560rpx',
        height: '72rpx',
      },
      scrollViewStyle:{ //滚动scrollview的样式
        height:'50vh'
      },
      payOrderTitle:'',//支付分组标题
      customStyleRadio:{
        height: "165rpx",
        backgroundColor: '#f0f3f5',
        margin: "10rpx" ,
        padding:"0 10rpx",
      },
      payInfoAllList: [{},{}],//支付方式列表
      payInfoCloneAllList: [],//克隆一个支付方式列表
      currentIndexClone: 0 ,// 记录用户选中的位置克隆
      isBackFromCustom: false ,// 是否是从自定义金额返回
      platform: checkClient(), // 平台， 微信or支付宝
      floatingPopupShow: false,
      refresh: 0,
      showResignDialog: false, // 重新签约
      personInfo: {}, // 重新签约信息
      reSignPrice: -1 // 重新签约记录价格
    }
  },
  // Data End
  filters: {
    priceFormat: function (value) {
      return (value / 100).toFixed(2)
    }
  },
  watch: {
    rechargePrice(newValue, oldValue) {
      console.log('newValue', newValue, 'oldValue', oldValue)
      if (!/^\d*\.?\d{0,2}$/.test(newValue) && newValue !== '') {
        this.rechargePrice = oldValue
        this.refresh++
        uni.$u.toast('请输入不超过两位小数的金额')
      }
    }
  },
  computed: {
    disabledBtn() {
      let disabled = true
      let now = new Date()
      let nowDay = now.getDate()
      let lastDay  = getMonthLength(now) 

      // 可充值日期
      let allow_recharge_date_list =this.rechargeGetSettingsData.allow_recharge_date_list? deepClone(this.rechargeGetSettingsData.allow_recharge_date_list) : []
      let lastDayIndex =this.rechargeGetSettingsData.allow_recharge_date_list? this.rechargeGetSettingsData.allow_recharge_date_list.indexOf(-1) : -1
      if (lastDayIndex > -1) { // 开启每月最后一天
        allow_recharge_date_list.splice(lastDayIndex, 1)
        allow_recharge_date_list.push(lastDay)
      }

      // 不可充值日期
      let not_allow_recharge_date_list =this.rechargeGetSettingsData.not_allow_recharge_date_list? deepClone(this.rechargeGetSettingsData.not_allow_recharge_date_list) : []
      let lastDayNotIndex = this.rechargeGetSettingsData.not_allow_recharge_date_list ? this.rechargeGetSettingsData.not_allow_recharge_date_list.indexOf(-1) : -1
      if (lastDayNotIndex > -1) { // 关闭每月最后一天
        not_allow_recharge_date_list.splice(lastDayNotIndex, 1)
        not_allow_recharge_date_list.push(lastDay)
      }

      if (allow_recharge_date_list && allow_recharge_date_list.length > 0 && !not_allow_recharge_date_list.length) {
        this.tips = `*本月${allow_recharge_date_list.join('号，')}号可进行充值`
        disabled = !allow_recharge_date_list.some(v => {
          return v == nowDay
        })
      } else if (not_allow_recharge_date_list && not_allow_recharge_date_list.length > 0 && !allow_recharge_date_list.length) {
        this.tips = `*本月${not_allow_recharge_date_list.join('号，')}号不可进行充值`
        disabled = !!not_allow_recharge_date_list.some(v => {
          return v == nowDay
        })
      } else {
        disabled = false
        this.tips = ''
      }
      this.isDisabled = disabled
      return disabled
    }
  },
  // Methods Start
  methods: {
    // 提交评价回调
    onModalSuccess() {
      console.log('onModalSuccess----');
    },
    onEditorSatisfaction() {
      this.sModalVisible = true
    },
    ...mapActions({
      setAbcRechargeCodeUrl: 'setAbcRechargeCodeUrl'
    }),
    numberMul(arg1, arg2) {
      if(!arg1){
        return 0
      }
      var m = 0
      var s1 = arg1.toString()
      var s2 = arg2.toString()
      try {
        m += s1.split('.')[1].length
      } catch (e) {}
      try {
        m += s2.split('.')[1].length
      } catch (e) {}

      return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m)
    },
    // 初始化支付
    async getWechatCongfigGet() {
      let params = {
        appid: uni.getStorageSync('appid') || Cache.get('userInfo').appid,
        company_id: Cache.get('userInfo').company_id,
        url: window.location.href.split('#')[0]
      }
			let res = null
			try {
				if(this.platform === 'wechat'){
					res = await getApiWechatCongfigGet(params)
				} else if(this.platform === 'wxwork'){
					res = await getQywechatConfigGet(params)
				}
				uni.hideLoading()
				if (res.code == 0) {
					if (res.data) {
						jweixin.config({
							beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
							debug: false,
							appId: res.data.appid,
							timestamp: res.data.timestamp,
							nonceStr: res.data.noncestr,
							signature: res.data.signature,
							jsApiList: ['checkJsApi', 'chooseWXPay','getBrandWCPayRequest'] // 把支付也初始化
						})
						jweixin.error(function(res){
							console.log('error~'+res)
						});
					}
				} else {
					uni.$u.toast(res.msg)
				}
			} catch (error) {
				uni.hideLoading()
				uni.$u.toast(error.message)
			}
    },
    getRechargeGetSettings() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiRechargeGetSettings({
        org_id: this.walletData.org_id,
        wallet_id: this.walletData.wallet_id
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.rechargeAmountList = res.data.recharge_amount_list.map(v => {
              return {
                money: divide(v)
              }
            })
            console.log("this.rechargeAmountList ",this.rechargeAmountList );
            this.determinePrice = this.rechargeAmountList[0].money
            this.rechargeGetSettingsData = res.data
            // 判断payway是否重复
            // let checkKey = {}
            // res.data.payinfos.map(v => {
            //   if (!checkKey[v.payway]) {
            //     checkKey[v.payway] = 1
            //   } else {
            //     checkKey[v.payway] += 1
            //   }
            // })
            // this.payinfosList = res.data.payinfos.map(v => {
            //   if (checkKey[v.payway] > 1) {
            //     v.payway_alias = v.payway_alias + '-' + v.sub_payway_alias
            //   }
            //   return v
            // })
            //重新组参，现在需要要变成树结构
            var resultList = this.setPayInfoListPrice(res.data)
            //payInfoAllList 赋值
            this.payInfoAllList = deepClone(this.getPayInfoNewlistTree(resultList))
            // 先保存一份原来的支付
            if(this.payInfoCloneAllList.length === 0 ){
              this.payInfoCloneAllList = deepClone(this.payInfoAllList)
            }

            console.log("this.payInfoAllList",this.payInfoAllList);
            //默认第一个现在不用了
            // if (this.payInfoAllList && this.payInfoAllList.length >= 1) {
            //   this.radioPayinfoId = this.payInfoAllList[0].childrenList[0].id
            //   this.rechargeModeSelectData = this.payInfoAllList[0].childrenList[0]
            //   this.payInfoAllList[0].childrenList[0].checked =true
            // } 
            // 充值优惠，目前只有充值赠送
            if (this.rechargeGetSettingsData.discount && this.rechargeGetSettingsData.discount.length) {
              this.rechargeGetSettingsData.discount = this.rechargeGetSettingsData.discount.map(v => {
                return {
                  coupon_fee: divide(v.coupon_fee),
                  limit_fee: divide(v.limit_fee),
                }
              })
              this.setGiveMoney(res.data.discount)
              let limitType = { 0: '活动期间', 1: '每周', 2: '每月', 3: '每日' }
              this.limitTips = `${limitType[this.rechargeGetSettingsData.cycle_type]}只能参与${this.rechargeGetSettingsData.cycle_limit_num}次充值赠送活动
`
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err)
        })
    },
    // 获取充值手续费金额
    getRechargeRateFee(parentIndex,index) {
      let params = {
        org_id: this.walletData.org_id,
        company_id: Cache.get('userInfo').company_id,
        money: this.numberMul(this.priceValue, 100),
        payinfo_id: this.rechargeModeSelectData.id,
        wallet_id: this.walletData.wallet_id,
        user_id: Cache.get('userInfo').user_id
      }
     return getApiRechargeRateFee(params)
        .then(res => {
          if (res.code == 0) {
						this.rechargeRateFee = res.data ||{}
            var rateFee =Reflect.has(this.rechargeRateFee,"rate_fee")?divide(this.rechargeRateFee.rate_fee):0
            var complimentaryFee =Reflect.has(this.rechargeRateFee,"complimentary_fee")?divide(this.rechargeRateFee.complimentary_fee):0
            console.log("getApiRechargeRateFee",rateFee,complimentaryFee);
            this.$set(this.payInfoAllList[parentIndex].childrenList[index],"rate_fee",rateFee)
            this.$set(this.payInfoAllList[parentIndex].childrenList[index],"complimentary_fee",complimentaryFee)
            console.log("getApiRechargeRateFee this.payInfoAllList",this.payInfoAllList);
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    // 初始化下充值赠送优惠
    setGiveMoney(data) {
      data.map(v => {
        this.rechargeAmountList = this.rechargeAmountList.map(item => {
          if (parseFloat(item.money) >= parseFloat(v.limit_fee)) {
            return {
              ...item,
              give_money: v.coupon_fee
            }
          } else {
            return item
          }
        })
      })
    },
    // 修改自定义金额
    changeRechargePrice(e) {
      console.log("changeRechargePrice", e);
      if(this.rechargeGetSettingsData.discount && this.rechargeGetSettingsData.discount.length){
        this.rechargeGetSettingsData.discount.map(v => {
          if (parseFloat(e.detail.value) >= parseFloat(v.limit_fee)) {
            this.customTip = v.coupon_fee
          }else{
            this.customTip = 0
          }
        })
      }
    },
    async getRechargeOrderCreate(price, type) {
      console.log("price", this.rechargeRateFee.rate_fee ,typeof this.rechargeRateFee.rate_fee);
      if (!this.radioPayinfoId) {
        this.isLoading = false
        return uni.$u.toast('此组织未开启钱包支付配置，联系管理员添加钱包类支付')
      }
      this.$showLoading({
        title: '充值中....',
        mask: true
      })
      var rateFee = 0
      var amount = 0
      if(this.rechargeRateFee.rate_fee < 0) {
        //百分比B的时候，要将手续费转成绝对值，并且金额不能加上手续费传给后台
        rateFee = this.numberMul(Math.abs(this.rechargeRateFee.rate_fee),100)
        amount = this.numberMul(price, 100)
      }else {
        //其他情况按照原来的规则进行
        rateFee = this.numberMul(this.rechargeRateFee.rate_fee,100)
        amount = plus(this.numberMul(price, 100),this.numberMul(this.rechargeRateFee.rate_fee,100))
      }
      let params = {
				rate_fee: rateFee,
        amount: amount, //手续费和充值金额相加
        payinfo_id: this.radioPayinfoId,
        wallet_id: this.walletData.wallet_id,
        // #ifdef H5
        return_url: getBaseUrl() + 'pages_bundle/recharge/recharge_status' // 支付完成跳转的地址针对location.href这种方式的充值
        // #endif
      }
      let originBackUrl = Cache.get('originBackUrl')
      if (originBackUrl) {
        params.backUrl = originBackUrl
      }
      if (type) {
        params.sign_type = type
      } else {
        delete params.sign_type
      } 
      getApiRechargeOrderCreate(params)
        .then(res => {
          this.tradeNo = ''
          
          if (res.code == 0) {
            this.isCustom = false
            this.resetCustomData()
            this.rechargeSettingShow = false

            this.tradeNo = res.data.trade_no
             // 成功的订单，已签约免密支付的会走这一步
            if (res.data.order_status === 'ORDER_SUCCESS') {
              uni.hideLoading()
              uni.showToast({
                title: '充值成功',
                icon: 'success',
                success: () => {
                  this.gotoRecharge()
                  this.isLoading = false
                }
              })
              return
            }
            console.log("getApiRechargeOrderCreate", res.data);
            // 如果是浦发代扣，并且有相同人员数据的 弹个窗告诉用户是否要继续签约
            let payWay = res.data?.payway || ''
            let payWaySub = res.data?.sub_payway || ''
            let extra = res.data.extra || {}
            let hasOtherAgreement = extra.has_other_agreement || false
            if (payWay === 'SPDBPay' && payWaySub === 'daikou' && hasOtherAgreement) {
              uni.hideLoading()
              this.personInfo = extra
              this.reSignPrice = price
              this.showResignDialog = true
              return
            }
            if (res.data.extra && res.data.extra.redirect) {
              this.isLoading = false
              uni.hideLoading()
              // 应农行说，部分ios无法直接通过location.href的方式打开缴费页面，以下做下农行缴费兼容处理
              if (this.platform === 'abc' && uni.$u.os() === 'ios' && res.data.payway === 'ABCPay' && res.data.sub_payway === 'jf' ) {
                abcJSBridgeReady(function(){
                  AlipayJSBridge.call('pushWindow', {
                    url: res.data.extra.redirect,		// 要打开的页面的url
                    param : {
                      closeCurrentWindow: true, // 可选参数，打开新窗口的同时，关闭当前窗口，默认false
                    }
                  })
                })
              } else {
                window.location.href = res.data.extra.redirect
              }
              return
            }
            // payway fastepay 农行，AliPay支付宝，WechatPay微信，sub_paypay不知道啥玩意，ShouqianbaPay收钱吧
            // sub_payway jsapi，h5，miniapp

            if (res.data.sub_payway == 'fastepay') {
              this.isLoading = false
              uni.hideLoading()
              window.location.href = res.data.extra.redirect
              // this.setAbcRechargeCodeUrl(res.data.extra.redirect)
              // if (store.getters.abcRechargeCodeUrl) this.$miRouter.push({
              // 	path: '/pages_bundle/recharge/abc_recharge_code',
              // 	query: {
              // 		tradeNo: res.data.trade_no,
              // 		type: 'ABC'
              // 	}
              // })
              return
            } else if (res.data.sub_payway == 'jsapi' || res.data.sub_payway === 'miniapp') {
              this.jsapiChooseWXPay(res.data.extra)
            } else if ((res.data.payway == 'AliPay' || res.data.payway == 'sub_paypay' || res.data.payway == 'ShouqianbaPay' || res.data.payway == 'WXYFPay') && res.data.sub_payway === 'h5') { // h5走location
              this.isLoading = false
              uni.hideLoading()
              // 手动设置下订单号，用于兼容支付宝h5支付
              // Cache.set('RECHARGETRADENO', this.tradeNo)
              window.location.href = res.data.extra
            } else if (res.data.payway == 'SPDBPay') { // 浦发银行充值
              // 浦发银行的通过form表单的方式进行提交数据
              const url = res.data.extra.url
              const params = res.data.extra
              delete params.url
              delete params.type // 后端自定义的自动，无需携带
              createForm(url, params)
            } else {
              this.isLoading = false
              uni.hideLoading()
              uni.$u.toast('暂不支持当前充值方式！等待工程师赶制中！')
            }
            // this.$miRouter.push({
            // path: '/pages_bundle/recharge/recharge_status',
            // query: {
            // 	money: this.money
            // }
            // })
            // uni.$u.toast('成功')
          } else {
            uni.hideLoading()
            this.isLoading = false
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          this.isLoading = false
          uni.hideLoading()
          this.resetCustomData()
          uni.$u.toast(err)
        })
    },
    // 拉起充值
    jsapiChooseWXPay(params) {
      let _this = this
      // #ifdef H5
        // 企业微信 和微信支付共用
        checkWxBridgeReady(params, function({res}) {
					console.log(99999, res)
          if (res.err_msg == 'get_brand_wcpay_request:ok') {
            // 使用以上方式判断前端返回,微信团队郑重提示：
            //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
            uni.hideLoading()
            uni.showToast({
              title: '充值成功',
              icon: 'success',
              success: () => {
                _this.isLoading = false
                _this.gotoRecharge()
              }
            })
          } else {
            uni.hideLoading()
            _this.resetDataBackFromCunstom()
            uni.showToast({
              title: '充值失败',
              icon: 'none',
              success: () => {
                _this.isLoading = false
              }
            })
          }
        })
      // #endif
      // #ifdef MP-WEIXIN || MP-ALIPAY
      payMpRequest(params).then(({res, provider}) => {
        uni.hideLoading()
        if (provider === 'alipay') { // 当为支付宝支付时需要额外判断状态码
          switch (res.resultCode) {
            case '9000': // 订单处理成功。
              uni.showToast({
                title: '充值成功',
                icon: 'success',
                success: () => {
                  _this.isLoading = false
                  _this.gotoRecharge()
                }
              })
              break;
            case '6001': // 用户中途取消
              this.isLoading = false
              this.resetDataBackFromCunstom()
              uni.showToast({
                title: '用户中途取消',
                icon: 'fail',
                success: () => {
                  _this.isLoading = false
                }
              })
              break;
            case '8000': // 正在处理中。支付结果未知（有可能已经支付成功）。
              // uni.showToast({
              //   title: '正在处理中',
              //   icon: 'fail'
              // })
              if (params.trade_no) {
                // this.$miRouter.push({
                //   path: '/pages_bundle/recharge/recharge_status',
                //   query: {
                //     trade_no: params.trade_no
                //   }
                // })
                uni.showToast({
                  title: '正在处理中',
                  icon: 'success',
                  success: () => {
                    _this.isLoading = false
                    _this.gotoRecharge(params.trade_no)
                  }
                })
              } else {
                uni.showToast({
                  title: '正在处理中',
                  icon: 'fail',
                  success: () => {
                    _this.isLoading = false
                  }
                })
              }
              break;
            case '6002': // 网络连接出错
            this.resetDataBackFromCunstom()
              uni.showToast({
                title: '网络连接出错',
                icon: 'fail',
                success: () => {
                  _this.isLoading = false
                }
              })
              break;
            case '6004': // 处理结果未知（有可能已经成功）
              // uni.showToast({
              //   title: '处理结果未知',
              //   icon: 'fail'
              // })
              uni.showToast({
                title: '正在处理中',
                icon: 'success',
                success: () => {
                  _this.isLoading = false
                  _this.gotoRecharge(params.trade_no)
                }
              })
              break;
            case '4': // 无权限调用
            this.resetDataBackFromCunstom()
              uni.showToast({
                title: '无权限调用',
                icon: 'fail',
                success: () => {
                  _this.isLoading = false
                }
              })
              break;
            default:
            _this.isLoading = false
            break;
          }
        } else {
          uni.showToast({
            title: '充值成功',
            icon: 'success',
            success: () => {
              _this.isLoading = false
              _this.gotoRecharge()
            }
          })
        }
      }).catch(({res, provider}) => {
        uni.hideLoading()
        uni.showToast({
          title: '充值失败',
          icon: 'none',
          success: () => {
            _this.isLoading = false
          }
        })
      })
      // #endif
    },
    async gotoRecharge(tradeNo, sleep) {
      // Cache.remove('RECHARGETRADENO')
      if(sleep) {
        await this.$sleep(1000)
      }
      this.isLoading = false
      uni.hideLoading()
      this.$miRouter.push({
        path: '/pages_bundle/recharge/recharge_status',
        query: {
          trade_no: tradeNo ? tradeNo : this.tradeNo
        }
      })
    },
    // 去选择钱包
    toSelectWalletFunc() {
      this.$miRouter.push('/pages_bundle/recharge/select_wallet')
    },

    // 确认自定义充值
   async confirmRechargeFunc(flag, type,currentPrice) {
      console.log("confirmRechargeFunc",flag,type, this.isLoading,this.disabledBtn);
      if (this.disabledBtn) return
      if (this.isLoading) return
      this.isLoading = true
      let price = ''
      if (type === 'custom') {
        this.isLoading = false
        if (!uni.$u.test.amount(this.rechargePrice)) {
          return uni.$u.toast('请输入正确的金额')
        }
        if(parseFloat(this.rechargeGetSettingsData.minimum_recharge_amount) > times(this.rechargePrice, 100)){
          return uni.$u.toast(`请输入最低充值金额${(this.rechargeGetSettingsData.minimum_recharge_amount / 100).toFixed(2)}`)
        }
        price = this.rechargePrice
         return await this.getPriceRateAndDiscount(price) 
      } else {
        price = this.rechargeAmountList[this.currentIndexClone].money
      }
      this.priceValue = price
      // if (type == 'custom' && (this.rechargeGetSettingsData.minimum_recharge_amount / 100).toFixed(2) > price) {
      //   this.isLoading = false
      //   return uni.$u.toast(`请输入最低充值金额${(this.rechargeGetSettingsData.minimum_recharge_amount / 100).toFixed(2)}`)
      // }
      this.isLoading = false
      // 检测支付方式是否为翼支付
			// 获取充值手续费金额  这个接口暂时不用，原来是点击的时候获取，现在是直接显示
     if(type === 'mode') {
        this.getRechargeOrderCreate(currentPrice)
        return
      }
      var childrenList = this.payInfoAllList[0]?this.payInfoAllList[0].childrenList:[]
      if (this.payInfoAllList && flag && (this.payInfoAllList.length > 1 || childrenList.length > 1)){
          return (this.rechargeSettingShow = true)
      }else
      if (this.payInfoAllList && flag && this.payInfoAllList.length === 1) {
        //如果是一个的时候直接拉起支付
        this.rechargeModeSelectData = this.payInfoAllList[0].childrenList[0] || []
        this.radioPayinfoId = this.payInfoAllList[0].childrenList[0].id ||''
        var key = this.numberMul(this.priceValue,100)
        this.$set(this.rechargeRateFee,'rate_fee',this.payInfoAllList[0].childrenList[0].rate_fee_data[key])
        var winFlag = await this.checkouWingPay(this.payInfoAllList[0].childrenList[0])
        console.log("winFlag 1111", winFlag);
        if (winFlag) {
          window.location.href = winFlag
          return
        }
        this.getRechargeOrderCreate(price)     
      }else{
        this.$u.toast("没有相关配置，请联系管理员")
      }
     
    },
    // 立即充值
    rechargeNowFunc(item, index) {
      // 加多个判断吧，防止部分手机不兼容css
      if (this.disabledBtn) return
      if(this.currentIndex == -1){
        this.resetDataBackFromCunstom()
      }
      this.resetPayInfoChecked()
      this.currentIndex = index
      this.currentIndexClone = index
      this.determinePrice = this.rechargeAmountList[this.currentIndex].money
      this.rechargePrice = ''
      this.customTip = ''
    },
    cancelRecharge() {
      this.isCustom = false
      this.isBackFromCustom = false
      this.currentIndex = 0
      this.currentIndexClone = 0
      this.determinePrice = this.rechargeAmountList[this.currentIndex].money
      this.rechargePrice = ''
      this.customTip = ''
      this.payInfoAllList = deepClone(this.payInfoCloneAllList)
    },
    showCustomDialog() {
      if (this.disabledBtn) return
      this.isCustom = true
    },
    /**
     * 单选按钮选中
     * @param {*} data 
     * @param {*} index 
     * @param {*} payIndex 
     */
    async radioRechargeChange(data,index,payIndex,rateFee) {
      console.log("radioRechargeChange",data,index, payIndex,rateFee,this.isBackFromCustom);   
      //先清除旧的，赋值新的
      this.resetPayInfoChecked()
      var oldList = deepClone(this.payInfoAllList)
      this.$set(oldList[payIndex].childrenList[index],'checked',true)
      this.$set(this,'payInfoAllList',oldList)
      console.log(" this.payInfoAllList", this.payInfoAllList);
      this.rechargeModeSelectData = data
      this.radioPayinfoId = this.payInfoAllList[payIndex].childrenList[index].id
      this.rechargeRateFee.rate_fee = rateFee
      var currentPrice = 0
      if(this.isBackFromCustom){
        currentPrice = this.rechargePrice
      }else{
        currentPrice = this.rechargeAmountList[this.currentIndexClone].money
      }
      var winFlag = await this.checkouWingPay(data)
      console.log("winFlag 2222", winFlag);
      if (winFlag) {
        window.location.href = winFlag
        return
      }
      //这里改成直接支付
      this.confirmRechargeFunc(false, 'mode',currentPrice)
    },
    /**
     * 组成一个新的树形
     * @param {*} oldList 
     */
    getPayInfoNewlistTree(oldList){
      var newNameList  = []
      var newList =[]
      if(Array.isArray(oldList)&&oldList.length>0){
        //拿去第一层的名字
        oldList.forEach(item=>{
          newNameList.push(item.payway_alias)
        })
        console.log("newNameList",newNameList);
        //名字去重一下
        newNameList = Array.from(new Set(newNameList))
        console.log("newNameList 去重后",newNameList);
        //重新组成树形的列表
        newNameList.forEach(subItem=>{
          var newItem = {
            name : subItem,
            childrenList : oldList.filter(resultItem=>{
                return  resultItem.payway_alias === subItem
            })
          }
          newList.push(newItem)
        })
      }
      console.log("newList",newList);
      return newList

    },
    /**
     * 重新组成一个新的payInfo 列表包含手续费跟赠送金额
     */
    setPayInfoListPrice(data){
      //将手续费和赠送金额加进去 20230503梓健改版 rate_fee_discount  新加字段存放每个支付方式的赠送金额和手续费
      var payInfosList = data.payinfos || []
      if(Array.isArray(payInfosList) && payInfosList.length > 0 ) {
        payInfosList.forEach(item => {
          var id = item.id 
          if(Reflect.has(item,"sub_payway_type") && item.sub_payway_type.length > 0) {
          item.sub_payway_alias =  item.sub_payway_alias + "-" + item.sub_payway_type
        }
          var findItem 
          //查找出来有的加进去
          if(Reflect.has(data,'rate_fee_discount') && data.rate_fee_discount.length > 0) {
            findItem = data.rate_fee_discount.find(subItem => {
              return subItem.payinfo_id === id
            })
            if(findItem){
              //赠送费金额转换成元
              if(Array.isArray(findItem.discount) && findItem.discount.length > 0) {
                findItem.discount[0].coupon_fee = divide(findItem.discount[0].coupon_fee)
                findItem.discount[0].limit_fee = divide(findItem.discount[0].limit_fee)
              }
              //手续费转换成元
              if(Reflect.has(findItem,"rate_fee_data") && Object.keys(findItem.rate_fee_data).length > 0) { 
                var  newRateList = {}
                  for(let key in findItem.rate_fee_data) {
                    newRateList[key] = divide(findItem.rate_fee_data[key])
                  }
                  item.rate_fee_data = newRateList
              }
              item.discount = findItem.discount || []
            }
          } 
        })
      }
      console.log("payInfosList",payInfosList);
      return payInfosList 
    },
    /**
     * 获取自定义金额的服务费和赠送金额
     */
    getPriceRateAndDiscount(price) {
      var payInfoList = deepClone(this.payInfoCloneAllList)
      if(payInfoList.length <=0) {
         this.$u.toast("没有相关配置，请联系管理员")
        return 
      }
      this.$showLoading({
        title: '请稍后....',
        mask: true
      })
      // 组参
      var params = {
        money : this.numberMul(price,100),
        org_id : this.walletData.org_id,
        wallet_id : this.walletData.wallet_id
      }
      //获取自定义金额的服务费和赠送金额
       apibookingRechargeGetRechargeRateDiscountPost(params).then(res => {
        console.log("res", res);
        uni.hideLoading()
        var resultData = res.data || []
        //重新组参
        payInfoList.forEach(item => {
            if(item.childrenList && item.childrenList.length > 0) {
              item.childrenList.forEach(subItem => {
                 var id = subItem.id 
                 var findItem =  resultData.find(resultItem => {
                    return resultItem.payinfo_id === id
                 })
                 if(findItem){
                  //赠送费金额转换成元
                  if(Array.isArray(findItem.discount) && findItem.discount.length > 0) {
                  findItem.discount[0].coupon_fee = divide(findItem.discount[0].coupon_fee)
                  findItem.discount[0].limit_fee = divide(findItem.discount[0].limit_fee)
                  }
                  subItem.discount = findItem.discount || []
                  //手续费转换成元
                  var  newRateList = {}
                  for(let key in findItem.rate_fee_data) {
                    newRateList[key] = divide(findItem.rate_fee_data[key])
                  }
                  subItem.rate_fee_data = newRateList
                 }
              })
            }
        })
        //重新赋值
        this.payInfoAllList = deepClone(payInfoList)
        console.log("this.payInfoAllList",this.payInfoAllList);
        this.currentIndex = -1
        this.currentIndexClone = 0
        this.determinePrice = ""
        //显示弹窗
        this.isCustom = false
        this.isBackFromCustom = true
        var childrenList = this.payInfoAllList[0]?this.payInfoAllList[0].childrenList:[]
        if(this.payInfoAllList && (this.payInfoAllList.length > 1  || childrenList.length > 1)){
          this.rechargeSettingShow = true 
        }
        //如果只有一个

        if(this.payInfoAllList.length ===1 && childrenList.length === 1){
          this.rechargeModeSelectData = this.payInfoAllList[0].childrenList[0] || []
          this.radioPayinfoId = this.payInfoAllList[0].childrenList[0].id ||''
          var key = this.numberMul(this.rechargePrice,100)
          this.$set(this.rechargeRateFee,'rate_fee',this.payInfoAllList[0].childrenList[0].rate_fee_data[key])
          this.getRechargeOrderCreate(this.rechargePrice)    
        }
        console.log("this.isCustom ",this.isCustom , this.isBackFromCustom);
       }).catch(error => {
        console.log("error", error);
        uni.hideLoading()
        this.$u.toast(error.message)
       })

    },
    /**
     * 关闭弹窗
     */
    closePopSettingShow(){
      this.rechargeSettingShow = false
      this.resetPayInfoChecked()
      if(this.isBackFromCustom){
        this.isCustom = true 
      }
    },
    /**
     * 重置一下基础数据
     */
    resetDataBackFromCunstom(){
      this.cancelRecharge()
      
    },
    /**
     * 重置选中
     */
    resetPayInfoChecked(){
      this.payInfoAllList.forEach(item => {
        if(item.childrenList){
          item.childrenList.forEach(subItem=> {
            subItem.checked = false 
          })
        }
      })
    },
    /**
     * 充值自定义金额数据
     */
    resetCustomData(){
      this.customTip = ''
      this.rechargePrice = ''
    },
    /**
     * 显示赠送金额
     * @param {*} item 
     */
    showCouponFee(item,flag) {
      var isShow = false 
      if(Reflect.has(item,"discount")&& Array.isArray(item.discount)&& item.discount.length > 0) {
        var price =  item.discount[0].coupon_fee || 0
        var limitFee = item.discount[0].limit_fee || 0
        var currentPrice =Number(this.isBackFromCustom ? this.rechargePrice : this.determinePrice) 
        console.log("currentPrice", currentPrice ,limitFee);
        isShow = (price > 0 && currentPrice >= limitFee)
       return  flag?isShow:price
      }
      
    },
    /**
     * 显示税率
     * @param {*} item 
     */
    showRateFee(item,flag) {
      var price  = 0
      var isShow = false 
      if(Reflect.has(item,"rate_fee_data") && Object.keys(item.rate_fee_data).length > 0) {
        var key
         if(this.isBackFromCustom){
          key = this.numberMul(this.rechargePrice,100)
         }else{
          key = this.rechargeAmountList[this.currentIndexClone].money
          key = this.numberMul(key,100)
         }
         price = item.rate_fee_data[key] || 0
         console.log("showRateFee",item.id,key,price);
         
         isShow = price != 0 
         return  flag?isShow:price
      }
    },
    /**
     * 取绝对值
     */
    mathAbsFee(price){
      return Math.abs(price)
    },
    // 检查翼支付
    checkouWingPay(itemData) {
      console.log("checkouWingPay", itemData);
      return new Promise((resolve) => {
        if (itemData) {
          var payway = itemData.payway || ''
          var subPayway = itemData.sub_payway || ''
          console.log("checkouWingPay payway", payway, subPayway);
          if (payway === 'WingPay' && subPayway === "daikou") {
            // 是翼支付并且是代扣模式要检测是否签约，没有签约先跳转签约
            var params = {
              payinfo_id: itemData.id
            }
            apiWingPayCheckSign(params).then(res => {
              console.log("res", res);
              if (res && res.code === 0) {
                var data = res.data || {}
                var url = data.url || ''
                resolve(url)
              } else {
                resolve(false)
              }
            }).catch(error => {
              console.log("error", error);
              resolve(false)
            })
          } else {
            resolve(false)
          }
        }else {
          resolve(false)
        }
      })
    },
    // 关闭签约弹窗
    closeResignDialog() {
      this.showResignDialog = false
    },
    // 重新签约和其他银行卡号签约
    onNextSign(type, price) {
      this.showResignDialog = false
      this.getRechargeOrderCreate(price, type)
    }
  },
  // Methods End
  onShow() {
   this.walletData = Cache.get('walletData')
   // this.walletData = store.getters.wallet
   if (this.walletData.org_id) {
		 this.rechargeSettingShow = false // 需要关闭一下支付方式弹框，因为show的时候不会调手续费接口了 调用手续费接口需要传金额，打开弹窗的时候才计算金额的
     this.currentIndex = 0
     this.isBackFromCustom = false
     this.getRechargeGetSettings()
   }
   this.floatingPopupShow = !this.floatingPopupShow
  },
  // Life Cycle Start
  async onLoad(options) {
    // #ifdef H5
    if (this.platform === 'wechat') {
			jweixin = require('jweixin-module')
		}else if(this.platform === 'wxwork'){
			//企业微信
			jweixin = require('weixin-jweixin').default
		}
    if (this.platform === 'wechat' || this.platform === 'wxwork') {
			// 因为企业微信需要初始化
      this.getWechatCongfigGet()
    }
    // 支付宝h5支付成功后跳回来会带上out_trade_no的，但不用它来做判断，万一那套他们改了呢
    // let tradeNo = Cache.get('RECHARGETRADENO')
    if (this.$Route.query.out_trade_no) {
      this.$showLoading({
        title: '支付中....',
        mask: true
      })
      this.gotoRecharge(this.$Route.query.out_trade_no, true)
      return
    }
    // #endif
    
    // 判断是否显示评价弹窗
    const moduleKey = this.satisfactionForm.module_key // key
    // const today = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd') // 获取当天时间
    // const isModuleDay = getStorage(moduleKey) // 获取当天弹窗时间
    const result = await this._appGetSatisfaction(moduleKey) // 获取记录
    if(result) {
      // if(isModuleDay && isModuleDay === today) { // 一天内只弹一次
      //   this.sModalVisible = false
      // } else { // 主动弹窗
      //   setStorage({name: moduleKey, content:today}) // 记录当天弹窗时间
      //   this.sModalVisible = true
      // }
      this.isShowStatisfaction = true
      this.statisfactionTips = result.tips
      this.tags = result.tags
      
      this.satisfactionForm.module_key = result.module_key
      // this.satisfactionForm.is_satisfied = result.is_satisfied
      this.satisfactionForm.reason = result.reason
    }
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.recharge {
  padding: 20rpx 40rpx;

  .ls-card {
    border-radius: 20rpx;
    background-color: #ffffff;
  }

  .header {
    .bb {
      border-bottom: 1px solid #ededed;
    }

    > view {
      padding: 20rpx 30rpx;
    }
  }

  .section {
    padding: 40rpx 30rpx;

    .recharge-template {
      padding-bottom: 30rpx;
      display: flex;
      flex-wrap: wrap;

      // 当前选择
      .active {
        color: #ffffff !important;
        background-color: $color-primary !important;

        .desc {
          color: #ffffff !important;
        }
      }
      .disabled{
        opacity: .5;
        pointer-events: none;
      }

      .template:nth-child(3n) {
        margin-right: 0;
      }

      .template {
        width: 190rpx;
        height: 165rpx;
        margin-right: 16rpx;
        margin-bottom: 16rpx;
        border-radius: 20rpx;
        background-color: $background-color;

        .money {
          font-size: 40rpx;
          font-weight: bold;
        }

        .desc {
          color: $color-text-secondary;
          font-weight: bold;
        }
      }
    }

    .recharge-btn {
      height: 84rpx;
      line-height: 84rpx;
      border-radius: 40rpx;
      background-color: $color-primary;
    }
  }

  .radio-box {
    // display: flex;
    // justify-content: space-between;
  }

  // 自定义金额输入框
  .custom-money {
    width: 500rpx;
    padding: 20rpx;
    background: rgba(228, 228, 228, 0.4);
    border-radius: 20rpx;
  }

  .template-pay{
    height: 130rpx;
    margin: 10rpx !important ;
    padding:0 40rpx;
    border-radius:  8rpx;
    border: solid 1px #e0e0e0;
  }
  .template-active{
    color: $color-primary !important;
    border: solid 1px $color-primary;
  }

  ::v-deep .u-icon__icon {
    font-size: 40rpx !important;
  }
}



</style>
