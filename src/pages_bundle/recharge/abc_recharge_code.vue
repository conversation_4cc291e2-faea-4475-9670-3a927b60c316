<template>
  <view :style="theme.style" id="my-qrcode">
    <view class="qrcode-box">
      <view class="qrcode-logo">
        <text>朴食健康食堂</text>
        <!-- <image class="qrcode-img" src="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/mini/9ee217baf244094d97ae0cfacb900dc71626493947894.png"></image> -->
      </view>
      <view class="qrcode-content">
        <view class="code" @click="clickQrcode">
          <uqrcode ref="uQRCode" :text="src" :size="185" />
        </view>
        <view class="code-title">点击二维码长按识别跳转掌银APP充值</view>
      </view>
    </view>
  </view>
</template>
<script>
import { getApiRechargeOrderQuery } from '@/api/user.js'
import store from '@/store'
export default {
  data() {
    return {
      src: '',
      type: '',
      tradeNo: '',
      timer: null
    }
  },
  onLoad(option) {},
  mounted() {},
  onShow() {
    if (store.getters.abcRechargeCodeUrl) {
      this.src = store.getters.abcRechargeCodeUrl
    } else {
      // uni.redirectTo({
      //   url: '/pages_bundle/recharge/recharge'
      // })
    }
    if (this.$Route.query.type) {
      this.type = this.$Route.query.type
    }
    if (this.$Route.query.tradeNo) {
      let _this = this
      this.tradeNo = this.$Route.query.tradeNo
      // 每隔五秒请求一遍 查询
      this.timer = setInterval(() => {
        this.funTimer()
      }, 5000)
    }
  },
  methods: {
    funTimer() {
      // setTimeout(() => {
      this.getRechargeOrderQuery(this.tradeNo)
      // 如需要停止定时器，只需加入以下：
      // }, 0)
    },
    getRechargeOrderQuery(tradeNo) {
      getApiRechargeOrderQuery({
        trade_no: tradeNo
      })
        .then(res => {
          if (res.code == 0) {
            // this.$once('hook:beforeDestroy', () => {
            //     clearInterval(timer);
            // })
            if (res.data.order_status === 'ORDER_SUCCESS') {
              clearInterval(this.timer)
              this.$miRouter.replace('/pages_bundle/recharge/recharge_status')
            }
            // uni.$u.toast('成功')
            // this.$miRouter.push('/pages_bundle/recharge/recharge_status')
          } else {
            uni.$u.toast(res.msg)
            clearInterval(this.timer)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    clickQrcode(e) {
      this.$refs.uQRCode.toTempFilePath({
        success: res => {
          uni.previewImage({
            current: res.tempFilePath,
            urls: [res.tempFilePath]
          })
        }
      })
    }
  },
  onUnload() {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss" scoped>
#my-qrcode {
  min-height: 100vh;
  background-color: $color-primary-light-3;

  .qrcode-box {
    padding: 40rpx;
    border-radius: 20px;
    overflow: hidden;

    .qrcode-logo {
      background-color: $color-primary-light-9;
      border-radius: 20rpx 20rpx 0rpx 0rpx;
      text-align: center;
      height: 96rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .qrcode-img {
        display: inline-block;
        width: 106rpx;
        height: 69rpx;
      }
    }

    .qrcode-content {
      padding: 75rpx 0;
      background-color: #ffffff;
      border-radius: 0 0 20rpx 20rpx;

      .code {
        display: flex;
        justify-content: center;
      }

      .code-title {
        font-family: MicrosoftYaHei;
        font-size: 30rpx;
        line-height: 24rpx;
        letter-spacing: 2rpx;
        color: #7c7c7c;
        text-align: center;
        padding: 40rpx 0 0 0rpx;
      }
    }
  }
}
</style>
