<template>
	<view :style="theme.style" class="appointment-order flex flex-col bg-white">
		<view class="flex-1" style="min-height: 0">
			<appoint-wrap
				ref="intentionFoodWrapRef"
				entranceType="intentionMenu"
				title="意向菜谱"
				:calendarDisabled="true"
				v-model="foodLists"
				:loading="loading"
				:mealTypeInfo="mealTypeInfoData"
				:nowMealType="nowMealTypeData"
				@selectfood="selectFoodHandle"
				@click="handleChange"
			>
				<template v-if="selectFood.food_data && selectFood.food_data.length">
					<view
						class="meal-goods-item"
						:id="`category-item-${cfood.category_id}`"
						v-for="(cfood, k) in selectFood.food_data"
						:key="k"
					>
						<view class="title xs m-b-20 p-t-20">{{ cfood.category }}</view>
						<view class="goods-lists" v-if="cfood.food.length">
							<view class="goods-item flex col-center m-b-30" v-for="(item, index) in cfood.food" :key="index">
								<u-image width="90rpx" height="90rpx" :src="item.image? item.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"></u-image>
								<view class="flex col-center row-between flex-1 m-l-20">
									<view class="goods-name line-1 black f-w-500">{{ item.food_name }}</view>
									<view class="flex row-between m-t-15">
										<image
											@click="like(item)"
											class="fabulous"
											:class="{ activeShake: item.is_like }"
											:src="item.is_like ? imgPath.img_fabulous : imgPath.img_un_fabulous"
										></image>
									</view>
								</view>
							</view>
						</view>
					</view>
				</template>
				<!-- 单个餐段 -->
				<view v-else class="flex flex-col col-center row-center">
					<image class="image-null" :src="themeImgPath.img_diancan_null"></image>
					<text class="muted m-t-40">当前餐段无菜谱查看</text>
				</view>
				<!-- 整个内容 -->
				<view slot="empty" class="flex flex-col col-center row-center">
					<image class="image-null" :src="themeImgPath.img_diancan_null"></image>
					<text class="muted m-t-40">当前餐段无菜谱查看</text>
				</view>
			</appoint-wrap>
		</view>

		<!-- <view v-show="bigFabulous" class="big-fabulous"> -->
		<u-transition :show="bigFabulous" class="big-fabulous" :customStyle="{ background: 'none' }" mode="fade" :duration="400">
			<!-- <image class="big-fabulous" :class="{ activeShake: fabulous }" src="themeImgPath.img_fabulous"></image> -->
			<image class="big-fabulous" :class="{ activeShake: fabulous }" :src="themeImgPath.img_fabulous"></image>
		</u-transition>
		<!-- </view> -->
		<!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
	</view>
</template>

<script>
import Cache from '@/utils/cache'
import {
	getApiBookingIntentFoodList,
	getApiBookingIntentFoodLikeFood,
	getApiBookingIntentFoodCancelLikeFood
} from '@/api/intent_food'
import { getApiUserReservationMealType } from '@/api/reservation'
import AppointWrap from '../components/appoint-wrap/appoint-wrap'
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'

export default {
	components: { AppointWrap, FloatingPopup },
	data() {
		return {
			imgPath: this.$imgPath,
			bigFabulous: false,
			fabulous: false,
			loading: false,
			nowMealTypeData: {},
			mealTypeInfoData: [],
			selectFood: {},
			foodLists: [],
			params: {},
			changeOption: null,
			floatingPopupShow: false
		}
	},
	watch: {
		fabulous(val) {
			if (val == true) {
				this.bigFabulous = true
				setTimeout(() => ((this.bigFabulous = false), (this.fabulous = false)), 400)
			}
		}
	},
	methods: {
		selectFoodHandle(value) {
			this.selectFood = value
		},
		// 获取菜品
		getFood() {
			this.loading = true
			return new Promise((reslove, reject) => {
				getApiBookingIntentFoodList(this.params)
					.then(res => {
						if (res.code == 0) {
							// 因为是拿预约点餐的列表
							// 只有一个组织
							if (Object.keys(res.data.results) && Object.keys(res.data.results).length) {
								let results = [{ open: true, ...res.data.results }]
								this.foodLists = results
							} else {
								this.foodLists = []
							}
						} else {
							uni.$u.toast(res.msg)
						}
						reslove()
					})
					.finally(error => {
						this.loading = false
						uni.stopPullDownRefresh()
					})
			})
		},
		async handleChange(event) {
			if (event.type == 'meal_type') {
				if (!event.value) return
				this.params[event.type] = event.value
			} else {
				if (!event.value.fulldate) return
				this.params[event.type] = event.value.fulldate
			}
			// await this.getFood()
			this.changeOption = event
			// 有餐段才去请求数据
			if (this.mealTypeInfoData.length) {
				this.getFood()
			}
		},
		like(item) {
			let params = {
				menu_food_id: item.menu_info.menu_food_id,
				menu_food_object: item.menu_info.menu_food_object,
				food_id: item.food_id
			}
			// 如果已经点赞，就调取消按钮
			if (item.is_like) {
				// 取消点赞
				this.getIntentFoodCancelLikeFood(params)
			} else {
				// 点赞
				this.getIntentFoodLikeFood(params)
			}
			// 取消点赞
			item.is_like = !item.is_like
			this.fabulous = item.is_like
		},
		getUserReservationMealType() {
			this.mealTypeInfoData = []
			getApiUserReservationMealType({
				org_id: this.params.org_id
			})
				.then(async res => {
					if (res.code == 0) {
						// 好像为了去默认传值
						if (res.data.now_meal_type.meal_type) {
							// 防止没用值的情况出现报错
							this.params.meal_type = res.data.now_meal_type.meal_type
							this.nowMealTypeData = res.data.now_meal_type
						} else {
							if (res.data.meal_type_info.length) {
								this.params.meal_type = res.data.meal_type_info[0].meal_type
								this.nowMealTypeData = res.data.meal_type_info[0]
							}
						}
						this.$nextTick(_ => {
							// 初始化当前餐段
							this.$refs.intentionFoodWrapRef.initMealType()
						})
						this.mealTypeInfoData = res.data.meal_type_info
						if (this.changeOption) {
							this.getFood()
						}
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err)
				})
		},
		// 点赞
		getIntentFoodLikeFood(params) {
			getApiBookingIntentFoodLikeFood(params)
				.then(async res => {
					if (res.code == 0) {
						// console.log(res.data)
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err)
				})
		},
		// 取消点赞
		getIntentFoodCancelLikeFood(params) {
			getApiBookingIntentFoodCancelLikeFood(params)
				.then(async res => {
					if (res.code == 0) {
						// console.log(res.data)
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err)
				})
		},
		getInitParams() {
			const userInfo = Cache.get('userInfo')
			const state = this.$store.state.appoint
			this.params.person_no = userInfo.person_no
			this.params.meal_type = this.nowMealTypeData.meal_type
			this.params.org_id = state.select.intent_org_id.id
			this.getUserReservationMealType()
		}
	},

	onLoad() {
		this.getInitParams()
	},
	onShow() {
		this.floatingPopupShow = !this.floatingPopupShow
	}
}
</script>

<style lang="scss" scoped>
.appointment-order {
	height: calc(100vh - env(safe-area-inset-bottom));

	.goods-name {
		width: 340rpx;
	}

	.fabulous {
		width: 54rpx;
		height: 54rpx;
	}

	.big-fabulous {
		position: fixed;
		top: 50%;
		left: 50%;
		margin-left: -150rpx;
		margin-top: -150rpx;
		width: 300rpx;
		height: 300rpx;
		background: none;
	}

	.activeShake {
		animation: shake 0.5s linear;
	}

	@keyframes shake {
		0% {
			transform: scale3d(1, 1, 1) rotate3d(0, 0, 1, 0eg);
		}
		25% {
			transform: scale3d(1.3, 1.3, 1.3) rotate3d(0, 0, 1, -10deg);
		}
		50% {
			transform: scale3d(1.3, 1.3, 1.5) rotate3d(0, 0, 1, -20deg);
		}
		75% {
			transform: scale3d(1.1, 1.1, 1.3) rotate3d(0, 0, 1, -10deg);
		}
		100% {
			transform: scale3d(1, 1, 1);
		}
	}
	.image-null {
		width: 240rpx;
		height: 176rpx;
		margin: 170rpx auto 0;
	}
}
</style>
