<!-- 选择食堂 -->
<template>
	<view :style="theme.style" class="choice-canteen">
		<!-- #ifndef MP-ALIPAY -->
		<u-navbar bg-color="transparent" left-icon-color="#fff" auto-back>
			<view class="white lg f-w-500" slot="center">选择组织</view>
		</u-navbar>
		<!-- #endif -->
		<!-- #ifdef MP-ALIPAY -->
		<!-- ALIPAY给个占位吧 -->
		<view class="white lg f-w-500" style="height: 46rpx;"></view>
		<!-- #endif -->
		<view class="choice-title p-b-20">请选择组织</view>
		<!-- 各大食堂 -->
		<view class="" v-for="(item, index) in orgList">
			<view class="flex row-between flex-center choice-canteen-item" @click="gotoshop(item)">
				<view class="flex">
					<text class="img-filter">
						<u-image width="40rpx" height="40rpx" :src="themeImgPath.img_bundle_icon_canteen"></u-image>
					</text>
					<view class="m-l-18 md f-w-500">{{ item.org_name }}</view>
				</view>
				<u-icon color="#999999" name="arrow-right" size="24rpx"></u-icon>
			</view>
		</view>
	</view>
</template>

<script>
import { getApiBookingIntentFoodOrgList } from '@/api/intent_food'
import Cache from '@/utils/cache'
import { mapMutations } from 'vuex'

export default {
	data() {
		return {
			imgPath: this.$imgPath,
			userinfo: {},
			orgList: []
		}
	},
	onLoad(option) {},
	onShow() {
		this.userinfo = Cache.get('userInfo')
		this.getIntentFoodOrgList()
	},

	methods: {
		...mapMutations(['SET_SELECT']),
		// 获取食堂组织
		getIntentFoodOrgList() {
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
			getApiBookingIntentFoodOrgList()
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.orgList = res.data
					} else {
						uni.hideLoading()
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},

		gotoshop(item) {
			// if (this.$store.state.appoint.select.cupboard && this.$store.state.appoint.select.cupboard.id) {
			// 	let cupboard = this.$store.state.appoint.select.cupboard
			// 	if (cupboard.ceil > 0) {
			// 		this.cupboardId = cupboard.id
			// 		this.cupboardName = cupboard.addr
			// 	}
			// }
			this.SET_SELECT({
				key: 'intent_org_id',
				data: item
			})
			// if (this.$Route.query.mode == 'back') {
			//   this.$miRouter.back()
			//   return
			// }
			this.$miRouter.replace({
				path: '/pages_bundle/intention_food/intention_food'
			})
		}
	}
}
</script>

<style lang="scss">
.choice-canteen {
	// background-image: linear-gradient(90deg, #a0fecf 0%, #12e294 0%, #12e294 0%, #12e2be 100%, #12e2be 100%);
	background-image: $bg-linear-gradient-2;
	background-size: 750rpx 308rpx;
	background-repeat: no-repeat;

	.choice-title {
		margin: 0 0 40rpx 70rpx;
		padding-top: 40rpx;
		font-size: 36rpx;
		font-weight: bold;
		color: #ffffff;
		line-height: 36rpx;
	}

	.choice-canteen-item {
		margin: 0 40rpx 20rpx 40rpx;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		background: #ffffff;
	}
}
</style>
