
 import { deepClone, divide } from "../../utils/util";
	/**
		 * 分日期去校验配送费
		 * @param list 配送列表,包含日期，餐段等数据
		 */
  export function verificationOrderData(list,params,verificationData) {
    console.log("verificationOrder", list);
    console.log("this.params.take_meal_type", params.take_meal_type);
    if(!list || list.length <= 0) {
      return false
    }
    if(params.take_meal_type === "cupboard" || params.take_meal_type === "waimai") {
      //初始化餐段 数据 
      var verificationFlag = true  //是否符合，默认是符合
      var messageError = ''   //错误提示
      var date = ''  // 日期
      var resultData = { isVerificationOrder: true , verificationOrderError: "" , isVerificationOrderFlag: true } //结果数据

      for (let i = 0; i < list.length; i++) {
        messageError = list[i].date
        date =   list[i].date
        var verificationDataList = []

        if(Reflect.has(verificationData,date)) {
          verificationDataList = deepClone(verificationData[date])
        }
        var subList =  list[i].stall

        if(Array.isArray(subList) && subList.length>0) {
          for (let j = 0; j < subList.length; j++) {
              var subPrice = subList[j].one_stall_fee //此单的费用
              var stallId =  subList[j].stall_id //消费点
              var mealType = subList[j].meal_type //餐段
              var price = 0
              console.log("verificationDataList", verificationDataList);
              var findItem = verificationDataList.find(verItem => {
                  return verItem.stallId === stallId && verItem.mealType === mealType
              })
              console.log("findItem",findItem);
              if(findItem){
                price = params.take_meal_type === 'cupboard'?findItem.cupboardDeliveryFee : findItem.waimaiDeliveryFee
              }
              console.log("subPrice", subPrice, price);
              //如果该餐段的钱小于起配费，就置灰按钮，如果用户点击要提示它餐段不符合
              if(subPrice<price) {
                verificationFlag = false 
                messageError = messageError +" 在 "+subList[j].stall_name + " " + subList[j].meal_type_alias + "点餐金额小于起送费：¥" + divide(price) + ",请重新选择！"
                break;
              }	
          }
        }
        //如果此时的餐段已经不符合了。就不用循环了，直接退出了
        if(!verificationFlag) {
          break
        }	
      }
      resultData.isVerificationOrder = verificationFlag
      resultData.verificationOrderError = messageError
      resultData.isVerificationOrderFlag = changeBtnFlag(list, params, verificationData)
      console.log("this.isVerificationOrder", verificationFlag, messageError);
      return resultData
    }else {
      resultData.isVerificationOrder = true
      resultData.verificationOrderError = ''
      resultData.isVerificationOrderFlag = true
      return resultData
    }
  }


  /**
   * 为了改Btn的颜色，遍历第二次，我服了
   * @param {*} list 
   */
  export function changeBtnFlag(list,params,verificationData) {
    var verificationFlagBtn = false //是否按钮显示下一步，默认是否 
    if(Array.isArray(list)&&list.length>0) {
      list.forEach(item=>{
        var date =  item.date
        var verificationDataList = []

        if(Reflect.has(verificationData,date)){
          verificationDataList = deepClone(verificationData[date])
        }

        if(Array.isArray(item.stall) && item.stall.length>0) {
           item.stall.forEach(subItem => {
             var subPrice = subItem.one_stall_fee
             var stallId =  subItem.stall_id //消费点
             var mealType = subItem.meal_type //餐段
             var price = 0
             var findItem = verificationDataList.find(verItem => {
                  return verItem.stallId === stallId && verItem.mealType === mealType
              })
              if(findItem) {
                price = params.take_meal_type === 'cupboard'?findItem.cupboardDeliveryFee : findItem.waimaiDeliveryFee
              }
              if(subPrice >= price) {
                verificationFlagBtn = true 
              }	
           })
        }
      })
    }
    return verificationFlagBtn
  }

  /**
   * 格式化按钮名称
   * @returns 
   */
  export function formateBtnText(params,isVerificationOrderFlag,cupboardDeliveryFee,waimaiDeliveryFee) {
    let text     
      if((params.take_meal_type === "cupboard" || params.take_meal_type === "waimai")) {
        if(isVerificationOrderFlag) {
          text = '下一步'
         }else {
          let price = params.take_meal_type === 'cupboard' ? cupboardDeliveryFee: waimaiDeliveryFee
          text = '满￥' + (price / 100).toFixed(2) + '起送'
        }
      }else {
        text = '下一步'
      } 
    return text
  }
