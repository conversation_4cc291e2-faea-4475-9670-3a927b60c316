<template>
	<view class="food-reasonable-collocation">
		<!-- 营养推荐 -->
		<view class="p-20" style="background-color: #f0f3f5;" v-for="(item, index) in nutrientList" :key="index">
			<view class="flex">
				<view :style="{ width: '170rpx', color: item.color }">{{ item.name }}</view>
				<view class="p-l-20">
					<text class="dot m-r-10" :style="{ backgroundColor: item.color }"></text>
					<text class="muted">碳水</text>
					<text :style="{ color: nutrientRecommend(item) ? 'red' : '' }">{{ item.carbohydrate }}g</text>
				</view>
				<view class="p-l-20">
					<text class="dot m-r-10" :style="{ backgroundColor: item.color }"></text>
					<text class="muted">蛋白质</text>
					<text :style="{ color: nutrientRecommend(item) ? 'red' : '' }">{{ item.protein }}g</text>
				</view>
				<view class="p-l-20">
					<text class="dot m-r-10" :style="{ backgroundColor: item.color }"></text>
					<text class="muted">脂肪</text>
					<text :style="{ color: nutrientRecommend(item) ? 'red' : '' }">{{ item.axunge }}g</text>
				</view>
			</view>
		</view>
		<view class="food-content">
			<!-- 	<view class="muted flex row-between food-title">
				<view class="">当前已点</view>
				<text class="icon iconfont icon-tihuan" style="font-size: 30rpx;"></text>
				<view class="">替换为</view>
			</view> -->
			<view class="flex">
				<view class="food-left">
					<view class="text-center food-title muted">当前已点</view>
					<scroll-view class="scroll-height" :scroll-y="true">
						<view
							:class="{ 'active-bg-white': foodIndex == shopCardFoodIndex }"
							class="p-l-20 p-r-20"
							v-for="(shopCardFoodItem, shopCardFoodIndex) in shopCardFood"
							:key="shopCardFoodIndex"
							@click="mealFood(shopCardFoodIndex)"
						>
							<view class="flex p-t-20">
								<u-image :src="shopCardFoodItem.image" width="60px" height="60px"></u-image>
								<view class="p-l-10 p-t-10">
									<view class="p-b-6">{{ shopCardFoodItem.name }}</view>
									<view class="mini">{{ shopCardFoodItem.nutrition_info.energy_kcal }}千卡</view>
								</view>
							</view>
							<view class="mini p-t-15 p-b-8 muted">
								<text class="p-r-15">碳水{{ shopCardFoodItem.nutrition_info.carbohydrate }}g</text>
								<text class="p-r-15">蛋白质{{ shopCardFoodItem.nutrition_info.protein }}g</text>
								<text class="p-r-15">脂肪{{ shopCardFoodItem.nutrition_info.axunge }}g</text>
							</view>
							<view class="">
								<price-format class="p-r-20" :price="shopCardFoodItem.discount" :size="30" color="#FF5757"></price-format>
								<price-format :price="shopCardFoodItem.price" :size="27" color="#c3c7ca" :lineThrough="true"></price-format>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="food-right">
					<view class="text-center food-title muted">替换为</view>
					<view class="p-l-20 p-r-20">
						<scroll-view class="scroll-height" :scroll-y="true">
							<view class="" v-for="(matchFoodItem, matchFoodIndex) in matchFoodList" :key="matchFoodIndex">
								<view class="flex p-t-20">
									<u-image :src="matchFoodItem.image" width="60px" height="60px"></u-image>
									<view class="p-l-10 p-t-10" style="width: 100%;">
										<view class="p-b-6">{{ matchFoodItem.name }}</view>
										<view class="mini">{{ matchFoodItem.nutrition_info.energy_kcal }}千卡</view>
										<view class="mini flex row-between">
											<text></text>
											<view
												v-if="funSpecsShow(matchFoodItem)"
												class=""
												style="position: relative;width: 100rpx;"
												@click.stop="openSpecsDialog(matchFoodItem, matchFoodIndex)"
											>
												<u-button
													text="选规格"
													type="primary"
													:color="variables.colorPrimary"
													:customStyle="customBtnStyle"
													size="mini"
												></u-button>
											</view>
											<view class="" v-else>
												<u-checkbox-group
													v-model="foodChekboxValue"
													placement="column"
													@change="foodChekboxReplace(matchFoodItem)"
												>
													<u-checkbox :active-color="variables.colorPrimary" :name="matchFoodItem.id"></u-checkbox>
												</u-checkbox-group>
											</view>
										</view>
									</view>
								</view>
								<view class="mini p-t-15 p-b-8 muted">
									<text class="p-r-15">碳水{{ matchFoodItem.nutrition_info.carbohydrate }}g</text>
									<text class="p-r-15">蛋白质{{ matchFoodItem.nutrition_info.protein }}g</text>
									<text class="p-r-15">脂肪{{ matchFoodItem.nutrition_info.axunge }}g</text>
								</view>
								<view class="">
									<price-format class="p-r-20" :price="matchFoodItem.price" :size="30" color="#FF5757"></price-format>
									<price-format :price="matchFoodItem.discount" :size="27" color="#c3c7ca" :lineThrough="true"></price-format>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>

		<view class="food-foot-box">
			<view class="food-foot-btn" @click="replaceClick">
				<u-button text="替换" shape="circle" :color="variables.bgLinearGradient1"></u-button>
			</view>
		</view>

		<!-- 选择规格 -->
		<u-popup :show="showSpecs" mode="center" @close="showSpecs = false" round="20">
			<view class="choose-specs">
				<view class="food-info flex m-l-30">
					<image class="food-info-img" :src="foodInfo.image"></image>
					<view class="">
						<view class="xl f-w-600 food-info-name">{{ foodInfo.food_name }}</view>
						<!-- <view class="mini m-t-8 m-b-16 text-ash">库存{{ foodInfo.stock }}份</view> -->
						<!-- <price-format :price="specsPrice" :size="30" color="#FF5757"></price-format> -->
					</view>
				</view>
				<view v-if="foodInfo.food_spec.length" class="m-t-30 m-l-30">
					<view class="mini text-ash">规格</view>
					<view class="specs-list">
						<view
							v-for="item in foodInfo.food_spec"
							:key="item.id"
							:class="['specs-item', item.id === specsId ? 'activeSpecs' : '']"
							@click="chooseSpecs('specs', item)"
						>
							{{ item.name }}
						</view>
					</view>
				</view>
				<view v-if="foodInfo.food_taste.length" class="m-t-30 m-l-30">
					<view class="mini text-ash">口味</view>
					<view class="specs-list">
						<view
							:class="['specs-item', item.id === tasteId ? 'activeSpecs' : '']"
							v-for="item in foodInfo.food_taste"
							:key="item.id"
							@click="chooseSpecs('taste', item)"
						>
							{{ item.name }}
						</view>
					</view>
				</view>
				<view class="already-choose m-t-30 mini">
					<text class="text-ash">已选：</text>
					{{ specsName }}{{ specsName && tasteName ? '、' : '' }}{{ tasteName }}
				</view>
				<view class="flex row-right p-t-20 p-r-20" @click="foodChekboxReplace(foodInfo,'popup')">
					<view class="" style="width: 120rpx;">
						<u-button text="确定" type="primary" :color="variables.colorPrimary" :customStyle="customBtnStyle" size="mini"></u-button>
					</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { getApiMatchFood, getApiReplaceShopCard } from '@/api/healthy'
export default {
	data() {
		return {
			params: {},
			foodIndex: 0,
			foodChekboxValue: [],
			nutrientList: [
				{
					name: '今日推荐摄入',
					color: '#6ce1d6',
					axunge: '0',
					carbohydrate: '0',
					protein: '0'
				},
				{
					name: '本餐推荐摄入',
					color: '#17e6a2',
					axunge: '0',
					carbohydrate: '0',
					protein: '0'
				},
				{
					name: '当前摄入',
					color: '#22e2f1',
					axunge: '0',
					carbohydrate: '0',
					protein: '0'
				}
			],
			nutrientMealType: {},
			shopCardFood: [],
			matchFoodList: [],
			customBtnStyle: {
				minWidth: '80rpx',
				height: '40rpx',
				lineHeight: '40rpx'
			},
			showSpecs: false,
			foodInfo: {
				food_spec: [],
				food_taste: []
			},
			specsId: -1,
			specsName: '',
			tasteId: -1,
			tasteName: ''
		}
	},
	onLoad(option) {
		this.params = this.$Route.query.data
		this.getMatchFood()
	},
	onShow() {},
	mounted() {},
	methods: {
		// 菜品和健康
		getMatchFood() {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiMatchFood(this.params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.nutrientMealType = res.data
						this.nutrientList = [
							{
								name: '今日推荐摄入',
								color: '#6ce1d6',
								axunge: res.data.today_axunge,
								carbohydrate: res.data.today_carbohydrate,
								protein: res.data.totay_protein
							},
							{
								name: '本餐推荐摄入',
								color: '#17e6a2',
								axunge: res.data.need_axunge,
								carbohydrate: res.data.need_carbohydrate,
								protein: res.data.need_protein
							},
							{
								name: '当前摄入',
								color: '#22e2f1',
								axunge: res.data.axunge,
								carbohydrate: res.data.carbohydrate,
								protein: res.data.protein
							}
						]
						// 购物车
						this.shopCardFood = res.data.shop_card_food.map(v => {
							v.specsId =  -1
							v.tasteId = -1
							return v
						})
						// 推荐菜数据
						this.matchFoodList = res.data.match_food_list.map(v => {
							v.specsId = v.food_spec.length >= 1 ? v.food_spec[0].id : -1
							v.specsName = v.food_spec.length >= 1 ? v.food_spec[0].name : -1
							v.tasteId = v.food_taste.length >= 1 ? v.food_taste[0].id : -1
							v.tasteName = v.food_taste.length >= 1 ? v.food_taste[0].name : -1
							return v
						})
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		nutrientRecommend(data) {
			let status = false
			if (data.name == '当前摄入') {
				if (this.nutrientMealType.carbohydrate > this.nutrientMealType.need_carbohydrate) {
					status = true
				} else if (this.nutrientMealType.protein > this.nutrientMealType.need_protein) {
					status = true
				} else if (this.nutrientMealType.axunge > this.nutrientMealType.need_axunge) {
					status = true
				} else {
					status = false
				}
			}
			return status
		},
		funSpecsShow(data) {
			let status = false
			if (data.food_spec.length <= 1 && data.food_taste.length <= 1) {
				status = false
			} else {
				status = true
			}
			return status
		},
		openSpecsDialog(info, index) {
			this.showSpecs = true
			this.foodInfo = info
			if (this.foodInfo.food_spec.length) {
				this.specsId = info.specsId
				this.specsName = info.specsName
			} else {
				this.specsId = -1
				this.specsName = ''
			}
			if (this.foodInfo.food_taste.length) {
				this.tasteId = info.tasteId
				this.tasteName = info.tasteName
			} else {
				this.tasteId = -1
				this.tasteName = ''
			}
		},
		chooseSpecs(type, e) {
			if (type === 'specs') {
				this.specsId = e.id
				this.specsName = e.name
				this.foodInfo.specsName = e.name
				this.foodInfo.specsId = e.id
			} else if (type === 'taste') {
				this.tasteId = e.id
				this.tasteName = e.name
				this.foodInfo.tasteName = e.name
				this.foodInfo.tasteId = e.id
			}
		},
		mealFood(index) {
			this.foodIndex = index
			this.foodChekboxValue = []
		},
		replaceClick() {
			uni.showModal({
				title: '替换',
				content: '是否确定替换菜品数据',
				confirmColor: '#18e6a2',
				success: res => {
					if (res.confirm) {
						let replaceList = this.shopCardFood.map(v => {
							return { food_id:v.id,spec_id:v.specsId, taste_id:v.tasteId}
						})
						this.getReplaceShopCard(replaceList)
					} else if (res.cancel) {
						// console.log('用户点击取消')
					}
				}
			})
		},
		// 替换菜品
		getReplaceShopCard(replaceList) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiReplaceShopCard({
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				company_id:this.params.company_id,
				date: this.params.date,
				meal_type: this.params.meal_type,
				menu_food_id: this.params.menu_food_id,
				menu_food_object: this.params.menu_food_object,
				organization_id:this.params.organization_id,
				organization_alias:this.params.organization_alias,
				consume_type:this.params.consume_type,
				food_data: replaceList,
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						uni.$u.toast('替换成功')
						setTimeout(()=> {
							this.$miRouter.back()
						}, 500);
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		foodChekboxReplace(val,type) {
			this.shopCardFood.splice(this.foodIndex, 1, val)
			if(type == 'popup'){
				this.showSpecs = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.food-reasonable-collocation {
	.dot {
		display: inline-block;
		width: 15rpx;
		height: 15rpx;
		border-radius: 10rpx;
	}
	.food-content {
		.food-title {
			height: 70rpx;
			line-height: 70rpx;
			background-color: #fff;
		}
		.food-left {
			width: 50%;
		}
		.food-right {
			width: 50%;
			background-color: #fff;
		}
	}
	.active-bg-white {
		transition: background-color 0.4s, transform 0.2s;
		background-color: #fff;
	}
	.scroll-height {
		height: 839rpx;
		// height: calc(100vh - 500rpx);
	}
	.food-foot-box {
		position: fixed;
		height: 120rpx;
		left: 0;
		bottom: 0rpx;
		width: 100%;
		background-color: #fff;
		.food-foot-btn {
			padding: 20rpx 40rpx;
		}
	}
	.choose-specs {
		width: 600rpx;
		padding: 30rpx 0;
		.food-info-img {
			width: 140rpx;
			height: 140rpx;
			border-radius: 18rpx;
			margin-right: 20rpx;
		}
		.food-info-name {
			max-width: 350rpx;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}
		.text-ash {
			color: #9da2a7;
		}
		.already-choose {
			background-color: #f6f8f8;
			padding: 14rpx 30rpx;
		}
		.food-total {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 30rpx 30rpx 0;
			line-height: 36rpx;
		}
		.specs-list {
			display: flex;
			flex-wrap: wrap;
			padding-bottom: 30rpx;
			.specs-item {
				margin: 10rpx 20rpx 0 0;
				padding: 0 20rpx;
				border: 1rpx #e3e3e3 solid;
				border-radius: 6rpx;
				text-align: center;
				font-size: 20rpx;
				line-height: 46rpx;
				max-width: 210rpx;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}
			.activeSpecs {
				color: $color-primary;
				border: 1rpx $color-primary solid;
			}
		}
	}
}
</style>
