<template>
	<view :style="theme.style" class="appoint-food-details">
		<view class="container flex m-t-30">
			<u-image width="120rpx" height="120rpx" radius="10" @click="handlerPreview(dataFood.image)" :src="dataFood.image ? dataFood.image : headUrl" ></u-image>
			<view class="flex-1 m-l-20">
				<view class="flex row-between">
					<view class="name">{{ dataFood.set_meal_name }}</view>
					<view v-if="dataFood.stock!==-1" class="mini muted m-t-10">库存{{ dataFood.stock }}份</view>
				</view>
				<view class="muted m-t-10 m-l-20" v-if="specFoodName">
					<text class="xs p-r-20">{{ specFoodName }}</text>
				</view>
				<view class="muted m-t-10">
					<text class="xs p-l-20">{{ nutritionInfo.carbohydrate.toFixed(2) }}</text>
					<text class="muted xs p-r-20">kcal/100g</text>
					<!-- <text class="xs p-r-20">蛋白质{{ nutritionInfo.protein.toFixed(2) }}克</text> -->
					<!-- <text class="xs p-r-20">脂肪{{ nutritionInfo.axunge.toFixed(2) }}克</text> -->
				</view>
				<view class="flex row-between col-center p-t-10">
					<view class="">
						<price-format
							v-if="dataFood.obj_name == 'set_meal' && dataFood.price_type == 'fixed'"
							:price="dataFood.price"
							:size="30"
							color="#FF5757"
						></price-format>
						<price-format
							v-else-if="dataFood.obj_name == 'set_meal' && dataFood.price_type == 'total' && !dataFood.display_style_button"
							:price="dataFood.set_meal_food_price"
							:size="30"
							color="#FF5757"
						></price-format>
						<!--240708隐藏折扣价格 -->
						<!-- <price-format
							v-if="dataFood.obj_name == 'set_meal' && dataFood.price_type == 'fixed'"
							class="m-l-10"
							:price="dataFood.discount"
							:size="24"
							color="#999"
							:lineThrough="true"
						></price-format>
						<price-format
							v-else-if="dataFood.obj_name == 'set_meal' && dataFood.price_type == 'total' && !dataFood.display_style_button"
							:price="dataFood.discount"
							:size="24"
							color="#999"
							:lineThrough="true"
						></price-format> -->
					</view>
					<!-- 数量加减 -->
					<number-box
						v-if="dataFood.stock && !dataFood.display_style_button"
						v-model="dataFood.count"
						:async-change="true"
						@change="selectCart($event, dataFood)"
					></number-box>
					<view
						v-else-if="dataFood.stock && dataFood.display_style_button"
						class=""
						style="position: relative;"
						@click.stop="openSpecsDialog(dataFood)"
					>
						<u-button text="选规格" type="primary" :customStyle="customBtnStyle" :color="variables.colorPrimary" size="mini"></u-button>
						<u-badge bg-color="#FF5757" max="99" :value="specification" absolute :offset="[-12, -10]"></u-badge>
					</view>
				</view>
			</view>
		</view>
		<view style="margin-bottom: 120rpx;" v-if="nutritionList && nutritionList.length">
			<view class="" v-for="(nutritionItem, index) in nutritionList" :key="index">
				<view class="p-20">{{ nutritionItem.name }}</view>

				<view class="container" v-if="dataFood.is_healthy_info">
					<view class="flex row-between">
						<view class="f-w-500 flex-1" v-if="foodLight[nutritionItem.food_light]">
							<text
								class="circular m-r-10"
								:style="{
									backgroundColor: foodLight[nutritionItem.food_light].color
								}"
							></text>
							<text>{{ foodLight[nutritionItem.food_light].name }}：</text>
							<text class="muted">{{ foodLight[nutritionItem.food_light].label }}</text>
						</view>
						<u-icon name="question-circle" color="#d2d2d2" size="28" @click="foodLampShowTips = true"></u-icon>
					</view>
					<view class="tag-list">
						<view class="flex col-center flex-wrap m-t-20">
							<view class="tag-item tag-recommend m-b-20"><u-icon name="thumb-up" color="#11e69e" size="28"></u-icon></view>
							<block v-if="labelData && labelData[nutritionItem.id].recommend_label.length">
								<view v-for="(item, index) in labelData[nutritionItem.id].recommend_label" :key="index" class="tag-item m-b-20">
									<text>{{ item }}</text>
								</view>
							</block>
							<block v-else><view class="m-b-20">--</view></block>
						</view>
						<view class="flex col-center flex-wrap m-t-20">
							<view class="tag-item  tag-not-recommend m-b-20"><u-icon name="warning" color="#ff5f5f" size="28"></u-icon></view>
							<block v-if="labelData && labelData[nutritionItem.id].not_recommend_label.length">
								<view
									v-for="(item, index) in labelData[nutritionItem.id].not_recommend_label"
									:key="index"
									class="tag-item m-b-20"
								>
									<text>{{ item }}</text>
								</view>
							</block>
							<block v-else><view class="m-b-20">--</view></block>
						</view>
					</view>
				</view>
				<view class="container" v-if="nutritionItem.ingredient_list && nutritionItem.ingredient_list.length">
					<view class="f-w-500">食材组成</view>
					<u-read-more ref="uReadMore" show-height="260rpx" text-indent="0" closeText="展开更多" :toggle="true" color="#999999">
						<view class="m-b-20">
							<view
								class="p-t-20 p-l-20 p-b-10 flex row-between col-center ingredients-box"
								v-for="(ingredientsItem, ingredientsIndex) in nutritionItem.ingredient_list"
								:key="ingredientsIndex"
							>
								<view>
									<view class="xs m-b-10">{{ ingredientsItem.ingredient_name }}</view>
									<view class="flex">
										<view
											class="mini muted flex"
											v-for="(ingredientNutritionItem, ingredientNutritionIndex) in ingredientsItem.ingredient_nutrition_list"
											:key="ingredientNutritionIndex"
											v-if="dataFood.is_healthy_info"
										>
											<view>{{ ingredientNutritionItem.name }}</view>
											<view >
												{{
													(ingredientNutritionIndex == ingredientsItem.ingredient_nutrition_list.length - 1 &&
														ingredientNutritionItem.value) ||
														ingredientNutritionItem.value + '/'
												}}
											</view>
										</view>
									</view>
								</view>
								<view class="primary" v-if="ingredientsItem.ingredient_sourced_code" @click="sourcedCodeClick(ingredientsItem)">
									溯源码
								</view>
							</view>
						</view>
					</u-read-more>
				</view>
				<view class="container" v-if="dataFood.is_healthy_info">
					<view class="flex row-between">
						<view>
							<text class="f-w-500">营养成分</text>
							<text class="muted">(每100克含量)</text>
						</view>
					</view>
					<view class="ring-charts-box">
						<view class="flex col-center">
							<view class="charts-box">
								<qiun-data-charts
									type="ring"
									:canvas2d="canvas2dStatus"
									:opts="initNutritionOpts(nutritionItem.food_nutrition_info)"
									:chartData="initNutrition(nutritionItem.food_nutrition_info)"
								/>
							</view>
							<view class="muted mini">
								<view class="p-t-0">
									<text>
										{{
											funNutritionPercentage(nutritionItem.food_nutrition_info, nutritionItem.food_nutrition_info.carbohydrate)
										}}%
									</text>
									<text class="p-l-20">{{ nutritionItem.food_nutrition_info.carbohydrate }}克</text>
								</view>
								<view class="p-t-20">
									<text>
										{{ funNutritionPercentage(nutritionItem.food_nutrition_info, nutritionItem.food_nutrition_info.protein) }}%
									</text>
									<text class="p-l-20">{{ nutritionItem.food_nutrition_info.protein }}克</text>
								</view>
								<view class="p-t-20">
									<text>
										{{ funNutritionPercentage(nutritionItem.food_nutrition_info, nutritionItem.food_nutrition_info.axunge) }}%
									</text>
									<text class="p-l-20">{{ nutritionItem.food_nutrition_info.axunge }}克</text>
								</view>
							</view>
						</view>
						<view class="text-center muted p-b-20">大约需要慢跑{{ nutritionItem.energyMin }}分钟</view>
					</view>
				</view>
				<view class="container nutrition-box" v-if="dataFood.is_healthy_info">
					<view class="nutrition-wrap m-t-30 xs p-b-20 flex row-between primary">
						<text class="text-center nutrition-width">营养元素</text>
						<text class="text-center nutrition-width">摄入量</text>
						<text class="text-center nutrition-width">单位</text>
					</view>

					<view class="item" v-for="(item, index) in nutritionItem.nutrientDictList" :key="index">
						<view class="nr black f-w-500 flex row-between m-b-10">
							<view class="text-center nutrition-wrap nutrition-width p-b-20">{{ item.name }}</view>
							<view class="text-center nutrition-wrap nutrition-width p-b-20">{{ item.value }}</view>
							<view class="text-center nutrition-wrap nutrition-width p-b-20">g</view>
						</view>
					</view>
					<view class="flex col-center row-center muted" @click="openMore(nutritionItem)">
						<text>更多营养元素</text>
						<u-icon name="arrow-down" color="#93989e" size="26"></u-icon>
					</view>
				</view>
			</view>
		</view>
		<view class="foot-fixed" v-show="allCount">
			<view class="cart col-center flex">
				<view class="flex-1">
					<view class="total-order">
						<text class="md muted">合计</text>
						<price-format class="m-l-10" :price="totalAmount" :size="36"></price-format>
					</view>
				</view>
				<view class="cart-num flex flex-center" @tap="showCart = !showCart">
					<u-badge bg-color="#FF5757" max="99" :value="allCount" absolute :offset="[0, 0]"></u-badge>
					<image class="icon-lg" :src="themeImgPath.img_bundle_shop_cart"></image>
				</view>
					<u-button
						:customStyle="{
							width: '200rpx',
							'border-radius': 0
						}"
						type="primary"
						size="large"
						:color="isVerificationOrderFlag?variables.colorPrimary:'#aaaaaa'"
						:text="formateText()"
						@click="gotoConfirmOrder"
					></u-button>
			</view>
		</view>
		<!-- 购物车列表 -->
		<cart-popup
			v-model="showCart"
			:lists="cartLists"
			:time="currentDate('yyyy-mm-dd 周w')"
			@clear="clearCard"
			@countchange="handleCountChange"
		></cart-popup>

		<!-- 选择规格 -->
		<u-popup :show="showSpecs" mode="center" @close="showSpecs = false" round="20">
			<view class="choose-specs">
				<view class="food-info flex m-l-30">
					<image class="food-info-img" :src="dataFood.image ? dataFood.image : headUrl"></image>
					<view class="">
						<view class="xl f-w-600 food-info-name">{{ dataFood.set_meal_name }}</view>
						<view v-if="dataFood.stock!==-1" class="mini m-t-8 m-b-16 text-ash">库存{{ dataFood.stock }}份</view>
						<price-format
							v-if="dataFood.obj_name === 'set_meal' && dataFood.obj_name == 'fixed'"
							:price="specsPrice"
							:size="30"
							color="#FF5757"
						></price-format>
					</view>
				</view>
				<scroll-view style="max-height: 400rpx" :scroll-y="true">
					<view class="m-t-30 m-l-30" v-for="styleItem in dataFood.set_meal_style_data" :key="styleItem.style_id">
						<view class="mini text-ash">{{ styleItem.style_name }}</view>
						<view class="specs-list">
							<view
								v-for="(specsItem, specsIndex) in styleItem.set_meal_food_data"
								:key="specsIndex"
								:class="[
									'specs-item',
									styleItem.setMealSpecsId === specsItem.spec_id ? 'activeSpecs' : '',
									specsItem.disabled ? 'disabled' : ''
								]"
								@click="setMealChooseSpecs(styleItem, specsItem)"
							>
								{{ specsItem.food_name }}
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="already-choose m-t-30 mini">
					<text class="text-ash">已选：</text>
					{{ selectSetMealSpecName }}
				</view>
				<view class="food-total">
					<view class="">
						<text class="lg m-r-20">合计</text>
						<price-format class="m-t-5" :size="40" :price="specsTotalPrice" color="#FF5757"></price-format>
					</view>
					<view class="">
						<number-box
							v-model="foodInfoCount"
							:async-change="true"
							@change="selectCart($event, dataFood, specsType)"
						></number-box>
					</view>
				</view>
			</view>
		</u-popup>
		<u-popup :show="sourcedCodeShow" mode="center" round="20" @close="sourcedCodeShow = false">
			<view class="sourced-code-popup">
				<view class="text-center p-t-30 p-b-30">食品溯源码</view>
				<view class="flex flex-center">
					<u-image :showLoading="true" :src="ingredientSourcedCodeImg" width="400rpx" height="400rpx"></u-image>
				</view>
			</view>
		</u-popup>
		<u-modal
			:show="foodLampShowTips"
			title="食物灯"
			confirmText="我知道了"
			:confirmColor="variables.colorPrimary"
			@confirm="foodLampShowTips = false"
		>
			<view class="food-lamp-box" slot="default">
				<text>​食物灯根据食物营养价值的高低，能帮助你快速判断该食物是否适合食用</text>
				<view class="modal-food-box m-t-20 m-b-20">
					<view class="m-b-15 m-t-10">
						<text class="circular m-r-20" style="backgroundColor:#63d496"></text>
						<text>绿灯食物：</text>
						<text class="muted">放心吃</text>
					</view>
					<view class="m-b-15 m-t-10">
						<text class="circular m-r-20" style="backgroundColor:#f7d149"></text>
						<text>黄灯食物：</text>
						<text class="muted">适量吃</text>
					</view>
					<view class="m-b-15 m-t-10">
						<text class="circular m-r-20" style="backgroundColor:red"></text>
						<text>红灯食物：</text>
						<text class="muted">建议少吃</text>
					</view>
				</view>
				<view class="f-w-500 xxl">GI</view>
				<view class="modal-food-box xs m-t-20 m-b-20">
					是指血糖生成指数，影响餐后2小时内血糖变化，低GI的食物有助于稳定餐后血糖，带来长时间的饱腹感
				</view>
				<view class="f-w-500 xxl">GL</view>
				<view class="modal-food-box xs m-t-20 m-b-20">是指升糖负荷，低GL的食物有助于稳定餐后血糖，降低食欲</view>
				<view class="f-w-500 xxl">嘌呤</view>
				<view class="modal-food-box xs m-t-20 m-b-20">
					是指人体内中的碱基，在体内氧化变成尿酸，高嘌呤的食物痛风人群要注意，过多食用会引起关节疼痛
				</view>
				<view class="f-w-500 xxl">高纤维</view>
				<view class="modal-food-box xs m-t-20 m-b-20">
					纤维量高的食物具有很强的饱腹感，饥饿感来的更慢，还会缓解便秘，调节体重
				</view>
				<view class="f-w-500 xxl">高钠食物</view>
				<view class="modal-food-box xs m-t-20 m-b-20">含钠量高的食物容易引起水肿和高血压</view>
				<view class="f-w-500 xxl">中高饱和脂肪、胆固醇的食物</view>
				<view class="modal-food-box xs m-t-20 m-b-20">这类食物容易引起心脑血管等疾病</view>
				<view class="muted mini p-t-20">参考依据:《中国营养科学全书》《中国居民膳食指南2022版》</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
import { timeFormat } from '@/utils/date'
import { apiShopcardCalcList, apiShopcardAdd, apiShopcardDelete, apiShopcardClean } from '@/api/shopcart'
import { getApiReservationGetFoodNutrition } from '@/api/healthy.js'
import { checkClient, formateVisitorParams } from '@/utils/util.js'
import { formateBtnText , verificationOrderData} from './orderUtil'
import Cache from '@/utils/cache'
export default {
	data() {
		return {
			platform: checkClient(), // 平台， 微信or支付宝
			canvas2dStatus: false,
			customBtnStyle: {
				minWidth: '80rpx',
				height: '40rpx',
				lineHeight: '40rpx'
			},
			showCart: false,
			totalAmount: 0,
			allCount: 0,
			count: 0,
			cartLists: [],
			params: {}, //外面传进来的params
			dataFood: {}, //菜品数据
			showSpecs: false,
			nutrientDictList: [
				{
					name: '碳水化合物',
					key: 'carbohydrate',
					value: 0
				},
				{
					name: '蛋白质',
					key: 'protein',
					value: 0
				},
				{
					name: '脂肪',
					key: 'axunge',
					value: 0
				}
			],
			nutritionKey: ['carbohydrate', 'axunge', 'protein'],
			specsType: 'setMealSpecs',
			specFoodName: '',
			// 三大营养合计
			nutritionInfo: {
				carbohydrate: 0,
				protein: 0,
				axunge: 0
			},
			// 营养元素
			nutritionList: [],
			foodLight: {
				green: {
					name: '绿灯食物',
					label: '放心吃',
					color: '#63d496'
				},
				yellow: {
					name: '黄灯食物',
					label: '适量吃',
					color: '#f7d149'
				},
				red: {
					name: '红灯食物',
					label: '建议少吃',
					color: 'red'
				}
			},
			foodLampShowTips: false,
			labelData: {},
			sourcedCodeShow: false,
			ingredientSourcedCodeImg: '',
			verificationData: {},//用来暂存每个消费点的配送费
			isVerificationOrder: true,//是否满足起送费
			verificationOrderError: '',//起送费错误提示
			orderOrgId: '',//配送点Id
			isVerificationOrderFlag: true,//其中有个一个满足起送费的标志
			currentTime: '', //订餐当前时间
			specification: 0, // 规格点
			headUrl: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'
		}
	},
	computed: {
		currentDate() {
			return format => timeFormat(Date.now(), format)
		},
		foodInfoCount: {
			get() {
				let count = 0
				this.cartLists.map(item => {
					if (item.date === this.params.date) {
						item.stall.map(stall => {
							if (stall.meal_type === this.params.meal_type) {
								stall.food.map(food => {
									// 套餐规格判断 排序好 拿已点套餐数量
									if (Number(food.set_meal_id) === this.dataFood.set_meal_id) {
										let cartSpecIds = [],
											setMealStyleSpecsIds = [],
											cartStyleIds = [],
											setMealStyleIds = []
										food.set_meal_style_data.filter(cartStyle => {
											if (cartStyle.spec_id) {
												cartSpecIds.push(cartStyle.spec_id)
												cartStyleIds.push(Number(cartStyle.style_id))
											}
										})
										this.dataFood.set_meal_style_data.filter(setMealStyle => {
											if (setMealStyle.setMealSpecsId) {
												setMealStyleSpecsIds.push(setMealStyle.setMealSpecsId)
												setMealStyleIds.push(Number(setMealStyle.style_id))
											}
										})
										cartSpecIds = cartSpecIds.sort((a, b) => {
											return a - b
										})
										setMealStyleSpecsIds = setMealStyleSpecsIds.sort((a, b) => {
											return a - b
										})
										cartStyleIds = cartStyleIds.sort((a, b) => {
											return a - b
										})
										setMealStyleIds = setMealStyleIds.sort((a, b) => {
											return a - b
										})
										if (
											cartSpecIds.length &&
											setMealStyleSpecsIds.length &&
											JSON.stringify(cartSpecIds) === JSON.stringify(setMealStyleSpecsIds) &&
											JSON.stringify(cartStyleIds) === JSON.stringify(setMealStyleIds)
										) {
											count = food.count
										}
									}
								})
							}
						})
					}
				})
				return count
			},
			set(val) {}
		},
		specsPrice() {
			let price = 0
			// 如果是固定金额 就只能拿
			if (this.dataFood.price_type == 'fixed') {
				price = this.dataFood.price
			}
			return price
		},
		specsTotalPrice() {
			let price = 0
			// 如果是固定金额 就只能拿
			if (this.dataFood.price_type == 'fixed') {
				price = this.dataFood.price
			} else {
				// 如果不是固定金额就判断有没有setMealSpecsId 拿规格合计
				if (this.dataFood.set_meal_style_data && this.dataFood.set_meal_style_data.length) {
					this.dataFood.set_meal_style_data.map(v => {
						v.set_meal_food_data.map(setMealFoodItem => {
							if (v.setMealSpecsId == setMealFoodItem.spec_id) {
								price += setMealFoodItem.food_price
							}
						})
					})
				}
			}
			return price
		},
		// 多规格 套餐的时候
		selectSetMealSpecName() {
			let setMealSpecName = []
			if (this.dataFood.set_meal_style_data && this.dataFood.set_meal_style_data.length) {
				this.dataFood.set_meal_style_data.map(v => {
					v.set_meal_food_data.map((setMealFoodItem, setMealFoodIndex) => {
						if (v.setMealSpecsId == setMealFoodItem.spec_id) {
							setMealSpecName.push(setMealFoodItem.food_name)
						}
					})
				})
			}
			return setMealSpecName.join('、')
		}
	},
	onLoad(option) {
		this.canvas2dStatus = this.platform === 'mp-alipay' ? false : true
		this.params = this.$decodeQuery(this.$Route.query.params)
		this.currentTime = this.params.currentDate || ''
		this.dataFood = this.$decodeQuery(this.$Route.query.data)
		this.verificationData =	Cache.get('verificationOrder')
		this.getCartLists()
	},
	onShow() {},
	mounted() {},
	methods: {
		// 获取套餐食品id
		getGoodIds() {
			let setMealStyleData = this.dataFood.set_meal_style_data || []
			let ids = []
			if (setMealStyleData && setMealStyleData.length > 0) {
				setMealStyleData.forEach(item => {
					let setMealFoodData = item.set_meal_food_data || []
					if (setMealFoodData && setMealFoodData.length > 0) {
						setMealFoodData.forEach(item => {
							ids.push(item.food_id)
						})
					}
				})
			}
			return ids
		},
		// t套餐营养
		getReservationGetFoodNutrition(ids, formatRealFoods) {
			if (!ids.length) {
				this.nutritionList = []
				this.nutritionInfo = {
					carbohydrate: 0,
					protein: 0,
					axunge: 0
				}
				return
			}
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
			getApiReservationGetFoodNutrition(formateVisitorParams({
				food_id_list: ids,
				org_id: this.dataFood.org_id
			}))
				.then(res => {
					console.log('营养信息接口res', res);
					uni.hideLoading()
					if (res.code == 0) {
						let energyMinTotal = 300 / 40
						// 新增修复Bug代码 --- 即便有ids，也需要清零
			  		this.nutritionList = []
						this.nutritionInfo = {
							carbohydrate: 0,
							protein: 0,
							axunge: 0
						}
						console.log('执行一次');
						res.data.results.forEach(nutritionItem => {
							console.log(nutritionItem.id)
							// 新增修复bug代码 --- start
							formatRealFoods.forEach((items) => {
								if (+items.food_id === nutritionItem.id) {
									this.nutritionInfo.carbohydrate += (nutritionItem.food_nutrition_info.carbohydrate * items.count)
									this.nutritionInfo.protein += nutritionItem.food_nutrition_info.protein * items.count
									this.nutritionInfo.axunge += nutritionItem.food_nutrition_info.axunge * items.count
									nutritionItem.food_nutrition_info.percentageTotal = Number(
										(
											nutritionItem.food_nutrition_info.carbohydrate +
											nutritionItem.food_nutrition_info.protein +
											nutritionItem.food_nutrition_info.axunge
										).toFixed(2)
									)
								}
							})
							// 新增修复bug代码 --- end
							// 注释的是原来的代码
							// this.nutritionInfo.carbohydrate += nutritionItem.food_nutrition_info.carbohydrate
							// this.nutritionInfo.protein += nutritionItem.food_nutrition_info.protein
							// this.nutritionInfo.axunge += nutritionItem.food_nutrition_info.axunge
							// nutritionItem.food_nutrition_info.percentageTotal = Number(
							// 	(
							// 		nutritionItem.food_nutrition_info.carbohydrate +
							// 		nutritionItem.food_nutrition_info.protein +
							// 		nutritionItem.food_nutrition_info.axunge
							// 	).toFixed(2)
							// )
							// 固定 每300卡跑40分钟
							nutritionItem.energyMin = (nutritionItem.food_nutrition_info.energy_kcal / energyMinTotal).toFixed(0)
							// 食材组成处理
							if (nutritionItem.ingredient_list.length) {
								nutritionItem.ingredient_list.forEach(ingredient => {
									ingredient.ingredient_nutrition_list = []
									ingredient.ingredient_sourced_code = ingredient.supplier_list.length
										? ingredient.supplier_list[0].sourced_code
										: ''
									if (
										ingredient.ingredient_nutrition_info &&
										Object.keys(ingredient.ingredient_nutrition_info) &&
										Object.keys(ingredient.ingredient_nutrition_info).length
									) {
										for (let nutrition in ingredient.ingredient_nutrition_info) {
											// 因为目前只需要3个营养 nutritionKey
											if (this.nutritionKey.includes(nutrition)) {
												ingredient.ingredient_nutrition_list.push({
													key: nutrition,
													name: this.nutritionName(nutrition),
													value: ingredient.ingredient_nutrition_info[nutrition] + 'g'
												})
											}
										}
									}
								})
							}
							// 营养列表
							nutritionItem.nutrientDictList = this.nutrientDictList.map(nutritionInfo => {
								nutritionInfo.value = nutritionItem.food_nutrition_info[nutritionInfo.key]
								return uni.$u.deepClone(nutritionInfo)
							})
						})
						this.labelData = res.data.label_data
						this.nutritionList = res.data.results
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.hideLoading()
					uni.$u.toast(error.message)
				})
		},
		funNutritionPercentage(item, data) {
			let percentage = data / item.percentageTotal
			return percentage ? percentage.toFixed(2) : 0
		},
		initNutritionOpts(data) {
			let opts = {
				dataLabel: false,
				labelShow: false,
				title: {
					name: data.energy_kcal ? data.energy_kcal : '0',
					fontSize: uni.upx2px(30),
					color: '#1d201e'
				},
				subtitle: {
					name: 'kcal',
					fontSize: 13,
					color: '#8f9295'
				},
				legend: {
					show: true,
					position: 'right',
					lineHeight: 25,
					float: 'left',
					itemGap: 0,
					padding: 0,
					margin: 0
				},
				extra: {
					ring: {
						ringWidth: 10
					},
					tooltip: {
						showBox: false
					}
				}
			}
			return opts
		},
		// 营养表
		initNutrition(data) {
			let chartData = {
				series: [
					{
						data: [
							{
								name: '碳水化合物',
								labelText: '碳水化合物:1000.00',
								color: '#a475fa',
								value: data.carbohydrate,
								labelShow: false
							},
							{
								name: '蛋白质',
								color: '#ff9e6a',
								value: data.protein,
								labelShow: false
							},
							{
								name: '脂肪',
								color: '#ff7569',
								value: data.axunge,
								labelShow: false
							}
						]
					}
				]
			}
			return chartData
		},
		getCartLists() {
			return new Promise((reslove, reject) => {
				apiShopcardCalcList(formateVisitorParams({
					payment_order_type: this.params.payment_order_type,
					take_meal_type: this.params.take_meal_type,
					company_id: this.params.company_id
				}))
					.then(res => {
						this.cartLists = res.data.shopcard
						this.totalAmount = res.data.all_date_fee
						this.allCount = res.data.all_date_count
						this.orderOrgId = this.params.org_id
						this.verificationOrder(this.cartLists)
						this.changeFood()
						this.specification = 0
						if (res.data.shopcard.length) {
							console.log('666', res.data.shopcard);
							console.log('params', this.params);
							const tempArray = res.data.shopcard.find((item) => {
								return this.params.date === item.date
							})
							// tempArray是过滤出来的当天的数据，当天的食材数据全部放在tempArray的第一个对象里面，所以tempArray里面只有1条
							if (tempArray !== undefined && tempArray.stall) {
								console.log('tempArray', tempArray)
								const tempArray2 = tempArray.stall.find((item) => {
									return item.stall_id === this.dataFood.org_id
								})
								// tempArray2是根据当前组织id与食物详情的组织id做比较过滤出来的食物数据,食物数据全部存在单个对象里面,
								// 所以tempArray2是根据组织id过滤出来的数据,这条数据保存了当前组织的所有食物数据,所以也只有一条
								if (tempArray2 !== undefined && tempArray2.food) {
									console.log('tempArray2', tempArray2)
									tempArray2.food.forEach((keys) => {
										if (+keys.set_meal_id === +this.dataFood.set_meal_id) {
											this.specification += +keys.count
										}
									})
								}
							}
						}
						reslove()
					})
					.catch(err => {
						uni.$u.toast(err)
					})
			})
		},
		changeFood() {
			const foods = [],
				specFoodNameList = [],
				specFoodIds = []
			if (this.cartLists && this.cartLists.length) {
				console.log('购物车', this.cartLists)
				this.cartLists.forEach(item => {
					item.stall.forEach(sitem => {
						sitem.food.forEach(fitem => {
							// 展示套餐菜品 如果是多规格就走这里
							// if (this.dataFood.display_style_button && fitem.set_meal_style_data && fitem.set_meal_style_data.length) {
							// 	fitem.set_meal_style_data.forEach(setMealFoodItem => {
							// 		specFoodNameList.push(setMealFoodItem.food_name)
							// 		specFoodIds.push(setMealFoodItem.food_id)
							// 	})
							// }
							foods.push({
								...fitem,
								meal_type: sitem.meal_type,
								date: item.date
							})
						})
					})
				})
			}
			// 没有多规格默认显示必选
			// if (!this.dataFood.display_style_button) {
			// 	this.dataFood.set_meal_style_data.forEach(setMealStyleItem => {
			// 		setMealStyleItem.set_meal_food_data.forEach(setMealFoodItem => {
			// 			specFoodNameList.push(setMealFoodItem.food_name)
			// 			specFoodIds.push(setMealFoodItem.food_id)
			// 		})
			// 	})
			// }
			// 页面显示套餐点了哪个菜名字
			console.log('foods', foods)
			this.specFoodName = ''
			const tempArray = this.cartLists.filter((item) => {
				return item.date === this.params.date
			})
			if (tempArray.length) {
				tempArray.forEach((items) => {
					items.stall.forEach((keys) => {
						keys.food.filter((res) => {
							if (res.organization_id === this.dataFood.org_id && +res.set_meal_id === this.dataFood.set_meal_id) {
								console.log('符合条件的res', res)
								res.set_meal_style_data.forEach((name) => {
									specFoodIds.push(name.food_id)
									this.specFoodName += (name.food_name + ' ' + '+' + ' ')
								})
							}
						})
					})
				})
			}
			this.specFoodName = this.specFoodName.substring(0, this.specFoodName.length -2)
			// this.specFoodName = specFoodNameList.join('+')
			foods.filter((item) => {
				if (item.date === this.params.date && +item.organization_id === this.dataFood.org_id && +item.set_meal_id === this.dataFood.set_meal_id) {
					console.log(111);
				}
			})
			// specFoodIds 如果是空就不调接口
			console.log(specFoodIds);
			// 新增维护代码 --- 用于匹配营养信息的数量问题，需要将realFoods传进getReservationGetFoodNutrition这个方法里面进行比对
			let realFoods = foods.filter((items) => {
				if (items && Reflect.has(items, "set_meal_style_data") && Array.isArray(items.set_meal_style_data)) {
					items.set_meal_style_data.forEach((keys) => {
						keys.count = items.count
					})
				}
				return items.date === this.params.date && +items.organization_id === this.dataFood.org_id && +items.set_meal_id === this.dataFood.set_meal_id
			})
			// 改造realFoods
			let formatRealFoods = []
			realFoods.forEach((keys) => {
				if (keys.set_meal_style_data) {
					keys.set_meal_style_data.forEach((k) => {
						formatRealFoods.push(k)
					})
				}
			})
			console.log('formatRealFoods', formatRealFoods)
			this.getReservationGetFoodNutrition(this.getGoodIds(), formatRealFoods)
			console.log('营养信息', this.nutritionInfo)
			this.$set(this.dataFood, 'count', 0)
			// // 兼容有规格的情况
			let count = 0
			foods.filter(item => {
				if (item.meal_type == this.params.meal_type && item.set_meal_id == this.dataFood.set_meal_id && item.date === this.params.date) {
					this.$set(this.dataFood, 'count', item.count)
				}
			})
			// console.log('this.dataFood', this.dataFood);
			// 获取菜品营养
		},
		clearCard() {
			apiShopcardClean(formateVisitorParams({
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				company_id: this.params.company_id
			}))
				.then(res => {
					this.nutritionList = []
					uni.$u.toast('清空成功')
					this.showCart = false
					this.getCartLists()
				})
				.catch(err => {
					uni.$u.toast(err.message)
				})
		},
		handleCountChange(data) {
			this.selectCart(data.value, data.food, 'setMealCart')
		},
		// 购物车加减
		async selectCart(val, item, type) {
			console.log('看看穿了什么东西', val, item, type)
			const params = {
				company_id: this.params.company_id,
				payment_order_type: this.params.payment_order_type,
				food_id: item.obj_name == 'set_meal' ? '' : item.food_id,
				date: item.date || this.params.date,
				take_meal_type: this.params.take_meal_type,
				meal_type: item.meal_type || this.params.meal_type,
				organization_id: item.org_id || item.organization_id,
				organization_alias: item.org_name || item.organization_alias,
				consume_type: item.consume_type,
				fuwu_fee: item.fuwu_fee,
				elevator_fee: item.elevator_fee,
				elevator_fee: item.elevator_fee,
				menu_food_id: item.menu_info.menu_food_id,
				menu_food_object: item.menu_info.menu_food_object,
				buy_limit_food_count:this.params.buyLimitFoodCount, // 单人单餐菜品限制份数
				stall_id: this.dataFood.stall_id,
				org_id: this.params.org_id
			}

			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			// 套餐菜品必选
			let setMealFlag = false,
				setMealFoodFlag = false
			if (type == 'setMealSpecs') {
				setMealFlag = item.set_meal_style_data.some((v, index, array) => {
					if (v.food_required && !v.setMealSpecsId) {
						uni.$u.toast(v.style_name + '请选择一个菜品')
						return true
					} else {
						return false
					}
				})
				// 如果没有必选 也要选择一个菜品
				setMealFoodFlag = item.set_meal_style_data.some(v => {
					if (v.setMealSpecsId) {
						return true
					} else {
						return false
					}
				})
			}
			if (setMealFlag && type == 'setMealSpecs') return
			if (!setMealFoodFlag && type == 'setMealSpecs') return uni.$u.toast('请选择一个菜品')
			// 套餐的时候 并且是选择规格的时候 太多种情况了 先分开实现一下功能流程，这个功能太恶心人了
			if (item.obj_name == 'set_meal' && item.display_style_button) {
				params.set_meal_id = item.set_meal_id
				params.set_meal_style_data = []
				item.set_meal_style_data.map(v => {
					if (v.setMealSpecsId) {
						let obj = {
							set_meal_style_id: '',
							food_id: '',
							spec_id: ''
						}
						obj.set_meal_style_id = v.style_id
						if (type == 'setMealCart') {
							obj.food_id = v.food_id
							obj.spec_id = v.spec_id
						} else {
							v.set_meal_food_data.map(k => {
								if (v.setMealSpecsId == k.spec_id) {
									obj.food_id = k.food_id
									obj.spec_id = k.spec_id
								}
							})
						}
						params.set_meal_style_data.push(obj)
					}
				})
			}
			//  套餐的时候 并且不是选择规格的时候
			if (item.obj_name == 'set_meal' && !item.display_style_button) {
				console.log('此时走的是这个条件')
				params.set_meal_id = item.set_meal_id
				params.set_meal_style_data = []
				params.set_meal_style_data = item.set_meal_style_data.map(v => {
					let obj = {
						set_meal_style_id: '',
						food_id: '',
						spec_id: ''
					}
					obj.set_meal_style_id = v.style_id
					// 因为购物车返回的格式不一样 额外做一个判断购物车获取菜品id和规格id
					if (type == 'setMealCart') {
						obj.food_id = v.food_id
						obj.spec_id = v.spec_id
					} else {
						v.set_meal_food_data.map(k => {
							obj.food_id = k.food_id
							obj.spec_id = k.spec_id
						})
					}
					return obj
				})
			}
			if ((type == 'setMealCart' && val > item.count) || (val > this.foodInfoCount && type == 'setMealSpecs') || (val > item.count && item.obj_name === 'set_meal' )) {
				await apiShopcardAdd(formateVisitorParams(params))
					.then(res => {
						uni.hideLoading()
						if (res.code == 0) {
							this.getCartLists()
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
			} else {
				await apiShopcardDelete(formateVisitorParams(params))
					.then(res => {
						uni.hideLoading()
						if (res.code == 0) {
							this.getCartLists()
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
			}
		},
		openSpecsDialog(info) {
			this.showSpecs = true
			this.specsType = 'setMealSpecs'
			// 清除赋值的setMealSpecsId
			this.dataFood.set_meal_style_data.forEach(setMealStyleItem => {
				this.$delete(setMealStyleItem, 'setMealSpecsId')
				setMealStyleItem.set_meal_food_data.forEach(setMealFoodItem => {
					this.$set(setMealFoodItem, 'disabled', false)
				})
			})
		},
		getChildSpecIds() {
			let selectSpecIds = [],
				specIds = {}
			this.dataFood.set_meal_style_data.forEach(item => {
				if (item.setMealSpecsId) {
					selectSpecIds.push(item.setMealSpecsId)
					specIds[item.style_id] = item.setMealSpecsId
				}
			})
			return { selectSpecIds, specIds }
		},
		setMealChooseSpecs(styleItem, specsItem) {
			// 单选
			// var idx = this.setMealSpecsIds.indexOf(specsItem.spec_id)
			//如果已经选中了，那就取消选中，如果没有，则选中
			// food_repeat 菜品是否重复选择
			let selectSpecIds = [],
				specIds = {}
			if (!this.dataFood.food_repeat) {
				let result = this.getChildSpecIds()
				selectSpecIds = result.selectSpecIds
				specIds = result.specIds
				if (styleItem.setMealSpecsId == specsItem.spec_id) {
					this.$delete(styleItem, 'setMealSpecsId')
					let result = this.getChildSpecIds()
					selectSpecIds = result.selectSpecIds
					specIds = result.specIds
				} else if (!selectSpecIds.includes(specsItem.spec_id)) {
					this.$set(styleItem, 'setMealSpecsId', specsItem.spec_id)
					let result = this.getChildSpecIds()
					selectSpecIds = result.selectSpecIds
					specIds = result.specIds
				} else {
					// 不给点击
					return
				}
				// 添加disabled 不给他选择
				this.dataFood.set_meal_style_data.forEach(setMealStyleItem => {
					if (setMealStyleItem.style_id !== styleItem.style_id) {
						setMealStyleItem.set_meal_food_data.forEach(setMealFoodItem => {
							if (selectSpecIds.includes(setMealFoodItem.spec_id)) {
								if (selectSpecIds.length > 1 && specIds[styleItem.style_id] != setMealFoodItem.spec_id) {
									// 当选中的specid只有不为1个时
									this.$set(setMealFoodItem, 'disabled', false)
								} else if (specIds[styleItem.style_id]) {
									this.$set(setMealFoodItem, 'disabled', true)
								}
							} else {
								this.$set(setMealFoodItem, 'disabled', false)
							}
						})
					}
				})
			} else {
				// 点击单选
				if (styleItem.setMealSpecsId == specsItem.spec_id) {
					this.$delete(styleItem, 'setMealSpecsId')
				} else {
					this.$set(styleItem, 'setMealSpecsId', specsItem.spec_id)
				}
			}
		},
		nutritionDetails(nutritionInfo) {
			this.$miRouter.push({
				path: '/pages_bundle/appoint/nutrition-details',
				query: {
					data: this.$encodeQuery(nutritionInfo)
				}
			})
		},
		openMore(data) {
			this.$miRouter.push({
				path: '/pages_bundle/appoint/nutrition-details',
				query: {
					data: this.$encodeQuery(data.food_nutrition_info)
				}
			})
		},
		sourcedCodeClick(data) {
			this.sourcedCodeShow = true
			this.ingredientSourcedCodeImg = data.ingredient_sourced_code
		},
		nutritionName(key) {
			let name = ''
			switch (key) {
				case 'energy_kcal':
					name = '千卡'
					break
				case 'carbohydrate':
					name = '碳水'
					break
				case 'axunge':
					name = '脂肪'
					break
				case 'protein':
					name = '蛋白质'
					break
				default:
					break
			}
			return name
		},
		//格式化按钮文字
		formateText() {
			var cupboardDeliveryFee = ''
			var waimaiDeliveryFee = ''
			var verificationDataList = this.verificationData[this.currentTime] || []
			var findItem = verificationDataList.find(verItem => {
							return verItem.stallId === this.orderOrgId && verItem.mealType === this.params.meal_type
					})
			if(findItem){
				cupboardDeliveryFee = findItem.cupboardDeliveryFee
				waimaiDeliveryFee = findItem.waimaiDeliveryFee
			}		
			return	formateBtnText(this.params,this.isVerificationOrderFlag,cupboardDeliveryFee,waimaiDeliveryFee) 
    },
		/**
		 * 分日期去校验配送费
		 * @param list 配送列表,包含日期，餐段等数据
		 */
		 verificationOrder(list){
			if(this.params.take_meal_type === 'waimai' || this.params.take_meal_type === 'cupboard') {
				var resultData =	verificationOrderData(list,this.params,this.verificationData)
				if(resultData) {
					this.isVerificationOrder = resultData.isVerificationOrder
					this.verificationOrderError = resultData.verificationOrderError
					this.isVerificationOrderFlag = resultData.isVerificationOrderFlag
				}
			}
		},
		//跳转到确认订单页面
		gotoConfirmOrder() {
      if (this.isVerificationOrder) {
				Cache.set('verificationOrder',this.verificationData)
        this.$miRouter.push({
          path: '/pages_bundle/appoint/confirm_order',
          query: {
            buyLimitFoodCount: this.params.buyLimitFoodCount,
            cupboardTakeawayFee:this.params.cupboardTakeawayFee
          }
        })
      }else{
				//错误显示提示
				uni.$u.toast(this.verificationOrderError)
			}
    },
		// 预览图片
		handlerPreview(url) {
			console.log("handlerPreview", url);
			if (url) {
				uni.previewImage({
					// 先filter找出为图片的item，再返回filter结果中的图片url
					urls: [url],
					current: url,
					fail() {
						uni.$u.toast('预览图片失败')
					},
				});
			} else {
				uni.$u.toast('暂无图片')
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.appoint-food-details {
	position: relative;
	// min-height: 100vh;
	height: 100%;
	// background-image: url($imgBasePath + '/foodDetails.png');
	// background-size: 750rpx 308rpx;
	// background-repeat: no-repeat;
	padding: 0 20rpx;
	.foot-fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10070;
		.cart {
			height: 100rpx;
			box-sizing: content-box;
			padding-bottom: env(safe-area-inset-bottom);
			position: relative;
			z-index: 10070;
			background-color: #fff;
			border-top: $border-base;
			.total-order {
				margin-left: 150rpx;
			}

			.cart-num {
				position: absolute;
				left: 28rpx;
				top: -25rpx;
				width: 100rpx;
				height: 100rpx;
				background: $color-primary;
				box-shadow: 0 0 12rpx 0rpx $color-primary;
				border-radius: 50%;
			}
		}
	}
	.tag-list {
		.tag-item {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: $font-size-xs;
			padding: 10rpx 16rpx;
			border-radius: 6rpx;
			border: $border-base;
			margin-right: 20rpx;
		}

		.tag-recommend {
			color: #11e69e;
			border-color: #dcf9ee;
			background-color: #dcf9ee;
		}
		.tag-not-recommend {
			color: #ff5f5f;
			border-color: #ffe7e7;
			background-color: #ffe7e7;
		}
	}
	.container {
		padding: 30rpx;
		background: #ffffff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		position: relative;
		.discount-text {
			text-decoration: line-through;
		}
		.ingredients-box {
			border-bottom: 1px solid #f2f3f5;
			color: #101010;
		}
		.ingredients-title {
			// width: 105px;
			// overflow: hidden;
			// white-space: nowrap;
			// text-overflow: ellipsis;
		}
	}
	.nutrition-box {
		.nutrition-wrap {
			border-bottom: $border-base;
		}
		.nutrition-width {
			width: 33.33%;
		}
		.item {
			padding: 12rpx 0;
		}
	}
	.food-lamp-box {
		max-height: 600rpx;
		overflow: auto;
		.modal-food-box {
			padding: 20rpx;
			border-radius: 10rpx;
			background-color: #f6f7fb;
		}
	}
	.ring-charts-box {
		margin-top: 20rpx;
		border-radius: 16rpx;
		background-color: #f6f7fb;
		.charts-box {
			height: 250rpx;
			width: 400rpx;
		}
	}
	.choose-specs {
		width: 600rpx;
		padding: 30rpx 0;
		.food-info-img {
			width: 140rpx;
			height: 140rpx;
			border-radius: 18rpx;
			margin-right: 20rpx;
		}
		.food-info-name {
			max-width: 350rpx;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}
		.text-ash {
			color: #9da2a7;
		}
		.already-choose {
			background-color: #f6f8f8;
			padding: 14rpx 30rpx;
		}
		.food-total {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 30rpx 30rpx 0;
			line-height: 36rpx;
		}
		.specs-list {
			display: flex;
			flex-wrap: wrap;
			padding-bottom: 30rpx;
			.specs-item {
				margin: 10rpx 20rpx 0 0;
				padding: 0 20rpx;
				border: 1rpx #e3e3e3 solid;
				border-radius: 6rpx;
				text-align: center;
				font-size: 20rpx;
				line-height: 46rpx;
				max-width: 210rpx;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}
			.activeSpecs {
				color: $color-primary;
				border: 1rpx solid $color-primary;
			}
		}
	}
	.circular {
		display: inline-block;
		width: 20rpx;
		height: 20rpx;
		border-radius: 20rpx;
	}
	.sourced-code-popup {
		width: 600rpx;
		height: 600rpx;
	}
	.disabled {
		opacity: 0.3;
	}
}
</style>
