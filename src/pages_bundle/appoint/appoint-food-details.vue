<template>
	<view :style="theme.style" class="appoint-food-details">
		<view class="container flex">

			<u-image width="120rpx" height="120rpx" radius="10" @click="handlerPreview(dataFood.image)" :src="dataFood.image? dataFood.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"></u-image>
			<view class="flex-1 m-l-20">
				<view class="flex row-between">
					<view class="name">{{ dataFood.food_name }}</view>
					<view v-if="dataFood.stock!==-1" class="mini muted m-t-10">库存{{ dataFood.stock }}份</view>
				</view>
				<view class="m-t-10" v-if="Object.keys(foodDetail).length">
					<text class="xs">{{ foodDetail.food_nutrition_info.energy_kcal }}</text>
					<text class="muted xs p-r-20">kcal/100g</text>
				</view>
				<view class="flex row-between col-center p-t-10">
					<view class="">
						<price-format :price="dataFood.discount" :size="30" color="#FF5757"></price-format>
						<!--240708隐藏折扣价格 -->
						<!-- <price-format
							class="m-l-10"
							:price="dataFood.price"
							:size="24"
							color="#999"
							:lineThrough="true"
						></price-format> -->
					</view>
					<!-- 数量加减 -->
					<number-box
						v-if="dataFood.stock && !dataFood.display_style_button"
						v-model="dataFood.count"
						:async-change="true"
						@change="selectCart($event, dataFood)"
					></number-box>
					<view
						v-else-if="dataFood.stock && dataFood.display_style_button"
						class=""
						style="position: relative;"
						@click.stop="openSpecsDialog(dataFood)"
					>
						<u-button
							text="选规格"
							type="primary"
							:customStyle="customBtnStyle"
							:color="variables.colorPrimary"
							size="mini"
						></u-button>
						<u-badge bg-color="#FF5757" max="99" :value="dataFood.count" absolute :offset="[-12, -10]"></u-badge>
					</view>
				</view>
			</view>
		</view>
		<view class="container" v-if="dataFood.is_healthy_info">
			<view class="flex row-between">
				<view class="f-w-500 flex-1" v-if="foodLight[foodDetail.food_light]">
					<text
						class="circular m-r-10"
						:style="{
							backgroundColor: foodLight[foodDetail.food_light].color
						}"
					></text>
					<text>{{ foodLight[foodDetail.food_light].name }}：</text>
					<text class="muted">{{ foodLight[foodDetail.food_light].label }}</text>
				</view>
				<u-icon name="question-circle" color="#d2d2d2" size="28" @click="foodLampShowTips = true"></u-icon>
			</view>
			<view class="tag-list">
				<!-- <view class="flex col-center flex-wrap m-t-20">
					<view class="tag-item tag-recommend m-b-20"><u-icon name="thumb-up" color="#11e69e" size="28"></u-icon></view>
					<block v-if="labelData && labelData.recommend_label.length">
						<view v-for="(item, index) in labelData.recommend_label" :key="index" class="tag-item m-b-20">
							<text>{{ item }}</text>
						</view>
					</block>
					<block v-else><view class="m-b-20">--</view></block>
				</view>
				<view class="flex col-center flex-wrap m-t-20">
					<view class="tag-item  tag-not-recommend m-b-20">
						<u-icon name="warning" color="#ff5f5f" size="28"></u-icon>
					</view>
					<block v-if="true">
						<view v-for="(item, index) in tagLists" :key="index" class="tag-item m-b-20">
							<text>{{ item }}</text>
						</view>
					</block>
					<block v-else><view class="m-b-20">--</view></block>
				</view> -->
				<view class="flex col-center flex-wrap m-t-20">
					<view v-for="(item, index) in tagLists" :key="index" class="tag-item m-b-20">
						<text>{{ item }}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="container" v-if="ingredientsList && ingredientsList.length">
			<view class="f-w-500">食材组成</view>
			<u-read-more ref="uReadMore" show-height="260rpx" text-indent="0" closeText="展开更多" :toggle="true" color="#999999">
				<view class="m-b-20">
					<view
						class="p-t-20 p-l-20 p-b-10 flex row-between col-center ingredients-box"
						v-for="(ingredientsItem, ingredientsIndex) in ingredientsList"
						:key="ingredientsIndex"
					>
						<u-image width="60rpx" height="60rpx" radius="10" :src="ingredientsItem.ingredient_image? ingredientsItem.ingredient_image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/miniapp/c16bfc0ab9b1a8168044474e037b71591684378025011.png'"></u-image>
						<view class="flex-1 m-l-20">
							<view class="xs m-b-10">{{ ingredientsItem.ingredient_name }}</view>
							<view class="flex" v-if="dataFood.is_healthy_info">
								<view
									class="mini muted flex"
									v-for="(ingredientNutritionItem,
									ingredientNutritionIndex) in ingredientsItem.ingredient_nutrition_list"
									:key="ingredientNutritionIndex"
								>
									<view>{{ ingredientNutritionItem.name }}</view>
									<view>
										{{
											(ingredientNutritionIndex == ingredientsItem.ingredient_nutrition_list.length - 1 &&
												ingredientNutritionItem.value) ||
												ingredientNutritionItem.value + '/'
										}}
									</view>
								</view>
							</view>
						</view>
						<view
							class="primary"
							v-if="ingredientsItem.ingredient_sourced_code"
							@click="sourcedCodeClick(ingredientsItem)"
						>
							溯源码
						</view>
					</view>
				</view>
			</u-read-more>
		</view>
		<view class="container" :class="{'nutrient-box': allCount > 0}" v-if="dataFood.is_healthy_info">
			<view class="flex row-between">
				<view>
					<text class="f-w-500">营养成分</text>
					<!-- <text class="muted">(每100克含量)</text> -->
				</view>
				<view class="switchButton flex">
					<view v-if="showNutritionWeight" class="gramWeight flex f-w-300 mini bg-white flex-center" :class="{'active-btn': nutritionType === 'weight'}" @click="changeNutritionType('weight')">
						<text>菜品克重</text>
					</view>
					<view class="percentage flex f-w-300 mini bg-white flex-center" :class="{'active-btn': nutritionType === 'default'}" @click="changeNutritionType('default')">
						<text>100g</text>
					</view>
				</view>
			</view>
			<view class="ring-charts-box bg-white">
				<view class="flex col-center">
					<view class="charts-box">
						<qiun-data-charts type="ring" :canvas2d="canvas2dStatus" :opts="optsring" :chartData="chartData" />
					</view>
					<view class="flex-1 muted m-l-40 m-r-20 xs">
						<view v-for="(nutrition, index) in nutrientDictList" :key="nutrition.key" class="flex row-between" :class="[index === 0 ? 'p-t-0' : 'p-t-20']">
							<view class="black">
								<view class="inline icon-charts m-r-10" :style="{ backgroundColor: nutrition.color }"></view>
								{{ nutrition.name }}
							</view>
							<view class="">
								<text>{{ funNutritionPercentage(foodNutritionInfo, nutrition.value) }}%</text>
								<text class="p-l-20">{{ nutrition.value }}克</text>
							</view>
						</view>
						<!-- <view class="p-t-20">
							<text>{{ funNutritionPercentage(foodNutritionInfo, nutrientDictList[1].value) }}%</text>
							<text class="p-l-20">{{ nutrientDictList[1].value }}克</text>
						</view>
						<view class="p-t-20">
							<text>{{ funNutritionPercentage(foodNutritionInfo, nutrientDictList[2].value) }}%</text>
							<text class="p-l-20">{{ nutrientDictList[2].value }}克</text>
						</view> -->
					</view>
				</view>
				<!-- <view class="text-center muted p-b-20">大约需要慢跑{{ energyMin }}分钟</view> -->

				<view class="nutrition-wrap m-t-30 xs p-b-20 flex row-between grey">
					<text class="text-center nutrition-width">营养元素</text>
					<text class="text-center nutrition-width">摄入量</text>
					<text class="text-center nutrition-width">单位</text>
				</view>
				<view class="item" v-for="(item, index) in nutrientDictList" :key="index">
					<view class="nr black f-w-500 flex row-between m-b-10">
						<view class="text-center nutrition-wrap nutrition-width p-b-20">{{ item.name }}</view>
						<view class="text-center nutrition-wrap nutrition-width p-b-20">{{ item.value }}</view>
						<view class="text-center nutrition-wrap nutrition-width p-b-20">g</view>
					</view>
				</view>
				<view class="flex col-center row-center muted" @click="openMore">
					<text>更多营养元素</text>
					<u-icon name="arrow-down" color="#93989e" size="26"></u-icon>
				</view>
			</view>
		</view>
		<!--需要查看菜品按钮-->
		<view v-show="isShowButton" :class="['check-button', allCount > 0 ? 'm-b-140' : 'm-b-40']" @click="checkDishNutrition">
			<span>需要查看菜品营养</span>
		</view>
		<!-- <view class="container nutrition-box" style="margin-bottom: 130rpx;">
			<view class="nutrition-wrap m-t-30 xs p-b-20 flex row-between primary">
				<text class="text-center nutrition-width">营养元素</text>
				<text class="text-center nutrition-width">摄入量</text>
				<text class="text-center nutrition-width">单位</text>
			</view>
			<view class="item" v-for="(item, index) in nutrientDictList" :key="index">
				<view class="nr black f-w-500 flex row-between m-b-10">
					<view class="text-center nutrition-wrap nutrition-width p-b-20">{{ item.name }}</view>
					<view class="text-center nutrition-wrap nutrition-width p-b-20">{{ item.value }}</view>
					<view class="text-center nutrition-wrap nutrition-width p-b-20">g</view>
				</view>
			</view>
			<view class="flex col-center row-center muted" @click="openMore">
				<text>更多营养元素</text>
				<u-icon name="arrow-down" color="#93989e" size="26"></u-icon>
			</view>
		</view> -->
		<view class="foot-fixed" v-show="allCount">
			<view class="cart col-center flex">
				<view class="flex-1">
					<view class="total-order">
						<text class="md muted">合计</text>
						<price-format class="m-l-10" :price="totalAmount" :size="36"></price-format>
					</view>
				</view>
				<view class="cart-num flex flex-center" @tap="showCart = !showCart">
					<u-badge bg-color="#FF5757" max="99" :value="allCount" absolute :offset="[0, 0]"></u-badge>
					<image class="icon-lg" :src="themeImgPath.img_bundle_shop_cart"></image>
				</view>
					<u-button
						:customStyle="{
							width: '200rpx',
							'border-radius': 0
						}"
						type="primary"
						size="large"
						:color="isVerificationOrderFlag?variables.colorPrimary:'#aaaaaa'"
						:text="formateText()"
						@click="gotoConfirmOrder"
					></u-button>
			</view>
		</view>
		<!-- 购物车列表 -->
		<cart-popup
			v-model="showCart"
			:lists="cartLists"
			:time="currentDate('yyyy-mm-dd 周w')"
			@clear="clearCard"
			@countchange="handleCountChange"
		></cart-popup>

		<!-- 选择规格 -->
		<u-popup :show="showSpecs" mode="center" @close="showSpecs = false" round="20">
			<view class="choose-specs">
				<view class="food-info flex m-l-30">
					<image class="food-info-img" :src="foodInfo.image? foodInfo.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"></image>
					<view class="">
						<view class="xl f-w-600 food-info-name">{{ foodInfo.food_name }}</view>
						<view style="height: 28rpx;" class="mini m-t-8 m-b-16 text-ash">
              <text v-if="foodInfo.stock!==-1">库存{{ foodInfo.stock }}份</text>
            </view>
						<price-format :price="specsPrice" :size="30" color="#FF5757"></price-format>
					</view>
				</view>
				<scroll-view style="max-height: 400rpx" :scroll-y="true">
					<view v-if="foodInfo.food_spec.length" class="m-t-30 m-l-30">
						<view class="mini text-ash">规格</view>
						<view class="specs-list">
							<view
								v-for="item in foodInfo.food_spec"
								:key="item.id"
								:class="['specs-item', item.id === specsId ? 'activeSpecs' : '']"
								@click="chooseSpecs('specs', item)"
							>
								{{ item.name }}
							</view>
						</view>
					</view>
					<view v-if="foodInfo.food_taste && foodInfo.food_taste.length" class="m-t-30 m-l-30">
						<view class="mini text-ash">口味</view>
						<view class="specs-list">
							<view
								:class="['specs-item', item.id === tasteId ? 'activeSpecs' : '']"
								v-for="item in foodInfo.food_taste"
								:key="item.id"
								@click="chooseSpecs('taste', item)"
							>
								{{ item.name }}
							</view>
						</view>
					</view>
				</scroll-view>
				<view class="already-choose m-t-30 mini">
					<text class="text-ash">已选：</text>
					{{ specsName }}{{ specsName && tasteName ? '、' : '' }}{{ tasteName }}
				</view>
				<view class="food-total">
					<view class="">
						<text class="lg m-r-20">合计</text>
						<price-format class="m-t-5" :size="40" :price="specsTotalPrice" color="#FF5757"></price-format>
					</view>
					<view class="">
						<number-box
							v-model="foodInfoCount"
							:async-change="true"
							@change="selectCart($event, foodInfo, 'specs')"
						></number-box>
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup :show="sourcedCodeShow" mode="center" round="20" @close="sourcedCodeShow = false">
			<view class="sourced-code-popup">
				<view class="text-center p-t-30 p-b-30">食品溯源码</view>
				<view class="flex flex-center">
					<u-image :showLoading="true" :src="ingredientSourcedCodeImg" width="400rpx" height="400rpx"></u-image>
				</view>
			</view>
		</u-popup>
		<u-modal
			:show="foodLampShowTips"
			title="食物灯"
			confirmText="我知道了"
			:confirmColor="variables.colorPrimary"
			@confirm="foodLampShowTips = false"
		>
			<view class="food-lamp-box" slot="default">
				<text>​食物灯根据食物营养价值的高低，能帮助你快速判断该食物是否适合食用</text>
				<view class="modal-food-box m-t-20 m-b-20">
					<view class="m-b-15 m-t-10">
						<text class="circular m-r-20" style="backgroundColor:#63d496"></text>
						<text>绿灯食物：</text>
						<text class="muted">放心吃</text>
					</view>
					<view class="m-b-15 m-t-10">
						<text class="circular m-r-20" style="backgroundColor:#f7d149"></text>
						<text>黄灯食物：</text>
						<text class="muted">适量吃</text>
					</view>
					<view class="m-b-15 m-t-10">
						<text class="circular m-r-20" style="backgroundColor:red"></text>
						<text>红灯食物：</text>
						<text class="muted">建议少吃</text>
					</view>
				</view>
				<view class="f-w-500 xxl">GI</view>
				<view class="modal-food-box xs m-t-20 m-b-20">
					是指血糖生成指数，影响餐后2小时内血糖变化，低GI的食物有助于稳定餐后血糖，带来长时间的饱腹感
				</view>
				<view class="f-w-500 xxl">GL</view>
				<view class="modal-food-box xs m-t-20 m-b-20">是指升糖负荷，低GL的食物有助于稳定餐后血糖，降低食欲</view>
				<view class="f-w-500 xxl">嘌呤</view>
				<view class="modal-food-box xs m-t-20 m-b-20">
					是指人体内中的碱基，在体内氧化变成尿酸，高嘌呤的食物痛风人群要注意，过多食用会引起关节疼痛
				</view>
				<view class="f-w-500 xxl">高纤维</view>
				<view class="modal-food-box xs m-t-20 m-b-20">
					纤维量高的食物具有很强的饱腹感，饥饿感来的更慢，还会缓解便秘，调节体重
				</view>
				<view class="f-w-500 xxl">高钠食物</view>
				<view class="modal-food-box xs m-t-20 m-b-20">含钠量高的食物容易引起水肿和高血压</view>
				<view class="f-w-500 xxl">中高饱和脂肪、胆固醇的食物</view>
				<view class="modal-food-box xs m-t-20 m-b-20">这类食物容易引起心脑血管等疾病</view>
				<view class="muted mini p-t-20">参考依据:《中国营养科学全书》《中国居民膳食指南2022版》</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
import { timeFormat } from '@/utils/date'
import { apiShopcardCalcList, apiShopcardAdd, apiShopcardDelete, apiShopcardClean, apiBookingHealthyNutritionFeedback } from '@/api/shopcart'
import { getApiReservationGetFoodNutrition } from '@/api/healthy.js'
import { checkClient, formateVisitorParams, divide, deepClone } from '@/utils/util.js'
import { formateBtnText , verificationOrderData} from './orderUtil'
import Cache from '@/utils/cache'
import NP from '@/utils/np.js'

export default {
	data() {
		return {
			platform: checkClient(), // 平台， 微信or支付宝
			canvas2dStatus: false,
			customBtnStyle: {
				minWidth: '80rpx',
				height: '40rpx',
				lineHeight: '40rpx'
			},
			showCart: false,
			totalAmount: 0,
			allCount: 0,
			count: 0,
			cartLists: [],
			params: {}, //外面传进来的params
			dataFood: {
				nutrition_info: {}
			}, //菜品数据
			showSpecs: false,
			foodInfo: {
				food_spec: [],
				food_taste: []
			},
			specsId: -1,
			specsName: '',
			tasteId: -1,
			tasteName: '',
			optsring: {
				dataLabel: false,
				labelShow: false,
				title: {
					name: '0',
					fontSize: uni.upx2px(30),
					color: '#1d201e'
				},
				subtitle: {
					name: 'kcal',
					fontSize: 13,
					color: '#8f9295'
				},
				legend: {
					show: false,
					position: 'right',
					lineHeight: 25,
					float: 'left',
					itemGap: 0,
					padding: 0,
					margin: 0
				},
				extra: {
					ring: {
						ringWidth: 10
					},
					tooltip: {
						showBox: false
					}
				}
			},
			chartData: {},
			nutrientDictList: [
				{
					name: '碳水化合物',
					key: 'carbohydrate',
					value: 0,
					color: '#a475fa'
				},
				{
					name: '蛋白质',
					key: 'protein',
					value: 0,
					color: '#fec26d'
				},
				{
					name: '脂肪',
					key: 'axunge',
					value: 0,
					color: '#ff9e69'
				}
			],
			sourcedCodeShow: false,
			ingredientSourcedCodeImg: '',
			nutritionKey: ['carbohydrate', 'axunge', 'protein'],
			foodDetail: {},
			ingredientsList: [],
			foodNutritionInfo: {},
			energyMin: 0,
			labelData: {
				recommend_label: [],
				not_recommend_label: []
			},
			foodLight: {
				green: {
					name: '绿灯食物',
					label: '推荐',
					color: '#63d496'
				},
				yellow: {
					name: '黄灯食物',
					label: '少食',
					color: '#f7d149'
				},
				red: {
					name: '红灯食物',
					label: '禁食',
					color: 'red'
				}
			},
			foodLampShowTips: false,
			verificationData: {},//用来暂存每个消费点的配送费
			isVerificationOrder: true,//是否满足起送费
			verificationOrderError: '',//起送费错误提示
			orderOrgId: '',//配送点Id
			isVerificationOrderFlag: true,//其中有个一个满足起送费的标志
			currentTime: '', //订餐当前时间
			nutritionType: 'default', //切换按钮
			showNutritionWeight: false,
			specWeightRatio: 1,
			tagLists: [],
			isShowButton: false
			// 三大营养合计
			// nutritionInfo: {
			// 	carbohydrate: 0,
			// 	protein: 0,
			// 	axunge: 0,
			// 	percentageTotal: 0
			// },
			// 营养元素
			// nutritionList: []
		}
	},
	computed: {
		currentDate() {
			return format => timeFormat(Date.now(), format)
		},
		foodInfoCount: {
			get() {
				let count = 0
				this.cartLists.map(item => {
					if (item.date === this.params.date) {
						item.stall.map(stall => {
							if (stall.meal_type === this.params.meal_type) {
								stall.food.map(food => {
									if (
										Number(food.food_id) === this.foodInfo.food_id &&
										food.spec_id === this.specsId &&
										food.taste_id === this.tasteId
									) {
										count = food.count
									}
								})
							}
						})
					}
				})
				return count
			},
			set(val) {}
		},
		specsPrice() {
			let price = 0
			if (this.foodInfo.food_spec && this.foodInfo.food_spec.length) {
				price = this.foodInfo.food_spec.find(item => item.id === this.specsId).food_price
			} else {
				this.foodInfo.price
			}
			return price
		},
		specsTotalPrice() {
			let price = 0
			if (this.foodInfo.food_spec && this.foodInfo.food_spec.length) {
				price = this.foodInfo.food_spec.find(item => item.id === this.specsId).food_price * this.foodInfoCount
			} else {
				this.foodInfo.discount * this.foodInfoCount
			}
			return price
		}
	},
	watch: {
		'foodDetail.food_nutrition_info': {
			handler(newVal) {
				let flag = true
				let nutrition = deepClone(newVal)
				let ratio = this.nutritionType === 'default' ? 1 : this.specWeightRatio
				let newObj = {}
				for (const key in nutrition) {
					let item = nutrition[key];
					if (key === 'element' || key === 'vitamin') {
						item = JSON.parse(item)
						Object.keys(item).forEach(v => {
							item[v] = this.timesHandle(item[v], ratio)
						})
						nutrition[key] = item
					} else {
						nutrition[key] = this.timesHandle(item, ratio)
					}
				}
				for (const key in nutrition) {
					if (key === 'element' || key === 'vitamin') {
						for (let k in nutrition[key]) {
							if (nutrition[key][k] !== 0) {
								flag = false
							}
						}
					} else {
						if (nutrition[key] !== 0 && key !== 'energy_kcal') {
							flag = false
							console.log(key, nutrition[key])
						}
					}
					
				}
				console.log(flag)
				this.isShowButton = flag
			}
		}
	},
	onLoad(option) {
		// 支付宝不支持2d
		this.canvas2dStatus = this.platform === 'mp-alipay' ? false : true
		this.params = this.$decodeQuery(this.$Route.query.params)
		this.currentTime = this.params.currentDate || ''
		this.dataFood = this.$decodeQuery(this.$Route.query.data)
		console.log(22334, this.dataFood)
		// this.dataFood = deepClone(this.$decodeQuery(this.$Route.query.data))
		// this.dataFood = deepClone(this.dataFood)
		this.verificationData =	Cache.get('verificationOrder')
		// 当有规格才显示菜品重量切换按钮
		if (this.dataFood.food_spec && this.dataFood.food_spec.length) {
			this.showNutritionWeight = true
			// 重量拿默认的
			for (let index = 0; index < this.dataFood.food_spec.length; index++) {
				const item = this.dataFood.food_spec[index];
				if (item.name === '默认') {
					this.specWeightRatio = item.weight / 100
					break
				}
			}
			// 防止没有默认的情况拿第一个吧
			if (!this.specWeight) {
				this.specWeightRatio = this.dataFood.food_spec[0].weight / 100
			}
		}
		this.getCartLists()
		// 获取营养
		this.getReservationGetFoodNutrition()
		// 获取食材组成
		console.log('params', this.params)
	},
	onShow() {
	},
	mounted() {},
	methods: {
		chooseSpecs(type, e, index) {
			if (type === 'specs') {
				this.specsId = e.id
				this.specsName = e.name
			} else if (type === 'taste') {
				this.tasteId = e.id
				this.tasteName = e.name
			}
		},
		getReservationGetFoodNutrition(ids) {
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
			getApiReservationGetFoodNutrition(formateVisitorParams({
				food_id_list: [this.dataFood.food_id],
				org_id: this.dataFood.org_id
			}))
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.labelData = res.data.label_data[this.dataFood.food_id]
						this.foodDetail = res.data.results[0]
						this.foodNutritionInfo = this.foodDetail.food_nutrition_info
						// 换算成分钟
						let energyMinTotal = 300 / 40
						// 固定 每300卡跑40分钟
						this.energyMin = (this.foodNutritionInfo.energy_kcal / energyMinTotal).toFixed(0)
						// 因为只有一条菜品
						let ingredientList = res.data.results[0].ingredient_list
						this.initIngredient(ingredientList)
						this.initNutritionHandle()
						// 初始化食物灯
						let foodLightType = {
							'yellow': 'suitable_label',
							'green': 'recommend_label',
							'red': 'not_recommend_label'
						}
						this.tagLists = res.data.nutrition_label[this.dataFood.food_id][foodLightType[this.foodDetail.food_light]]
						// 当有菜品克重时给默认为菜品克重
						if (this.specWeightRatio) {
							this.changeNutritionType('weight')
						}
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.hideLoading()
					uni.$u.toast(error.message)
				})
		},
		plusHandle(a, b) {
			return NP.plus(a, b)
		},
		divideHandle(a, b) {
			return NP.divide(a, b)
		},
		timesHandle(a, b) {
			return NP.times(a, b)
		},
		// 初始化营养数据
		initNutritionHandle(ratio = 1) {
			this.foodNutritionInfo.percentageTotal = Number(
				(
					this.foodNutritionInfo.carbohydrate * ratio +
					this.foodNutritionInfo.protein * ratio +
					this.foodNutritionInfo.axunge * ratio
				).toFixed(2)
			)
			this.nutrientDictList = this.nutrientDictList.map(v => {
				v.value = this.timesHandle(this.foodNutritionInfo[v.key], ratio)
				return v
				// this.$set(v, 'value', this.foodNutritionInfo[v.key] * ratio)
			})
			let data = this.foodDetail.food_nutrition_info
			this.optsring.title.name = data.energy_kcal ? this.timesHandle(data.energy_kcal, ratio) : '0'
			this.chartData = {
				series: [
					{
						data: [
							{
								name: '碳水化合物',
								color: '#a475fa',
								value: this.timesHandle(data.carbohydrate, ratio),
								labelShow: false
							},
							{
								name: '蛋白质',
								color: '#fec26d',
								value: this.timesHandle(data.protein, ratio),
								labelShow: false
							},
							{
								name: '脂肪',
								color: '#ff9e69',
								value: this.timesHandle(data.axunge, ratio),
								labelShow: false
							}
						]
					}
				]
			}
		},
		funNutritionPercentage(item, data) {
			if (item.hasOwnProperty ('percentageTotal')){
				let percentage = this.divideHandle(data, item.percentageTotal) * 100
				return percentage ? percentage.toFixed(2) : 0
			}
		},
		// 食材组成
		initIngredient(list) {
			if (list && list.length) {
				this.ingredientsList = list.map(v => {
					v.ingredient_sourced_code = v.supplier_list.length ? v.supplier_list[0].sourced_code : ''
					v.ingredient_nutrition_list = []
					if (
						v.ingredient_nutrition_info &&
						Object.keys(v.ingredient_nutrition_info) &&
						Object.keys(v.ingredient_nutrition_info).length
					) {
						for (let nutrition in v.ingredient_nutrition_info) {
							// 因为目前只需要3个营养 nutritionKey
							if (this.nutritionKey.includes(nutrition)) {
								v.ingredient_nutrition_list.push({
									key: nutrition,
									name: this.nutritionName(nutrition),
									value: v.ingredient_nutrition_info[nutrition] + 'g'
								})
							}
						}
					}
					return v
				})
				if(this.ingredientsList.length){
					this.$nextTick(() => {
						this.$refs.uReadMore.init()
					})
				}
			}
		},
		getCartLists() {
			return new Promise((reslove, reject) => {
				apiShopcardCalcList(formateVisitorParams({
					payment_order_type: this.params.payment_order_type,
					take_meal_type: this.params.take_meal_type,
					company_id: this.params.company_id
				}))
					.then(res => {
						this.cartLists = res.data.shopcard
						this.totalAmount = res.data.all_date_fee
						this.allCount = res.data.all_date_count
						this.orderOrgId = this.params.org_id
						this.verificationOrder(this.cartLists)
						this.changeFood()
						reslove()
					})
					.catch(err => {
						uni.$u.toast(err)
					})
			})
		},
		changeFood() {
			const foods = []
			if (this.cartLists && this.cartLists.length) {
				this.cartLists.forEach(item => {
					item.stall.forEach(sitem => {
						sitem.food.forEach(fitem => {
							foods.push({
								...fitem,
								meal_type: sitem.meal_type,
								date: item.date
							})
						})
					})
				})
			}
			// this.$set(this.dataFood, 'count', 0)
			// // 兼容有规格的情况
			let count = 0
			foods.filter(item => {
				if (item.meal_type == this.params.meal_type && item.food_id == this.dataFood.food_id && item.date === this.params.date) {
					this.$set(this.dataFood, 'count', item.count)
				}
			})
				// const tempObj = foods.filter(item => {
				// 	return item.meal_type == this.params.meal_type && item.food_id == this.dataFood.food_id && item.date === this.params.date
				// })
				// if (tempObj.length !== 0) {
				// 	// this.$set(this.dataFood, 'count', tempObj[0].count)
				// 	tempObj.forEach((item) => {
				// 		this.$set(this.dataFood, 'count', item.count)
				// 	})
				// }
		},
		clearCard() {
			apiShopcardClean(formateVisitorParams({
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				company_id: this.params.company_id
			}))
				.then(res => {
					uni.$u.toast('清空成功')
					this.showCart = false
					this.dataFood.count = 0
					this.getCartLists()
				})
				.catch(err => {
					uni.$u.toast(err.message)
				})
		},
		handleCountChange(data) {
			this.selectCart(data.value, data.food)
		},
		// 购物车加减
		async selectCart(val, item, type) {
			console.log('看看穿了什么东西', val, item, type)
			const params = {
				company_id: this.params.company_id,
				payment_order_type: this.params.payment_order_type,
				food_id: item.obj_name == 'set_meal' ? '' : item.food_id,
				date: item.date || this.params.date,
				take_meal_type: this.params.take_meal_type,
				meal_type: item.meal_type || this.params.meal_type,
				organization_id: item.org_id || item.organization_id,
				organization_alias: item.org_name || item.organization_alias,
				consume_type: item.consume_type,
				fuwu_fee: item.fuwu_fee,
				elevator_fee: item.elevator_fee,
				menu_food_id: item.menu_info.menu_food_id,
				menu_food_object: item.menu_info.menu_food_object,
				spec_id: item.spec_id,
				taste_id: item.taste_id,
				buy_limit_food_count:this.params.buyLimitFoodCount ,// 单人单餐菜品限制份数
				cupboard_takeaway_fee:this.params.cupboardTakeawayFee, // 取餐柜打包费
				stall_id: this.dataFood.stall_id,
				org_id: this.params.org_id,
			}
			if (this.params.reservationSettingId) {
				params.reservation_setting_id = this.params.reservationSettingId
			}
			if (type === 'specs') {
				if (this.specsId) {
					params.spec_id = this.specsId
				}
				if (this.tasteId) {
					params.taste_id = this.tasteId
				}
			}
			// 20250506 后端说菜品也要加规格id喔
			if (item.obj_name !== 'set_meal' && type !== 'specs') {
				let foodSpec = item.food_spec || []
				if (foodSpec && foodSpec.length > 0) {
					let specId = foodSpec[0].id
					if (specId) {
						params.spec_id = specId
					}
				}
			}
			if (item.obj_name == 'set_meal') {
				params.set_meal_id = item.set_meal_id
				params.set_meal_style_data = []
				item.set_meal_style_data.map(v => {
					let obj = {
						set_meal_style_id: '',
						food_id: '',
						spec_id: ''
					}
					obj.set_meal_style_id = v.style_id
					obj.food_id = v.food_id
					obj.spec_id = v.spec_id
					params.set_meal_style_data.push(obj)
				})
			}
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			if ((!type && val > item.count) || (type === 'specs' && val > this.foodInfoCount)) {
				await apiShopcardAdd(formateVisitorParams(params))
					.then(res => {
						uni.hideLoading()
						if (res.code == 0) {
							this.getCartLists()
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
			} else {
				// 新增代码 --- 解决无法最后清零的问题
				if (this.dataFood.count === 0) return
				await apiShopcardDelete(formateVisitorParams(params))
					.then(res => {
						uni.hideLoading()
						if (res.code == 0) {
							this.dataFood.count--
							this.getCartLists()
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
			}
		},
		openSpecsDialog(info) {
			this.showSpecs = true
			this.foodInfo = info
			if (this.foodInfo.food_spec.length) {
				this.specsId = this.foodInfo.food_spec[0].id
				this.specsName = this.foodInfo.food_spec[0].name
			} else {
				this.specsId = -1
				this.specsName = ''
			}
			if (this.foodInfo.food_taste.length) {
				this.tasteId = this.foodInfo.food_taste[0].id
				this.tasteName = this.foodInfo.food_taste[0].name
			} else {
				this.tasteId = -1
				this.tasteName = ''
			}
		},
		openMore() {
			let nutrition = deepClone(this.foodDetail.food_nutrition_info)
			let ratio = this.nutritionType === 'default' ? 1 : this.specWeightRatio
			for (const key in nutrition) {
				let item = nutrition[key];
				if (key === 'element' || key === 'vitamin') {
					item = JSON.parse(item)
					Object.keys(item).forEach(v => {
						item[v] = this.timesHandle(item[v], ratio)
					})
					nutrition[key] = JSON.stringify(item)
				} else {
					nutrition[key] = this.timesHandle(item, ratio)
				}
			}
			this.$miRouter.push({
				path: '/pages_bundle/appoint/nutrition-details',
				query: {
					data: this.$encodeQuery(nutrition)
				}
			})
		},
		sourcedCodeClick(data) {
			this.sourcedCodeShow = true
			this.ingredientSourcedCodeImg = data.ingredient_sourced_code
		},
		nutritionName(key) {
			let name = ''
			switch (key) {
				case 'energy_kcal':
					name = '千卡'
					break
				case 'carbohydrate':
					name = '碳水'
					break
				case 'axunge':
					name = '脂肪'
					break
				case 'protein':
					name = '蛋白质'
					break
				default:
					break
			}
			return name
		},
		//格式化按钮文字
		formateText() {
			var cupboardDeliveryFee = ''
			var waimaiDeliveryFee = ''
			var verificationDataList = this.verificationData[this.currentTime] || []
			var findItem = verificationDataList.find(verItem => {
							return verItem.stallId === this.orderOrgId && verItem.mealType === this.params.meal_type
					})
			if(findItem){
				cupboardDeliveryFee = findItem.cupboardDeliveryFee
				waimaiDeliveryFee = findItem.waimaiDeliveryFee
			}		
			return	formateBtnText(this.params,this.isVerificationOrderFlag,cupboardDeliveryFee,waimaiDeliveryFee)  
    },
			/**
		 * 分日期去校验配送费
		 * @param list 配送列表,包含日期，餐段等数据
		 */
		 verificationOrder(list){
			if(this.params.take_meal_type === 'waimai' || this.params.take_meal_type === 'cupboard') {
				var resultData =	verificationOrderData(list,this.params,this.verificationData)
				if(resultData) {
					this.isVerificationOrder = resultData.isVerificationOrder
					this.verificationOrderError = resultData.verificationOrderError
					this.isVerificationOrderFlag = resultData.isVerificationOrderFlag
				}
			}
		},
		//跳转到确认订单页面
		gotoConfirmOrder() {
      if (this.isVerificationOrder) {
				Cache.set('verificationOrder',this.verificationData)
        this.$miRouter.push({
          path: '/pages_bundle/appoint/confirm_order',
          query: {
            buyLimitFoodCount: this.params.buyLimitFoodCount,
            cupboardTakeawayFee:this.params.cupboardTakeawayFee
          }
        })
      }else{
				//错误显示提示
				uni.$u.toast(this.verificationOrderError)
			}
    },
		changeNutritionType(type) {
			this.nutritionType = type
			if (type === 'default') {
				this.initNutritionHandle()
			} else {
				this.initNutritionHandle(this.specWeightRatio)
			}
		},
		checkDishNutrition() {
			apiBookingHealthyNutritionFeedback(
				{
					food_id: this.dataFood.food_id
				}
			)
			.then(res => {
				if (res.code === 0) {
					uni.$u.toast('已收到您的反馈！')
				} else {
					uni.$u.toast('获取失败！')
				}
			})
		},
		// 预览图片
		handlerPreview(url) {
			console.log("handlerPreview", url);
			if (url) {
				uni.previewImage({
					// 先filter找出为图片的item，再返回filter结果中的图片url
					urls: [url],
					current: url,
					fail() {
						uni.$u.toast('预览图片失败')
					},
				});
			} else {
				uni.$u.toast('暂无图片')
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.appoint-food-details {
	position: relative;
	height: 100%;
	padding: 0 20rpx;
	.foot-fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 10070;
		.cart {
			height: 100rpx;
			box-sizing: content-box;
			padding-bottom: env(safe-area-inset-bottom);
			position: relative;
			z-index: 10070;
			background-color: #fff;
			border-top: $border-base;
			.total-order {
				margin-left: 150rpx;
			}

			.cart-num {
				position: absolute;
				left: 28rpx;
				top: -25rpx;
				width: 100rpx;
				height: 100rpx;
				background: $color-primary;
				box-shadow: 0 0 12rpx 0rpx $color-primary;
				border-radius: 50%;
			}
		}
	}
	.tag-list {
		.tag-item {
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: $font-size-xs;
			padding: 10rpx 16rpx;
			border-radius: 6rpx;
			border: $border-base;
			margin-right: 20rpx;
		}

		.tag-recommend {
			color: #11e69e;
			border-color: #dcf9ee;
			background-color: #dcf9ee;
		}
		.tag-not-recommend {
			color: #ff5f5f;
			border-color: #ffe7e7;
			background-color: #ffe7e7;
		}
	}
	.container {
		padding: 30rpx;
		background: #ffffff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		margin-top: 30rpx;
		position: relative;
		.switchButton {
			.gramWeight {
				width: 110rpx;
				height: 42rpx;
				color:  #1D201E;
				border: 1rpx solid #E4E4E4;
				border-right: unset;
				border-radius: 6rpx 0rpx 0rpx 6rpx;
			}
			.percentage {
				width: 110rpx;
				height: 42rpx;
				color:  #1D201E;
				border: 1rpx solid #E4E4E4;
				border-left: unset;
				border-radius: 0rpx 6rpx 6rpx 0rpx;
			}
		}
		.discount-text {
			text-decoration: line-through;
		}
		.ingredients-box {
			border-bottom: 1px solid #f2f3f5;
			color: #101010;
		}
	}
	.check-button {
		display: flex;
		justify-content: center;
		align-items: center;
		span {
			color: $color-primary;
			text-decoration-line: underline;
		}
	}
	.choose-specs {
		width: 600rpx;
		padding: 30rpx 0;
		.food-info-img {
			width: 140rpx;
			height: 140rpx;
			border-radius: 18rpx;
			margin-right: 20rpx;
		}
		.food-info-name {
			max-width: 350rpx;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}
		.text-ash {
			color: #9da2a7;
		}
		.already-choose {
			background-color: #f6f8f8;
			padding: 14rpx 30rpx;
		}
		.food-total {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 30rpx 30rpx 0;
			line-height: 36rpx;
		}
		.specs-list {
			display: flex;
			flex-wrap: wrap;
			padding-bottom: 30rpx;
			.specs-item {
				margin: 10rpx 20rpx 0 0;
				padding: 0 20rpx;
				border: 1rpx #e3e3e3 solid;
				border-radius: 6rpx;
				text-align: center;
				font-size: 20rpx;
				line-height: 46rpx;
				max-width: 210rpx;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}
			.activeSpecs {
				color: $color-primary;
				border: 1rpx $color-primary solid;
			}
		}
	}
	.nutrition-box {
		.nutrition-wrap {
			border-bottom: $border-base;
		}
		.nutrition-width {
			width: 33.33%;
		}
		.item {
			padding: 12rpx 0;
		}
	}
	.food-lamp-box {
		max-height: 600rpx;
		overflow: auto;
		.modal-food-box {
			padding: 20rpx;
			border-radius: 10rpx;
			background-color: #f6f7fb;
		}
	}
	.ring-charts-box {
		margin-top: 20rpx;
		border-radius: 16rpx;
		// background-color: #f6f7fb;
		.charts-box {
			height: 250rpx;
			width: 250rpx;
		}
		.nutrition-wrap {
			border-bottom: $border-base;
		}
		.nutrition-width {
			width: 33.33%;
		}
		.item {
			padding: 12rpx 0;
		}
	}
	.circular {
		display: inline-block;
		width: 20rpx;
		height: 20rpx;
		border-radius: 20rpx;
	}
	.sourced-code-popup {
		width: 600rpx;
		height: 600rpx;
	}
	.active-btn{
		background-color: $color-primary;
		color: #fff !important;
	}
	.icon-charts{
		width: 14rpx;
		height: 14rpx;
		border-radius: 50%;
	}
	.nutrient-box{
		margin-bottom: 40rpx;
	}
}
</style>
