<template>
	<view class="user-appointment" :style="theme.style + `height: ${current == 1 ? '98vh' : '80vh'};`">
		<!-- #ifndef MP-ALIPAY -->
		<u-navbar bg-color="#fff" left-icon-color="#000" @leftClick="leftClick">
			<view class="lg f-w-500" slot="center">我的预约</view>
		</u-navbar>
		<!-- #endif -->

		<view class="" style="background-color: #fff;">
			<u-tabs
				:list="tabs"
				:line-color="variables.colorPrimary"
				:active-style="{ fontWeight: 500, color: '#101010' }"
				@change="tabsChange"
			></u-tabs>
		</view>
		<!-- </view> -->
		<view class="tabs-content flex-1">
			<view style="height: 100%" v-for="(item, index) in tabs" :key="index" v-show="current == index">
        <appointment-lists ref="appointmentListsRef" :bottom="120"  :type="item.type" ></appointment-lists>
      </view>
    </view>
    <view class="appoint-btn flex row-around" v-show="showAppointBtn && !isAddressVisitor">
      <view class="flex-1" @click="gotoPath()">
        <u-button text="去预约" shape="circle" :color="variables.bgLinearGradient1"></u-button>
      </view>
      <view class="flex-1" @click="gotoApply" v-if="current == 1 && cancelReviews">
        <u-button text="批量取消" shape="circle" plain :color="variables.colorPrimary"></u-button>
      </view>
    </view>
		<!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
  </view>
</template>

<script>
import appointmentLists from '../components/appointment/lists'
import Cache from '@/utils/cache'
import { mapMutations, mapGetters } from 'vuex'
import { getApiBookingUserGetCardUserList, getApiBookingUserGetCanteenList, getApiTakeMealTypeList,getApiUserReservationSettings } from '@/api/reservation'
import { formateVisitorParams } from "@/utils/util.js"
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'
export default {
	components: {
		appointmentLists,
		FloatingPopup
	},
	data() {
		return {
			current: 0,
			tabs: [
				{
					name: '单餐',
					type: 'now'
				},
				{
					name: '汇总',
					type: 'all'
				}
			],
			checked: [],
			showAppointBtn: true, // 去预约按钮展示，修复ios fixed层级的问题
			appointmentListsRef: [],
			cancelReviews:true,
			floatingPopupShow: false
		}
	},
	onShow() {
		// 要做刷新哦，防止页面回退数据不是最新的
		this.$nextTick(()=>{
		if (this.$refs.appointmentListsRef) {
			if (Array.isArray(this.$refs.appointmentListsRef)) {
				this.$refs.appointmentListsRef.forEach(v => {
					v.initMescroll()
				})
			}
		}
		})
		this.floatingPopupShow = !this.floatingPopupShow
	},
	computed: {
		...mapGetters(['isAddressVisitor'])
	},
	mounted() {
		this.userinfo = Cache.get('userInfo')
		this.$eventbus.$on('changeCalendarPicker', val => {
			this.showAppointBtn = !val
		})
		this.getUserReservationSettings()
	},
	methods: {
		...mapMutations(['SET_SELECT']),
		tabsChange({ index }) {
			this.current = index
		},
		// 获取食堂
		getBookingUserGetCardUserList() {
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
			getApiBookingUserGetCardUserList({
				company_id: this.userinfo.company_id
			})
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						if (res.data.length && res.data.length <= 1) {
							this.getBookingUserGetCanteenList(res.data[0])
						} else {
							this.$miRouter.push({
								path: '/pages_bundle/select/select_diner?type=reservation'
							})
						}
						// if (this.$store.state.appoint.select.person.person_no) {
						// 	this.getBookingUserGetCanteenList(this.$store.state.appoint.select.person)
						// }
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.hideLoading()
					uni.$u.toast(error.message)
				})
		},
		// 获取食堂组织
		getBookingUserGetCanteenList(personData) {
			getApiBookingUserGetCanteenList({
				groups: personData.groups,
				company_id: this.userinfo.company_id,
				user_id: this.userinfo.user_id
			})
				.then(res => {
					if (res.code == 0) {
						if (res.data.length && res.data.length <= 1) {
							this.getTakeMealTypeList(personData, res.data[0])
						} else {
							this.$miRouter.push({
								path: '/pages_bundle/select/select_diner?type=reservation'
							})
						}
						// if (this.$store.state.appoint.select.org.org_id) {
						// 	this.getTakeMealTypeList(this.$store.state.appoint.select.person,this.$store.state.appoint.select.org.org_id)
						// }
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		// 获取堂食方式
		getTakeMealTypeList(personData, org) {
			getApiTakeMealTypeList({
				groups: personData.groups,
				organization_id: org.org_id
			})
				.then(res => {
					if (res.code == 0) {
						if (res.data.length && res.data.length <= 1) {
							this.SET_SELECT({
								key: 'person',
								data: personData
							})
							this.SET_SELECT({
								key: 'org',
								data: org
							})
							this.SET_SELECT({
								key: 'take_meal_type',
								data: res.data[0].take_meal
							})
							this.$miRouter.push({
								path: '/pages_bundle/appoint/appoint_order'
							})
						} else {
							this.$miRouter.push({
								path: '/pages_bundle/select/select_diner?type=reservation'
							})
						}
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		// 隐藏批量
		getUserReservationSettings() {
			getApiUserReservationSettings(formateVisitorParams({
				company_id: this.userinfo.company_id,
				person_no:this.userinfo.person_no 
			}))
				.then(res => {
					if (res.code == 0) {
						this.cancelReviews = res.data.cancel_reviews
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		gotoPath() {
			this.getBookingUserGetCardUserList()
			// this.$miRouter.push({
			// 	path: '/pages_bundle/select/select_diner?type=reservation'
			// })
		},
		leftClick() {
			if (this.isAddressVisitor) {
				this.$miRouter.pushTab({ path: '/pages/user/user' })
			} else {
				this.$miRouter.pushTab({ path: '/pages/index/index' })
			}
		},
		gotoApply() {
      this.$miRouter.push({
      	path: '/pages_order/review/apply/select_meal',
				query: {
					type: 'reservation'
				}
      })
    }
	}
}
</script>

<style lang="scss">
page {
	padding: 0;
}
.user-appointment {
	/* #ifdef MP-ALIPAY */
	height: 80vh;
	/* #endif */
	/* #ifndef MP-ALIPAY */
	height: 94vh;
	/* #endif */
	display: flex;
	flex-direction: column;
	.tabs-content {
		min-height: 0;
	}

  .appoint-btn {
    position: fixed;
    left: 0;
    bottom: 0rpx;
    width: 100%;
    padding: 20rpx 40rpx;
    height: 120rpx;
    background-color: #fff;
    .flex-1{
      flex: 1;
      padding: 0 10rpx;
    }
  }
}
</style>
