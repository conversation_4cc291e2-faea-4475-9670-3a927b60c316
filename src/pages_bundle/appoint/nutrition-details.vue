<template>
  <view :style="theme.style" class="nutrition-details">
    <view class="ls-card m-t-30">
      <view class="nutrition-wrap m-t-30 xs p-b-20 flex row-between primary">
        <text class="text-left nutrition-width">营养元素</text>
        <text class="text-right" style="width: 20%">摄入量</text>
        <text class="text-right nutrition-width">单位</text>
      </view>
      <view class="item" v-for="(item, index) in nutrientIngestionDataList" :key="index">
        <view class="nr black f-w-500 flex row-between m-b-10">
          <view class="title-text text-left nutrition-width">{{ item.name }}</view>
          <view class="title-text text-right" style="width: 20%">{{ item.weight }}</view>
          <view class="title-text text-right nutrition-width">{{ item.unit }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { nutrientIngestionData } from 'pages_health/utils/nutrient'
export default {
  data() {
    return {
      dataDetails: {},
      nutrientIngestionDataList: [],
      type: ''
    }
  },
  onLoad(option) {
    this.dataDetails = this.$decodeQuery(this.$Route.query.data)
    this.type = this.$Route.query.type
    this.initNutrient()
  },
  onShow() {},
  mounted() {},
  methods: {
    initNutrient() {
      // 营养分析进入 格式不一样
      if (this.type === 'dict') {
        this.nutrientIngestionDataList = nutrientIngestionData.map(v => {
          v.weight = this.dataDetails[v.key]
          return v
        })
      } else {
        let elementDetails = JSON.parse(this.dataDetails.element)
        let vitaminDetails = JSON.parse(this.dataDetails.vitamin)
        this.nutrientIngestionDataList = nutrientIngestionData.map(v => {
          if (elementDetails && Object.keys(elementDetails).length) {
            for (let element in elementDetails) {
              if (element == v.key) {
                v.weight = elementDetails[element]
              }
            }
          }
          if (vitaminDetails && Object.keys(vitaminDetails)) {
            for (let vitamin in vitaminDetails) {
              if (vitamin == v.key) {
                v.weight = vitaminDetails[vitamin]
              }
            }
          }
          if (v.key == 'axunge') {
            v.weight = this.dataDetails.axunge
          }
          if (v.key == 'carbohydrate') {
            v.weight = this.dataDetails.carbohydrate
          }
          if (v.key == 'protein') {
            v.weight = this.dataDetails.protein
          }
          if (v.key == 'cholesterol') {
					  v.weight = this.dataDetails.cholesterol
					}
          if (v.key == 'dietary_fiber') {
					  v.weight = this.dataDetails.dietary_fiber
					}
          return v
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.nutrition-details {
  padding: 20rpx;
  .ls-card {
    padding: 30rpx;
    border-radius: 20rpx;
    background-color: #ffffff;
  }
  .nutrition-wrap {
    border-bottom: $border-base;
  }
  .item {
    padding: 12rpx 0;
  }
  .nutrition-width {
    width: 33.33%;
  }
}
</style>
