<template>
	<view :style="theme.style" class="confirm_order">
		<!-- Header 头部 Start -->
		<view class="header" v-if="false">
			<view class="title lg f-w-500 black">配送信息</view>

			<!-- Component 地址卡片 Start -->
			<address-card>
				<template v-if="true">
					<view slot="title" class="lg f-w-500 black">综合放射科B3室</view>
					<view slot="user-info" class="muted nr m-t-14">
						<text>赵子龙</text>
						<text class="m-l-28">17673268633</text>
					</view>
				</template>
				<template v-else>
					<view slot="title" class="lg f-w-500 black">请添加地址</view>
				</template>
			</address-card>
			<!-- Component 地址卡片 End -->
		</view>
		<!-- Header End -->

		<!-- 取餐柜 -->
		<view v-if="params.take_meal_type === 'cupboard' && isAutoSaveInCupboard" class="cupboard">
			<!-- <view class="title lg f-w-500 black">收货地址：</view> -->
			<view class="cupboard-choose">
				<view class="m-r-30">存放餐柜：</view>
				<view class="flex" @click="gotoChooseCupboard">
					<view v-if="!cupboardName" class="text-ash">请选择餐柜</view>
					<view v-else>{{ cupboardName }}</view>
					<u-icon color="#767a7d" name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
		</view>

		<!-- Section 头部 Start -->
		<view class="section">
			<block v-for="(item, index) in cartLists" :key="index">
				<view class="flex col-center row-between p-b-20">
					<view class="title lg f-w-500 ">{{ item.date }}</view>
				</view>
				<view class="order-list ls-card bg-white">
					<block v-for="(sitem, sindex) in item.stall" :key="sindex">
		<!-- 				<view class="cupboard-btn-box flex row-right" v-if="params.take_meal_type == 'cupboard'">
							<view class="cupboard-btn m-r-20" :class="{ cupboardBtnActive: sitem.reservation_take_meal_type === 'on_scene' }" @click="cupboardPickMethod(sitem,{date: item.date},'on_scene')">堂食</view>
							<view class="cupboard-btn" :class="{ cupboardBtnActive: sitem.reservation_take_meal_type === 'takeaway' }" @click="cupboardPickMethod(sitem,{date: item.date},'takeaway')">打包</view>
						</view> -->
						<!-- 标题 -->
						<view class="borderBottom flex row-between f-w-500">
							<text class="black line-1">{{ sitem.stall_name }}</text>
							<text class="primary flex-none">{{ sitem.meal_type_alias }}</text>
						</view>

						<!-- 食物及数量 -->
						<view class="food borderBottom">
							<block v-for="(fitem, findex) in sitem.food" :key="findex">
								<view class="m-t-30 flex row-between f-w-500">
									<view class="food-name  flex-6 f-w-500" v-if="fitem.obj_name == 'food'">
										<view class="">{{ fitem.food_name }}</view>
										<view class="text-ash xs  m-l-30 flex flex-wrap p-t-10">
											{{ fitem.spec_name }}{{ fitem.spec_name && fitem.taste_name ? '、' : '' }}{{ fitem.taste_name }}
										</view>
									</view>
									<view class="food-name  flex-6 f-w-500" v-if="fitem.obj_name == 'set_meal'">
										<view class="">{{ fitem.set_meal_name }}</view>
										<view class="text-ash xs  m-l-20 flex flex-wrap p-t-10">
											<view class="" v-for="(setMealItem, setMealIndex) in fitem.set_meal_style_data" :key="setMealIndex">
												{{
													(setMealIndex == fitem.set_meal_style_data.length - 1 && setMealItem.food_name) ||
														setMealItem.food_name + '+'
												}}
											</view>
										</view>
									</view>
									<view class="flex row-between food-money-number">
										<!-- Food Money -->
										<price-format
											class="m-l-10"
											:price="fitem.obj_name == 'set_meal' ? fitem.price : fitem.spec_price"
											color="#FF5757"
										></price-format>
										<!-- Food Num -->
										<number-box
											v-model="fitem.count"
											:min="1"
											:async-change="true"
											@change="
												cartCountChange($event, fitem, {
													date: item.date,
													meal_type: sitem.meal_type
												}, sitem)
											"
										></number-box>
									</view>
								</view>
							</block>
						</view>

						<!-- 费用 Cost -->
						<view class="borderBottom" v-if="(sitem.packing_fee || sitem.fuwu_fee)">
							<view class="flex row-between" v-if="sitem.packing_fee">
								<text class="muted">打包费：</text>
								<price-format class="m-l-10" :price="sitem.packing_fee" :size="32"></price-format>
							</view>
							<view class="m-t-24 flex row-between" v-if="sitem.fuwu_fee">
								<text class="muted">服务费：</text>
								<div class="text-right">
									<price-format class="m-l-10" :price="sitem.fuwu_fee" :size="32"></price-format>
									<div class="xxs muted" v-if="params.take_meal_type === 'waimai' && sitem.elevator_fee">含无电梯额外服务费 <price-format class="m-l-10" :price="sitem.elevator_fee" :size="22"></price-format></div>
								</div>
							</view>
						</view>

						<!-- 合计 -->
						<view class="borderBottom flex row-between">
					<!-- 		<view class="flex flex-center">
								<text class="muted m-r-10">份数：</text>
								<number-box
									v-model="sitem.count"
									:min="1"
									:async-change="true"
									@change="
										stallCountChange($event, {
											date: item.date,
											meal_type: sitem.meal_type,
											organization_id: sitem.stall_id,
											count: sitem.count
										})
									"
								></number-box>
							</view> -->
							<!-- 取餐柜的打包费 -->
								<view v-if="sitem.reservation_take_meal_type === 'takeaway'">
									<text class="muted m-r-20">打包费</text>
									<price-format :price="sitem.cupboard_takeaway_fee" :size="28" :weight="500" color="#999999"></price-format>
								</view>
								<view>
									<text class="muted m-r-20">合计</text>
									<price-format :price="sitem.one_stall_fee" :weight="500" :size="34"></price-format>
								</view>
						</view>
					</block>
				</view>
			</block>
		</view>
		<!-- Section End -->

		<!-- Footer 底部 Start -->
		<view class="footer bg-white">
			<view class="footer--warpper flex row-between">
				<view class="muted flex col-center">
						<text class="m-r-20 md">合计</text>
						<view class="black"><price-format :price="totalAmount" :size="36" :weight="500"></price-format></view>
				</view>

				<u-button
					:loading="loading"
					:customStyle="{
						width: '240rpx',
						margin: 0,
						'border-radius': 0
					}"
					type="primary"
					size="large"
					:color="isVerificationOrderFlag?variables.colorPrimary:'#aaaaaa'"
					:text="formateText()"
					@click="onSubmitOrder"
				></u-button>
				<!-- <view class="submit-btn white flex flex-center" >提交订单</view> -->
			</view>
		</view>
		<!-- 保存、删除提示框 -->
		<u-modal :show="cupboardFullShow" :title="'提示'">
		  <view class="xl text-center m-t-30 m-b-30" >{{ cupboardFullMsg }}</view>
		  <view slot="confirmButton" class="flex flex-center row-around">
		    <view class="confirm-btn">
		      <u-button text="关闭" shape="circle" color="#BDBDBD" @click="cupboardFullShow = false"></u-button>
		    </view>
		    <view class="confirm-btn" v-if="!cupboardDisabledBtn">
		      <u-button text="继续" :disabled="cupboardDisabledBtn" shape="circle" :color="variables.colorPrimary" @click="clickCupboardFull"></u-button>
		    </view>
		  </view>
		</u-modal>
	</view>
</template>

<script>
import {
	apiShopcardCalcList,
	apiShopcardAddCount,
	apiShopcardAdd,
	apiShopcardDelete,
	apiShopcardReduceCount,
	getApiReservationChange
} from '@/api/shopcart'
import { apiReservationOrderCreate } from '@/api/user'
import { apiGetCupboardAddress,getApiCheckCupboardNum, getApiStallLimitData } from '@/api/reservation'
import Cache from '@/utils/cache'
import { formateVisitorParams ,divide } from '@/utils/util.js'
import { formateBtnText , verificationOrderData} from './orderUtil'
import { mapGetters } from 'vuex'
export default {
	// Data Start
	data() {
		return {
			loading: false,
			params: {},
			orderCreateParams:{}, // 提交订单需要的参数
			totalAmount: 0,
			cartLists: [],
			cupboardName: '',
			cupboardId: '',
			stallIdList: [],
			isAutoSaveInCupboard:true,
			cupboardFullShow:false,
			cupboardFullMsg:"",
			cupboardDisabledBtn:false, // 取餐柜返回102 禁止提交订单
			buyLimitFoodCount:0,
			verificationData: {},//配送费信息
			isVerificationOrder: true,//是否满足起送费
			orderOrgId: '',//配送点Id
			isVerificationOrderFlag: true,//其中有个一个满足起送费的标志
			currentTime: '', // 当前点击的日期
			stallLimitSettings: {} // 消费规则限制数据，key使用[org_id]_[stall_id]_[meal_type]
		}
	},
	// Data End

	computed: {
		...mapGetters(['isAddressVisitor']),
	},

	// Methods Start
	methods: {
		cupboardPickMethod(sitem,date,type){
			console.log(type)
			this.$set(sitem, 'reservation_take_meal_type', type)
			let params = {
				date:date.date,
				reservation_take_meal_type: type,
				meal_type: sitem.meal_type,
				organization_id: sitem.stall_id,
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
			}
			this.getReservationChange(params)
		},
		// 取餐柜打包费
		getReservationChange(params) {
			getApiReservationChange(params)
				.then(res => {
					this.getShopcardCalcList()
				})
				.catch(err => {
					uni.$u.toast(err.msg)
				})
		},
		onSubmitOrder() {
			if (!this.isVerificationOrder) {
        return  uni.$u.toast(this.verificationOrderError)
			}
			this.orderCreateParams = {
				person_no: this.params.person_no,
				take_meal_type: this.params.take_meal_type,
				payment_order_type: this.params.payment_order_type,
				org_id: this.params.org_id,
				company_id: this.params.company_id,
				user_id: this.params.user_id
			}
      if (this.params.take_meal_type === 'cupboard' && this.isAutoSaveInCupboard &&  !this.cupboardId) {
        this.loading = false
        return uni.$u.toast('请选择取餐柜')
      }
			if (this.params.take_meal_type === 'cupboard' && this.isAutoSaveInCupboard) {
				this.orderCreateParams.cupboard_id = Number(this.cupboardId)
				this.orderCreateParams.receipt_adders = this.cupboardName
			}
			if (this.params.take_meal_type === 'waimai'){
				this.orderCreateParams.addr_id = this.$store.state.appoint.select.address_info.addr_center_id
			}
			if(this.params.take_meal_type === 'cupboard'){
				this.getCheckCupboardNum(this.orderCreateParams)
			}else{
				this.getReservationOrderCreate(this.orderCreateParams)
			}
		},
		// 提交订单
		getReservationOrderCreate(params){
			if (this.loading) {
				return
			}
			this.loading = true
			apiReservationOrderCreate(formateVisitorParams(params, true))
				.then(res => {
					if (res.code === 0) {
						let data = res.data
						data.stall_ids = this.stallIdList
						this.$miRouter.replace({
							path: '/pages_bundle/payment/payment',
							query: {
								// #ifdef MP-ALIPAY
								data: this.$encodeQuery(data),
								// #endif
								// #ifndef MP-ALIPAY
								data: data,
								// #endif
								takeMealType: this.params.take_meal_type
							}
						})
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err.data.msg)
				})
				.finally(res => {
					this.loading = false
				})
		},
		getShopcardCalcList() {
			let params = {
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				company_id: this.params.company_id
			}
			if (this.params.take_meal_type === 'waimai'){
				params.addr_id = this.$store.state.appoint.select.address_info.addr_center_id
			}
			apiShopcardCalcList(formateVisitorParams(params, true))
				.then(res => {
					// this.cartLists = res.data.shopcard
					this.totalAmount = res.data.all_date_fee
					this.stallIdList = []
					this.cartLists = res.data.shopcard.map(item => {
						item.stall.map(stall => {
							if (this.stallIdList.indexOf(stall.stall_id) === -1) {
								this.stallIdList.push(stall.stall_id)
							}
						})
						return item
					})
					//每一次购物车发生变化重新算配送费是否满足
					this.verificationOrder(this.cartLists)
				})
				.catch(err => {
					uni.$u.toast(err.message)
				})
		},
		async stallCountChange(val, { date, organization_id, meal_type, count }) {
			const params = {
				take_meal_type: this.params.take_meal_type,
				payment_order_type: this.params.payment_order_type,
				date,
				organization_id,
				meal_type,
				company_id: this.params.company_id
			}

			if (val > count) {
				await apiShopcardAddCount(formateVisitorParams(params, true)).catch(err => {
					uni.$u.toast(err.data.msg)
				})
			} else {
				await apiShopcardReduceCount(formateVisitorParams(params, true)).catch(err => {
					uni.$u.toast(err.data.msg)
				})
			}
			this.getShopcardCalcList()
		},
		async cartCountChange(val, item, obj, stall) {
			this.orderOrgId =  item.organization_id
			this.currentTime = obj.date
			this.params.meal_type = obj.meal_type
			console.log("stallCountChange",this.params, val, item, obj, stall);
			// 做个缓存吧，不要一直请求接口数据
			const limitKey = `${item.organization_id}_${stall.stall_id}_${obj.meal_type}`
			// 如果已经请求过当前规则的，则直接使用不重新请求了
			if (!this.stallLimitSettings[limitKey]) {
				await this.getStallLimitData({
					meal_type: obj.meal_type, // 餐段
					org_id: this.params.org_id,
					person_no: this.params.person_no,
					stall_id: stall.stall_id
				}, limitKey)
			}
			const currentLimitSetting = this.stallLimitSettings[limitKey]

			const params = {
				payment_order_type: this.params.payment_order_type,
				food_id: item.obj_name == 'set_meal' ? '' : item.food_id, //如果是套餐 food 传空
				date: obj.date,
				take_meal_type: this.params.take_meal_type,
				meal_type: obj.meal_type,
				organization_id: item.organization_id,
				organization_alias: item.organization_alias,
				consume_type: item.consume_type,
				fuwu_fee: item.fuwu_fee,
				elevator_fee: item.elevator_fee,
				company_id: this.params.company_id,
				menu_food_id: item.menu_info.menu_food_id,
				menu_food_object: item.menu_info.menu_food_object,
				spec_id: item.spec_id,
				taste_id: item.taste_id,
				buy_limit_food_count: currentLimitSetting.buy_limit_food_count,
				reservation_setting_id: currentLimitSetting.reservation_setting_id,
				stall_id: stall.stall_id,
				org_id: this.params.org_id
			}
			if (item.obj_name == 'set_meal') {
				params.set_meal_id = item.set_meal_id
				params.set_meal_style_data = []
				params.set_meal_style_data = item.set_meal_style_data.map(v => {
					let obj = {
						set_meal_style_id: v.style_id,
						food_id: v.food_id,
						spec_id: v.spec_id
					}
					return obj
				})
			}
			if (val > item.count) {
				await apiShopcardAdd(formateVisitorParams(params, true))
					.then(res => {
						uni.hideLoading()
						if (res.code == 0) {
							this.getShopcardCalcList()
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
			} else {
				await apiShopcardDelete(formateVisitorParams(params, true))
					.then(res => {
						uni.hideLoading()
						if (res.code == 0) {
							this.getShopcardCalcList()
							// uni.$u.toast(res.msg)
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
				// await apiShopcardDelete(params).catch(err => {
				//   uni.$u.toast(err.data.msg)
				// })
			}
		},
		getInitParams() {
			const userInfo = Cache.get('userInfo')
			const state = this.$store.state.appoint
			this.params.company_id = this.isAddressVisitor?state.select.address_info.company_id:userInfo.company_id // 游客免登录扫码的话，用码绑定的公司id
			this.params.take_meal_type = state.select.take_meal_type
			this.params.payment_order_type = state.select.payment_order_type
			this.params.person_no = state.select.person.person_no
			this.params.org_id = state.select.org.org_id
			this.params.user_id = userInfo.user_id
			this.getShopcardCalcList()
			if(this.params.take_meal_type === 'cupboard'){
				this.getCupboardAddress()
			}
		},
    async getCupboardAddress() {
      await apiGetCupboardAddress({
        org_id:this.params.org_id
      })
        .then(res => {
          if (res.code === 100) {
						// 判断是否需要选择取餐柜
            this.isAutoSaveInCupboard = false
          }
        })
        .catch(err => {
          uni.$u.toast(err.msg)
        })
    },
		// 是否判断取餐柜已满
    async getCheckCupboardNum(params) {
      await getApiCheckCupboardNum(params)
        .then(res => {
					if(res.code === 0){
					 this.getReservationOrderCreate(params)
					}else if (res.code === 101) {
						// 判断是否需要选择取餐柜
						this.cupboardFullShow = true
						this.cupboardFullMsg = res.msg
          }else if (res.code === 102){
						this.cupboardFullShow = true
						this.cupboardDisabledBtn= true
						this.cupboardFullMsg = res.msg
					}
        })
        .catch(err => {
          uni.$u.toast(err.msg)
        })
    },
		// 继续取餐柜
		clickCupboardFull(){
			this.getReservationOrderCreate(this.orderCreateParams)
		},
		// 选取餐柜
		gotoChooseCupboard() {
			this.$miRouter.push({
				path: '/pages_bundle/appoint/choose_cupboard',
				query: {
					data: this.$encodeQuery(this.stallIdList)
				}
			})
		},
		/**
		 * 设置按钮文字
		 */
		formateText() {
			var cupboardDeliveryFee = ''
			var waimaiDeliveryFee = ''
			var verificationDataList = this.verificationData[this.currentTime] || []
			console.log("verificationDataList", verificationDataList,  "this.orderOrgId",this.orderOrgId, "this.params.meal_type",this.params.meal_type);
			var findItem = verificationDataList.find(verItem => {
							return verItem.stallId === this.orderOrgId && verItem.mealType === this.params.meal_type
					})
					console.log("findItem", findItem);
			if(findItem) {
				cupboardDeliveryFee = findItem.cupboardDeliveryFee
				waimaiDeliveryFee = findItem.waimaiDeliveryFee
			}	
			var text =	formateBtnText(this.params,this.isVerificationOrderFlag,cupboardDeliveryFee,waimaiDeliveryFee)  
      if(text === '下一步') {
				text = '提交订单'
			}
			return text
    },
		/**
		 * 分日期去校验配送费
		 * @param list 配送列表,包含日期，餐段等数据
		 */
		 verificationOrder(list) {
			if(this.params.take_meal_type === 'waimai' || this.params.take_meal_type === 'cupboard') {
				var resultData =	verificationOrderData(list,this.params,this.verificationData)
				console.log("resultData", resultData);
				if(resultData) {
					this.isVerificationOrder = resultData.isVerificationOrder
					this.verificationOrderError = resultData.verificationOrderError
					this.isVerificationOrderFlag = resultData.isVerificationOrderFlag
				}
			}
		},
		// 获取档口预约点餐限制相关的参数
		async getStallLimitData(params, key) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			const [err, res] = await this.$to(getApiStallLimitData(formateVisitorParams({
				meal_type: params.meal_type, // 餐段
				org_id: params.org_id,
				person_no: this.params.person_no,
				stall_id: params.stall_id
			})))
			uni.hideLoading()
			if (err) {
				uni.$u.toast(err.message)
				return
			}
			if (res.code == 0) {
				this.stallLimitSettings[key] = res.data
			} else {
				uni.$u.toast(res.msg)
			}
		},
	},
	// Methods End
	onShow() {
		this.getInitParams()
		if (this.$store.state.appoint.select.cupboard && this.$store.state.appoint.select.cupboard.id) {
			let cupboard = this.$store.state.appoint.select.cupboard
			if (cupboard.ceil > 0) {
				this.cupboardId = cupboard.id
				this.cupboardName = cupboard.addr
			}
		}
	},
	// Life Cycle Start
	onLoad(option) {
		// this.buyLimitFoodCount = this.$Route.query.buyLimitFoodCount
		this.currentTime =this.$Route.query.currentDate || ''
	},
	// Life Cycle End
	created(){
		//页面创建获取配送费信息
	 this.verificationData =	Cache.get('verificationOrder')
	 if(this.verificationData&&Object.keys(this.verificationData).length>0){
		//初始化给个默认的消费点
		this.orderOrgId = Object.keys(this.verificationData)[0]||''
		console.log("this.orderOrgId",this.orderOrgId);
	 }
	}
}
</script>

<style lang="scss">
.confirm_order {
	padding: 40rpx;

	.title {
		position: relative;
		padding-left: 24rpx;
		// padding-bottom: 20rpx;
	}

	.title::before {
		content: '';
		width: 6rpx;
		height: 30rpx;
		position: absolute;
		left: 0;
		top: 10%;
		background-color: $color-primary;
	}

	.ls-card {
		border-radius: 20rpx;
	}

	.text-ash {
		color: #9da2a7;
	}

	.cupboard {
		.cupboard-choose {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 30rpx;
			margin-bottom: 15rpx;
			background-color: #ffffff;
			border-radius: 20rpx;
		}
		.text-ash {
			color: #c0c4cc;
		}
	}

	.section {
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
		.cupboard-btn-box{
			.cupboard-btn{
				padding: 5rpx 30rpx;
				color: #11E69E;
				border: 1rpx solid #11E69E;
				border-radius: 10rpx;
			}
			.cupboardBtnActive{
				background-color: #11E69E;
				color: #fff;
			}
		}
		.order-list {
			padding: 0 36rpx;

			.borderBottom {
				border-bottom: 1px solid $border-color-base;
			}

			> view {
				padding: 30rpx 0;
			}

			.food {
				> view:first-child {
					margin-top: 0;
				}

				// .food-name {
				// 	width: 260rpx;
				// }
				.food-money-number {
					width: 240rpx;
				}
			}

			.minus,
			.plus {
				color: $color-primary;
				width: 32rpx;
				height: 32rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				border: 1px solid $color-primary;
				border-radius: 4rpx;
			}

			.plus {
				background-color: $color-primary;
				color: #fff;
				border-color: $color-primary;
			}

			.input {
				width: 50rpx;
			}
		}
	}

	.footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		padding-left: 38rpx;
		border-top: 1px solid #eaecee;
		padding-bottom: env(safe-area-inset-bottom);

		&--warpper {
			height: 100rpx;
		}

		.submit-btn {
			width: 200rpx;
			height: 100%;
			background-color: $color-primary;
		}
	}
	.confirm-btn {
	  width: 36%;
	}
}
</style>
