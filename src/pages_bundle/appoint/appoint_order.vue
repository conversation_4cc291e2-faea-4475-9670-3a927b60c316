<template>
	<view :style="theme.style" class="">
		<view class="appointment-order flex flex-col bg-white">
			<view class="flex-1" style="min-height: 0">
				<appoint-wrap
					ref="appointWrapRef"
					title="预约点餐"
					v-model="foodLists"
					:loading="loading"
					@click="handleChange"
					:mealTypeInfo="mealTypeInfoData"
					:nowMealType="nowMealTypeData"
					:nutrientMealType="nutrientMealType"
					@selectfood="selectFoodHandle"
					@openInstructions="showInstructions = true"
					:docData="docData"
				>
					<template>
						<view
							class="meal-goods-item"
							:id="`category-item-${cfood.category_id}`"
							v-for="(cfood, k) in selectFood.food_data"
							:key="k"
						>
							<view class="title xs m-b-20 p-t-20">{{ cfood.category }}</view>
							<view class="goods-lists" v-if="cfood.food.length">
								<view
									class="goods-item flex p-b-30"
									:class="{ disabled: !item.stock }"
									v-for="(item, index) in cfood.food"
									:key="index"
								>
									<u-image width="120rpx" height="120rpx" radius="10"  @click="handlerPreview(item.image)" :src="item.image? item.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"></u-image>
									<view class="flex-1 m-l-20" @click="clickFoodDetail(item)">
										<view class="flex row-between col-center">
											<view class="goods-name flex col-center f-w-500">
												<text class="dot-nutrition m-r-10 m-b-5" v-if="nutrientMealType.is_healthy_info && item.food_light" :style="{ backgroundColor: item.food_light? foodLight[item.food_light].color: '' }"></text>
												<!-- set_meal 套餐 -->
												<text>{{ item.obj_name == 'set_meal' ? item.set_meal_name : item.food_name }}</text>
											</view>
											<!-- 绿灯 -->
										</view>
										<view class="flex row-between" style="height: 38rpx;">
											<view class="mini muted m-t-10" v-if="item.stock!==-1">库存{{ item.stock }}份</view>
											<view class="mini muted m-t-10" v-if="item.buy_limit">限购：{{ item.buy_limit }}份</view>
										</view>
										<view :key="item.count" class="flex row-between m-t-15">
											<view class="price">
												<!-- 套餐的时候 如果是price_type = total 并且是所有样式菜品只有一个的时候 算所有菜品的价格合计，如果是fixed 就拿固定金额-->
												<price-format
													v-if="item.obj_name == 'set_meal' && item.price_type == 'fixed'"
													:price="item.price"
													:size="30"
													color="#FF5757"
												></price-format>
												<price-format
													v-else-if="item.obj_name == 'set_meal' && item.price_type == 'total' && !item.display_style_button"
													:price="item.set_meal_food_price"
													:size="30"
													color="#FF5757"
												></price-format>
												<price-format
													v-else-if="item.obj_name == 'food'"
													:price="item.price"
													:size="30"
													color="#FF5757"
												></price-format>
												<!--240708隐藏折扣价格 -->
												<!-- <price-format
													v-if="item.obj_name == 'set_meal' && item.price_type == 'fixed'"
													class="m-l-10"
													:price="item.discount"
													:size="24"
													color="#999"
													:lineThrough="true"
												></price-format>
												<price-format
													v-else-if="item.obj_name == 'set_meal' && item.price_type == 'total' && !item.display_style_button"
													:price="item.discount"
													:size="24"
													color="#999"
													:lineThrough="true"
												></price-format>
												<price-format
													v-else-if="item.obj_name == 'food'"
													:price="item.discount"
													:size="24"
													color="#999"
													:lineThrough="true"
												></price-format> -->
											</view>
											<!-- 数量加减 -->
											<number-box
												v-if="item.stock && !item.display_style_button"
												v-model="item.count"
												:async-change="true"
												@change="selectCart($event, item, '', selectFood.org_id)"
											></number-box>
											<view
												v-else-if="item.stock && item.display_style_button"
												class=""
												style="position: relative"
												@click.stop="openSpecsDialog(item)"
											>
												<u-button
													text="选规格"
													type="primary"
													:color="variables.colorPrimary"
													:customStyle="customBtnStyle"
													size="mini"
												></u-button>
												<u-badge bg-color="#FF5757" max="99" :value="item.count" absolute :offset="[-12, -10]"></u-badge>
											</view>
										</view>
									</view>
								</view>
							</view>
							<view v-else class="flex flex-col col-center">
								<image class="image-null" :src="themeImgPath.img_diancan_null"></image>
								<text class="muted m-t-40">暂无菜品</text>
							</view>
						</view>
					</template>
					<view slot="empty" class="flex flex-col col-center">
						<image class="image-null" :src="themeImgPath.img_diancan_null"></image>
						<text class="muted m-t-40">暂未营业 , 请稍后再来</text>
					</view>
				</appoint-wrap>
			</view>

			<!-- 购物车 -->
			<!-- v-show="cartLists.length!==0" -->
			<view class="cart flex col-center" v-show="allCount">
				<view class="flex-1">
					<view class="total-order">
						<text class="md muted">合计</text>
						<price-format class="m-l-10" :price="totalAmount" :size="36"></price-format>
					</view>
				</view>
				<!-- 营养超标 -->
	<!-- 			<view class="collocation-box flex row-between" v-if="nutrientRecommend()">
						<view class="text" @click="collocationClick">本餐摄入超标,查看推荐搭配></view>
						<view class="recommend-but" @click="clickRecommend">智能推荐</view>
				</view> -->
				<view class="cart-num flex flex-center" @tap="showCart = !showCart">
					<u-badge bg-color="#FF5757" max="99" :value="allCount" absolute :offset="[0, 0]"></u-badge>
					<image class="icon-lg" :src="themeImgPath.img_bundle_shop_cart"></image>
				</view>
				<!-- <router-link to="/pages_bundle/appoint/confirm_order"> -->
					<u-button
						:customStyle="{
							width: '240rpx',
							'border-radius': 0
						}"
						type="primary"
						size="large"
						:color="isVerificationOrderFlag?variables.colorPrimary:'#aaaaaa'"
						:text="formateText()"
            @click="gotoConfirmOrder()"
					></u-button>
				<!-- </router-link> -->
			</view>
			<!-- 购物车列表 -->
			<cart-popup
				v-model="showCart"
				:lists="cartLists"
				:time="currentDate('yyyy-mm-dd 周w')"
				@clear="clearCard"
				@countchange="handleCountChange"
			></cart-popup>
		</view>
		<!-- 选择规格 -->
		<u-popup :show="showSpecs" mode="center" @close="showSpecs = false" round="20">
			<view class="choose-specs">
				<view class="food-info flex m-l-30">
					<image class="food-info-img" :src="foodInfo.image? foodInfo.image: 'https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/be8b9937bcba4202fc34cf1339a85efd1675923634225.png'"></image>
					<view class="">
						<view class="xl f-w-600 food-info-name">{{ foodInfo.obj_name == 'set_meal' ? foodInfo.set_meal_name : foodInfo.food_name }}</view>
						<view v-if="foodInfo.stock!==-1" class="mini m-t-8 m-b-16 text-ash">库存{{ foodInfo.stock }}份</view>
						<price-format
							v-if="(foodInfo.obj_name === 'set_meal' && foodInfo.obj_name == 'fixed') || foodInfo.obj_name === 'food'"
							:price="specsPrice"
							:size="30"
							color="#FF5757"
						></price-format>
					</view>
				</view>
				<!-- 判断菜品的规格 -->
				<view class="" v-if="foodInfo.obj_name == 'food'">
					<scroll-view style="max-height: 400rpx" :scroll-y="true">
						<view v-if="foodInfo.food_spec.length" class="m-t-30 m-l-30">
							<view class="mini text-ash">规格</view>
							<view class="specs-list">
								<view
									v-for="item in foodInfo.food_spec"
									:key="item.id"
									:class="['specs-item', item.id === specsId ? 'activeSpecs' : '']"
									@click="chooseSpecs('specs', item)"
								>
									{{ item.name }}
								</view>
							</view>
						</view>
						<view v-if="foodInfo.food_taste.length" class="m-t-30 m-l-30">
							<view class="mini text-ash">口味</view>
							<view class="specs-list">
								<view
									:class="['specs-item', item.id === tasteId ? 'activeSpecs' : '']"
									v-for="item in foodInfo.food_taste"
									:key="item.id"
									@click="chooseSpecs('taste', item)"
								>
									{{ item.name }}
								</view>
							</view>
						</view>
					</scroll-view>
					<view class="already-choose m-t-30 mini">
						<text class="text-ash">已选：</text>
						{{ specsName }}{{ specsName && tasteName ? '、' : '' }}{{ tasteName }}
					</view>
				</view>
				<view v-else-if="foodInfo.obj_name == 'set_meal'">
						<scroll-view style="max-height: 400rpx" :scroll-y="true">
							<view
								class="m-t-30 m-l-30"
								v-for="(styleItem) in foodInfo.set_meal_style_data"
								:key="styleItem.style_id"
							>
								<view class="mini text-ash">{{ styleItem.style_name }}</view>
								<view class="specs-list">
									<view
										v-for="(specsItem, specsIndex) in styleItem.set_meal_food_data"
										:key="specsIndex"
										:class="[
											'specs-item',
											styleItem.setMealSpecsId === specsItem.spec_id ? 'activeSpecs' : '',
											specsItem.disabled ? 'disabled' : ''
										]"
										@click="setMealChooseSpecs(styleItem, specsItem)"
									>
										{{ specsItem.food_name }}
									</view>
								</view>
							</view>
						</scroll-view>
					<view class="already-choose m-t-30 mini">
						<text class="text-ash">已选：</text>
						{{ selectSetMealSpecName }}
					</view>
				</view>
				<view class="food-total">
					<view class="">
						<text class="lg m-r-20">合计</text>
						<price-format class="m-t-5" :size="40"  :price="specsTotalPrice" color="#FF5757"></price-format>
					</view>
					<view class="">
						<number-box
							v-model="foodInfoCount"
							:async-change="true"
							@change="selectCart($event, foodInfo, specsType, selectFood.org_id)"
						></number-box>
					</view>
				</view>
				<view class="icon-close-popup" @click="showSpecs = false">
					<u-icon name="close-circle" color="#d8d5db" size="40"></u-icon>
				</view>
			</view>
		</u-popup>
		<!-- 说明弹窗 -->
		<u-popup :show="showInstructions" mode="center" @close="showInstructions = false" round="20">
			<view class="instructions flex flex-col p-40">
				<view class="flex flex-center m-b-40">
					<text class="xxl f-w-600">能量</text>
				</view>
				<view class="m-b-40">
					<text class="lg">记录当餐的能量摄入情况，并根据您选择菜品的能量进行计算，点餐时可适当调整能量的摄入，从而实现健康饮食的目标。</text>
				</view>
				<view>
					<u-button shape="circle" type="primary" text="确定" @click="showInstructions = false"></u-button>
				</view>
			</view>
		</u-popup>
		<!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
	</view>
</template>

<script>
import { timeFormat } from '@/utils/date'
import { formateVisitorParams } from '@/utils/util.js'
import { apiGetReservationStallFood, apiMonthReservationOrder } from '@/api/user'
import { apiShopcardCalcList, apiShopcardAdd, apiShopcardDelete, apiShopcardClean } from '@/api/shopcart'
import { getApiUserReservationMealType, getApiStallLimitData } from '@/api/reservation'
import Cache from '@/utils/cache'
import { getApiMealTypeNutrient } from '@/api/healthy.js'
import { formateBtnText , verificationOrderData} from './orderUtil'
import { deepClone } from '../../utils/util'
import AppointWrap from '../components/appoint-wrap/appoint-wrap'
import { mapGetters, mapActions } from 'vuex'
import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'

export default {
	components: { AppointWrap, FloatingPopup },
	data() {
		return {
			params: {
				date: timeFormat(Date.now(), 'yyyy-mm-dd'),
				take_meal_type: 'on_scene',
				meal_type: 'breakfast',
				buyLimitFoodCount:0,
				cupboardTakeawayFee:0,
				reservationSettingId: ''
			},
			loading: false,
			showCart: false,

			totalAmount: 0,
			allCount: 0,
			foodLists: [],
			cartLists: [],
			mealTypeInfoData: [],
			nowMealTypeData: { meal_type: 'breakfast' },
			showSpecs: false,
			showInstructions: false,
			foodInfo: {
				food_spec: [],
				food_taste: []
			},
			specsId: -1,
			specsName: '',
			tasteId: -1,
			tasteName: '',
			nutrientMealType: {},
			selectFood: {},
			customBtnStyle: {
				minWidth: '80rpx',
				height: '40rpx',
				lineHeight: '40rpx'
			},
			docData: {},
			foodKey: 1,
			setMealStyleSpecsData: [], //选规格的数据
			specsType: '',
			cupboardDeliveryFee: '', // 取餐柜起送费
			waimaiDeliveryFee: '', // 外卖起送费
      // 红绿灯
      foodLight: {
        green: {
          name: '绿灯食物',
          label: '放心吃',
          color: '#63d496'
        },
        yellow: {
          name: '黄灯食物',
          label: '适量吃',
          color: '#f7d149'
        },
        red: {
          name: '红灯食物',
          label: '建议少吃',
          color: 'red'
        }
      },
			changeOption: null,
			verificationData: {},//用来暂存每个消费点的配送费
			isVerificationOrder: true,//是否满足起送费
			verificationOrderError: '',//起送费错误提示
			isVerificationOrderFlag: true,//其中有个一个满足起送费的标志
			currentTime: '', // 当前点击的日期
			floatingPopupShow: false
		}
	},
	methods: {
		...mapActions({
      setRemoveCouponItem: 'setRemoveCouponItem'
    }),
		// 获取当前月已经预约/报餐的日期,用于日历上dot-lists的显示
		getMonthOrder(event) {
			if (event.type == 'meal_type') return
			let params = {
				person_no: this.params.person_no,
				month: timeFormat(new Date(event.value.fulldate).getTime(), 'yyyymm'),
				company_id: this.params.company_id,
				start_date: event.value.range[0],
				end_date: event.value.range[1]
			}
			let apiPromise = apiMonthReservationOrder(formateVisitorParams(params))
			return new Promise((resolve, reject) => {
				apiPromise
					.then(res => {
						if (Object.keys(res.data) && Object.keys(res.data).length) {
							this.docData = res.data
						}
						resolve(res)
					})
					.catch(() => {
						reject()
					})
			})
		},

		selectFoodHandle(value) {
			this.selectFood = value
      this.cupboardDeliveryFee = value.cupboard_delivery_fee
      this.waimaiDeliveryFee = value.waimai_delivery_fee
			this.getStallLimitData(value.org_id)
		},
		// 获取档口预约点餐限制相关的参数
		getStallLimitData(stallId) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiStallLimitData(formateVisitorParams({
				meal_type:this.params.meal_type, // 餐段
				org_id:this.params.org_id,
				person_no:this.params.person_no,
				stall_id:stallId
			}))
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						// 单人单餐菜品限制份数
						this.params.buyLimitFoodCount = res.data.buy_limit_food_count
						// 拿到取餐柜打包费金额
						this.params.cupboardTakeawayFee = res.data.cupboard_takeaway_fee
						// 规则id
						this.params.reservationSettingId = res.data.reservation_setting_id
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.msg)
				})
		},
		// 日期改变，食堂改变
		async handleChange(event) {
			if (event.type == 'meal_type') {
				if (!event.value) return
				this.params[event.type] = event.value
			} else {
				if (!event.value.fulldate) return
				this.params[event.type] = event.value.fulldate
				this.currentTime = event.value.fulldate
			}
			this.changeOption = event
			// 有餐段才去请求数据
			if (this.mealTypeInfoData.length) {
				this.getChangeData(event)
			}
		},
		async getChangeData(event) {
			console.log("getChangeData",event);
			if (this.loading) return console.warn('多次请求接口已拦截1！')
			this.loading = true
			await this.getFood()
			this.changeFood()
			// 显示已点餐段
			this.getMonthOrder(event)
			this.getMealTypeNutrient()
		},
		// 获取菜品营养信息
		getMealTypeNutrient() {
      if (this.isAddressVisitor) return
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiMealTypeNutrient(formateVisitorParams({
				date: this.params.date,
				meal_type: this.params.meal_type,
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				org_id: this.params.org_id
			}))
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.nutrientMealType = res.data
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		// 获取菜品
		getFood() {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			console.log("this.params",this.params);
			return new Promise((reslove, reject) => {
				apiGetReservationStallFood(formateVisitorParams(this.params))
					.then(res => {
						if (res.code == 0) {
							// this.foodLists = res.data
							res.data.map((v,index)=>{
								// 是否 在appoint-wrap展开消费点菜品列表
									v.open = false

								return v
							})
							// 循环将分类count的key写入this.foodLists
							// res.data[0].food_data.map((item)=>{
							// 	item = Object.assign(item, {sort_total_count: 0})
							// })
							this.foodLists = res.data
              if (res.data.length) {
                this.cupboardDeliveryFee = res.data[0].cupboard_delivery_fee
                this.waimaiDeliveryFee = res.data[0].waimai_delivery_fee
								//原来没有分消费点，现在要求分消费点不同的配送费去判断,并且加入日期与餐段
								var resulstList = res.data || []
								var currentTime = this.currentTime
								if(Array.isArray(resulstList) || resulstList.length>0) {
									resulstList.forEach(item => {
										var orgId = item.org_id || ""
										if(orgId){
											if(!Reflect.has(this.verificationData, currentTime)) {
												//没有这个属性先建这个对象
												this.$set(this.verificationData,currentTime, [])
											}

											var findIndex = -1
										  var findItem = this.verificationData[currentTime].find((subItem,index) => {
												   if(subItem.stallId === orgId && subItem.mealType === this.params.meal_type) {
														findIndex = index
														return  subItem
													 }
											})
											console.log("this.verificationData",this.verificationData,findItem,findIndex);
											if(findItem) {
												this.verificationData[currentTime][findIndex].cupboardDeliveryFee = item.cupboard_delivery_fee || 0
												this.verificationData[currentTime][findIndex].waimaiDeliveryFee =  item.waimai_delivery_fee || 0

											}else {
												var  parmas = {
													stallId : orgId,
													mealType : this.params.meal_type,
													cupboardDeliveryFee : item.cupboard_delivery_fee || 0,
													waimaiDeliveryFee : item.waimai_delivery_fee || 0,
												}
												this.verificationData[currentTime].push(parmas)
											}
										}
								 })
								}
								console.log("this.verificationData 111", this.verificationData);
								Cache.set('verificationOrder',this.verificationData)
              }
						} else {
							uni.$u.toast(res.msg)
						}
						reslove()
					}).catch((error) => {
						reject(error)
						this.setAppointData('food')
					})
					.finally(error => {
						uni.hideLoading()
						this.loading = false
						uni.stopPullDownRefresh()
					})
			})
		},
		getCartLists() {
			let params = {
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				company_id: this.params.company_id
			}
			if (this.params.take_meal_type === 'waimai'){
				params.addr_id = this.params.addr_id
			}
			return new Promise((reslove, reject) => {
				apiShopcardCalcList(formateVisitorParams(params, true))
					.then(res => {
						this.cartLists = res.data.shopcard
						this.totalAmount = res.data.all_date_fee
						this.allCount = res.data.all_date_count
						this.changeFood()
						this.verificationOrder(this.cartLists)
						console.log('121212',this.cartLists)
						reslove()
					})
					.catch(err => {
						this.setAppointData('cart')
						uni.$u.toast(err)
					})
			})
		},
		// 购物车加减
		async selectCart(val, item, type, stallId) {
			console.log(222, val, item, type, stallId)
			const params = {
				company_id: this.params.company_id,
				payment_order_type: this.params.payment_order_type,
				food_id: item.obj_name == 'set_meal' ? '' : item.food_id,
				date: item.date || this.params.date,
				take_meal_type: this.params.take_meal_type,
				meal_type: item.meal_type || this.params.meal_type,
				organization_id: item.org_id || item.organization_id,
				organization_alias: item.org_name || item.organization_alias,
				consume_type: item.consume_type,
				fuwu_fee: item.fuwu_fee,
				elevator_fee: item.elevator_fee,
				menu_food_id: item.menu_info.menu_food_id,
				menu_food_object: item.menu_info.menu_food_object,
				spec_id: item.spec_id,
				taste_id: item.taste_id,
				buy_limit_food_count:this.params.buyLimitFoodCount ,// 单人单餐菜品限制份数
				cupboard_takeaway_fee:this.params.cupboardTakeawayFee, // 取餐柜打包费
				stall_id: stallId,
				org_id: this.params.org_id
			}
			if (this.params.reservationSettingId) {
				params.reservation_setting_id = this.params.reservationSettingId
			}
			if (type === 'specs') {
				if (this.specsId) {
					params.spec_id = this.specsId
				}
				if (this.tasteId) {
					params.taste_id = this.tasteId
				}
			}
			// 套餐菜品必选
			let setMealFlag = false,setMealFoodFlag = false;
			if (type == 'setMealSpecs') {
				// 找到必选的
				setMealFlag = item.set_meal_style_data.some((v, index, array) => {
				 if ((v.food_required && !v.setMealSpecsId)) {
						uni.$u.toast(v.style_name + '请选择一个菜品')
						return true
					} else {
						return false
					}
				})
				// 如果没有必选 也要选择一个菜品
				setMealFoodFlag = item.set_meal_style_data.some(v=>{
					 if(v.setMealSpecsId){
						return true
					}else{
						return false
					}
				})
			}
			if (setMealFlag && type == 'setMealSpecs') return
			if (!setMealFoodFlag && type == 'setMealSpecs') return	uni.$u.toast('请选择一个菜品')
			// 20250506 后端说菜品也要加规格id喔
			if (item.obj_name !== 'set_meal' &&  type !== 'specs') {
				let foodSpec = item.food_spec || []
				if (foodSpec && foodSpec.length > 0) {
					let specId = foodSpec[0].id
					if (specId) {
						params.spec_id = specId
					}
				}
			}
			// 套餐的时候 并且是选择规格的时候 太多种情况了 先分开实现一下功能流程，这个功能太恶心人了
			if (item.obj_name == 'set_meal' && item.display_style_button) {
				params.set_meal_id = item.set_meal_id
				params.set_meal_style_data = []
				item.set_meal_style_data.map(v => {
					if (v.setMealSpecsId) {
						let obj = {
							set_meal_style_id: '',
							food_id: '',
							spec_id: ''
						}
						obj.set_meal_style_id = v.style_id
						if (type == 'setMealCart') {
							obj.food_id = v.food_id
							obj.spec_id = v.spec_id
						} else {
							v.set_meal_food_data.map(k => {
								if (v.setMealSpecsId == k.spec_id) {
									obj.food_id = k.food_id
									obj.spec_id = k.spec_id
								}
							})
						}
						params.set_meal_style_data.push(obj)
					}
				})
			}
			//  套餐的时候 并且不是选择规格的时候
			if (item.obj_name == 'set_meal' && !item.display_style_button) {
				params.set_meal_id = item.set_meal_id
				params.set_meal_style_data = []
				params.set_meal_style_data = item.set_meal_style_data.map(v => {
					let obj = {
						set_meal_style_id: '',
						food_id: '',
						spec_id: ''
					}
					obj.set_meal_style_id = v.style_id
					// 因为购物车返回的格式不一样 额外做一个判断购物车获取菜品id和规格id
					if (type == 'setMealCart') {
						obj.food_id = v.food_id
						obj.spec_id = v.spec_id
					} else {
						v.set_meal_food_data.map(k => {
							obj.food_id = k.food_id
							obj.spec_id = k.spec_id
						})
					}
					return obj
				})
			}
			if (
				((!type || type == 'setMealCart') && val > item.count) ||
				(type === 'specs' && val > this.foodInfoCount) ||
				(type === 'setMealSpecs' && val > this.foodInfoCount)
			) {
				await apiShopcardAdd(formateVisitorParams(params, true))
					.then(res => {
						if (res.code == 0) {
							this.getCartLists()
							this.getMealTypeNutrient()
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
			} else {
				await apiShopcardDelete(formateVisitorParams(params, true))
					.then(res => {
						if (res.code == 0) {
							this.getCartLists()
							this.getMealTypeNutrient()
						} else {
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						uni.$u.toast(err.data.msg)
					})
			}
		},
		changeFood() {
			const foods = []
			if (this.cartLists && this.cartLists.length) {
				this.cartLists.forEach(item => {
					item.stall.forEach(sitem => {
						sitem.food.forEach(fitem => {
							foods.push({
								...fitem,
								meal_type: sitem.meal_type,
								date: item.date
							})
						})
					})
				})
			}
			if (this.foodLists && this.foodLists.length) {
				this.foodLists.forEach(litem => {
					litem.food_data.forEach(ditem => {
						ditem.food.forEach(fitem => {
							this.$set(fitem, 'count', 0)
							// foods.forEach((item) => {
							// 	if (item.food_id == fitem.food_id && item.meal_type ==
							// this.params.meal_type && item.date == this.params.date
							// 	) {
							// 		this.$set(fitem, 'count', item.count)
							// 	} else {
							// 	}
							// })
							// 兼容有规格的情况
							let List = []
							if (fitem.obj_name == 'food') {
								List = foods.filter(
									item =>
										item.food_id == fitem.food_id && item.meal_type == this.params.meal_type && item.date == this.params.date
								)
							} else if (fitem.obj_name == 'set_meal') {
								List = foods.filter(
									item =>
										item.set_meal_id == fitem.set_meal_id &&
										item.meal_type == this.params.meal_type &&
										item.date == this.params.date
								)
							}
							let count = 0
							if (List.length) {
								List.forEach(listItem => {
									count += listItem.count
								})
								this.$set(fitem, 'count', count)
							}
						})
					})
				})
			}
		},
		clearCard() {
			apiShopcardClean(formateVisitorParams({
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				company_id: this.params.company_id
			}, true))
				.then(res => {
					uni.$u.toast('清空成功')
					this.showCart = false
					Cache.remove('verificationOrder')
					this.getCartLists()
					this.getMealTypeNutrient()
				})
				.catch(err => {
					uni.$u.toast(err.message)
				})
		},
		handleCountChange(data) {
			this.selectCart(data.value, data.food, 'setMealCart', data.stall_id)
		},
		async getInitParams() {
			const userInfo = Cache.get('userInfo')
			const state = this.$store.state.appoint
			this.params.company_id = this.isAddressVisitor?state.select.address_info.company_id:userInfo.company_id // 游客免登录扫码的话，用码绑定的公司id
			this.params.take_meal_type = state.select.take_meal_type
			this.params.payment_order_type = state.select.payment_order_type
			this.params.person_no = state.select.person.person_no
			this.params.org_id = state.select.org.org_id
			this.params.user_id = userInfo.user_id
			if (this.params.take_meal_type === 'waimai'){
				this.params.addr_id = state.select.address_info.addr_center_id
			}
			this.getUserReservationMealType()
			//第一次进来获取缓存的列表
			if(Object.keys(this.verificationData).length === 0) {
				this.verificationData = Cache.get('verificationOrder')|| {}
			}
		},
		getUserReservationMealType() {
			this.mealTypeInfoData = []
      this.$showLoading({
        title: '加载中...',
        mask: true
      })
			getApiUserReservationMealType(formateVisitorParams({
				org_id: this.params.org_id
			}))
				.then(async res => {
					if (res.code == 0) {
						// 好像为了去默认传值
						if (res.data.now_meal_type.meal_type) {
							// 防止没用值的情况出现报错
							this.params.meal_type = res.data.now_meal_type.meal_type
							this.nowMealTypeData = res.data.now_meal_type
						} else {
							if (res.data.meal_type_info.length) {
								this.params.meal_type = res.data.meal_type_info[0].meal_type
								this.nowMealTypeData = res.data.meal_type_info[0]
							}
						}
						this.mealTypeInfoData = res.data.meal_type_info
						this.$nextTick(_ => {
							// 初始化当前餐段
							this.$refs.appointWrapRef.initMealType()
						})
						// await this.getFood()
						if (this.changeOption) {
							await this.getChangeData(this.changeOption)
						}
						this.getCartLists()
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err)
				})
		},
		openSpecsDialog(info) {
			this.showSpecs = true
			this.foodInfo = info
			console.log(12222, info)
			// 只有菜品才做默认，还有套餐set_meal
			if (info.obj_name == 'food') {
				this.specsType = 'specs'
				if (this.foodInfo.food_spec && this.foodInfo.food_spec.length) {
					this.specsId = this.foodInfo.food_spec[0].id
					this.specsName = this.foodInfo.food_spec[0].name
				} else {
					this.specsId = -1
					this.specsName = ''
				}
				if (this.foodInfo.food_taste && this.foodInfo.food_taste.length) {
					this.tasteId = this.foodInfo.food_taste[0].id
					this.tasteName = this.foodInfo.food_taste[0].name
				} else {
					this.tasteId = -1
					this.tasteName = ''
				}
			} else if (info.obj_name == 'set_meal') {
				this.specsType = 'setMealSpecs'
				// 清除赋值的setMealSpecsId
				this.foodInfo.set_meal_style_data.forEach(setMealStyleItem => {
					this.$delete(setMealStyleItem, 'setMealSpecsId')
					setMealStyleItem.set_meal_food_data.forEach(setMealFoodItem=>{
						this.$set(setMealFoodItem, 'disabled', false)
					})
				})
			}
		},
		chooseSpecs(type, e, index) {
			if (type === 'specs') {
				this.specsId = e.id
				this.specsName = e.name
			} else if (type === 'taste') {
				this.tasteId = e.id
				this.tasteName = e.name
			}
		},
		getChildSpecIds() {
			let selectSpecIds = [],
				specIds = {}
			this.foodInfo.set_meal_style_data.forEach(item => {
				if (item.setMealSpecsId) {
					selectSpecIds.push(item.setMealSpecsId)
					specIds[item.style_id] = item.setMealSpecsId
				}
			})
			return { selectSpecIds, specIds }
		},
		setMealChooseSpecs(styleItem, specsItem) {
			// 单选
			// var idx = this.setMealSpecsIds.indexOf(specsItem.spec_id)
			//如果已经选中了，那就取消选中，如果没有，则选中
			// food_repeat 菜品是否重复选择
			let selectSpecIds = [],
				specIds = {},
				result = {}
			if (!this.foodInfo.food_repeat) {
				result = this.getChildSpecIds()
				selectSpecIds = result.selectSpecIds
				specIds = result.specIds
				if (styleItem.setMealSpecsId == specsItem.spec_id) {
					this.$delete(styleItem, 'setMealSpecsId')
					result = this.getChildSpecIds()
					selectSpecIds = result.selectSpecIds
					specIds = result.specIds
				} else if (!selectSpecIds.includes(specsItem.spec_id)) {
					this.$set(styleItem, 'setMealSpecsId', specsItem.spec_id)
					result = this.getChildSpecIds()
					selectSpecIds = result.selectSpecIds
					specIds = result.specIds
				} else {
					// 不给点击
					return
				}
				// 添加disabled 不给他选择
				this.foodInfo.set_meal_style_data.forEach(setMealStyleItem => {
					// 当前点击样式id与当前遍历的样式id不等于的时候执行
					if (setMealStyleItem.style_id !== styleItem.style_id) {
						setMealStyleItem.set_meal_food_data.forEach(setMealFoodItem => { //遍历当前菜品规格
							// 选中的规格id 包含当前遍历的规格id
							if (selectSpecIds.includes(setMealFoodItem.spec_id)) {
								// 当前有选中的规格id并且当前点击选中的样式规格id与遍历规格的id不等时
								if (selectSpecIds.length > 1 && specIds[styleItem.style_id] != setMealFoodItem.spec_id) {
									// 当选中的specid只有不为1个时
									// 当已选中的规格样式id 与当前遍历的规格id 相等时
									if(specIds[setMealStyleItem.style_id] == setMealFoodItem.spec_id) {
										this.$set(setMealFoodItem, 'disabled', false)
									}
								} else if (specIds[styleItem.style_id]) {
									this.$set(setMealFoodItem, 'disabled', true)
								}
							} else {
								this.$set(setMealFoodItem, 'disabled', false)
							}
						})
					}
				})
			} else {
				// 点击单选
				if (styleItem.setMealSpecsId == specsItem.spec_id) {
					this.$delete(styleItem, 'setMealSpecsId')
				} else {
					this.$set(styleItem, 'setMealSpecsId', specsItem.spec_id)
				}
			}
		},
		clickFoodDetail(data) {
			console.log("clickFoodDetail",data);
			Cache.set('verificationOrder',this.verificationData)
			var params = deepClone(this.params)
			params.currentDate = this.currentTime
			this.$miRouter.push({
				path:
					data.obj_name == 'food'
						? '/pages_bundle/appoint/appoint-food-details'
						: '/pages_bundle/appoint/appint-set-meal-food-details',
				query: {
					data: this.$encodeQuery({
						is_healthy_info: this.nutrientMealType.is_healthy_info,
						stall_id:this.selectFood.org_id, // 消费规则限制后端要求加stall_id
						...data
					}),
					params: this.$encodeQuery(params),
				}
			})
		},
		clickRecommend(){
			// let params = {
			// 	date: this.params.date,
			// 	meal_type: this.params.meal_type,
			// 	payment_order_type: this.params.payment_order_type,
			// 	take_meal_type: this.params.take_meal_type,
			// 	company_id: this.params.company_id,
			// 	food_id_list:[]//菜品id
			// }
			this.$miRouter.push('/pages_health/healthy/menu/menu_recommend')
		},
		// 判断是否要显示隐藏
		// nutrientRecommend(){
		// 	let status = false
		// 	if(this.nutrientMealType.is_healthy_info){
		// 		if(this.nutrientMealType.carbohydrate>this.nutrientMealType.need_carbohydrate){
		// 			status = true
		// 		}else if (this.nutrientMealType.protein>this.nutrientMealType.need_protein){
		// 			status = true
		// 		}else if (this.nutrientMealType.axunge>this.nutrientMealType.need_axunge){
		// 			status = true
		// 		}else{
		// 			status = false
		// 		}
		// 	}
		// 	return status
		// },
		collocationClick(){
			let params = {
				date: this.params.date,
				meal_type: this.params.meal_type,
				payment_order_type: this.params.payment_order_type,
				take_meal_type: this.params.take_meal_type,
				company_id: this.params.company_id,
				organization_id:"",
				organization_alias:"",
				menu_food_id: '',
				menu_food_object: '',
				consume_type:"",
				food_id_list:[]//菜品id
			}
			if(this.foodLists.length>0){
				this.foodLists.map(foodListsItem=>{
					foodListsItem.food_data.map(foodDataItem=>{
						if(foodDataItem.food[0]){
							params.menu_food_id = foodDataItem.food[0].menu_info.menu_food_id
							params.menu_food_object = foodDataItem.food[0].menu_info.menu_food_object
							params.organization_id = foodDataItem.food[0].org_id
							params.organization_alias = foodDataItem.food[0].org_name
							params.consume_type = foodDataItem.food[0].consume_type
						}
						foodDataItem.food.map(foodItem=>{
							params.food_id_list.push(foodItem.food_id)
						})
					})
				})
			}
			// return
			this.$miRouter.push({
			  path: '/pages_bundle/appoint/food-reasonable-collocation',
			  query: {
			    // #ifdef MP-ALIPAY
			    data: this.$encodeQuery(params),
			    // #endif
			    // #ifndef MP-ALIPAY
			    data: params,
			    // #endif
			  }
			})
		},
    formateText() {
			return	formateBtnText(this.params,this.isVerificationOrderFlag,this.cupboardDeliveryFee,this.waimaiDeliveryFee)
    },
    gotoConfirmOrder() {
      if (this.isVerificationOrder) {
				Cache.set('verificationOrder',this.verificationData)
        this.$miRouter.push({
          path: '/pages_bundle/appoint/confirm_order',
          query: {
            buyLimitFoodCount: this.params.buyLimitFoodCount,
            cupboardTakeawayFee:this.params.cupboardTakeawayFee,
						currentDate: this.currentTime
          }
        })
      }else{
				//错误显示提示
				uni.$u.toast(this.verificationOrderError)
			}
    },
		/**
		 * @description
		 */
		setAppointData(key) {
			this.foodLists = []
			this.cartLists = []
			this.totalAmount = 0
			this.allCount = 0
			// switch (key) {
			// 	case 'food':
			// 		this.foodLists = []
			// 		break;
			// 	case 'cart':
			// 		this.cartLists = []
			// 		this.totalAmount = 0
			// 		this.allCount = 0
			// 		break;
			// 	default:
			// 		break;
			// }
		},
		/**
		 * 分日期去校验配送费
		 * @param list 配送列表,包含日期，餐段等数据
		 */
		 verificationOrder(list){
			 if(this.params.take_meal_type === 'waimai' || this.params.take_meal_type === 'cupboard') {
				var resultData =	verificationOrderData(list,this.params,this.verificationData)
				console.log("resultData", resultData);
				if(resultData) {
					this.isVerificationOrder = resultData.isVerificationOrder
					this.verificationOrderError = resultData.verificationOrderError
					this.isVerificationOrderFlag = resultData.isVerificationOrderFlag
			 	}

			 }
		},
    // 预览图片
    handlerPreview(url) {
      console.log("handlerPreview", url);
      if (url) {
        uni.previewImage({
          // 先filter找出为图片的item，再返回filter结果中的图片url
          urls: [url],
          current: url,
          fail() {
            uni.$u.toast('预览图片失败')
          },
        });
      } else {
        uni.$u.toast('暂无图片')
      }
    }
	},
	computed: {
		...mapGetters(['isAddressVisitor']),
		currentDate() {
			return format => timeFormat(Date.now(), format)
		},
		foodInfoCount: {
			get() {
				let count = 0
				this.cartLists.map(item => {
					if (item.date === this.params.date) {
						item.stall.map(stall => {
							if (stall.meal_type === this.params.meal_type) {
								stall.food.map(food => {
									if (food.obj_name == 'set_meal') {
										// 套餐规格判断 排序好 拿已点套餐数量
										if (Number(food.set_meal_id) === this.foodInfo.set_meal_id) {
											let cartSpecIds = [],
												setMealStyleSpecsIds = [],
												cartStyleIds = [],
												setMealStyleIds = [];
											food.set_meal_style_data.filter(cartStyle => {
												if (cartStyle.spec_id) {
													cartSpecIds.push(cartStyle.spec_id)
													cartStyleIds.push(Number(cartStyle.style_id))
												}
											})
											this.foodInfo.set_meal_style_data.filter(setMealStyle => {
												if (setMealStyle.setMealSpecsId) {
													setMealStyleSpecsIds.push(setMealStyle.setMealSpecsId)
													setMealStyleIds.push(Number(setMealStyle.style_id))
												}
											})
											cartSpecIds = cartSpecIds.sort((a, b) => {
												return a - b
											})
											setMealStyleSpecsIds = setMealStyleSpecsIds.sort((a, b) => {
												return a - b
											})
											cartStyleIds = cartStyleIds.sort((a, b) => {
												return a - b
											})
											setMealStyleIds = setMealStyleIds.sort((a, b) => {
												return a - b
											})
											if (
												cartSpecIds.length &&
												setMealStyleSpecsIds.length &&
												JSON.stringify(cartSpecIds) === JSON.stringify(setMealStyleSpecsIds) &&
												JSON.stringify(cartStyleIds) === JSON.stringify(setMealStyleIds)
											) {
												count = food.count
											}
										}
									} else if (food.obj_name == 'food') {
										if (
											Number(food.food_id) === this.foodInfo.food_id &&
											food.spec_id === this.specsId &&
											food.taste_id === this.tasteId
										) {
											count = food.count
										}
									}
								})
							}
						})
					}
				})
				return count
			},
			set(val) {}
		},
		specsPrice() {
			let price = 0
			if (this.foodInfo.obj_name == 'food') {
				if (this.foodInfo.food_spec && this.foodInfo.food_spec.length) {
					price = this.foodInfo.food_spec.find(item => item.id === this.specsId).food_price
				} else {
					price = this.foodInfo.price
				}
			} else if (this.foodInfo.obj_name == 'set_meal') {
				if (this.foodInfo.price_type == 'fixed') {
					price = this.foodInfo.price
				}
			}
			return price
		},
		specsTotalPrice() {
			let price = 0
			if (this.foodInfo.obj_name == 'food') {
				if (this.foodInfo.food_spec && this.foodInfo.food_spec.length) {
					price = this.foodInfo.food_spec.find(item => item.id === this.specsId).food_price * this.foodInfoCount
				} else {
					price = this.foodInfo.discount * this.foodInfoCount
				}
			} else if (this.foodInfo.obj_name == 'set_meal') {
				// 如果是固定金额 就只能拿
				if (this.foodInfo.price_type == 'fixed') {
					price = this.foodInfo.price
				} else {
					// 如果不是固定金额就判断有没有setMealSpecsId 拿规格合计
					if (this.foodInfo.set_meal_style_data && this.foodInfo.set_meal_style_data.length) {
						this.foodInfo.set_meal_style_data.map(v => {
							v.set_meal_food_data.map(setMealFoodItem => {
								if (v.setMealSpecsId == setMealFoodItem.spec_id) {
									price += setMealFoodItem.food_price
								}
							})
						})
					}
				}
			}
			return price
		},
		// 多规格 套餐的时候
		selectSetMealSpecName() {
			let setMealSpecName = []
			if (this.foodInfo.obj_name == 'set_meal') {
					if (this.foodInfo.set_meal_style_data && this.foodInfo.set_meal_style_data.length) {
						this.foodInfo.set_meal_style_data.map(v => {
							v.set_meal_food_data.map((setMealFoodItem,setMealFoodIndex) => {
								if (v.setMealSpecsId == setMealFoodItem.spec_id) {
									 setMealSpecName.push(setMealFoodItem.food_name)
								}
							})
						})
					}
					}
			return setMealSpecName.join('、')
		},
    canGotoPay() {
      let flag
      if (this.params.take_meal_type === 'cupboard' && this.totalAmount < this.cupboardDeliveryFee) {
        flag = false
      } else if (this.params.take_meal_type === 'waimai' && this.totalAmount < this.waimaiDeliveryFee) {
        flag =  false
      } else {
        flag =  true
      }
      return flag
    }
  },
	onLoad() {
		this.getInitParams()
	},
	// 下拉刷新会调两次菜品接口 先关掉
	// async onPullDownRefresh() {
	// 	// await this.getFood()
	// 	this.getCartLists()
	// },
	async onShow() {
		// 删除vuex中的选择优惠券数据，在payment确定订单页面中有用到优惠券 --mtj
		this.setRemoveCouponItem()
		// 因为日期change 返回默认执行了一次getfood 进入下一个页面返回就没数据了 需要调一次
		// this.getFood()
		// 每次show的时候重置下
		// await this.getInitParams()
		// 如果当前是有餐段数据的可以请求下购物车的数据了
		if (this.mealTypeInfoData.length) {
			// this.getCartLists()
			if (this.changeOption) {
				await this.getChangeData(this.changeOption)
			}
			this.getCartLists()
		}
		this.floatingPopupShow = !this.floatingPopupShow
	}
}
</script>

<style lang="scss" scoped>
.appointment-order {
	height: 100vh;

	.goods-lists {
		.goods-item {
			&.disabled {
				opacity: 0.6;
			}
			.goods-name {
				max-width: 350rpx;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
			}
			.dot-nutrition {
			  display: inline-block;
			  width: 15rpx;
			  height: 15rpx;
			  border-radius: 10rpx;
			}
		}
	}

	.image-null {
		width: 240rpx;
		height: 176rpx;
		margin: 170rpx auto 0;
	}

	.cart {
		height: 100rpx;
		box-sizing: content-box;
		padding-bottom: env(safe-area-inset-bottom);
		position: relative;
		z-index: 10070;
		background-color: #fff;
		border-top: $border-base;

		.total-order {
			margin-left: 150rpx;
		}
		.collocation-box{
			position: absolute;
			left: 0rpx;
			top: -100rpx;
			padding: 30rpx;
			height: 100rpx;
			width: 100%;
			background-color:#ff8889;
			 opacity: 0.7;
			.text{
				color: #fff;
			}
			.recommend-but{
				background-color: #fff;
				padding-top:10rpx;
				padding-bottom:40rpx;
				padding-left:30rpx;
				padding-right:30rpx;
				border-radius: 20rpx;
				color: $color-primary;
			}
		}
		.cart-num {
			position: absolute;
			left: 28rpx;
			top: -25rpx;
			width: 100rpx;
			height: 100rpx;
			background: $color-primary;
			box-shadow: 0 0 12rpx 0rpx $color-primary;
			border-radius: 50%;
		}
	}
}
.instructions {
	width: 600rpx;
}
.choose-specs {
	width: 600rpx;
	padding: 30rpx 0;
	position: relative;
	.food-info-img {
		width: 140rpx;
		height: 140rpx;
		border-radius: 18rpx;
		margin-right: 20rpx;
	}
	.food-info-name {
		max-width: 350rpx;
		text-overflow: ellipsis;
		white-space: nowrap;
		overflow: hidden;
	}
	.text-ash {
		color: #9da2a7;
	}
	.already-choose {
		background-color: #f6f8f8;
		padding: 14rpx 30rpx;
	}
	.food-total {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 30rpx 30rpx 0;
		line-height: 36rpx;
	}
	.specs-list {
		display: flex;
		flex-wrap: wrap;
		padding-bottom: 30rpx;
		.specs-item {
			margin: 10rpx 20rpx 0 0;
			padding: 0 20rpx;
			border: 2rpx #e3e3e3 solid;
			border-radius: 6rpx;
			text-align: center;
			font-size: 20rpx;
			line-height: 46rpx;
			max-width: 210rpx;
			text-overflow: ellipsis;
			white-space: nowrap;
			overflow: hidden;
		}
		.activeSpecs {
			color: $color-primary;
			border: 2rpx $color-primary solid;
		}
	}
	.disabled {
		opacity: 0.3;
	}
	.icon-close-popup{
		position: absolute;
		bottom: -70rpx;
		left: 50%;
	}
}
</style>
