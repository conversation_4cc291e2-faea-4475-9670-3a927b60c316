<template>
  <view class="choose_cupboard">
    <view v-if="showText" class="red md text-center p-t-30 p-b-20">柜子已满，请选择其他柜子或选择取餐方式</view>
    <view class="cupboard-list">
      <view
        :class="['cupboard-item', item.addr === cupboardAddr ? 'activeCupboard' : '']"
        v-for="(item,index) in cupboardList"
        :key="index"
        @click="chooseCupboard(item)"
      >
        {{ item.addr }}
      </view>
    </view>
    <view v-if="!cupboardList.length" class="text-center">
      <u-empty class="p-b-20 p-t-20" iconSize="60" mode="message" text="暂无数据">
        <text class="p-b-20"></text>
      </u-empty>
    </view>
  </view>
</template>

<script>
import { apiGetCupboardAddress } from '@/api/reservation'
import { mapMutations } from 'vuex'
export default {
  data() {
    return {
      showText: false,
      cupboardList: [],
      cupboardAddr: '',
      stallIdList: []
    }
  },
  onLoad() {
    this.stallIdList = this.$decodeQuery(this.$Route.query.data) || []
    console.log(111,this.$Route.query.data, this.stallIdList)
    this.stallIdList.map((item, index) => {
      this.getCupboardAddress(item, index)
    })
  },
  methods: {
    ...mapMutations(['SET_SELECT']),
    async getCupboardAddress(org_id, index) {
      await apiGetCupboardAddress({
        org_id
      })
        .then(res => {
          if (this.cupboardList.length) {
            this.cupboardList = this.cupboardList.filter(x => res.data.cupboard_list.some(y => y.id === x.id))
          } else {
            this.cupboardList = res.data.cupboard_list
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    chooseCupboard(e) {
      this.cupboardAddr = e.addr
      if (e.ceil > 0) {
        this.showText = false
        this.SET_SELECT({
          key: 'cupboard',
          data: e
        })
        this.$miRouter.back()
      } else {
        this.showText = true
      }
    }
  }
}
</script>

<style lang="scss">
.choose_cupboard {
  background-color: #ffffff;
  .cupboard-list {
    display: flex;
    flex-wrap: wrap;
    // justify-content: space-between;
    margin: 0 20rpx;
    padding-bottom: 30rpx;
    .cupboard-item {
      width: 30%;
      margin: 20rpx 0;
      border: 1rpx #f3f3f3 solid;
      border-radius: 20rpx;
      height: 120rpx;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .cupboard-item:nth-child(-n+2){
      margin-right: 30rpx;
    }
    .activeCupboard {
      color: $color-primary;
      border: 1rpx $color-primary solid;
    }
  }
}
</style>
