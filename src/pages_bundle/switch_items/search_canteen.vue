<template>
  <view :style="theme.style" class="payment">
    <!-- Section 主体 Start -->
    <view class="section bg-white">
      <view class="m-b-20">
        <u-input
          placeholder="请输入食堂名称"
          border="surround"
          v-model="shopName"
          prefixIcon="search"
          prefixIconStyle="font-size: 22px;color: #909399"
        ></u-input>
      </view>
      <view class="item bb" hover-class="click" v-for="(item, index) in options" :key="index" @click="selectCanteen(item)">
        {{ item.company_name }}
      </view>
    </view>
    <!-- Section End -->
  </view>
</template>

<script>
import { apiBookingUserProjectList } from '@/api/app'
export default {
  // Data Start
  data() {
    return {
      shopName: '',
      options: []
    }
  },
  // Data End

  // Methods Start
  methods: {
    getUserProjectList() {
      apiBookingUserProjectList()
        .then(res => {
          if (res.code == 0) {
            this.options = res.data
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          console.log(err)
          uni.$u.toast(err.message)
        })
    },
    selectCanteen() {
      this.$miRouter.back()
    }
  },
  // Methods End

  // Life Cycle Start
  onLoad() {
    this.getUserProjectList()
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.payment {
  .ls-card {
    border-radius: 20rpx;
  }

  .section {
    padding: 40rpx 40rpx 1rpx 40rpx;
    margin-bottom: calc(100rpx + env(safe-area-inset-bottom));
    border-top: $border-base;
    border-radius: 0 0 20rpx 20rpx;

    .bb {
      border-bottom: $border-base;
    }

    .bb:last-child {
      border-bottom: 0;
    }

    .item {
      padding: 20rpx 0;
      padding-left: 60rpx;
      // margin-bottom: 30rpx;
    }

    .click {
      background-color: $background-color;
    }
  }
}
</style>
