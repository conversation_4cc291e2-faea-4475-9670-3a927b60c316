<template>
	<view :style="theme.style" class="switch-phone">
		<view class="switch-phone-wrapp">
			<u-form :model="formData" :rules="rules" ref="uformRef" errorType="toast">
				<u-form-item label="所在项目" borderBottom labelWidth="150">
					<u-input v-model="coampanForm.companyName" color="#aeb0b2" border="none" readonly inputAlign="right" style="height:70rpx"></u-input>
				</u-form-item>
				<u-form-item label="姓名" borderBottom labelWidth="150">
					<u-input v-model="coampanForm.name" color="#aeb0b2" border="none" readonly inputAlign="right" style="height:70rpx"></u-input>
				</u-form-item>
				<u-form-item label="人员编号" borderBottom labelWidth="150">
					<u-input v-model="coampanForm.person_no" color="#aeb0b2" border="none" readonly inputAlign="right" style="height:70rpx"></u-input>
				</u-form-item>

				<u-form-item prop="phone" borderBottom label="原绑定手机号" labelWidth="200">
					<!-- <u-input
						v-model="formData.phone"
						border="none"
						maxlength="11"
						minlength="8"
						placeholder="请输入手机号码"
						inputAlign="right"
						disabled
						style="height:70rpx"
					></u-input> -->
					<u-input v-model="formData.phone" color="#aeb0b2" border="none" readonly inputAlign="right" style="height:70rpx"></u-input>
				</u-form-item>
				<u-form-item prop="code" label="短信验证码" labelWidth="200">
					<u-input v-model="formData.code" border="none" maxlength="6" inputAlign="right" placeholder="请输入验证码" style="height:70rpx"></u-input>
					<view slot="right" class="p-l-20">
						<u-code :seconds="seconds" ref="uCodeRef" @change="codeChange"></u-code>
						<u-button @click="handlerRefreshCode" type="primary" :color="variables.colorPrimary" :customStyle="customBtnStyle" plain="" size="small">
							{{ tips }}
						</u-button>
					</view>
				</u-form-item>
			</u-form>
		</view>
		<div class="p-t-20 tips-wrapp">
			<div class="p-b-10">温馨提示：</div>
			<div class="p-b-10">1.需输入原手机号并进行校验，校验成功后提交绑定人员；</div>
			<div class="p-b-10">2.提交成功后换绑为当前登录的手机号；</div>
			<div class="p-b-10">3.同一用户换绑手机一天只能进行3次校验</div>
		</div>
		<!-- Footer 底部 Start -->
		<view class="footer">
			<view class="footer--warpper flex flex-center">
				<u-button
					shape="circle"
					text="提交"
					:color="variables.bgLinearGradient1"
					@click="clickSwitchPhone"
				></u-button>
			</view>
		</view>
		<!-- Footer End -->
		<verify-code ref="verifyCode" :is-number="true" @success="verifyCodeSuccess" @refresh="verifyCodeRefresh"></verify-code>
	</view>
</template>

<script>
import { apiSmsSend, setChangeCardInfoPhone, getLoginVerifyCode } from '@/api/app'
import Base64 from 'base-64'

export default {
	data() {
		return {
			formData: {
				phone: '',
				code: '',
			},
			rules: {
				phone: [
					{
						// min: 8,
						// max: 11,
						required: true,
						message: '请输入手机号码',
						trigger: ['blur', 'change']
					},
					// {
					// 	validator: (rule, value, callback) => {
					// 		return uni.$u.test.mobile(value)
					// 	},
					// 	message: '手机号码不正确',
					// 	// 触发器可以同时用blur和change
					// 	trigger: ['change', 'blur']
					// }
				],
				code: [
					{
						max: 6,
						required: true,
						message: '请输入验证码',
						trigger: ['blur', 'change']
					}
				]
			},
			tips: '',
			seconds: 60,
			customBtnStyle: {
				minWidth: '180rpx',
				height: '55rpx',
				lineHeight: '55rpx',
				padding: '0 10rpx'
			},
			coampanForm: {},
			resultTxtList: []
		}
	},
	onReady() {
		//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
		this.$refs.uformRef.setRules(this.rules)
	},
	onLoad(option) {
		this.coampanForm = this.$decodeQuery(this.$Route.query.data)
		console.log(11231, this.coampanForm)
		if (this.coampanForm.phone) {
			this.formData.phone = this.formatPrivacyPhone(this.coampanForm.phone)
		}
	},
	onShow() {},
	mounted() {},
	methods: {
		formatPrivacyPhone(value) {
			// 后台手机可以输8-11位的哦，神奇的东西
			if (value) {
				// 不按前三后四的格式来哦，直接隐藏4-7位哦
				// let len = value.length - 7
				// const reg = new RegExp(`(\\d{3})\\d{${len}}(\\d{4})`)
				if (value.length >= 8) { // 手机号>=8位数
					const reg = new RegExp(`(\\d{3})\\d{4}(\\d)`)
					return value.replace(reg, '$1****$2')
				} else { // 不足八位的直接隐藏第四位及后面的
					let len = value.length - 3
					return `${value.substring(0, 3)}${"****".substring(0, len)}`
				}
			} else {
				return value
			}
		},
		codeChange(text) {
			this.tips = text
		},
		getCode() {
			this.$refs.uformRef.validateField('phone', res => {
				if (res.length) return
				if (!this.$refs.uCodeRef.canGetCode) return
				let value = {
          value1: this.resultTxtList[0],
          value2: this.resultTxtList[1]
        }
				let keys = Base64.encode(JSON.stringify(value))
				apiSmsSend({
					phone: this.coampanForm.phone,
					code: keys
				}).then(res => {
					this.$refs.uCodeRef.start()
				})
			})
		},
		changeCardInfoPhone() {
			this.$showLoading({
				title: '绑定中....',
				mask: true
			})
			setChangeCardInfoPhone({
				company_id: this.coampanForm.company_id,
				person_no: this.coampanForm.person_no,
				name: this.coampanForm.name,
				phone: this.coampanForm.phone,
				code: this.formData.code
			})
				.then(async res => {
					uni.hideLoading()
					if (res.code == 0) {
						uni.$u.toast('绑定成功')
						await this.$sleep(700)
						this.$miRouter.replaceAll({
							path: '/pages/index/index'
						})
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err)
				})
		},
		clickSwitchPhone() {
			this.$refs.uformRef
				.validate()
				.then(res => {
					this.changeCardInfoPhone()
				})
				.catch(errors => {
					console.log(errors)
					// uni.$u.toast(errors)
				})
		},
    // 获取图形验证码
    getVerCode(phone, flag) {
      console.log("phone", phone);
      if (!phone || !/^1[3456789]\d{9}$/.test(phone)) {
        return this.$u.toast('请检查你的手机号码是否正确')
      }
      getLoginVerifyCode({ phone }).then(res => {
				if (res && res.code == 0) {
					let data = res.data || ''
					if (data) {
						let keys = Base64.decode(data.key) ? JSON.parse(Base64.decode(data.key)) : ''
						console.log("getLoginVerifyCode", keys);
						this.resultTxtList = []
						if (keys && typeof keys === 'object') {
							for (let keyName in keys) {
								this.resultTxtList.push(keys[keyName])
							}
						}
						if (this.$refs.verifyCode) {
							this.$refs.verifyCode.setResultTxt(this.resultTxtList)
							if (flag) {
								this.$refs.verifyCode.init()
							} else {
								this.$refs.verifyCode.open()
							}
						}
					}
				} else {
					uni.$u.toast(res.msg)
				}
			}).catch(err => { })
		},
    // 更新验证码啊
    handlerRefreshCode() {
			if (!this.$refs.uCodeRef.canGetCode) return
      this.getVerCode(this.coampanForm.phone, false)
    },
		verifyCodeSuccess(value) {
      console.log("verifyCodeSuccess", value);
      this.getCode()
    },
		verifyCodeRefresh() {
      this.getVerCode(this.coampanForm.phone, true)
    }
	}
}
</script>

<style lang="scss" scoped>
.switch-phone {
	padding: 20rpx;
	.text-right {
		width: 100%;
		text-align: right;
	}
	.switch-phone-wrapp {
		padding: 20rpx;
		border-radius: 20rpx;
		background-color: #fff;
	}
	.tips-wrapp {
		color: #9d9fa2;
		font-size: 24rpx;
	}
	.footer {
		width: 100%;
		position: fixed;
		bottom: 0;
		left: 0;
		padding: 0 40rpx;
		padding-bottom: env(safe-area-inset-bottom);

		&--warpper {
			height: 190rpx;
		}
	}
	.vrcode {
		width: 120rpx;
		height: 56rpx;
 }
}
</style>
