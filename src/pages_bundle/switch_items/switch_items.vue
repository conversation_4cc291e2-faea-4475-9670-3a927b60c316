<template>
  <view :style="theme.style" class="payment">
    <!-- Section 主体 Start -->
    <scroll-view scroll-y="true">
      <view class="section bg-white">
        <block v-for="(item, index) in itemsData" :key="index">
          <view class="item flex row-between col-center" @click="switchItemsFunc(item)">
            <view class="black">
              {{ item.company_name }}
              <text class="muted sm m-l-20">({{ item.name }})</text>
            </view>
            <u-icon v-if="item.flag" name="checkmark" :color="variables.colorPrimary" size="36rpx"></u-icon>
          </view>
        </block>
      </view>
    </scroll-view>
    <!-- Section End -->

    <!-- Footer 底部 Start -->
    <view class="footer bg-white">
      <view class="footer--warpper flex flex-center">
        <u-button
          text="添加项目"
          :color="variables.bgLinearGradient1"
          @click="toAddItemsFunc"
        ></u-button>
      </view>
    </view>
    <!-- Footer End -->
  </view>
</template>

<script>
import { getApiUserGetProjectCardUserList, setApiChangeProjectPoint } from '@/api/app'
import Cache from '@/utils/cache'
import { mapActions, mapGetters, mapMutations } from 'vuex'
import { apiQueryUserinfo } from '@/api/user.js'
export default {
  // Data Start
  data() {
    return {
      userinfo: {},
      itemsData: []
    }
  },
  // Data End

  // Methods Start
  methods: {
    ...mapActions({
      setUserInfo: 'setUserInfo',
      setIsVIP: 'setIsVIP'
    }),
    ...mapMutations(['SET_SELECT','CLEAR_SELECT']),
    queryUserinfo() {
      apiQueryUserinfo()
        .then(res => {
          this.$toast({
            title: '切换成功'
          })
          this.setUserInfo(res.data)
          uni.setStorageSync('companyId', res.data.company_id)
          this.$miRouter.replaceAll({ path: `/pages/index/index` })
        })
        .catch(err => {
          console.log('获取用户信息err', err)
        })
    },
    getUserGetProjectCardUserList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiUserGetProjectCardUserList()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.itemsData = res.data.map(v => {
              if (v.company_id == this.userinfo.company_id && v.person_no == this.userinfo.person_no) {
                v.flag = true
              } else {
                v.flag = false
              }
              return v
            })
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    toAddItemsFunc() {
      this.$miRouter.push('/pages_bundle/switch_items/add_items')
    },
    setChangeProjectPoint(parmas) {
      this.$showLoading({
        title: '切换中....',
        mask: true
      })
      setApiChangeProjectPoint(parmas)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            // 清理预约就餐信息
						this.CLEAR_SELECT()
      //       this.SET_SELECT({
      //         key: 'take_meal_type',
      //         data: ''
      //       })
      //       this.SET_SELECT({
      //         key: 'org',
      //         data: {}
      //       })
      //       this.SET_SELECT({
      //         key: 'person',
      //         data: {}
      //       })
						// this.SET_SELECT({
						//   key: 'intent_org_id',
						//   data: ''
						// })
            this.queryUserinfo()
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err.message)
        })
    },
    switchItemsFunc(item) {
      let parmas = {
        company_id: item.company_id,
        company_name: item.company_name,
        name: item.name,
        person_no: item.person_no
      }
      this.setChangeProjectPoint(parmas)
    }
  },
  // Methods End

  // Life Cycle Start
  onLoad() {},
  onShow() {
    this.userinfo = Cache.get('userInfo')

    this.getUserGetProjectCardUserList()
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.payment {
  .ls-card {
    border-radius: 20rpx;
  }

  .section {
    padding: 0 40rpx;
    // margin-bottom: 200rpx;
    margin-bottom: calc(200rpx + env(safe-area-inset-bottom));
    border-top: $border-base;

    .item {
      padding: 40rpx 0;
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 190rpx;
    }
  }
}
</style>
