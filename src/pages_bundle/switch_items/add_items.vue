<template>
  <view :style="theme.style" class="payment" @touchmove="popupShow = true" @touchend="popupShow = false">
    <!-- Section 主体 Start -->
    <view class="section bg-white">
      <view class="item">
        <view class="black nr m-b-20">项目名称</view>
        <select-lay
          :value="coampanForm.company_id"
          slabel="company_name"
          svalue="company_id"
          :zindex="9999"
          :showplaceholder="false"
          :options="options"
          @selectitem="selectitem"
        />
        <!-- <input placeholder="请输入项目名称" border="surround" v-model="value"></input> -->
      </view>

      <view class="item">
        <view class="black nr m-b-20">姓名</view>
        <u-input placeholder="请输入您的姓名" border="surround" v-model="coampanForm.name"></u-input>
      </view>

      <view class="item">
        <view class="black nr m-b-20">人员编号</view>
        <u-input placeholder="请输入您的用户编号" border="surround" v-model="coampanForm.person_no"></u-input>
      </view>
      <view class="item" v-if="isShowIdCard">
        <view class="black nr m-b-20">身份证号</view>
        <u-input
          placeholder="请输入身份证号"
          border="surround"
          v-model="coampanForm.id_number"
        ></u-input>
      </view>
      <view class="item" v-if="coampanForm.register_input_department">
        <view class="black nr m-b-20">所属部门</view>
        <select-lay
          :value="coampanForm.departmentId"
          slabel="group_name"
          svalue="id"
          :placeholder="coampanForm.register_department_tips"
          :showplaceholder="false"
          :options="departmentOptions"
          @selectitem="selectDepartment"
        />
      </view>
    </view>
    <!-- Section End -->

    <!-- Footer 底部 Start -->
    <view class="footer bg-white">
      <view class="footer--warpper flex flex-center">
        <u-button
          text="确认"
          :color="variables.bgLinearGradient1"
          @click="confirmAddItemsFunc"
        ></u-button>
      </view>
    </view>
    <!-- Footer End -->

    <!-- Components Modal Start -->

    <!-- 弹窗-余额上限 -->
    <u-modal :show="isError" :title="'添加失败'" confirmColor="#5A6080">
      <view class="xl text-center" v-html="content"></view>
    </u-modal>
    <u-modal
      :show="createCardTipsShow"
      :title="'提示'"
      confirmColor="#5A6080"
      confirmText="确定"
      @confirm="confirmCreateCardFunc"
      :showCancelButton="true"
      cancelText="取消"
      @cancel="createCardTipsShow = false"
    >
      <view class="">{{ createCardTips }}</view>
    </u-modal>

    <u-modal
      :show="changePhoneShow"
      :title="'提示'"
      confirmColor="#5A6080"
      confirmText="换绑手机号"
      @confirm="changePhoneConfirm"
      :showCancelButton="true"
      cancelText="取消"
      @cancel="changePhoneShow = false"
    >
      <view class="">{{ switchTips }}</view>
    </u-modal>
    <!-- 开启审批的弹窗 -->
    <popup-layout :show.sync="showApprovePopup" title="提示" content="自注册审批已提交，需等待管理员审批。" alignCenter>
      <template slot="footer">
        <view class="popup-footer m-t-50">
          <u-button :custom-style="confirmStyle" type="primary" text="确定" @click="confirmHandle"></u-button>
        </view>
      </template>
    </popup-layout>
    <!-- Components Modal End -->
    <!-- 客服弹窗 -->
    <CustomerServicePopup :popupShow="popupShow" />
  </view>
</template>

<script>
import { apiBookingUserProjectList, setUserBindProjectPoint } from '@/api/app'
import { apiQueryUserinfo, apiCreateCardInfo } from '@/api/user.js'
import { apiBookingApproveRegisterGetAllowRegisterPost, apiBookingApproveRegisterGetDepartmentListPost } from '@/api/register'
import { mapActions, mapGetters, mapMutations } from 'vuex'
import CustomerServicePopup from '@/components/customer-service-popup/customer-service-popup.vue'
import Cache from '@/utils/cache'
import popupLayout from '@/components/popup/popup-layout'
import { encrypted } from '@/utils/aesUtil'

export default {
  components: {
    popupLayout,
    CustomerServicePopup
  },
  // Data Start
  data() {
    return {
      popupShow: false,
      coampanForm: {
        person_no: '',
        name: '',
        company_id: '',
        id_number: '',
        departmentId: '', // 部门ID
        register_input_department: false, // 是否显示部门
        register_department_tips: '', // 提示
        register_department_required: false // 是否必填
      },
      options: [],
      departmentOptions: [], // 部门列表数据
      createCardTipsShow: false,
      createCardTips: '',
      isError: false,
      // 弹窗提示文字
      content:
        '‘黄小二’已被绑定，<span class="u-error">请确认信息是否正确 </span>或 <span class="u-error">先解除绑定再进行添加</span>',
      changePhoneShow: false,
      switchTips: '',
      companyName: '',
      showApprovePopup: false,
      confirmStyle: {
        color: '#fff',
        width: '50%',
        height: '65rpx'
      },
      phone: '', // 添加项目点的手机，用于用户已绑定其他手机号，的情况
      isShowIdCard: false  // 是否显示身份证
    }
  },
  // Data End

  // Methods Start
  methods: {
    ...mapActions({
      setUserInfo: 'setUserInfo'
    }),
    ...mapMutations(['SET_SELECT', 'CLEAR_SELECT']),
    toSearchCanteen() {
      // this.$miRouter.push('/pages_bundle/switch_items/search_canteen')
    },
    queryUserinfo() {
      apiQueryUserinfo()
        .then(res => {
          this.$toast({
            title: '切换成功'
          })
          this.setUserInfo(res.data)
          let memberCenterChannelId = Cache.get('memberCenterChannelId') || ''
          if (memberCenterChannelId) {
            this.$miRouter.replaceAll({
              path: `/pages_member/member_center/VIP_page`
            })
          } else {
            this.$miRouter.replace({ path: '/pages/index/index' })
          }
        })
        .catch(err => {
          console.log('获取用户信息err', err)
        })
    },
    getUserProjectList() {
      let params = {}
      let companyId = Cache.get('projectListCompanyId')
      if (companyId) {
        params.company_id = companyId
        this.coampanForm.company_id = companyId
      }
      apiBookingUserProjectList(params)
        .then(res => {
          if (res.code == 0) {
            this.options = res.data
            if (this.coampanForm.company_id) {
              this.getUserSetting(this.coampanForm.company_id)
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          console.log(err)
          uni.$u.toast(err.message)
        })
    },
    // 设置项目点
    setUserProjectList(parmas) {
      this.$showLoading({
        title: '绑定中....',
        mask: true
      })
      setUserBindProjectPoint(parmas)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            uni.$u.toast('绑定成功')
            // 清理预约就餐信息
            this.CLEAR_SELECT()
            //       this.SET_SELECT({
            //         key: 'take_meal_type',
            //         data: ''
            //       })
            //       this.SET_SELECT({
            //         key: 'org',
            //         data: {}
            //       })
            //       this.SET_SELECT({
            //         key: 'person',
            //         data: {}
            //       })
            // this.SET_SELECT({
            //   key: 'intent_org_id',
            //   data: ''
            // })
            this.queryUserinfo()
          } else if (res.code === 101) {
            this.createCardTipsShow = true
            this.createCardTips = res.msg
          } else if (res.code === 102) {
            this.phone = res.data.phone || ''
            this.changePhoneShow = true
            this.switchTips = res.msg
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    setCreateCardInfo() {
      let params = {
        company_id: this.coampanForm.company_id,
        person_no: this.coampanForm.person_no,
        name: this.coampanForm.name,
      }
      if (this.coampanForm.id_number) {
        params.id_number = encrypted(params.id_number)
      }
      // 如果有部门信息，则添加到请求参数
      if (this.coampanForm.register_input_department && this.coampanForm.departmentId) {
        params.card_department_group_id = this.coampanForm.departmentId
      }
      this.$showLoading({
        title: '创建中....',
        mask: true
      })
      apiCreateCardInfo(params)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            uni.$u.toast('创建成功')
            let memberCenterChannelId = Cache.get('memberCenterChannelId') || ''
            if (memberCenterChannelId) {
              this.$miRouter.replaceAll({
                path: `/pages_member/member_center/VIP_page`
              })
            } else {
              this.$miRouter.replaceAll({
                path: `/pages/index/index`
              })
            }
          } else if (res.code === 200) {
            // 200表示需要审批的情况
            this.createCardTipsShow = false
            this.showApprovePopup = true
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    // 创建新用户
    confirmCreateCardFunc() {
      this.setCreateCardInfo()
    },
    selectitem(index, item) {
      if (index >= 0) {
        this.companyName = item.company_name
        this.coampanForm.company_id = String(item.company_id)
        this.getUserSetting(item.company_id)
      } else {
        this.coampanForm.company_id = ''
        this.isShowIdCard = false
      }
    },
    // 选择部门
    selectDepartment(index, item) {
      if (index >= 0) {
        this.coampanForm.departmentId = String(item.id)
      } else {
        this.coampanForm.departmentId = ''
      }
    },
    changePhoneConfirm() {
      this.changePhoneShow = false
      this.$miRouter.push({
        path: '/pages_bundle/switch_items/switch_phone',
        query: {
          data: this.$encodeQuery({ companyName: this.companyName, ...this.coampanForm, phone: this.phone })
        }
      })
    },
    confirmAddItemsFunc() {
      let params = {
        person_no: this.coampanForm.person_no,
        name: this.coampanForm.name,
        company_id: this.coampanForm.company_id
      }
      if (!this.coampanForm.company_id) {
        return uni.$u.toast('请选择项目点')
      }
      if (!this.coampanForm.name) {
        return uni.$u.toast('请填写姓名')
      }
      if (!this.coampanForm.person_no) {
        return uni.$u.toast('请填写用户编号')
      }
      if (this.coampanForm.id_number && !uni.$u.test.idCard(this.coampanForm.id_number)) {
        return uni.$u.toast('身份证号错误')
      }
      // 如果部门字段显示且为必填，但用户未填写，则提示
      if (this.coampanForm.register_input_department && this.coampanForm.register_department_required && !this.coampanForm.departmentId) {
        return uni.$u.toast('请选择所属部门')
      }
      if (this.coampanForm.id_number) {
        params.id_number = encrypted(this.coampanForm.id_number)
      }
      // 如果有部门信息，则添加到请求参数
      if (this.coampanForm.register_input_department && this.coampanForm.departmentId) {
        params.card_department_group_id = this.coampanForm.departmentId
      }
      this.setUserProjectList(params)
      // setTimeout(() => {
      // 	this.$miRouter.back()
      // }, 500)
    },
    confirmHandle() {
      this.showApprovePopup = false
      this.$miRouter.replaceAll({
        path: `/pages/index/index`
      })
    },
    // 获取项目点设置
    async getUserSetting(companyId) {
      this.$showLoading({
        title: '加载中....',
        mask: true
      })
      this.isLoading = true
      const [err, res] = await this.$to(apiBookingApproveRegisterGetAllowRegisterPost({
        company_id: companyId
      }))
      uni.hideLoading()
      this.isLoading = false
      if (err) {
        uni.$u.toast(err.message)
        return
      }
      if (res && res.code === 0) {
        let data = res.data || {}
        this.isShowIdCard = data.allow_register_id_number
        // 获取部门字段是否必填
        this.coampanForm.register_input_department = data.register_input_department || false
        this.coampanForm.register_department_tips = data.register_department_tips || ''
        this.coampanForm.register_department_required = data.register_department_required || false
        // 如果需要显示部门字段，调用获取部门列表的接口
        if (this.coampanForm.register_input_department) {
          this.getDepartmentList(companyId)
        } else {
          // 不需要显示部门时，清空部门列表
          this.departmentOptions = []
        }
      } 
    },
    // 获取部门列表
    async getDepartmentList(companyId) {
      this.$showLoading({
        title: '获取部门...',
        mask: true
      })
      const [err, res] = await this.$to(apiBookingApproveRegisterGetDepartmentListPost({
        company_id: companyId
      }))
      uni.hideLoading()
      if (err) {
        uni.$u.toast(err.message)
        return
      }
      if (res && res.code === 0) {
        // 设置部门列表数据
        this.departmentOptions = res.data || []
      } else {
        uni.$u.toast(res.msg || '获取部门列表失败')
        this.departmentOptions = []
      }
    }
  },
  // Methods End
  onShow() {
    this.getUserProjectList()
  },
  // Life Cycle Start
  onLoad() {}
  // Life Cycle End
}
</script>

<style lang="scss">
.payment {
  .ls-card {
    border-radius: 20rpx;
  }

  .section {
    padding: 40rpx;
    margin-bottom: calc(100rpx + env(safe-area-inset-bottom));
    border-top: $border-base;

    .item {
      margin-bottom: 30rpx;
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 190rpx;
    }
  }
}
</style>
