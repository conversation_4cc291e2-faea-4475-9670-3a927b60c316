# u-calendar 组件新增 initialMonth 属性

## 功能说明

为 u-calendar 组件新增了 `initialMonth` 属性，用于设置日历打开时初始滚动到指定的月份，而不是默认滚动到 `minDate` 对应的月份。

## 属性说明

### initialMonth

- **类型**: `String | Number`
- **默认值**: `''`
- **说明**: 初始滚动到指定月份
- **格式支持**:
  - 字符串格式：`'2024-06'` 或 `'2024-06-15'`
  - 时间戳格式：`1717200000000`

## 使用示例

### 基础用法

```vue
<template>
    <u-calendar 
        :show="show" 
        @close="show = false"
        @confirm="onConfirm"
        :minDate="'2024-01-01'"
        :maxDate="'2024-12-31'"
        initialMonth="2024-06"
        title="选择日期"
    />
</template>

<script>
export default {
    data() {
        return {
            show: false
        }
    },
    methods: {
        onConfirm(date) {
            console.log('选择的日期:', date)
            this.show = false
        }
    }
}
</script>
```

### 使用时间戳格式

```vue
<template>
    <u-calendar 
        :show="show" 
        @close="show = false"
        @confirm="onConfirm"
        :minDate="minDate"
        :maxDate="maxDate"
        :initialMonth="targetTimestamp"
        title="选择日期"
    />
</template>

<script>
export default {
    data() {
        return {
            show: false,
            minDate: '2024-01-01',
            maxDate: '2024-12-31',
            targetTimestamp: new Date('2024-08-15').getTime()
        }
    },
    methods: {
        onConfirm(date) {
            console.log('选择的日期:', date)
            this.show = false
        }
    }
}
</script>
```

## 注意事项

1. `initialMonth` 指定的月份必须在 `minDate` 和 `maxDate` 的范围内，否则不会生效
2. 如果不设置 `initialMonth` 或设置为空字符串，则保持原有行为（滚动到 `minDate` 对应的月份）
3. 该属性支持动态修改，当 `initialMonth` 变化时会重新设置滚动位置

## 实现原理

1. 在 `props.js` 中新增 `initialMonth` 属性定义
2. 在 `selectedChange` 计算属性中添加对 `initialMonth` 的监听
3. 在 `setMonth` 方法中调用新增的 `setInitialScroll` 方法
4. `setInitialScroll` 方法解析 `initialMonth` 并设置 `scrollIntoView` 属性

## 兼容性

- 该功能向下兼容，不会影响现有代码
- 同时修改了 `src/pages_order/components/u-calendar/` 和 `src/uni_modules/uview-ui/components/u-calendar/` 两个版本
