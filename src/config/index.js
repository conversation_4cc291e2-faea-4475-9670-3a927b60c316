/*
 * @Author: jiuylq
 * @Date: 2023-01-04 19:08:28
 * @LastEditors: jiuylq
 * @LastEditTime: 2024-08-14 10:45:48
 * @Description: 请填写简介
 */
import Cache from '@/utils/cache'
import { getRunSceneSync } from '@/utils/util'
let production = process.env.VUE_APP_MINAPP
// #ifdef H5
production = window.location.origin
// #endif
console.log(process.env.VUE_APP_MINAPP, 666)
// 支付宝环境的话没法改同步。。。
// let envVersion = getRunSceneSync()
let envVersion = 'develop'

// #ifdef MP-WEIXIN
envVersion = uni.getAccountInfoSync().miniProgram.envVersion;
// #endif

console.log(1111, envVersion)

// #ifndef H5
switch (envVersion) {
  case "develop": // 开发版
    production = process.env.VUE_APP_MINAPP
    break;
  case "trial": // 体验版
    production = process.env.VUE_APP_MINAPP
    break;
  case "release": // 正式版
    production = process.env.VUE_APP_MINAPP
    break;
}
// #endif

// if(location.href.indexOf('debug') > -1){
// 	production = 'https://h5-v4.debug.packertec.com'//测试环境
// }else{
// 	production = "https://h5-v4.packertec.com"//正式环境
// }
// console.log(222, window.location.origin)
/** S API BaseURL **/
const baseURLMap = {
	// 开发环境
	// development: 'https://h5-v4.debug.packertec.com',
	development: process.env.VUE_APP_MINAPP,
	// 生产环境
	production: production,
	// production: 'http://**************:9000',
}
let baseURL = baseURLMap[process.env.NODE_ENV]

console.log(envVersion)
console.log(process.env.NODE_ENV)
console.log(baseURL)

if (process.env.NODE_ENV === 'development' || (location && location.href.indexOf('debug') > -1)) {
  const settingUrl = Cache.get('BASEURL')
  if (settingUrl) {
    baseURL = settingUrl
  }
}
let baseImgURL = 'https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/'
/** E API BaseURL **/

module.exports = {
  version: '1.0.0', // 版本号
  baseURL ,// API Base URL
  baseImgURL // 图片服务地址，一般不分测试正式，只有一个
}
