!function(e){"object"==typeof exports&&"object"==typeof module?module.exports=e():e()}(function(){try{var s,p={modules:{}},d=p._={};!function(){var M={function:!0,object:!0},q=M[typeof window]&&window||this;var e=q.JSON,t=q.JSON3,r=!1,a=function e(t,c){t=t||q.Object(),c=c||q.Object();var l=t.Number||q.Number,u=t.String||q.String,r=t.Object||q.Object,g=t.Date||q.Date,a=t.SyntaxError||q.SyntaxError,h=t.TypeError||q.TypeError,_=t.Math||q.Math,t=t.JSON||q.JSON;"object"==typeof t&&t&&(c.stringify=t.stringify,c.parse=t.parse);var m,r=r.prototype,v=r.toString,o=r.hasOwnProperty;function y(e,t){try{e()}catch(e){t&&t()}}var p,w,b,S,d,k,i,n,f,j,s,O,P,N,C,A,D,$,x,T,E,I,R,U,H,L=new g(-0xc782b5b800cec);function J(e){return null!=J[e]?J[e]:("bug-string-char-index"==e?t="a"!="a"[0]:"json"==e?t=J("json-stringify")&&J("date-serialization")&&J("json-parse"):"date-serialization"==e?(t=J("json-stringify")&&L)&&(i=c.stringify,y(function(){t='"-271821-04-20T00:00:00.000Z"'==i(new g(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==i(new g(864e13))&&'"-000001-01-01T00:00:00.000Z"'==i(new g(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==i(new g(-1))})):(a='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}',"json-stringify"==e&&((n="function"==typeof(i=c.stringify))&&((r=function(){return 1}).toJSON=r,y(function(){n="0"===i(0)&&"0"===i(new l)&&'""'==i(new u)&&i(v)===m&&i(m)===m&&i()===m&&"1"===i(r)&&"[1]"==i([r])&&"[null]"==i([m])&&"null"==i(null)&&"[null,null,null]"==i([m,v,null])&&i({a:[r,!0,!1,null,"\0\b\n\f\r\t"]})==a&&"1"===i(null,r)&&"[\n 1,\n 2\n]"==i([1,2],null,1)},function(){n=!1})),t=n),"json-parse"==e&&("function"==typeof(s=c.parse)&&y(function(){0!==s("0")||s(!1)||(r=s(a),(o=5==r.a.length&&1===r.a[0])&&(y(function(){o=!s('"\t"')}),o&&y(function(){o=1!==s("01")}),o&&y(function(){o=1!==s("1.")})))},function(){o=!1}),t=o)),J[e]=!!t);var t,r,a,i,n,s,o}function B(e){return j(this)}return y(function(){L=-109252==L.getUTCFullYear()&&0===L.getUTCMonth()&&1===L.getUTCDate()&&10==L.getUTCHours()&&37==L.getUTCMinutes()&&6==L.getUTCSeconds()&&708==L.getUTCMilliseconds()}),J["bug-string-char-index"]=J["date-serialization"]=J.json=J["json-stringify"]=J["json-parse"]=null,J("json")||(p="[object Function]",w="[object Number]",b="[object String]",S="[object Array]",d=J("bug-string-char-index"),k=function(e,t){var r,s,a,i=0;for(a in(r=function(){this.valueOf=0}).prototype.valueOf=0,s=new r)o.call(s,a)&&i++;return s=null,(k=i?function(e,t){var r,a,i=v.call(e)==p;for(r in e)i&&"prototype"==r||!o.call(e,r)||(a="constructor"===r)||t(r);(a||o.call(e,r="constructor"))&&t(r)}:(s=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],function(e,t){var r,a,i=v.call(e)==p,n=!i&&"function"!=typeof e.constructor&&M[typeof e.hasOwnProperty]&&e.hasOwnProperty||o;for(r in e)i&&"prototype"==r||!n.call(e,r)||t(r);for(a=s.length;r=s[--a];)n.call(e,r)&&t(r)}))(e,t)},J("json-stringify")||J("date-serialization")||(i={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},n="000000",f=function(e,t){return(n+(t||0)).slice(-e)},j=function(e){var t,r,a,i,n,s,o,c,l,u,p,d;return d=L?function(e){t=e.getUTCFullYear(),r=e.getUTCMonth(),a=e.getUTCDate(),n=e.getUTCHours(),s=e.getUTCMinutes(),o=e.getUTCSeconds(),c=e.getUTCMilliseconds()}:(l=_.floor,u=[0,31,59,90,120,151,181,212,243,273,304,334],p=function(e,t){return u[t]+365*(e-1970)+l((e-1969+(t=+(1<t)))/4)-l((e-1901+t)/100)+l((e-1601+t)/400)},function(e){for(a=l(e/864e5),t=l(a/365.2425)+1970-1;p(t+1,0)<=a;t++);for(r=l((a-p(t,0))/30.42);p(t,r+1)<=a;r++);a=1+a-p(t,r),n=l((i=(e%864e5+864e5)%864e5)/36e5)%24,s=l(i/6e4)%60,o=l(i/1e3)%60,c=i%1e3}),(j=function(e){return-1/0<e&&e<1/0?(d(e),e=(t<=0||1e4<=t?(t<0?"-":"+")+f(6,t<0?-t:t):f(4,t))+"-"+f(2,r+1)+"-"+f(2,a)+"T"+f(2,n)+":"+f(2,s)+":"+f(2,o)+"."+f(3,c)+"Z",t=r=a=n=s=o=c=null):e=null,e})(e)},J("json-stringify")&&!J("date-serialization")?(s=c.stringify,c.stringify=function(e,t,r){var a=g.prototype.toJSON;return g.prototype.toJSON=B,r=s(e,t,r),g.prototype.toJSON=a,r}):(O="\\u00",P=function(e){var t=e.charCodeAt(0);return(e=i[t])||O+f(2,t.toString(16))},N=/[\x00-\x1f\x22\x5c]/g,C=function(e){return N.lastIndex=0,'"'+(N.test(e)?e.replace(N,P):e)+'"'},A=function(e,t,r,a,i,n,s){var o,c,l,u,p,d,_,f;if(y(function(){o=t[e]}),"object"==typeof o&&o&&(o.getUTCFullYear&&"[object Date]"==v.call(o)&&o.toJSON===g.prototype.toJSON?o=j(o):"function"==typeof o.toJSON&&(o=o.toJSON(e))),(o=r?r.call(t,e,o):o)==m)return o===m?o:"null";switch((c="object"==(_=typeof o)?v.call(o):c)||_){case"boolean":case"[object Boolean]":return""+o;case"number":case w:return-1/0<o&&o<1/0?""+o:"null";case"string":case b:return C(""+o)}if("object"==typeof o){for(d=s.length;d--;)if(s[d]===o)throw h();if(s.push(o),l=[],_=n,n+=i,c==S){for(p=0,d=o.length;p<d;p++)u=A(p,o,r,a,i,n,s),l.push(u===m?"null":u);f=l.length?i?"[\n"+n+l.join(",\n"+n)+"\n"+_+"]":"["+l.join(",")+"]":"[]"}else k(a||o,function(e){var t=A(e,o,r,a,i,n,s);t!==m&&l.push(C(e)+":"+(i?" ":"")+t)}),f=l.length?i?"{\n"+n+l.join(",\n"+n)+"\n"+_+"}":"{"+l.join(",")+"}":"{}";return s.pop(),f}},c.stringify=function(e,t,r){var a,i,n;if(M[typeof t]&&t)if((n=v.call(t))==p)i=t;else if(n==S)for(var s,o={},c=0,l=t.length;c<l;)s=t[c++],"[object String]"!=(n=v.call(s))&&"[object Number]"!=n||(o[s]=1);if(r)if((n=v.call(r))==w){if(0<(r-=r%1))for(10<r&&(r=10),a="";a.length<r;)a+=" "}else n==b&&(a=r.length<=10?r:r.slice(0,10));return A("",((s={})[""]=e,s),i,o,a,"",[])})),J("json-parse")||(D=u.fromCharCode,$={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},E=function(){throw x=T=null,a()},I=function(){for(var e,t,r,a,i,n=T,s=n.length;x<s;)switch(i=n.charCodeAt(x)){case 9:case 10:case 13:case 32:x++;break;case 123:case 125:case 91:case 93:case 58:case 44:return e=d?n.charAt(x):n[x],x++,e;case 34:for(e="@",x++;x<s;)if((i=n.charCodeAt(x))<32)E();else if(92==i)switch(i=n.charCodeAt(++x)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:e+=$[i],x++;break;case 117:for(t=++x,r=x+4;x<r;x++)48<=(i=n.charCodeAt(x))&&i<=57||97<=i&&i<=102||65<=i&&i<=70||E();e+=D("0x"+n.slice(t,x));break;default:E()}else{if(34==i)break;for(i=n.charCodeAt(x),t=x;32<=i&&92!=i&&34!=i;)i=n.charCodeAt(++x);e+=n.slice(t,x)}if(34==n.charCodeAt(x))return x++,e;E();default:if(t=x,45==i&&(a=!0,i=n.charCodeAt(++x)),48<=i&&i<=57){for(48==i&&48<=(i=n.charCodeAt(x+1))&&i<=57&&E(),a=!1;x<s&&48<=(i=n.charCodeAt(x))&&i<=57;x++);if(46==n.charCodeAt(x)){for(r=++x;r<s&&!((i=n.charCodeAt(r))<48||57<i);r++);r==x&&E(),x=r}if(101==(i=n.charCodeAt(x))||69==i){for(43!=(i=n.charCodeAt(++x))&&45!=i||x++,r=x;r<s&&!((i=n.charCodeAt(r))<48||57<i);r++);r==x&&E(),x=r}return+n.slice(t,x)}a&&E();var o=n.slice(x,x+4);if("true"==o)return x+=4,!0;if("fals"==o&&101==n.charCodeAt(x+4))return x+=5,!1;if("null"==o)return x+=4,null;E()}return"$"},R=function(e){var t,r;if("$"==e&&E(),"string"==typeof e){if("@"==(d?e.charAt(0):e[0]))return e.slice(1);if("["==e){for(t=[];"]"!=(e=I());)r?","==e&&"]"!=(e=I())||E():r=!0,","==e&&E(),t.push(R(e));return t}if("{"==e){for(t={};"}"!=(e=I());)r?","==e&&"}"!=(e=I())||E():r=!0,","!=e&&"string"==typeof e&&"@"==(d?e.charAt(0):e[0])&&":"==I()||E(),t[e.slice(1)]=R(I());return t}E()}return e},U=function(e,t,r){(r=H(e,t,r))===m?delete e[t]:e[t]=r},H=function(e,t,r){var a,i=e[t];if("object"==typeof i&&i)if(v.call(i)==S)for(a=i.length;a--;)U(v,k,i);else k(i,function(e){U(i,e,r)});return r.call(e,t,i)},c.parse=function(e,t){var r;return x=0,T=""+e,r=R(I()),"$"!=I()&&E(),x=T=null,t&&v.call(t)==p?H(((e={})[""]=r,e),"",t):r})),c.runInContext=e,c}(q,q.JSON3={noConflict:function(){return r||(r=!0,q.JSON=e,q.JSON3=t,e=t=null),a}});q.JSON={parse:a.parse,stringify:a.stringify}}.call(this),function(t){if(t.atob)try{t.atob(" ")}catch(e){t.atob=(a=t.atob,r.original=a,r)}else{var o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/;t.btoa=function(e){for(var t,r,a,i="",n=0,s=(e=String(e)).length%3;n<e.length;)(255<(t=e.charCodeAt(n++))||255<(r=e.charCodeAt(n++))||255<(a=e.charCodeAt(n++)))&&p.log("Failed to execute 'btoa' on 'Window': The string to be encoded contains characters outside of the Latin1 range."),i+=o.charAt((t=t<<16|r<<8|a)>>18&63)+o.charAt(t>>12&63)+o.charAt(t>>6&63)+o.charAt(63&t);return s?i.slice(0,s-3)+"===".substring(s):i},t.atob=function(e){e=String(e).replace(/[\t\n\f\r ]+/g,""),s.test(e)||p.log("Failed to execute 'atob' on 'Window': The string to be decoded is not correctly encoded."),e+="==".slice(2-(3&e.length));for(var t,r,a,i="",n=0;n<e.length;)t=o.indexOf(e.charAt(n++))<<18|o.indexOf(e.charAt(n++))<<12|(r=o.indexOf(e.charAt(n++)))<<6|(a=o.indexOf(e.charAt(n++))),i+=64===r?String.fromCharCode(t>>16&255):64===a?String.fromCharCode(t>>16&255,t>>8&255):String.fromCharCode(t>>16&255,t>>8&255,255&t);return i}}function r(e){return a(String(e).replace(/[\t\n\f\r ]+/g,""))}var a}(window),String.prototype.replaceAll||(String.prototype.replaceAll=function(e,t){return"[object regexp]"===Object.prototype.toString.call(e).toLowerCase()?this.replace(e,t):this.replace(new RegExp(e,"g"),t)}),v=Array.prototype,y=Function.prototype,w=Object.prototype,b=v.slice,S=w.toString,k=w.hasOwnProperty,y.bind,j=v.forEach,v.indexOf,v=Array.isArray,O={},P=d.each=function(e,t,r){if(null==e)return!1;if(j&&e.forEach===j)e.forEach(t,r);else if(d.isArray(e)&&e.length===+e.length){for(var a=0,i=e.length;a<i;a++)if(a in e&&t.call(r,e[a],a,e)===O)return!1}else for(var n in e)if(k.call(e,n)&&t.call(r,e[n],n,e)===O)return!1},d.map=function(e,a){var i=[];return null==e?i:Array.prototype.map&&e.map===Array.prototype.map?e.map(a):(P(e,function(e,t,r){i.push(a(e,t,r))}),i)},d.extend=function(r){return P(b.call(arguments,1),function(e){for(var t in e)k.call(e,t)&&void 0!==e[t]&&(r[t]=e[t])}),r},d.extend2Lev=function(r){return P(b.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(d.isObject(e[t])&&d.isObject(r[t])?d.extend(r[t],e[t]):r[t]=e[t])}),r},d.coverExtend=function(r){return P(b.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&void 0===r[t]&&(r[t]=e[t])}),r},d.isArray=v||function(e){return"[object Array]"===S.call(e)},d.isFunction=function(e){if(!e)return!1;e=Object.prototype.toString.call(e);return"[object Function]"==e||"[object AsyncFunction]"==e},d.isArguments=function(e){return!(!e||!k.call(e,"callee"))},d.toArray=function(e){return e?e.toArray?e.toArray():d.isArray(e)||d.isArguments(e)?b.call(e):d.values(e):[]},d.values=function(e){var t=[];return null==e||P(e,function(e){t[t.length]=e}),t},d.indexOf=function(e,t){var r=e.indexOf;if(r)return r.call(e,t);for(var a=0;a<e.length;a++)if(t===e[a])return a;return-1},d.hasAttributes=function(e,t){if("string"==typeof t)return d.hasAttribute(e,t);if(d.isArray(t)){for(var r=!1,a=0;a<t.length;a++)if(d.hasAttribute(e,t[a])){r=!0;break}return r}},d.hasAttribute=function(e,t){return e.hasAttribute?e.hasAttribute(t):!(!e.attributes[t]||!e.attributes[t].specified)},d.filter=function(e,t,r){var a=Object.prototype.hasOwnProperty;if(e.filter)return e.filter(t);for(var i,n=[],s=0;s<e.length;s++)a.call(e,s)&&(i=e[s],t.call(r,i,s,e)&&n.push(i));return n},d.inherit=function(e,t){return e.prototype=new t,(e.prototype.constructor=e).superclass=t.prototype,e},d.trim=function(e){return e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")},d.isObject=function(e){return null!=e&&"[object Object]"==S.call(e)},d.isEmptyObject=function(e){if(d.isObject(e)){for(var t in e)if(k.call(e,t))return!1;return!0}return!1},d.isUndefined=function(e){return void 0===e},d.isString=function(e){return"[object String]"==S.call(e)},d.isDate=function(e){return"[object Date]"==S.call(e)},d.isBoolean=function(e){return"[object Boolean]"==S.call(e)},d.isNumber=function(e){return"[object Number]"==S.call(e)&&/[\d\.]+/.test(String(e))},d.isElement=function(e){return!(!e||1!==e.nodeType)},d.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},d.safeJSONParse=function(e){var t=null;try{t=JSON.parse(e)}catch(e){return!1}return t},d.decodeURIComponent=function(t){var r=t;try{r=decodeURIComponent(t)}catch(e){r=t}return r},d.decodeURI=function(t){var r=t;try{r=decodeURI(t)}catch(e){r=t}return r},d.isDecodeURI=function(e,t){return e?d.decodeURI(t):t},d.encodeDates=function(r){return d.each(r,function(e,t){d.isDate(e)?r[t]=d.formatDate(e):d.isObject(e)&&(r[t]=d.encodeDates(e))}),r},d.mediaQueriesSupported=function(){return void 0!==window.matchMedia||void 0!==window.msMatchMedia},d.getScreenOrientation=function(){var e=screen.msOrientation||screen.mozOrientation||(screen.orientation||{}).type,t="未取到值";return e?t=-1<e.indexOf("landscape")?"landscape":"portrait":d.mediaQueriesSupported()&&((e=window.matchMedia||window.msMatchMedia)("(orientation: landscape)").matches?t="landscape":e("(orientation: portrait)").matches&&(t="portrait")),t},d.now=Date.now||function(){return(new Date).getTime()},d.throttle=function(r,a,i){var n,s,o,c=null,l=0;i=i||{};function u(){l=!1===i.leading?0:d.now(),c=null,o=r.apply(n,s),c||(n=s=null)}return function(){var e=d.now();l||!1!==i.leading||(l=e);var t=a-(e-l);return n=this,s=arguments,t<=0||a<t?(c&&(clearTimeout(c),c=null),l=e,o=r.apply(n,s),c||(n=s=null)):c||!1===i.trailing||(c=setTimeout(u,t)),o}},d.hashCode=function(e){if("string"!=typeof e)return 0;var t=0;if(0==e.length)return t;for(var r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t&=t;return t},d.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+t(e.getMilliseconds())},d.getRandomBasic=(f=(new Date).getTime(),function(e){return Math.ceil((f=(9301*f+49297)%233280)/233280*e)}),d.getRandom=function(){if("function"==typeof Uint32Array){var e="";if("undefined"!=typeof crypto?e=crypto:"undefined"!=typeof msCrypto&&(e=msCrypto),d.isObject(e)&&e.getRandomValues){var t=new Uint32Array(1);return e.getRandomValues(t)[0]/Math.pow(2,32)}}return d.getRandomBasic(1e19)/1e19},d.searchObjDate=function(r){d.isObject(r)&&d.each(r,function(e,t){d.isObject(e)?d.searchObjDate(r[t]):d.isDate(e)&&(r[t]=d.formatDate(e))})},d.searchZZAppStyle=function(e){void 0!==e.properties.$project&&(e.project=e.properties.$project,delete e.properties.$project),void 0!==e.properties.$token&&(e.token=e.properties.$token,delete e.properties.$token)},d.formatJsonString=function(t){try{return JSON.stringify(t,null,"  ")}catch(e){return JSON.stringify(t)}},d.formatString=function(e,t){return t=/(^[1-9]\d*$)/.test(t)?t<3e3?t:3e3:500,d.isNumber(t)&&e.length>t?(p.log("字符串长度超过限制，已经做截取--"+e),e.slice(0,t)):e},d.searchObjString=function(r){var a=["$element_selector","$element_path"];d.isObject(r)&&d.each(r,function(e,t){d.isObject(e)?d.searchObjString(r[t]):d.isString(e)&&(r[t]=d.formatString(e,-1<d.indexOf(a,t)?1024:p.para.max_string_length))})},d.parseSuperProperties=function(e){var r=e.properties,a=JSON.parse(JSON.stringify(e));d.isObject(r)&&(d.each(r,function(e,t){if(d.isFunction(e))try{r[t]=e(a),d.isFunction(r[t])&&(p.log("您的属性- "+t+" 格式不满足要求，我们已经将其删除"),delete r[t])}catch(e){delete r[t],p.log("您的属性- "+t+" 抛出了异常，我们已经将其删除")}}),d.strip_sa_properties(r))},d.filterReservedProperties=function(r){d.isObject(r)&&d.each(["distinct_id","user_id","id","date","datetime","event","events","first_id","original_id","device_id","properties","second_id","time","users"],function(e,t){e in r&&(t<3?(delete r[e],p.log("您的属性- "+e+"是保留字段，我们已经将其删除")):p.log("您的属性- "+e+"是保留字段，请避免其作为属性名"))})},d.searchConfigData=function(e){if("object"==typeof e&&e.$option){var t=e.$option;return delete e.$option,t}return{}},d.unique=function(e){for(var t,r=[],a={},i=0;i<e.length;i++)(t=e[i])in a||(a[t]=!0,r.push(t));return r},d.strip_sa_properties=function(e){return d.isObject(e)&&d.each(e,function(t,r){var a;d.isArray(t)&&(a=[],d.each(t,function(e){d.isString(e)?a.push(e):p.log("您的数据-",r,t,"的数组里的值必须是字符串,已经将其删除")}),e[r]=a),d.isString(t)||d.isNumber(t)||d.isDate(t)||d.isBoolean(t)||d.isArray(t)||d.isFunction(t)||"$option"===r||(p.log("您的数据-",r,t,"-格式不满足要求，我们已经将其删除"),delete e[r])}),e},d.strip_empty_properties=function(e){var r={};return d.each(e,function(e,t){null!=e&&(r[t]=e)}),r},d.base64Encode=function(e){return btoa(encodeURIComponent(e).replace(/%([0-9A-F]{2})/g,function(e,t){return String.fromCharCode("0x"+t)}))},d.base64Decode=function(e){e=d.map(atob(e).split(""),function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)});return decodeURIComponent(e.join(""))},d.UUID=function(){var e=(e=String(screen.height*screen.width))&&/\d{5,}/.test(e)?e.toString(16):String(31242*d.getRandom()).replace(".","").slice(0,8),e=_()+"-"+d.getRandom().toString(16).replace(".","")+"-"+function(){var e,t,r=navigator.userAgent,i=[],a=0;function n(e,t){for(var r=0,a=0;a<t.length;a++)r|=i[a]<<8*a;return e^r}for(e=0;e<r.length;e++)t=r.charCodeAt(e),i.unshift(255&t),4<=i.length&&(a=n(a,i),i=[]);return(a=0<i.length?n(a,i):a).toString(16)}()+"-"+e+"-"+_();return e||(String(d.getRandom())+String(d.getRandom())+String(d.getRandom())).slice(2,15)},d.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=d.decodeURIComponent(e);e=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===e||e&&"string"!=typeof e[1]&&e[1].length?"":d.decodeURIComponent(e[1])},d.urlParse=function(e){function t(e){this._fields={Username:4,Password:5,Port:7,Protocol:2,Host:6,Path:8,URL:0,QueryString:9,Fragment:10},this._values={},this._regex=null,this._regex=/^((\w+):\/\/)?((\w+):?(\w+)?@)?([^\/\?:]+):?(\d+)?(\/?[^\?#]+)?\??([^#]+)?#?(\w*)/,void 0!==e&&this._parse(e)}return t.prototype.setUrl=function(e){this._parse(e)},t.prototype._initValues=function(){for(var e in this._fields)this._values[e]=""},t.prototype.addQueryString=function(e){if("object"!=typeof e)return!1;var t,r=this._values.QueryString||"";for(t in e)r=new RegExp(t+"[^&]+").test(r)?r.replace(new RegExp(t+"[^&]+"),t+"="+e[t]):"&"===r.slice(-1)?r+t+"="+e[t]:""===r?t+"="+e[t]:r+"&"+t+"="+e[t];this._values.QueryString=r},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:"",e+=this._values.Fragment?"#"+this._values.Fragment:""},t.prototype.getUrl=function(){var e="";return e+=this._values.Origin,e+=this._values.Port?":"+this._values.Port:"",e+=this._values.Path,e+=this._values.QueryString?"?"+this._values.QueryString:""},t.prototype._parse=function(e){this._initValues();var t,r=this._regex.exec(e);for(t in r||p.log("DPURLParser::_parse -> Invalid URL"),this._fields)void 0!==r[this._fields[t]]&&(this._values[t]=r[this._fields[t]]);this._values.Hostname=this._values.Host.replace(/:\d+$/,""),this._values.Origin=this._values.Protocol+"://"+this._values.Hostname},new t(e)},d.addEvent=function(){function o(e){return e&&(e.preventDefault=o.preventDefault,e.stopPropagation=o.stopPropagation,e._getPath=o._getPath),e}o._getPath=function(){var r=this;return this.path||this.composedPath&&this.composedPath()||function(){try{var e=r.target,t=[e];if(null===e||null===e.parentElement)return[];for(;null!==e.parentElement;)e=e.parentElement,t.unshift(e);return t}catch(e){return[]}}()},o.preventDefault=function(){this.returnValue=!1},o.stopPropagation=function(){this.cancelBubble=!0};!function(e,t,r){var a,i,n,s=!(!d.isObject(p.para.heatmap)||!p.para.heatmap.useCapture);d.isObject(p.para.heatmap)&&void 0===p.para.heatmap.useCapture&&"click"===t&&(s=!0),e&&e.addEventListener?e.addEventListener(t,function(e){e._getPath=o._getPath,r.call(this,e)},s):(t=e[s="on"+t],e[s]=(a=e,i=r,n=t,function(e){if(e=e||o(window.event)){e.target=e.srcElement;var t,r=!0;return"function"==typeof n&&(t=n(e)),e=i.call(a,e),r=!1===t||!1===e?!1:r}}))}.apply(null,arguments)},d.addHashEvent=function(e){var t="pushState"in window.history?"popstate":"hashchange";d.addEvent(window,t,e)},d.addSinglePageEvent=function(e){var t,r=location.href,a=window.history.pushState,i=window.history.replaceState;d.isFunction(window.history.pushState)&&(window.history.pushState=function(){a.apply(window.history,arguments),e(r),r=location.href}),d.isFunction(window.history.replaceState)&&(window.history.replaceState=function(){i.apply(window.history,arguments),e(r),r=location.href}),t=!window.document.documentMode&&a?"popstate":"hashchange",d.addEvent(window,t,function(){e(r),r=location.href})},d.cookie={get:function(e){for(var t=e+"=",r=document.cookie.split(";"),a=0;a<r.length;a++){for(var i=r[a];" "==i.charAt(0);)i=i.substring(1,i.length);if(0==i.indexOf(t))return d.decodeURIComponent(i.substring(t.length,i.length))}return null},set:function(e,t,r,a){var i="",n="",s="",o="";function c(e){return!!e&&e.replaceAll(/\r\n/g,"")}r=null==r?73e3:r,(a=void 0===a?p.para.cross_subdomain:a)&&(i=(l="url解析失败"===(l=d.getCurrentDomain(location.href))?"":l)?"; domain="+l:""),0!==r&&(u=new Date,"s"===String(r).slice(-1)?u.setTime(u.getTime()+1e3*Number(String(r).slice(0,-1))):u.setTime(u.getTime()+24*r*60*60*1e3),n="; expires="+u.toGMTString()),d.isString(p.para.set_cookie_samesite)&&""!==p.para.set_cookie_samesite&&(o="; SameSite="+p.para.set_cookie_samesite),p.para.is_secure_cookie&&(s="; secure");var l="",r="",u="";e&&(l=c(e)),t&&(r=c(t)),i&&(u=c(i)),l&&r&&(document.cookie=l+"="+encodeURIComponent(r)+n+"; path=/"+u+o+s)},encrypt:function(e){return"data:enc;"+d.rot13obfs(e)},decrypt:function(e){return e=e.substring("data:enc;".length),e=d.rot13defs(e)},resolveValue:function(e){return e=d.isString(e)&&0===e.indexOf("data:enc;")?d.cookie.decrypt(e):e},remove:function(e,t){t=void 0===t?p.para.cross_subdomain:t,d.cookie.set(e,"",-1,t)},getCookieName:function(e,t){var r="";if(t=t||location.href,!1===p.para.cross_subdomain){try{r=d.URL(t).hostname}catch(e){p.log(e)}r="string"==typeof r&&""!==r?"sajssdk_2015_"+e+"_"+r.replace(/\./g,"_"):"sajssdk_2015_root_"+e}else r="sajssdk_2015_cross_"+e;return r},getNewUser:function(){return null!==this.get("sensorsdata_is_new_user")||null!==this.get(this.getCookieName("new_user"))}},d.getElementContent=function(e,t){var r="",a="";return e.textContent?r=d.trim(e.textContent):e.innerText&&(r=d.trim(e.innerText)),a=(r=r&&r.replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255))||"","input"!==t&&"INPUT"!==t||("button"===e.type||"submit"===e.type||p.para.heatmap&&"function"==typeof p.para.heatmap.collect_input&&p.para.heatmap.collect_input(e))&&(a=e.value||""),a},d.getEleInfo=function(e){if(!e.target)return!1;var t=e.target,r=t.tagName.toLowerCase(),e={};return e.$element_type=r,e.$element_name=t.getAttribute("name"),e.$element_id=t.getAttribute("id"),e.$element_class_name="string"==typeof t.className?t.className:null,e.$element_target_url=t.getAttribute("href"),e.$element_content=d.getElementContent(t,r),(e=d.strip_empty_properties(e)).$url=d.isDecodeURI(p.para.url_is_decode,location.href),e.$url_path=location.pathname,e.$title=s||document.title,e.$viewport_width=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0,e},d.localStorage={get:function(e){return window.localStorage.getItem(e)},parse:function(e){var t;try{t=JSON.parse(d.localStorage.get(e))||null}catch(e){p.log(e)}return t},set:function(e,t){window.localStorage.setItem(e,t)},remove:function(e){window.localStorage.removeItem(e)},isSupport:function(){var t=!0;try{var e="__sensorsdatasupport__",r="testIsSupportStorage";d.localStorage.set(e,r),d.localStorage.get(e)!==r&&(t=!1),d.localStorage.remove(e)}catch(e){t=!1}return t}},d.sessionStorage={isSupport:function(){var t=!0,e="__sensorsdatasupport__",r="testIsSupportStorage";try{t=!(!sessionStorage||!sessionStorage.setItem)&&(sessionStorage.setItem(e,r),sessionStorage.removeItem(e,r),!0)}catch(e){t=!1}return t}},d.isSupportCors=function(){return void 0!==window.XMLHttpRequest&&("withCredentials"in new XMLHttpRequest||"undefined"!=typeof XDomainRequest)},d.xhr=function(e){if(e)return void 0!==window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest?new XMLHttpRequest:"undefined"!=typeof XDomainRequest?new XDomainRequest:null;if(void 0!==window.XMLHttpRequest)return new XMLHttpRequest;if(window.ActiveXObject)try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(e){try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){p.log(e)}}},d.ajax=function(e){function t(e){if(!e)return"";try{return JSON.parse(e)}catch(e){return{}}}e.timeout=e.timeout||2e4,e.credentials=void 0===e.credentials||e.credentials;var r=d.xhr(e.cors);if(!r)return!1;e.type||(e.type=e.data?"POST":"GET"),e=d.extend({success:function(){},error:function(){}},e),p.debug.protocol.ajax(e.url);var a,i=e.success,n=e.error;e.success=function(e){i(e),a&&(clearTimeout(a),a=null)},e.error=function(e){n(e),a&&(clearTimeout(a),a=null)},a=setTimeout(function(){!function(){try{d.isObject(r)&&r.abort&&r.abort()}catch(e){p.log(e)}a&&(clearTimeout(a),a=null,e.error&&e.error(),r.onreadystatechange=null,r.onload=null,r.onerror=null)}()},e.timeout),"undefined"!=typeof XDomainRequest&&r instanceof XDomainRequest&&(r.onload=function(){e.success&&e.success(t(r.responseText)),r.onreadystatechange=null,r.onload=null,r.onerror=null},r.onerror=function(){e.error&&e.error(t(r.responseText),r.status),r.onreadystatechange=null,r.onerror=null,r.onload=null}),r.onreadystatechange=function(){try{4==r.readyState&&(200<=r.status&&r.status<300||304==r.status?e.success(t(r.responseText)):e.error(t(r.responseText),r.status),r.onreadystatechange=null,r.onload=null)}catch(e){r.onreadystatechange=null,r.onload=null}},r.open(e.type,e.url,!0);try{e.credentials&&(r.withCredentials=!0),d.isObject(e.header)&&d.each(e.header,function(e,t){r.setRequestHeader&&r.setRequestHeader(t,e)}),e.data&&(e.cors||r.setRequestHeader&&r.setRequestHeader("X-Requested-With","XMLHttpRequest"),"application/json"===e.contentType?r.setRequestHeader&&r.setRequestHeader("Content-type","application/json; charset=UTF-8"):r.setRequestHeader&&r.setRequestHeader("Content-type","application/x-www-form-urlencoded"))}catch(e){p.log(e)}r.send(e.data||null)},d.loadScript=function(e){e=d.extend({success:function(){},error:function(){},appendCall:function(e){document.getElementsByTagName("head")[0].appendChild(e)}},e);var t=null;"css"===e.type&&((t=document.createElement("link")).rel="stylesheet",t.href=e.url),"js"===e.type&&((t=document.createElement("script")).async="async",t.setAttribute("charset","UTF-8"),t.src=e.url,t.type="text/javascript"),t.onload=t.onreadystatechange=function(){this.readyState&&"loaded"!==this.readyState&&"complete"!==this.readyState||(e.success(),t.onload=t.onreadystatechange=null)},t.onerror=function(){e.error(),t.onerror=null},e.appendCall(t)},d.getHostname=function(e,t){t&&"string"==typeof t||(t="hostname解析异常");var r=null;try{r=d.URL(e).hostname}catch(e){p.log("getHostname传入的url参数不合法！")}return r||t},d.getQueryParamsFromUrl=function(e){var t={},e=e.split("?")[1]||"";return t=e?d.getURLSearchParams("?"+e):t},d.getURLSearchParams=function(e){function t(e){return decodeURIComponent(e)}for(var r={},a=(e=e||"").substring(1).split("&"),i=0;i<a.length;i++){var n,s=a[i].indexOf("=");-1!==s&&(n=a[i].substring(0,s),s=a[i].substring(s+1),n=t(n),s=t(s),r[n]=s)}return r},d.URL=function(e){var t,r,a={};return"function"==typeof window.URL&&function(){try{return"http://modernizr.com/"===new URL("http://modernizr.com/").href}catch(e){return!1}}()?(a=new URL(e)).searchParams||(a.searchParams=(r=d.getURLSearchParams(a.search),{get:function(e){return r[e]}})):(!1===/^https?:\/\/.+/.test(e)&&p.log("Invalid URL"),e=d.urlParse(e),a.hash="",a.host=e._values.Host?e._values.Host+(e._values.Port?":"+e._values.Port:""):"",a.href=e._values.URL,a.password=e._values.Password,a.pathname=e._values.Path,a.port=e._values.Port,a.search=e._values.QueryString?"?"+e._values.QueryString:"",a.username=e._values.Username,a.hostname=e._values.Hostname,a.protocol=e._values.Protocol?e._values.Protocol+":":"",a.origin=e._values.Origin?e._values.Origin+(e._values.Port?":"+e._values.Port:""):"",a.searchParams=(t=d.getURLSearchParams("?"+e._values.QueryString),{get:function(e){return t[e]}})),a},d.getCurrentDomain=function(e){var t=p.para.current_domain;switch(typeof t){case"function":var r=t();return""!==r&&""!==d.trim(r)&&-1!==r.indexOf(".")?r:"url解析失败";case"string":return""!==t&&""!==d.trim(t)&&-1!==t.indexOf(".")?t:"url解析失败";default:r=d.getCookieTopLevelDomain();return""===e||""===r?"url解析失败":r}},d.getCookieTopLevelDomain=function(e){e=e||location.hostname;var t=e||!1;if(!t)return"";var r=t.split(".");if(d.isArray(r)&&2<=r.length&&!/^(\d+\.)+\d+$/.test(t))for(var a="."+r.splice(r.length-1,1);0<r.length;)if(a="."+r.splice(r.length-1,1)+a,document.cookie="sensorsdata_domain_test=true; path=/; domain="+a,-1!==document.cookie.indexOf("sensorsdata_domain_test=true")){var i=new Date;return i.setTime(i.getTime()-1e3),document.cookie="sensorsdata_domain_test=true; expires="+i.toGMTString()+"; path=/; domain="+a,a}return""},d.isBaiduTraffic=function(){var e=document.referrer,t="baidu.com";if(!e)return!1;try{var r=d.URL(e).hostname;return r&&r.substring(r.length-t.length)===t}catch(e){return!1}return!1},d.getReferrerEqid=function(){var e=d.getQueryParamsFromUrl(document.referrer);return d.isEmptyObject(e)||!e.eqid?d.UUID().replace(/-/g,""):e.eqid},d.getReferrerEqidType=function(){var e=d.getQueryParamsFromUrl(document.referrer);return d.isEmptyObject(e)||!e.eqid?"baidu_sem_keyword_id":"baidu_seo_keyword_id"},d.getBaiduKeyword={data:{},id:function(){return this.data.id||(this.data.id=d.getReferrerEqid(),this.data.id)},type:function(){return this.data.type||(this.data.type=d.getReferrerEqidType(),this.data.type)}},d.isReferralTraffic=function(e){return""===(e=e||document.referrer)||d.getCookieTopLevelDomain(d.getHostname(e))!==d.getCookieTopLevelDomain()},d.ry=function(e){return new d.ry.init(e)},d.ry.init=function(e){this.ele=e},d.ry.init.prototype={addClass:function(e){return-1===(" "+this.ele.className+" ").indexOf(" "+e+" ")&&(this.ele.className=this.ele.className+(""===this.ele.className?"":" ")+e),this},removeClass:function(e){var t=" "+this.ele.className+" ";return-1!==t.indexOf(" "+e+" ")&&(this.ele.className=t.replace(" "+e+" "," ").slice(1,-1)),this},hasClass:function(e){return-1!==(" "+this.ele.className+" ").indexOf(" "+e+" ")},attr:function(e,t){return"string"==typeof e&&d.isUndefined(t)?this.ele.getAttribute(e):("string"==typeof e&&(t=String(t),this.ele.setAttribute(e,t)),this)},offset:function(){var e=this.ele.getBoundingClientRect();if(e.width||e.height){var t=this.ele.ownerDocument.documentElement;return{top:e.top+window.pageYOffset-t.clientTop,left:e.left+window.pageXOffset-t.clientLeft}}return{top:0,left:0}},getSize:function(){if(!window.getComputedStyle)return{width:this.ele.offsetWidth,height:this.ele.offsetHeight};try{var e=this.ele.getBoundingClientRect();return{width:e.width,height:e.height}}catch(e){return{width:0,height:0}}},getStyle:function(e){return this.ele.currentStyle?this.ele.currentStyle[e]:this.ele.ownerDocument.defaultView.getComputedStyle(this.ele,null).getPropertyValue(e)},wrap:function(e){e=document.createElement(e);return this.ele.parentNode.insertBefore(e,this.ele),e.appendChild(this.ele),d.ry(e)},getCssStyle:function(e){var t=this.ele.style.getPropertyValue(e);if(t)return t;var r=null;if(!(r="function"==typeof window.getMatchedCSSRules?getMatchedCSSRules(this.ele):r)||!d.isArray(r))return null;for(var a=r.length-1;0<=a;a--)if(t=r[a].style.getPropertyValue(e))return t},sibling:function(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e},next:function(){return this.sibling(this.ele,"nextSibling")},prev:function(e){return this.sibling(this.ele,"previousSibling")},siblings:function(e){return this.siblings((this.ele.parentNode||{}).firstChild,this.ele)},children:function(e){return this.siblings(this.ele.firstChild)},parent:function(){var e=(e=this.ele.parentNode)&&11!==e.nodeType?e:null;return d.ry(e)},previousElementSibling:function(){var e=this.ele;if("previousElementSibling"in document.documentElement)return d.ry(e.previousElementSibling);for(;e=e.previousSibling;)if(1===e.nodeType)return d.ry(e);return d.ry(null)},getSameTypeSiblings:function(){for(var e=this.ele,t=e.parentNode,r=e.tagName.toLowerCase(),a=[],i=0;i<t.children.length;i++){var n=t.children[i];1===n.nodeType&&n.tagName.toLowerCase()===r&&a.push(t.children[i])}return a}},d.strToUnicode=function(e){if("string"!=typeof e)return p.log("转换unicode错误",e),e;for(var t="",r=0;r<e.length;r++)t+="\\"+e.charCodeAt(r).toString(16);return t},d.getReferrer=function(e){return"string"!=typeof(e=e||document.referrer)?"取值异常_referrer异常_"+String(e):"string"==typeof(e=(e=0===e.indexOf("https://www.baidu.com/")?e.split("?")[0]:e).slice(0,p.para.max_referrer_string_length))?e:""},d.getKeywordFromReferrer=function(e,t){e=e||document.referrer;var r=p.para.source_type.keyword;if(document&&"string"==typeof e){if(0!==e.indexOf("http"))return""===e?"未取到值_直接打开":"未取到值_非http的url";var a=d.getReferSearchEngine(e),i=d.getQueryParamsFromUrl(e);if(d.isEmptyObject(i))return p.para.preset_properties.search_keyword_baidu&&d.isBaiduTraffic()?void 0:"未取到值";var n;for(s in r)if(a===s&&"object"==typeof i)if(n=r[s],d.isArray(n))for(var s=0;s<n.length;s++){var o=i[n[s]];if(o)return t?{active:o}:o}else if(i[n])return t?{active:i[n]}:i[n];return p.para.preset_properties.search_keyword_baidu&&d.isBaiduTraffic()?void 0:"未取到值"}return"取值异常_referrer异常_"+String(e)},d.getWxAdIdFromUrl=function(e){var t=d.getQueryParam(e,"gdt_vid"),r=d.getQueryParam(e,"hash_key"),a=d.getQueryParam(e,"callbacks"),e={click_id:"",hash_key:"",callbacks:""};return d.isString(t)&&t.length&&(e.click_id=16==t.length||18==t.length?t:"参数解析不合法",d.isString(r)&&r.length&&(e.hash_key=r),d.isString(a)&&a.length&&(e.callbacks=a)),e},d.getReferSearchEngine=function(e){var t=d.getHostname(e);if(!t||"hostname解析异常"===t)return"";p.para.source_type.keyword;var r,a={baidu:[/^.*\.baidu\.com$/],bing:[/^.*\.bing\.com$/],google:[/^www\.google\.com$/,/^www\.google\.com\.[a-z]{2}$/,/^www\.google\.[a-z]{2}$/],sm:[/^m\.sm\.cn$/],so:[/^.+\.so\.com$/],sogou:[/^.*\.sogou\.com$/],yahoo:[/^.*\.yahoo\.com$/]};for(r in a)for(var i=a[r],n=0,s=i.length;n<s;n++)if(i[n].test(t))return r;return"未知搜索引擎"},d.getSourceFromReferrer=function(){function e(e,t){for(var r=0;r<e.length;r++)if(-1!==t.split("?")[0].indexOf(e[r]))return 1}var t="("+p.para.source_type.utm.join("|")+")\\=[^&]+",r=p.para.source_type.search,a=p.para.source_type.social,i=document.referrer||"",n=d.info.pageProp.url;if(n){t=n.match(new RegExp(t));return t&&t[0]?"付费广告流量":e(r,i)?"自然搜索流量":e(a,i)?"社交网站流量":""===i?"直接流量":"引荐流量"}return"获取url异常"},d.info={initPage:function(){var e=d.getReferrer(),t=location.href,r=d.getCurrentDomain(t);r||p.debug.jssdkDebug("url_domain异常_"+t+"_"+r),this.pageProp={referrer:e,referrer_host:e?d.getHostname(e):"",url:t,url_host:d.getHostname(t,"url_host取值异常"),url_domain:r}},pageProp:{},campaignParams:function(){var t,e=p.source_channel_standard.split(" "),r={};return d.isArray(p.para.source_channel)&&0<p.para.source_channel.length&&(e=e.concat(p.para.source_channel),e=d.unique(e)),d.each(e,function(e){(t=d.getQueryParam(location.href,e)).length&&(r[e]=t)}),r},campaignParamsStandard:function(a,i){a=a||"",i=i||"";var e=d.info.campaignParams(),n={},s={};return d.each(e,function(e,t,r){-1!==(" "+p.source_channel_standard+" ").indexOf(" "+t+" ")?n[a+t]=r[t]:s[i+t]=r[t]}),{$utms:n,otherUtms:s}},properties:function(){return{$timezone_offset:(new Date).getTimezoneOffset(),$screen_height:Number(screen.height)||0,$screen_width:Number(screen.width)||0,$lib:"js",$lib_version:String(p.lib_version)}},currentProps:{},register:function(e){d.extend(d.info.currentProps,e)}},d.autoExeQueue=function(){return{items:[],enqueue:function(e){this.items.push(e),this.start()},dequeue:function(){return this.items.shift()},getCurrentItem:function(){return this.items[0]},isRun:!1,start:function(){0<this.items.length&&!this.isRun&&(this.isRun=!0,this.getCurrentItem().start())},close:function(){this.dequeue(),this.isRun=!1,this.start()}}},d.trackLink=function(e,a,i){var n=null;return(e=e||{}).ele&&(n=e.ele),e.event&&(n=e.target||e.event.target),i=i||{},!(!n||"object"!=typeof n)&&(!n.href||/^javascript/.test(n.href)||n.target||n.download||n.onclick?(p.track(a,i),!1):(e.event&&t(e.event),void(e.ele&&d.addEvent(e.ele,"click",function(e){t(e)}))));function t(e){e.stopPropagation(),e.preventDefault();var t=!1;function r(){t||(t=!0,location.href=n.href)}setTimeout(r,1e3),p.track(a,i,r)}},d.eventEmitter=function(){this._events=[],this.pendingEvents=[]},d.eventEmitter.prototype={emit:function(t){var r=[].slice.call(arguments,1);d.each(this._events,function(e){e.type===t&&e.callback.apply(e.context,r)}),this.pendingEvents.push({type:t,data:r}),20<this.pendingEvents.length&&this.pendingEvents.shift()},on:function(t,r,a,e){"function"==typeof r&&(this._events.push({type:t,callback:r,context:a||this}),e=!1!==e,0<this.pendingEvents.length&&e&&d.each(this.pendingEvents,function(e){e.type===t&&r.apply(a,e.data)}))},tempAdd:function(e,t){if(t&&e)return this.emit(e,t)},isReady:function(){}},d.rot13obfs=function(e,t){t="number"==typeof t?t:13;for(var r=(e=String(e)).split(""),a=0,i=r.length;a<i;a++)r[a].charCodeAt(0)<126&&(r[a]=String.fromCharCode((r[a].charCodeAt(0)+t)%126));return r.join("")},d.rot13defs=function(e){e=String(e);return d.rot13obfs(e,113)},d.urlSafeBase64=(g={"+":"-","/":"_","=":"."},h={"-":"+",_:"/",".":"="},{encode:function(e){return e.replace(/[+/=]/g,function(e){return g[e]})},decode:function(e){return e.replace(/[-_.]/g,function(e){return h[e]})},trim:function(e){return e.replace(/[.=]{1,2}$/,"")},isBase64:function(e){return/^[A-Za-z0-9+/]*[=]{0,2}$/.test(e)},isUrlSafeBase64:function(e){return/^[A-Za-z0-9_-]*[.]{0,2}$/.test(e)}}),d.setCssStyle=function(t){var r=document.createElement("style");r.type="text/css";try{r.appendChild(document.createTextNode(t))}catch(e){r.styleSheet.cssText=t}var e=document.getElementsByTagName("head")[0],t=document.getElementsByTagName("script")[0];e?e.children.length?e.insertBefore(r,e.children[0]):e.appendChild(r):t.parentNode.insertBefore(r,t)},d.isIOS=function(){return!!navigator.userAgent.match(/iPhone|iPad|iPod/i)},d.getIOSVersion=function(){try{var e=navigator.appVersion.match(/OS (\d+)[._](\d+)[._]?(\d+)?/);return e&&e[1]?Number.parseInt(e[1],10):""}catch(e){return""}},d.getUA=function(){var e,t={},r=navigator.userAgent.toLowerCase();return(e=r.match(/opera.([\d.]+)/))?t.opera=Number(e[1].split(".")[0]):(e=r.match(/msie ([\d.]+)/))?t.ie=Number(e[1].split(".")[0]):(e=r.match(/edge.([\d.]+)/))?t.edge=Number(e[1].split(".")[0]):(e=r.match(/firefox\/([\d.]+)/))?t.firefox=Number(e[1].split(".")[0]):(e=r.match(/chrome\/([\d.]+)/))?t.chrome=Number(e[1].split(".")[0]):(e=r.match(/version\/([\d.]+).*safari/))&&(t.safari=Number(e[1].match(/^\d*.\d*/))),t},d.getDomBySelector=function(e){if(!d.isString(e))return null;var i=e.split(">"),e=null;return(e=function e(t){var r,a=i.shift();if(!a)return t;try{r=function(e,t){var r;if("body"===(e=d.trim(e)))return document.getElementsByTagName("body")[0];if(0===e.indexOf("#"))e=e.slice(1),r=document.getElementById(e);else if(-1<e.indexOf(":nth-of-type")){if(!(e=e.split(":nth-of-type"))[0]||!e[1])return null;var a=e[0];if(!(e=e[1].match(/\(([0-9]+)\)/))||!e[1])return null;var i=Number(e[1]);if(!(d.isElement(t)&&t.children&&0<t.children.length))return null;for(var n=t.children,s=0;s<n.length;s++)if(d.isElement(n[s])){var o=n[s].tagName.toLowerCase();if(o===a&&0==--i){r=n[s];break}}if(0<i)return null}return r||null}(a,t)}catch(e){p.log(e)}return r&&d.isElement(r)?e(r):null}())&&d.isElement(e)?e:null},d.jsonp=function(t){if(!d.isObject(t)||!d.isString(t.callbackName))return p.log("JSONP 请求缺少 callbackName"),!1;t.success=d.isFunction(t.success)?t.success:function(){},t.error=d.isFunction(t.error)?t.error:function(){},t.data=t.data||"";var r,a=document.createElement("script"),i=document.getElementsByTagName("head")[0],n=null,s=!1;i.appendChild(a),d.isNumber(t.timeout)&&(n=setTimeout(function(){return!s&&(t.error("timeout"),window[t.callbackName]=function(){p.log("call jsonp error")},n=null,i.removeChild(a),void(s=!0))},t.timeout)),window[t.callbackName]=function(){clearTimeout(n),n=null,t.success.apply(null,arguments),window[t.callbackName]=function(){p.log("call jsonp error")},i.removeChild(a)},-1<t.url.indexOf("?")?t.url+="&callbackName="+t.callbackName:t.url+="?callbackName="+t.callbackName,d.isObject(t.data)&&(r=[],d.each(t.data,function(e,t){r.push(t+"="+e)}),t.data=r.join("&"),t.url+="&"+t.data),a.onerror=function(e){if(s)return!1;window[t.callbackName]=function(){p.log("call jsonp error")},clearTimeout(n),n=null,i.removeChild(a),t.error(e),s=!0},a.src=t.url},d.listenPageState=function(e){({visibleHandle:d.isFunction(e.visible)?e.visible:function(){},hiddenHandler:d.isFunction(e.hidden)?e.hidden:function(){},visibilityChange:null,hidden:null,isSupport:function(){return void 0!==document[this.hidden]},init:function(){void 0!==document.hidden?(this.hidden="hidden",this.visibilityChange="visibilitychange"):void 0!==document.mozHidden?(this.hidden="mozHidden",this.visibilityChange="mozvisibilitychange"):void 0!==document.msHidden?(this.hidden="msHidden",this.visibilityChange="msvisibilitychange"):void 0!==document.webkitHidden&&(this.hidden="webkitHidden",this.visibilityChange="webkitvisibilitychange"),this.listen()},listen:function(){var e;this.isSupport()?(e=this,document.addEventListener(e.visibilityChange,function(){document[e.hidden]?e.hiddenHandler():e.visibleHandle()},1)):document.addEventListener?(window.addEventListener("focus",this.visibleHandle,1),window.addEventListener("blur",this.hiddenHandler,1)):(document.attachEvent("onfocusin",this.visibleHandle),document.attachEvent("onfocusout",this.hiddenHandler))}}).init()},d.isSupportBeaconSend=function(){var e=d.getUA(),t=!1,r=navigator.userAgent.toLowerCase();return/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)?(r=(r.match(/os [\d._]*/gi)+"").replace(/[^0-9|_.]/gi,"").replace(/_/gi,".").split("."),void 0===e.safari&&(e.safari=r[0]),r[0]&&r[0]<13?(41<e.chrome||30<e.firefox||25<e.opera||12<e.safari)&&(t=!0):(41<e.chrome||30<e.firefox||25<e.opera||11.3<e.safari)&&(t=!0)):(38<e.chrome||13<e.edge||30<e.firefox||25<e.opera||11<e.safari)&&(t=!0),t},d.secCheck={isHttpUrl:function(e){if("string"!=typeof e)return!1;return!1!==/^https?:\/\/.+/.test(e)||(p.log("Invalid URL"),!1)},removeScriptProtocol:function(e){if("string"!=typeof e)return"";for(var t=/^\s*javascript/i;t.test(e);)e=e.replace(t,"");return e}},p.para_default={preset_properties:{search_keyword_baidu:!1,latest_utm:!0,latest_traffic_source_type:!0,latest_search_keyword:!0,latest_referrer:!0,latest_referrer_host:!1,latest_landing_page:!1,latest_wx_ad_click_id:void 0,url:!0,title:!0},encrypt_cookie:!1,img_use_crossorigin:!1,name:"sa",max_referrer_string_length:200,max_string_length:500,cross_subdomain:!0,show_log:!0,is_debug:!1,debug_mode:!1,debug_mode_upload:!1,session_time:0,use_client_time:!1,source_channel:[],send_type:"image",vtrack_ignore:{},auto_init:!0,is_track_single_page:!1,is_single_page:!1,batch_send:!1,source_type:{},callback_timeout:200,datasend_timeout:3e3,queue_timeout:300,is_track_device_id:!1,ignore_oom:!0,app_js_bridge:!1,url_is_decode:!1,js_error:!1},p.addReferrerHost=function(e){var t="取值异常";d.isObject(e.properties)&&(e.properties.$first_referrer&&(e.properties.$first_referrer_host=d.getHostname(e.properties.$first_referrer,t)),"track"!==e.type&&"track_signup"!==e.type||("$referrer"in e.properties&&(e.properties.$referrer_host=""===e.properties.$referrer?"":d.getHostname(e.properties.$referrer,t)),p.para.preset_properties.latest_referrer&&p.para.preset_properties.latest_referrer_host&&(e.properties.$latest_referrer_host=""===e.properties.$latest_referrer?"":d.getHostname(e.properties.$latest_referrer,t))))},p.addPropsHook=function(e){p.para.preset_properties&&p.para.preset_properties.url&&("track"===e.type||"track_signup"===e.type)&&void 0===e.properties.$url&&(e.properties.$url=d.isDecodeURI(p.para.url_is_decode,window.location.href)),p.para.preset_properties&&p.para.preset_properties.title&&("track"===e.type||"track_signup"===e.type)&&void 0===e.properties.$title&&(e.properties.$title=s||document.title)},p.initPara=function(e){p.para=e||p.para||{};var t,r={};if(d.isObject(p.para.is_track_latest))for(var a in p.para.is_track_latest)r["latest_"+a]=p.para.is_track_latest[a];for(t in p.para.preset_properties=d.extend({},p.para_default.preset_properties,r,p.para.preset_properties||{}),p.para_default)void 0===p.para[t]&&(p.para[t]=p.para_default[t]);"string"==typeof p.para.server_url&&(p.para.server_url=d.trim(p.para.server_url),p.para.server_url&&("://"===p.para.server_url.slice(0,3)?p.para.server_url=location.protocol.slice(0,-1)+p.para.server_url:"//"===p.para.server_url.slice(0,2)?p.para.server_url=location.protocol+p.para.server_url:"http"!==p.para.server_url.slice(0,4)&&(p.para.server_url=""))),"string"!=typeof p.para.web_url||"://"!==p.para.web_url.slice(0,3)&&"//"!==p.para.web_url.slice(0,2)||("://"===p.para.web_url.slice(0,3)?p.para.web_url=location.protocol.slice(0,-1)+p.para.web_url:p.para.web_url=location.protocol+p.para.web_url),"image"!==p.para.send_type&&"ajax"!==p.para.send_type&&"beacon"!==p.para.send_type&&(p.para.send_type="image"),p.debug.protocol.serverUrl(),p.bridge.initPara(),p.bridge.initState();var i={datasend_timeout:6e3,send_interval:6e3};d.localStorage.isSupport()&&d.isSupportCors()&&"object"==typeof localStorage?!(p.para.batch_send=!1)===p.para.batch_send?(p.para.batch_send=d.extend({},i),p.para.use_client_time=!0):"object"==typeof p.para.batch_send&&(p.para.use_client_time=!0,p.para.batch_send=d.extend({},i,p.para.batch_send)):p.para.batch_send=!1;var n=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"],s=["www.baidu.","m.baidu.","m.sm.cn","so.com","sogou.com","youdao.com","google.","yahoo.com/","bing.com/","ask.com/"],e=["weibo.com","renren.com","kaixin001.com","douban.com","qzone.qq.com","zhihu.com","tieba.baidu.com","weixin.qq.com"],i={baidu:["wd","word","kw","keyword"],google:"q",bing:"q",yahoo:"p",sogou:["query","keyword"],so:"q",sm:"q"};"object"==typeof p.para.source_type&&(p.para.source_type.utm=d.isArray(p.para.source_type.utm)?p.para.source_type.utm.concat(n):n,p.para.source_type.search=d.isArray(p.para.source_type.search)?p.para.source_type.search.concat(s):s,p.para.source_type.social=d.isArray(p.para.source_type.social)?p.para.source_type.social.concat(e):e,p.para.source_type.keyword=d.isObject(p.para.source_type.keyword)?d.extend(i,p.para.source_type.keyword):i);e=["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"];if(p.para.heatmap&&!d.isObject(p.para.heatmap)&&(p.para.heatmap={}),d.isObject(p.para.heatmap)&&(p.para.heatmap.clickmap=p.para.heatmap.clickmap||"default",p.para.heatmap.scroll_notice_map=p.para.heatmap.scroll_notice_map||"not_collect",p.para.heatmap.scroll_delay_time=p.para.heatmap.scroll_delay_time||4e3,p.para.heatmap.scroll_event_duration=p.para.heatmap.scroll_event_duration||18e3,p.para.heatmap.renderRefreshTime=p.para.heatmap.renderRefreshTime||1e3,p.para.heatmap.loadTimeout=p.para.heatmap.loadTimeout||1e3,!0!==p.para.heatmap.get_vtrack_config&&(p.para.heatmap.get_vtrack_config=!1),(i=d.isArray(p.para.heatmap.track_attr)?d.filter(p.para.heatmap.track_attr,function(e){return e&&"string"==typeof e}):[]).push("data-sensors-click"),p.para.heatmap.track_attr=i,d.isObject(p.para.heatmap.collect_tags)?!0===p.para.heatmap.collect_tags.div?p.para.heatmap.collect_tags.div={ignore_tags:e,max_level:1}:d.isObject(p.para.heatmap.collect_tags.div)?(p.para.heatmap.collect_tags.div.ignore_tags?d.isArray(p.para.heatmap.collect_tags.div.ignore_tags)||(p.log("ignore_tags 参数必须是数组格式"),p.para.heatmap.collect_tags.div.ignore_tags=e):p.para.heatmap.collect_tags.div.ignore_tags=e,p.para.heatmap.collect_tags.div.max_level&&(-1===d.indexOf([1,2,3],p.para.heatmap.collect_tags.div.max_level)&&(p.para.heatmap.collect_tags.div.max_level=1))):p.para.heatmap.collect_tags.div=!1:p.para.heatmap.collect_tags={div:!1}),d.isArray(p.para.server_url)&&p.para.server_url.length)for(t=0;t<p.para.server_url.length;t++)/sa\.gif[^\/]*$/.test(p.para.server_url[t])||(p.para.server_url[t]=p.para.server_url[t].replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));else/sa\.gif[^\/]*$/.test(p.para.server_url)||"string"!=typeof p.para.server_url||(p.para.server_url=p.para.server_url.replace(/\/sa$/,"/sa.gif").replace(/(\/sa)(\?[^\/]+)$/,"/sa.gif$2"));"string"==typeof p.para.server_url&&(p.para.debug_mode_url=p.para.debug_mode_url||p.para.server_url.replace("sa.gif","debug")),!0===p.para.noCache?p.para.noCache="?"+(new Date).getTime():p.para.noCache="",p.para.callback_timeout>p.para.datasend_timeout&&(p.para.datasend_timeout=p.para.callback_timeout),p.para.callback_timeout>p.para.queue_timeout&&(p.para.queue_timeout=p.para.callback_timeout),p.para.queue_timeout>p.para.datasend_timeout&&(p.para.datasend_timeout=p.para.queue_timeout)},p.initError=function(){p.para.js_error&&(window.onerror=function(e,t,r,a,i){var n={error_type:"code_error",error_message:e,error_stack:"",error_details:{error_url:t,error_line:r,error_col:a=a||window.event&&window.event.errorCharacter||0}};if(i&&i.stack)n.error_stack=i.stack.toString(),n.error_stack&&3e3<n.error_stack.length&&(n.error_stack=n.error_stack.slice(0,3e3));else if(arguments.callee){for(var s=[],o=arguments.callee.caller,c=3;o&&0<--c&&(s.push(o.toString()),o!==o.caller);)o=o.caller;s=s.join(","),n.error_stack=s}else n.error_stack="";n.error_details=JSON.stringify(n.error_details),p.track("$js_error",n)},window.addEventListener("error",function(t){var r,a,i;t.message||(r=(i=t.target||e.srcElement).outerHTML,a={error_type:"resource_error",error_message:"",error_stack:"",error_details:{outerHTML:r=r&&200<r.length?r.slice(0,200):r,src:i&&i.src,tagName:i&&i.tagName,id:i&&i.id,className:i&&i.className,name:i&&i.name,type:i&&i.type,timeStamp:t.timeStamp}},i.src!==window.location.href&&(i.src&&i.src.match(/.*\/(.*)$/)&&!i.src.match(/.*\/(.*)$/)[1]||a.error_details.src&&window.XMLHttpRequest&&((i=new XMLHttpRequest).open("HEAD",a.error_details.src),i.send(),i.onload=function(e){200!=e.target.status&&(a.error_message=e.target.status+" "+e.target.statusText),a.error_details=JSON.stringify(a.error_details),p.track("$js_error",a)})))},!0),window.addEventListener("unhandledrejection",function(e){e={error_type:"unhandledrejection",error_message:e.reason.message,error_stack:e.reason.stack,error_details:"unhandledrejection"};e.error_stack=e.error_stack.toString(),e.error_stack&&3e3<e.error_stack.length&&(e.error_stack=e.error_stack.slice(0,3e3)),p.track("$js_error",e)},!0))},p.readyState={state:0,historyState:[],stateType:{1:"1-init未开始",2:"2-init开始",3:"3-store完成"},getState:function(){return this.historyState.join("\n")},setState:function(e){String(e)in this.stateType&&(this.state=e),this.historyState.push(this.stateType[e])}},p.setPreConfig=function(e){p.para=e.para,p._q=e._q},p.setInitVar=function(){p._t=p._t||+new Date,p.lib_version="2.0.1",p.is_first_visitor=!1,p.source_channel_standard="utm_source utm_medium utm_campaign utm_content utm_term"},p.log=function(){if((d.sessionStorage.isSupport()&&"true"===sessionStorage.getItem("sensorsdata_jssdk_debug")||p.para.show_log)&&(!d.isObject(arguments[0])||!0!==p.para.show_log&&"string"!==p.para.show_log&&!1!==p.para.show_log||(arguments[0]=d.formatJsonString(arguments[0])),"object"==typeof console&&console.log))try{return console.log.apply(console,arguments)}catch(e){console.log(arguments[0])}},p.enableLocalLog=function(){if(d.sessionStorage.isSupport())try{sessionStorage.setItem("sensorsdata_jssdk_debug","true")}catch(e){p.log("enableLocalLog error: "+e.message)}},p.disableLocalLog=function(){d.sessionStorage.isSupport()&&sessionStorage.removeItem("sensorsdata_jssdk_debug")};var a={setOnlineState:function(e){!0===e&&d.isObject(p.para.jsapp)&&"function"==typeof p.para.jsapp.getData?(p.para.jsapp.isOnline=!0,e=p.para.jsapp.getData(),d.isArray(e)&&0<e.length&&d.each(e,function(e){d.isJSONString(e)&&p.sendState.pushSend(JSON.parse(e))})):p.para.jsapp.isOnline=!1},autoTrackIsUsed:!(p.debug={distinct_id:function(){},jssdkDebug:function(){},_sendDebug:function(e){p.track("_sensorsdata2019_debug",{_jssdk_debug_info:e})},apph5:function(e){var t="app_h5打通失败-",r=e.output,a=e.step,e=e.data||"";"all"!==r&&"console"!==r||p.log({1:t+"use_app_track为false",2:t+"Android或者iOS，没有暴露相应方法",3.1:t+"Android校验server_url失败",3.2:t+"iOS校验server_url失败",4.1:t+"H5 校验 iOS server_url 失败",4.2:t+"H5 校验 Android server_url 失败"}[a]),("all"===r||"code"===r)&&d.isObject(p.para.is_debug)&&p.para.is_debug.apph5&&(e.type&&"profile"===e.type.slice(0,7)||(e.properties._jssdk_debug_info="apph5-"+String(a)))},defineMode:function(e){var t={1:{title:"当前页面无法进行可视化全埋点",message:"App SDK 与 Web JS SDK 没有进行打通，请联系贵方技术人员修正 App SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"},2:{title:"当前页面无法进行可视化全埋点",message:"App SDK 与 Web JS SDK 没有进行打通，请联系贵方技术人员修正 Web JS SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"},3:{title:"当前页面无法进行可视化全埋点",message:"Web JS SDK 没有开启全埋点配置，请联系贵方工作人员修正 SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_web_all-1573964.html"},4:{title:"当前页面无法进行可视化全埋点",message:"Web JS SDK 配置的数据校验地址与 App SDK 配置的数据校验地址不一致，请联系贵方工作人员修正 SDK 的配置，详细信息请查看文档。",link_text:"配置文档",link_url:"https://manual.sensorsdata.cn/sa/latest/tech_sdk_client_link-1573913.html"}};return!(!e||!t[e])&&t[e]},protocol:{protocolIsSame:function(e,t){try{if(d.URL(e).protocol!==d.URL(t).protocol)return!1}catch(e){return p.log("不支持 _.URL 方法"),!1}return!0},serverUrl:function(){d.isString(p.para.server_url)&&""!==p.para.server_url&&!this.protocolIsSame(p.para.server_url,location.href)&&p.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。\n因为：1、https 下面发送 http 的图片请求会失败。2、http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。")},ajax:function(e){if(e===p.para.server_url)return!1;d.isString(e)&&""!==e&&!this.protocolIsSame(e,location.href)&&p.log("SDK 检测到您的数据发送地址和当前页面地址的协议不一致，建议您修改成一致的协议。因为 http 页面使用 https + ajax 方式发数据，在 ie9 及以下会丢失数据。")}}}),isReady:function(e){e()},getUtm:function(){return d.info.campaignParams()},getStayTime:function(){return(new Date-p._t)/1e3},setProfileLocal:function(e){if(!d.localStorage.isSupport())return p.setProfile(e),!1;if(!d.isObject(e)||d.isEmptyObject(e))return!1;var t=d.localStorage.parse("sensorsdata_2015_jssdk_profile"),r=!1;if(d.isObject(t)&&!d.isEmptyObject(t)){for(var a in e)!(a in t&&t[a]!==e[a])&&a in t||(t[a]=e[a],r=!0);r&&(d.localStorage.set("sensorsdata_2015_jssdk_profile",JSON.stringify(t)),p.setProfile(e))}else d.localStorage.set("sensorsdata_2015_jssdk_profile",JSON.stringify(e)),p.setProfile(e)},setInitReferrer:function(){var e=d.getReferrer();p.setOnceProfile({_init_referrer:e,_init_referrer_host:d.info.pageProp.referrer_host})},setSessionReferrer:function(){var e=d.getReferrer();c.setSessionPropsOnce({_session_referrer:e,_session_referrer_host:d.info.pageProp.referrer_host})},setDefaultAttr:function(){d.info.register({_current_url:location.href,_referrer:d.getReferrer(),_referring_host:d.info.pageProp.referrer_host})},trackHeatMap:function(e,t,r){var a,i,n;"object"==typeof e&&e.tagName&&(a=e.tagName.toLowerCase(),i=e.parentNode.tagName.toLowerCase(),n=p.para.heatmap&&p.para.heatmap.track_attr?p.para.heatmap.track_attr:["data-sensors-click"],"button"===a||"a"===a||"a"===i||"button"===i||"input"===a||"textarea"===a||d.hasAttributes(e,n)||u.start(null,e,a,t,r))},trackAllHeatMap:function(e,t,r){var a;"object"==typeof e&&e.tagName&&(a=e.tagName.toLowerCase(),u.start(null,e,a,t,r))},autoTrackSinglePage:function(e,t){var r;function a(){var e=d.info.campaignParams(),a={};return d.each(e,function(e,t,r){-1!==(" "+p.source_channel_standard+" ").indexOf(" "+t+" ")?a["$"+t]=r[t]:a[t]=r[t]}),a}r=this.autoTrackIsUsed?d.info.pageProp.url:d.info.pageProp.referrer;var i=!(e=d.isObject(e)?e:{}).not_set_profile;function n(e,t){p.track("$pageview",d.extend({$referrer:d.isDecodeURI(p.para.url_is_decode,r),$url:d.isDecodeURI(p.para.url_is_decode,location.href),$url_path:location.pathname,$title:s||document.title},e,a()),t),r=location.href}e.not_set_profile&&delete e.not_set_profile,n(e,t),this.autoTrackSinglePage=n,p.is_first_visitor&&i&&(i={},p.para.preset_properties.search_keyword_baidu&&d.isReferralTraffic(document.referrer)&&d.isBaiduTraffic()&&(i.$search_keyword_id=d.getBaiduKeyword.id(),i.$search_keyword_id_type=d.getBaiduKeyword.type(),i.$search_keyword_id_hash=d.hashCode(i.$search_keyword_id)),p.setOnceProfile(d.extend({$first_visit_time:new Date,$first_referrer:d.isDecodeURI(p.para.url_is_decode,d.getReferrer()),$first_browser_language:navigator.language||"取值异常",$first_browser_charset:"string"==typeof document.charset?document.charset.toUpperCase():"取值异常",$first_traffic_source_type:d.getSourceFromReferrer(),$first_search_keyword:d.getKeywordFromReferrer()},a(),i)),p.is_first_visitor=!1)},autoTrackWithoutProfile:function(e,t){e=d.isObject(e)?e:{},this.autoTrack(d.extend(e,{not_set_profile:!0}),t)},autoTrack:function(t,r){t=d.isObject(t)?t:{};var e=d.info.campaignParams(),a={};d.each(e,function(e,t,r){-1!==(" "+p.source_channel_standard+" ").indexOf(" "+t+" ")?a["$"+t]=r[t]:a[t]=r[t]});e=!t.not_set_profile;t.not_set_profile&&delete t.not_set_profile;var i=location.href;p.para.is_single_page&&d.addHashEvent(function(){var e=d.getReferrer(i);p.track("$pageview",d.extend({$referrer:d.isDecodeURI(p.para.url_is_decode,e),$url:d.isDecodeURI(p.para.url_is_decode,location.href),$url_path:location.pathname,$title:s||document.title},a,t),r),i=location.href}),p.track("$pageview",d.extend({$referrer:d.isDecodeURI(p.para.url_is_decode,d.getReferrer()),$url:d.isDecodeURI(p.para.url_is_decode,location.href),$url_path:location.pathname,$title:s||document.title},a,t),r),p.is_first_visitor&&e&&(e={},p.para.preset_properties.search_keyword_baidu&&d.isReferralTraffic(document.referrer)&&d.isBaiduTraffic()&&(e.$search_keyword_id=d.getBaiduKeyword.id(),e.$search_keyword_id_type=d.getBaiduKeyword.type(),e.$search_keyword_id_hash=d.hashCode(e.$search_keyword_id)),p.setOnceProfile(d.extend({$first_visit_time:new Date,$first_referrer:d.isDecodeURI(p.para.url_is_decode,d.getReferrer()),$first_browser_language:navigator.language||"取值异常",$first_browser_charset:"string"==typeof document.charset?document.charset.toUpperCase():"取值异常",$first_traffic_source_type:d.getSourceFromReferrer(),$first_search_keyword:d.getKeywordFromReferrer()},a,e)),p.is_first_visitor=!1),this.autoTrackIsUsed=!0},getAnonymousID:function(){return d.isEmptyObject(p.store._state)?"请先初始化SDK":p.store._state._first_id||p.store._state.first_id||p.store._state._distinct_id||p.store._state.distinct_id},setPlugin:function(e){if(!d.isObject(e))return!1;d.each(e,function(e,t){d.isFunction(e)&&(d.isObject(window.SensorsDataWebJSSDKPlugin)&&window.SensorsDataWebJSSDKPlugin[t]?e(window.SensorsDataWebJSSDKPlugin[t]):p.log(t+"没有获取到,请查阅文档，调整"+t+"的引入顺序！"))})},useModulePlugin:function(){p.use.apply(p,arguments)},useAppPlugin:function(){this.setPlugin.apply(this,arguments)}};function t(){this.sendingData=0,this.sendingItemKeys=[]}p.setCustomTitle=function(e){s=void 0;for(var t=0;t<e.length;t++)if(e[t].$title){s=e[t].$title;break}},p.quick=function(){var e=Array.prototype.slice.call(arguments),t=e[0],r=e.slice(1);if(p.setCustomTitle(r),"string"==typeof t&&a[t])return a[t].apply(a,r);"function"==typeof t?t.apply(p,r):p.log("quick方法中没有这个功能"+e[0])},p.use=function(e,t){return d.isString(e)?d.isObject(window.SensorsDataWebJSSDKPlugin)&&d.isObject(window.SensorsDataWebJSSDKPlugin[e])&&d.isFunction(window.SensorsDataWebJSSDKPlugin[e].init)?(window.SensorsDataWebJSSDKPlugin[e].init(p,t),window.SensorsDataWebJSSDKPlugin[e]):d.isObject(p.modules)&&d.isObject(p.modules[e])&&d.isFunction(p.modules[e].init)?(p.modules[e].init(p,t),p.modules[e]):void p.log(e+"没有获取到,请查阅文档，调整"+e+"的引入顺序！"):(p.log("use插件名称必须是字符串！"),!1)},p.trackCustom=function(e){e&&(n.check({properties:e})&&!e.custom_type&&(e.custom_type="default_type"),p.track("CustomEvent",e))},p.track=function(e,t,r){n.check({event:e,properties:t})&&n.send({type:"track",event:e,properties:t},r)},p.trackLink=function(e,t,r){"object"==typeof e&&e.tagName?d.trackLink({ele:e},t,r):"object"==typeof e&&e.target&&e.event&&d.trackLink(e,t,r)},p.trackLinks=function(a,i,n){return n=n||{},!(!a||"object"!=typeof a)&&(!(!a.href||/^javascript/.test(a.href)||a.target)&&void d.addEvent(a,"click",function(e){e.preventDefault();var t=!1;function r(){t||(t=!0,location.href=a.href)}setTimeout(r,1e3),p.track(i,n,r)}))},p.setProfile=function(e,t){n.check({propertiesMust:e})&&n.send({type:"profile_set",properties:e},t)},p.setOnceProfile=function(e,t){n.check({propertiesMust:e})&&n.send({type:"profile_set_once",properties:e},t)},p.appendProfile=function(r,e){n.check({propertiesMust:r})&&(d.each(r,function(e,t){d.isString(e)?r[t]=[e]:d.isArray(e)?r[t]=e:(delete r[t],p.log("appendProfile属性的值必须是字符串或者数组"))}),d.isEmptyObject(r)||n.send({type:"profile_append",properties:r},e))},p.incrementProfile=function(e,t){var r=e;d.isString(e)&&((e={})[r]=1),n.check({propertiesMust:e})&&(!function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&!/-*\d+/.test(String(e[t])))return;return 1}(e)?p.log("profile_increment的值只能是数字"):n.send({type:"profile_increment",properties:e},t))},p.deleteProfile=function(e){n.send({type:"profile_delete"},e),c.set("distinct_id",d.UUID()),c.set("first_id","")},p.unsetProfile=function(e,t){var r=e,a={};d.isString(e)&&(e=[]).push(r),d.isArray(e)?(d.each(e,function(e){d.isString(e)?a[e]=!0:p.log("profile_unset给的数组里面的值必须时string,已经过滤掉",e)}),n.send({type:"profile_unset",properties:a},t)):p.log("profile_unset的参数是数组")},p.identify=function(e,t){"number"==typeof e&&(e=String(e));var r,a=c.getFirstId();void 0===e?(r=d.UUID(),a?c.set("first_id",r):c.set("distinct_id",r)):n.check({distinct_id:e})?!0===t?a?c.set("first_id",e):c.set("distinct_id",e):a?c.change("first_id",e):c.change("distinct_id",e):p.log("identify的参数必须是字符串")},p.trackSignup=function(e,t,r,a){var i;n.check({distinct_id:e,event:t,properties:r})&&(i=c.getFirstId()||c.getDistinctId(),c.set("distinct_id",e),n.send({original_id:i,distinct_id:e,type:"track_signup",event:t,properties:r},a))},p.registerPage=function(e){n.check({properties:e})?d.extend(d.info.currentProps,e):p.log("register输入的参数有误")},p.clearAllRegister=function(e){c.clearAllProps(e)},p.clearPageRegister=function(e){if(d.isArray(e)&&0<e.length)for(var t=0;t<e.length;t++)d.isString(e[t])&&e[t]in d.info.currentProps&&delete d.info.currentProps[e[t]];else if(!0===e)for(var t in d.info.currentProps)delete d.info.currentProps[t]},p.register=function(e){n.check({properties:e})?c.setProps(e):p.log("register输入的参数有误")},p.registerOnce=function(e){n.check({properties:e})?c.setPropsOnce(e):p.log("registerOnce输入的参数有误")},p.registerSession=function(e){n.check({properties:e})?c.setSessionProps(e):p.log("registerSession输入的参数有误")},p.registerSessionOnce=function(e){n.check({properties:e})?c.setSessionPropsOnce(e):p.log("registerSessionOnce输入的参数有误")},p.login=function(e,t){var r,a;"number"==typeof e&&(e=String(e)),n.check({distinct_id:e})?(r=c.getFirstId(),e!==(a=c.getDistinctId())?(r||c.set("first_id",a),p.trackSignup(e,"$SignUp",{},t)):t&&t()):(p.log("login的参数必须是字符串"),t&&t())},p.logout=function(e){var t=c.getFirstId();t?(c.set("first_id",""),!0===e?(e=d.UUID(),c.set("distinct_id",e)):c.set("distinct_id",t)):p.log("没有first_id，logout失败")},p.getPresetProperties=function(){var e,a,t={$is_first_day:d.cookie.getNewUser(),$referrer:d.isDecodeURI(p.para.url_is_decode,d.info.pageProp.referrer)||"",$referrer_host:d.info.pageProp.referrer?d.getHostname(d.info.pageProp.referrer):"",$url:d.isDecodeURI(p.para.url_is_decode,location.href),$url_path:location.pathname,$title:document.title||"",_distinct_id:c.getDistinctId()},t=d.extend({},d.info.properties(),p.store.getProps(),(e=d.info.campaignParams(),a={},d.each(e,function(e,t,r){-1!==(" "+p.source_channel_standard+" ").indexOf(" "+t+" ")?a["$"+t]=r[t]:a[t]=r[t]}),a),t);return p.para.preset_properties.latest_referrer&&p.para.preset_properties.latest_referrer_host&&(t.$latest_referrer_host=""===t.$latest_referrer?"":d.getHostname(t.$latest_referrer)),t},p.detectMode=function(){function e(e){var t,r=p.bridge.initDefineBridgeInfo();function a(){var e=[];r.touch_app_bridge||e.push(p.debug.defineMode("1")),d.isObject(p.para.app_js_bridge)||(e.push(p.debug.defineMode("2")),r.verify_success=!1),d.isObject(p.para.heatmap)&&"default"==p.para.heatmap.clickmap||e.push(p.debug.defineMode("3")),"fail"===r.verify_success&&e.push(p.debug.defineMode("4"));e={callType:"app_alert",data:e};SensorsData_App_Visual_Bridge&&SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info?SensorsData_App_Visual_Bridge.sensorsdata_visualized_alert_info(JSON.stringify(e)):window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(e))}d.isObject(window.SensorsData_App_Visual_Bridge)&&window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode&&(!0===window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode||window.SensorsData_App_Visual_Bridge.sensorsdata_visualized_mode())&&(d.isObject(p.para.heatmap)&&"default"==p.para.heatmap.clickmap&&d.isObject(p.para.app_js_bridge)&&"success"===r.verify_success?e?sa_jssdk_app_define_mode(p,e):(t=location.protocol,t=-1<d.indexOf(["http:","https:"],t)?t:"https:",d.loadScript({success:function(){setTimeout(function(){"undefined"!=typeof sa_jssdk_app_define_mode&&sa_jssdk_app_define_mode(p,e)},0)},error:function(){},type:"js",url:t+"//static.sensorsdata.cn/sdk/"+p.lib_version+"/vapph5define.min.js"})):a())}var t={searchKeywordMatch:location.search.match(/sa-request-id=([^&#]+)/),isSeachHasKeyword:function(){var e=this.searchKeywordMatch;return!!(e&&e[0]&&e[1])&&("string"==typeof sessionStorage.getItem("sensors-visual-mode")&&sessionStorage.removeItem("sensors-visual-mode"),!0)},hasKeywordHandle:function(){var e=this.searchKeywordMatch,t=location.search.match(/sa-request-type=([^&#]+)/),r=location.search.match(/sa-request-url=([^&#]+)/);u.setNotice(r),d.sessionStorage.isSupport()&&(r&&r[0]&&r[1]&&sessionStorage.setItem("sensors_heatmap_url",decodeURIComponent(r[1])),sessionStorage.setItem("sensors_heatmap_id",e[1]),t&&t[0]&&t[1]?"1"===t[1]||"2"===t[1]||"3"===t[1]?(t=t[1],sessionStorage.setItem("sensors_heatmap_type",t)):t=null:t=null!==sessionStorage.getItem("sensors_heatmap_type")?sessionStorage.getItem("sensors_heatmap_type"):null),this.isReady(e[1],t)},isReady:function(e,t,r){p.para.heatmap_url?d.loadScript({success:function(){setTimeout(function(){"undefined"!=typeof sa_jssdk_heatmap_render&&(sa_jssdk_heatmap_render(p,e,t,r),"object"==typeof console&&"function"==typeof console.log&&(p.heatmap_version&&p.heatmap_version===p.lib_version||console.log("heatmap.js与sensorsdata.js版本号不一致，可能存在风险!")))},0)},error:function(){},type:"js",url:p.para.heatmap_url}):p.log("没有指定heatmap_url的路径")},isStorageHasKeyword:function(){return d.sessionStorage.isSupport()&&"string"==typeof sessionStorage.getItem("sensors_heatmap_id")},storageHasKeywordHandle:function(){u.setNotice(),t.isReady(sessionStorage.getItem("sensors_heatmap_id"),sessionStorage.getItem("sensors_heatmap_type"),location.href)}},r={isStorageHasKeyword:function(){return d.sessionStorage.isSupport()&&"string"==typeof sessionStorage.getItem("sensors-visual-mode")},isSearchHasKeyword:function(){return!!location.search.match(/sa-visual-mode=true/)&&("string"==typeof sessionStorage.getItem("sensors_heatmap_id")&&sessionStorage.removeItem("sensors_heatmap_id"),!0)},loadVtrack:function(){d.loadScript({success:function(){},error:function(){},type:"js",url:p.para.vtrack_url||location.protocol+"//static.sensorsdata.cn/sdk/"+p.lib_version+"/vtrack.min.js"})},messageListener:function(e){if("sa-fe"!==e.data.source)return!1;var t;"v-track-mode"===e.data.type&&(e.data.data&&e.data.data.isVtrack&&(d.sessionStorage.isSupport()&&sessionStorage.setItem("sensors-visual-mode","true"),e.data.data.userURL&&location.search.match(/sa-visual-mode=true/)?(e=(t=e.data.data.userURL,d.secCheck.isHttpUrl(t)?d.secCheck.removeScriptProtocol(t):(p.log("可视化模式检测 URL 失败"),!1)))&&(window.location.href=e):r.loadVtrack()),window.removeEventListener("message",r.messageListener,!1))},removeMessageHandle:function(){window.removeEventListener&&window.removeEventListener("message",r.messageListener,!1)},verifyVtrackMode:function(){window.addEventListener&&window.addEventListener("message",r.messageListener,!1),r.postMessage()},postMessage:function(){window.parent&&window.parent.postMessage&&window.parent.postMessage({source:"sa-web-sdk",type:"v-is-vtrack",data:{sdkversion:"1.18.11"}},"*")},notifyUser:function(){function t(e){if("sa-fe"!==e.data.source)return!1;"v-track-mode"===e.data.type&&(e.data.data&&e.data.data.isVtrack&&alert("当前版本不支持，请升级部署神策数据治理"),window.removeEventListener("message",t,!1))}window.addEventListener&&window.addEventListener("message",t,!1),r.postMessage()}};function a(){p.readyState.setState(3);new p.JSBridge({type:"visualized",app_call_js:function(){"undefined"!=typeof sa_jssdk_app_define_mode?e(!0):e(!1)}});e(!1),p.bridge.app_js_bridge_v1(),d.info.initPage(),p.para.is_track_single_page&&d.addSinglePageEvent(function(t){function e(e){e=e||{},t!==location.href&&(d.info.pageProp.referrer=t,t=d.isDecodeURI(p.para.url_is_decode,t),p.quick("autoTrack",d.extend({$url:d.isDecodeURI(p.para.url_is_decode,location.href),$referrer:t},e)))}var r;"boolean"==typeof p.para.is_track_single_page?e():"function"==typeof p.para.is_track_single_page&&(r=p.para.is_track_single_page(),d.isObject(r)?e(r):!0===r&&e())}),p.para.batch_send&&(d.addEvent(window,"onpagehide"in window?"pagehide":"unload",function(e){p.batchSend.clearPendingStatus()}),p.batchSend.batchInterval()),p.store.init(),p.vtrackcollect.init(),p.readyState.setState(4),p._q&&d.isArray(p._q)&&0<p._q.length&&d.each(p._q,function(e){p[e[0]].apply(p,Array.prototype.slice.call(e[1]))}),d.isObject(p.para.heatmap)&&(u.initHeatmap(),u.initScrollmap())}p.para.heatmap&&p.para.heatmap.collect_tags&&d.isObject(p.para.heatmap.collect_tags)&&d.each(p.para.heatmap.collect_tags,function(e,t){"div"!==t&&e&&p.heatmap.otherTags.push(t)}),t.isSeachHasKeyword()?t.hasKeywordHandle():window.parent!==self&&r.isSearchHasKeyword()?r.verifyVtrackMode():t.isStorageHasKeyword()?t.storageHasKeywordHandle():window.parent!==self&&r.isStorageHasKeyword()?r.verifyVtrackMode():(a(),r.notifyUser())},t.prototype={add:function(e){d.isObject(e)&&(this.writeStore(e),"track_signup"!==e.type&&"$pageview"!==e.event||this.sendStrategy())},clearPendingStatus:function(){this.sendingItemKeys.length&&this.removePendingItems(this.sendingItemKeys)},remove:function(e){0<this.sendingData&&--this.sendingData,d.isArray(e)&&0<e.length&&d.each(e,function(e){d.localStorage.remove(e)})},send:function(e){var t,r=this;d.isString(p.para.server_url)&&""!==p.para.server_url||d.isArray(p.para.server_url)&&p.para.server_url.length?(t=d.isArray(p.para.server_url)?p.para.server_url[0]:p.para.server_url,d.ajax({url:t,type:"POST",data:"data_list="+encodeURIComponent(d.base64Encode(JSON.stringify(e.vals))),credentials:!1,timeout:p.para.batch_send.datasend_timeout,cors:!0,success:function(){r.remove(e.keys),r.removePendingItems(e.keys)},error:function(){0<r.sendingData&&--r.sendingData,r.removePendingItems(e.keys)}})):p.log("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！")},appendPendingItems:function(e){if(!1!==d.isArray(e)){this.sendingItemKeys=d.unique(this.sendingItemKeys.concat(e));try{var t=this.getPendingItems(),r=d.unique(t.concat(e));localStorage.setItem("sawebjssdk-sendingitems",JSON.stringify(r))}catch(e){}}},removePendingItems:function(t){if(!1!==d.isArray(t)){this.sendingItemKeys.length&&(this.sendingItemKeys=d.filter(this.sendingItemKeys,function(e){return-1===d.indexOf(t,e)}));try{var e=this.getPendingItems(),r=d.filter(e,function(e){return-1===d.indexOf(t,e)});localStorage.setItem("sawebjssdk-sendingitems",JSON.stringify(r))}catch(e){}}},getPendingItems:function(){var e=[];try{var t=localStorage.getItem("sawebjssdk-sendingitems");t&&(e=JSON.parse(t))}catch(e){}return e},sendPrepare:function(e){this.appendPendingItems(e.keys);var t=e.vals;0<t.length&&this.send({keys:e.keys,vals:t})},sendStrategy:function(){if(!1===document.hasFocus())return!1;var e=this.readStore();0<e.keys.length&&0===this.sendingData&&(this.sendingData=1,this.sendPrepare(e))},batchInterval:function(){var e=this;setInterval(function(){e.sendStrategy()},p.para.batch_send.send_interval)},readStore:function(){for(var e=[],t=[],r=null,a=(new Date).getTime(),i=localStorage.length,n=this.getPendingItems(),s=0;s<i;s++){var o=localStorage.key(s);0===o.indexOf("sawebjssdk-")&&/^sawebjssdk\-\d+$/.test(o)&&(n.length&&-1<d.indexOf(n,o)||((r=localStorage.getItem(o))?(r=d.safeJSONParse(r))&&d.isObject(r)?(r._flush_time=a,e.push(o),t.push(r)):(localStorage.removeItem(o),p.log("localStorage-数据parse异常"+r)):(localStorage.removeItem(o),p.log("localStorage-数据取值异常"+r))))}return{keys:e,vals:t}},writeStore:function(e){var t=String(d.getRandom()).slice(2,5)+String(d.getRandom()).slice(2,5)+String((new Date).getTime()).slice(3);localStorage.setItem("sawebjssdk-"+t,JSON.stringify(e))}},p.batchSend=new t;var r={getSendUrl:function(e,t){var r=d.base64Encode(t),t="crc="+d.hashCode(r);return-1!==e.indexOf("?")?e+"&data="+encodeURIComponent(r)+"&ext="+encodeURIComponent(t):e+"?data="+encodeURIComponent(r)+"&ext="+encodeURIComponent(t)},getSendData:function(e){var t=d.base64Encode(e),e="crc="+d.hashCode(t);return"data="+encodeURIComponent(t)+"&ext="+encodeURIComponent(e)},getInstance:function(e){var t=this.getSendType(e),e=new this[t](e),r=e.start;return e.start=function(){d.isObject(p.para.is_debug)&&p.para.is_debug.storage&&p.store.requests&&p.store.requests.push({name:this.server_url,initiatorType:this.img?"img":"xmlhttprequest",entryType:"resource",requestData:this.data});var e=this;r.apply(this,arguments),setTimeout(function(){e.isEnd(!0)},p.para.callback_timeout)},e.end=function(){this.callback&&this.callback();var e=this;setTimeout(function(){e.lastClear&&e.lastClear()},p.para.datasend_timeout-p.para.callback_timeout)},e.isEnd=function(e){var t;this.received||(this.received=!0,this.end(),t=this,!e||p.para.queue_timeout-p.para.callback_timeout<=0?t.close():setTimeout(function(){t.close()},p.para.queue_timeout-p.para.callback_timeout))},e},getRealtimeInstance:function(e){var t=this.getSendType(e),e=new this[t](e),r=e.start;return e.start=function(){var e=this;r.apply(this,arguments),setTimeout(function(){e.isEnd(!0)},p.para.callback_timeout)},e.end=function(){this.callback&&this.callback();var e=this;setTimeout(function(){e.lastClear&&e.lastClear()},p.para.datasend_timeout-p.para.callback_timeout)},e.isEnd=function(e){this.received||(this.received=!0,this.end())},e},getSendType:function(e){var t=["image","ajax","beacon"],r=t[0];return r="ajax"===(r="beacon"===(r=(e.config&&-1<d.indexOf(t,e.config.send_type)?e.config:p.para).send_type)&&!1===d.isSupportBeaconSend()?"image":r)&&!1===d.isSupportCors()?"image":r},image:function(e){this.callback=e.callback,this.img=document.createElement("img"),this.img.width=1,this.img.height=1,p.para.img_use_crossorigin&&(this.img.crossOrigin="anonymous"),this.data=e.data,this.server_url=r.getSendUrl(e.server_url,e.data)}};r.image.prototype.start=function(){var e=this;p.para.ignore_oom&&(this.img.onload=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()},this.img.onerror=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()},this.img.onabort=function(){this.onload=null,this.onerror=null,this.onabort=null,e.isEnd()}),this.img.src=this.server_url},r.image.prototype.lastClear=function(){this.img.src=""},r.ajax=function(e){this.callback=e.callback,this.server_url=e.server_url,this.data=r.getSendData(e.data)},r.ajax.prototype.start=function(){var e=this;d.ajax({url:this.server_url,type:"POST",data:this.data,credentials:!1,timeout:p.para.datasend_timeout,cors:!0,success:function(){e.isEnd()},error:function(){e.isEnd()}})},r.beacon=function(e){this.callback=e.callback,this.server_url=e.server_url,this.data=r.getSendData(e.data)},r.beacon.prototype.start=function(){var e=this;"object"==typeof navigator&&"function"==typeof navigator.sendBeacon&&navigator.sendBeacon(this.server_url,this.data),setTimeout(function(){e.isEnd()},40)};var i={};p.sendState=i,p.events=new d.eventEmitter,i.queue=d.autoExeQueue(),i.getSendCall=function(e,t,r){if(p.is_heatmap_render_mode)return!1;if(p.readyState.state<3)return p.log("初始化没有完成"),!1;e._track_id=Number(String(d.getRandom()).slice(2,5)+String(d.getRandom()).slice(2,4)+String((new Date).getTime()).slice(-4)),p.para.use_client_time&&(e._flush_time=(new Date).getTime());var a=e;e=JSON.stringify(e);t={data:a,config:t,callback:r};if(p.events.tempAdd("send",a),!p.para.app_js_bridge&&p.para.batch_send&&localStorage.length<200)return p.log(a),p.batchSend.add(t.data),!1;p.bridge.dataSend(t,this,r),p.log(a)},i.prepareServerUrl=function(e){if("object"==typeof e.config&&e.config.server_url)this.sendCall(e,e.config.server_url,e.callback);else if(d.isArray(p.para.server_url)&&p.para.server_url.length)for(var t=0;t<p.para.server_url.length;t++)this.sendCall(e,p.para.server_url[t]);else"string"==typeof p.para.server_url&&""!==p.para.server_url?this.sendCall(e,p.para.server_url,e.callback):p.log("当前 server_url 为空或不正确，只在控制台打印日志，network 中不会发数据，请配置正确的 server_url！")},i.sendCall=function(e,t,r){e={server_url:t,data:JSON.stringify(e.data),callback:r,config:e.config};d.isObject(p.para.jsapp)&&!p.para.jsapp.isOnline&&"function"==typeof p.para.jsapp.setData?(delete e.callback,e=JSON.stringify(e),p.para.jsapp.setData(e)):p.para.use_client_time?this.realtimeSend(e):this.pushSend(e)},i.pushSend=function(e){var e=r.getInstance(e),t=this;e.close=function(){t.queue.close()},this.queue.enqueue(e)},i.realtimeSend=function(e){r.getRealtimeInstance(e).start()};var n={};(p.saEvent=n).checkOption={regChecks:{regName:/^((?!^distinct_id$|^original_id$|^time$|^properties$|^id$|^first_id$|^second_id$|^users$|^events$|^event$|^user_id$|^date$|^datetime$)[a-zA-Z_$][a-zA-Z\d_$]{0,99})$/i},checkPropertiesKey:function(e){var r=this,a=!0;return d.each(e,function(e,t){r.regChecks.regName.test(t)||(a=!1)}),a},check:function(e,t){return"string"==typeof this[e]?this[this[e]](t):d.isFunction(this[e])?this[e](t):void 0},str:function(e){return d.isString(e)||p.log("请检查参数格式,必须是字符串"),!0},properties:function(e){return d.strip_sa_properties(e),e&&(d.isObject(e)?this.checkPropertiesKey(e)||p.log("properties 里的自定义属性名需要是合法的变量名，不能以数字开头，且只包含：大小写字母、数字、下划线，自定义属性不能以 $ 开头"):p.log("properties可以没有，但有的话必须是对象")),!0},propertiesMust:function(e){return d.strip_sa_properties(e),void 0===e||!d.isObject(e)||d.isEmptyObject(e)?p.log("properties必须是对象且有值"):this.checkPropertiesKey(e)||p.log("properties 里的自定义属性名需要是合法的变量名，不能以数字开头，且只包含：大小写字母、数字、下划线，自定义属性不能以 $ 开头"),!0},event:function(e){return d.isString(e)&&this.regChecks.regName.test(e)||p.log("请检查参数格式，eventName 必须是字符串，且需是合法的变量名，即不能以数字开头，且只包含：大小写字母、数字、下划线和 $,其中以 $ 开头的表明是系统的保留字段，自定义事件名请不要以 $ 开头"),!0},test_id:"str",group_id:"str",distinct_id:function(e){return!(!d.isString(e)||!/^.{1,255}$/.test(e))||(p.log("distinct_id必须是不能为空，且小于255位的字符串"),!1)}},n.check=function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&!this.checkOption.check(t,e[t]))return!1;return!0},n.send=function(e,t){var r={distinct_id:c.getDistinctId(),lib:{$lib:"js",$lib_method:"code",$lib_version:String(p.lib_version)},properties:{}};d.isObject(e)&&d.isObject(e.properties)&&!d.isEmptyObject(e.properties)&&(e.properties.$lib_detail&&(r.lib.$lib_detail=e.properties.$lib_detail,delete e.properties.$lib_detail),e.properties.$lib_method&&(r.lib.$lib_method=e.properties.$lib_method,delete e.properties.$lib_method)),d.extend(r,p.store.getUnionId(),e),d.isObject(e.properties)&&!d.isEmptyObject(e.properties)&&d.extend(r.properties,e.properties),e.type&&"profile"===e.type.slice(0,7)||(r.properties=d.extend({},d.info.properties(),c.getProps(),c.getSessionProps(),d.info.currentProps,r.properties),p.para.preset_properties.latest_referrer&&!d.isString(r.properties.$latest_referrer)&&(r.properties.$latest_referrer="取值异常"),p.para.preset_properties.latest_search_keyword&&!d.isString(r.properties.$latest_search_keyword)&&(p.para.preset_properties.search_keyword_baidu&&d.isString(r.properties.$search_keyword_id)&&d.isNumber(r.properties.$search_keyword_id_hash)&&d.isString(r.properties.$search_keyword_id_type)||(r.properties.$latest_search_keyword="取值异常")),p.para.preset_properties.latest_traffic_source_type&&!d.isString(r.properties.$latest_traffic_source_type)&&(r.properties.$latest_traffic_source_type="取值异常"),p.para.preset_properties.latest_landing_page&&!d.isString(r.properties.$latest_landing_page)&&(r.properties.$latest_landing_page="取值异常"),"not_collect"===p.para.preset_properties.latest_wx_ad_click_id?(delete r.properties._latest_wx_ad_click_id,delete r.properties._latest_wx_ad_hash_key,delete r.properties._latest_wx_ad_callbacks):p.para.preset_properties.latest_wx_ad_click_id&&!d.isString(r.properties._latest_wx_ad_click_id)&&(r.properties._latest_wx_ad_click_id="取值异常",r.properties._latest_wx_ad_hash_key="取值异常",r.properties._latest_wx_ad_callbacks="取值异常"),d.isString(r.properties._latest_wx_ad_click_id)&&(r.properties.$url=d.isDecodeURI(p.para.url_is_decode,window.location.href))),r.properties.$time&&d.isDate(r.properties.$time)?(r.time=+r.properties.$time,delete r.properties.$time):p.para.use_client_time&&(r.time=+new Date);e=p.vtrackcollect.customProp.getVtrackProps(JSON.parse(JSON.stringify(r)));d.isObject(e)&&!d.isEmptyObject(e)&&(r.properties=d.extend(r.properties,e)),p.para.firefly_system_number||0===p.para.firefly_system_number?r.properties=d.extend(r.properties,{firefly_system_number:p.para.firefly_system_number}):(console.error("系统编号未配置成功"),r.properties=d.extend(r.properties,{firefly_system_number:""})),!p.para.firefly_apc_type&&0!==p.para.firefly_apc_type||!p.para.firefly_model_name?(console.error("渠道类型或模块名称信息未初始化"),r.properties=d.extend(r.properties,{firefly_apc_type:"",firefly_model_name:""})):/^\d+$/.test(p.para.firefly_apc_type)&&0<p.para.firefly_apc_type?r.properties=d.extend(r.properties,{firefly_apc_type:p.para.firefly_apc_type,firefly_model_name:p.para.firefly_model_name}):(console.error("H5采集插件渠道类型有误，未在日志中生成渠道信息"),r.properties=d.extend(r.properties,{firefly_apc_type:"",firefly_model_name:p.para.firefly_model_name})),d.parseSuperProperties(r),d.filterReservedProperties(r.properties),d.searchObjDate(r),d.searchObjString(r),d.searchZZAppStyle(r);e=d.searchConfigData(r.properties);l.checkIsAddSign(r),l.checkIsFirstTime(r),p.addReferrerHost(r),p.addPropsHook(r),!0===p.para.debug_mode?(p.log(r),this.debugPath(JSON.stringify(r),t)):p.sendState.getSendCall(r,e,t)},n.debugPath=function(e,t){var r=e,a="",a=-1!==p.para.debug_mode_url.indexOf("?")?p.para.debug_mode_url+"&data="+encodeURIComponent(d.base64Encode(e)):p.para.debug_mode_url+"?data="+encodeURIComponent(d.base64Encode(e));d.ajax({url:a,type:"GET",cors:!0,header:{"Dry-Run":String(p.para.debug_mode_upload)},success:function(e){!0===d.isEmptyObject(e)?alert("debug数据发送成功"+r):alert("debug失败 错误原因"+JSON.stringify(e))}})};var c=p.store={requests:[],_sessionState:{},_state:{distinct_id:"",first_id:"",props:{}},getProps:function(){return this._state.props||{}},getSessionProps:function(){return this._sessionState},getDistinctId:function(){return this._state._distinct_id||this._state.distinct_id},getUnionId:function(){var e={},t=this._state._first_id||this._state.first_id,r=this._state._distinct_id||this._state.distinct_id;return t&&r?(e.login_id=r,e.anonymous_id=t):e.anonymous_id=r,e},getFirstId:function(){return this._state._first_id||this._state.first_id},toState:function(e){var t=null;if(null!=e&&d.isJSONString(e))if(t=JSON.parse(e),this._state=d.extend(t),t.distinct_id){if("object"==typeof t.props){for(var r in t.props)"string"==typeof t.props[r]&&(t.props[r]=t.props[r].slice(0,p.para.max_referrer_string_length));this.save()}}else this.set("distinct_id",d.UUID()),p.debug.distinct_id("1",e);else this.set("distinct_id",d.UUID()),p.debug.distinct_id("2",e)},initSessionState:function(){var e=d.cookie.get("sensorsdata2015session"),t=null;null!==e&&"object"==typeof(t=JSON.parse(e))&&(this._sessionState=t||{})},setOnce:function(e,t){e in this._state||this.set(e,t)},set:function(e,t){this._state=this._state||{};var r=this._state.distinct_id;this._state[e]=t,"first_id"===e?delete this._state._first_id:"distinct_id"===e&&delete this._state._distinct_id,this.save(),"distinct_id"===e&&r&&p.events.tempAdd("changeDistinctId",t)},change:function(e,t){this._state["_"+e]=t},setSessionProps:function(e){var t=this._sessionState;d.extend(t,e),this.sessionSave(t)},setSessionPropsOnce:function(e){var t=this._sessionState;d.coverExtend(t,e),this.sessionSave(t)},setProps:function(e,t){var r,a={};for(r in a=t?e:d.extend(this._state.props||{},e))"string"==typeof a[r]&&(a[r]=a[r].slice(0,p.para.max_referrer_string_length));this.set("props",a)},setPropsOnce:function(e){var t=this._state.props||{};d.coverExtend(t,e),this.set("props",t)},clearAllProps:function(e){if(this._sessionState={},d.isArray(e)&&0<e.length)for(var t=0;t<e.length;t++)d.isString(e[t])&&-1===e[t].indexOf("latest_")&&e[t]in this._state.props&&delete this._state.props[e[t]];else for(var t in this._state.props)1!==t.indexOf("latest_")&&delete this._state.props[t];this.sessionSave({}),this.save()},sessionSave:function(e){this._sessionState=e,d.cookie.set("sensorsdata2015session",JSON.stringify(this._sessionState),0)},save:function(){var e=JSON.parse(JSON.stringify(this._state));delete e._first_id,delete e._distinct_id;e=JSON.stringify(e);p.para.encrypt_cookie&&(e=d.cookie.encrypt(e)),d.cookie.set(this.getCookieName(),e,73e3,p.para.cross_subdomain)},getCookieName:function(){var e="";if(!1===p.para.cross_subdomain){try{e=d.URL(location.href).hostname}catch(e){p.log(e)}e="string"==typeof e&&""!==e?"sa_jssdk_2015_"+e.replace(/\./g,"_"):"sa_jssdk_2015_root"}else e="sensorsdata2015jssdkcross";return e},init:function(){this.initSessionState();var e=d.UUID(),t=d.cookie.get(this.getCookieName());null===(t=d.cookie.resolveValue(t))?(p.is_first_visitor=!0,this.set("distinct_id",e)):(d.isJSONString(t)&&JSON.parse(t).distinct_id||(p.is_first_visitor=!0),this.toState(t)),l.setDeviceId(e),l.storeInitCheck(),l.checkIsFirstLatest()}},l={checkIsAddSign:function(e){"track"===e.type&&(d.cookie.getNewUser()?e.properties.$is_first_day=!0:e.properties.$is_first_day=!1)},is_first_visit_time:!1,checkIsFirstTime:function(e){"track"===e.type&&"$pageview"===e.event&&(this.is_first_visit_time?(e.properties.$is_first_time=!0,this.is_first_visit_time=!1):e.properties.$is_first_time=!1)},setDeviceId:function(e){var t=null,r=d.cookie.get("sensorsdata2015jssdkcross"),a={},t=(t=null!=(r=d.cookie.resolveValue(r))&&d.isJSONString(r)&&(a=JSON.parse(r)).$device_id?a.$device_id:t)||e;!0===p.para.cross_subdomain?c.set("$device_id",t):(a.$device_id=t,a=JSON.stringify(a),p.para.encrypt_cookie&&(a=d.cookie.encrypt(a)),d.cookie.set("sensorsdata2015jssdkcross",a,null,!0)),p.para.is_track_device_id&&(d.info.currentProps.$device_id=t)},storeInitCheck:function(){var e,t,r;p.is_first_visitor?(e=23-(r=new Date).getHours(),t=59-r.getMinutes(),r=59-r.getSeconds(),d.cookie.set(d.cookie.getCookieName("new_user"),"1",3600*e+60*t+r+"s"),this.is_first_visit_time=!0):(d.cookie.getNewUser()||(this.checkIsAddSign=function(e){"track"===e.type&&(e.properties.$is_first_day=!1)}),this.checkIsFirstTime=function(e){"track"===e.type&&"$pageview"===e.event&&(e.properties.$is_first_time=!1)})},checkIsFirstLatest:function(){var i=d.info.pageProp.url_domain,n={};""===i&&(i="url解析失败");var e,t=d.getKeywordFromReferrer(document.referrer,!0);p.para.preset_properties.search_keyword_baidu?d.isReferralTraffic(document.referrer)&&(!d.isBaiduTraffic()||d.isObject(t)&&t.active?(p.store._state.props.$search_keyword_id&&delete p.store._state.props.$search_keyword_id,p.store._state.props.$search_keyword_id_type&&delete p.store._state.props.$search_keyword_id_type,p.store._state.props.$search_keyword_id_hash&&delete p.store._state.props.$search_keyword_id_hash):(n.$search_keyword_id=d.getBaiduKeyword.id(),n.$search_keyword_id_type=d.getBaiduKeyword.type(),n.$search_keyword_id_hash=d.hashCode(n.$search_keyword_id))):(p.store._state.props.$search_keyword_id&&delete p.store._state.props.$search_keyword_id,p.store._state.props.$search_keyword_id_type&&delete p.store._state.props.$search_keyword_id_type,p.store._state.props.$search_keyword_id_hash&&delete p.store._state.props.$search_keyword_id_hash),p.store.save(),d.each(p.para.preset_properties,function(e,t){if(-1===t.indexOf("latest_"))return!1;if(t=t.slice(7),e){if("wx_ad_click_id"===t&&"not_collect"===e)return!1;if("utm"!==t&&"url解析失败"===i)"wx_ad_click_id"===t?(n._latest_wx_ad_click_id="url的domain解析失败",n._latest_wx_ad_hash_key="url的domain解析失败",n._latest_wx_ad_callbacks="url的domain解析失败"):n["$latest_"+t]="url的domain解析失败";else if(d.isReferralTraffic(document.referrer))switch(t){case"traffic_source_type":n.$latest_traffic_source_type=d.getSourceFromReferrer();break;case"referrer":n.$latest_referrer=d.isDecodeURI(p.para.url_is_decode,d.info.pageProp.referrer);break;case"search_keyword":d.getKeywordFromReferrer()?n.$latest_search_keyword=d.getKeywordFromReferrer():d.isObject(p.store._state.props)&&p.store._state.props.$latest_search_keyword&&delete p.store._state.props.$latest_search_keyword;break;case"landing_page":n.$latest_landing_page=d.isDecodeURI(p.para.url_is_decode,location.href);break;case"wx_ad_click_id":var r=d.getWxAdIdFromUrl(location.href);n._latest_wx_ad_click_id=r.click_id,n._latest_wx_ad_hash_key=r.hash_key,n._latest_wx_ad_callbacks=r.callbacks}}else if("utm"===t&&p.store._state.props)for(var a in p.store._state.props)(0===a.indexOf("$latest_utm")||0===a.indexOf("_latest_")&&a.indexOf("_latest_wx_ad_")<0)&&delete p.store._state.props[a];else p.store._state.props&&"$latest_"+t in p.store._state.props?delete p.store._state.props["$latest_"+t]:"wx_ad_click_id"==t&&p.store._state.props&&!1===e&&d.each(["_latest_wx_ad_click_id","_latest_wx_ad_hash_key","_latest_wx_ad_callbacks"],function(e){e in p.store._state.props&&delete p.store._state.props[e]})}),p.register(n),p.para.preset_properties.latest_utm&&(t=(e=d.info.campaignParamsStandard("$latest_","_latest_")).$utms,e=e.otherUtms,d.isEmptyObject(t)||p.register(t),d.isEmptyObject(e)||p.register(e))}};p.bridge={is_verify_success:!1,initPara:function(){var e={is_send:!1,white_list:[],is_mui:!1};"object"==typeof p.para.app_js_bridge?p.para.app_js_bridge=d.extend({},e,p.para.app_js_bridge):!0===p.para.use_app_track||!0===p.para.app_js_bridge||"only"===p.para.use_app_track?(!0===p.para.use_app_track_is_send&&(e.is_send=!0),p.para.app_js_bridge=d.extend({},e)):"mui"===p.para.use_app_track&&(e.is_mui=!0,p.para.app_js_bridge=d.extend({},e)),!1===p.para.app_js_bridge.is_send&&p.log("设置了 is_send:false,如果打通失败，数据将被丢弃！")},initState:function(){function e(e){function t(e){var t={hostname:"",project:""};try{t.hostname=d.URL(e).hostname,t.project=d.URL(e).searchParams.get("project")||"default"}catch(e){p.log(e)}return t}var r=t(e),e=t(p.para.server_url);if(r.hostname===e.hostname&&r.project===e.project)return 1;if(0<p.para.app_js_bridge.white_list.length)for(var a=0;a<p.para.app_js_bridge.white_list.length;a++){var i=t(p.para.app_js_bridge.white_list[a]);if(i.hostname===r.hostname&&i.project===r.project)return 1}}var t;d.isObject(p.para.app_js_bridge)&&!p.para.app_js_bridge.is_mui&&(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&d.isObject(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url?e(window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url)&&(p.bridge.is_verify_success=!0):d.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track&&((t=window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url())&&e(t)&&(p.bridge.is_verify_success=!0)))},initDefineBridgeInfo:function(){var e={touch_app_bridge:!0,verify_success:!1};return window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&d.isObject(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url||d.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track?p.bridge.is_verify_success?e.verify_success="success":e.verify_success="fail":"object"==typeof SensorsData_APP_JS_Bridge&&(SensorsData_APP_JS_Bridge.sensorsdata_verify&&SensorsData_APP_JS_Bridge.sensorsdata_visual_verify||SensorsData_APP_JS_Bridge.sensorsdata_track)?!SensorsData_APP_JS_Bridge.sensorsdata_verify||!SensorsData_APP_JS_Bridge.sensorsdata_visual_verify||SensorsData_APP_JS_Bridge.sensorsdata_visual_verify(JSON.stringify({server_url:p.para.server_url}))?e.verify_success="success":e.verify_success="fail":!/sensors-verify/.test(navigator.userAgent)&&!/sa-sdk-ios/.test(navigator.userAgent)||window.MSStream?e.touch_app_bridge=!1:p.bridge.iOS_UA_bridge()?e.verify_success="success":e.verify_success="fail",e},iOS_UA_bridge:function(){if(/sensors-verify/.test(navigator.userAgent)){if((e=navigator.userAgent.match(/sensors-verify\/([^\s]+)/))&&e[0]&&"string"==typeof e[1]&&2===e[1].split("?").length){var e=e[1].split("?"),t=null,r=null;try{t=d.URL(p.para.server_url).hostname,r=d.URL(p.para.server_url).searchParams.get("project")||"default"}catch(e){p.log(e)}return!(!t||t!==e[0]||!r||r!==e[1])}return!1}return!!/sa-sdk-ios/.test(navigator.userAgent)},dataSend:function(e,t,r){var a,i,n,s=e.data;d.isObject(p.para.app_js_bridge)&&!p.para.app_js_bridge.is_mui?window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage&&d.isObject(window.SensorsData_iOS_JS_Bridge)&&window.SensorsData_iOS_JS_Bridge.sensorsdata_app_server_url?p.bridge.is_verify_success?(window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify({callType:"app_h5_track",data:d.extend({server_url:p.para.server_url},s)})),"function"==typeof r&&r()):p.para.app_js_bridge.is_send?(p.debug.apph5({data:s,step:"4.1",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():d.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_get_server_url&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_track?p.bridge.is_verify_success?(SensorsData_APP_New_H5_Bridge.sensorsdata_track(JSON.stringify(d.extend({server_url:p.para.server_url},s))),"function"==typeof r&&r()):p.para.app_js_bridge.is_send?(p.debug.apph5({data:s,step:"4.2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():"object"==typeof SensorsData_APP_JS_Bridge&&(SensorsData_APP_JS_Bridge.sensorsdata_verify||SensorsData_APP_JS_Bridge.sensorsdata_track)?SensorsData_APP_JS_Bridge.sensorsdata_verify?!SensorsData_APP_JS_Bridge.sensorsdata_verify(JSON.stringify(d.extend({server_url:p.para.server_url},s)))&&p.para.app_js_bridge.is_send?(p.debug.apph5({data:s,step:"3.1",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():(SensorsData_APP_JS_Bridge.sensorsdata_track(JSON.stringify(d.extend({server_url:p.para.server_url},s))),"function"==typeof r&&r()):!/sensors-verify/.test(navigator.userAgent)&&!/sa-sdk-ios/.test(navigator.userAgent)||window.MSStream?d.isObject(p.para.app_js_bridge)&&!0===p.para.app_js_bridge.is_send?(p.debug.apph5({data:s,step:"2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r():(a=null,p.bridge.iOS_UA_bridge()?(a=document.createElement("iframe"),n=s,n=(n=JSON.stringify(d.extend({server_url:p.para.server_url},n))).replaceAll(/\r\n/g,""),i="sensorsanalytics://trackEvent?event="+(n=encodeURIComponent(n)),a.setAttribute("src",i),document.documentElement.appendChild(a),a.parentNode.removeChild(a),a=null,"function"==typeof r&&r()):p.para.app_js_bridge.is_send?(p.debug.apph5({data:s,step:"3.2",output:"all"}),t.prepareServerUrl(e)):"function"==typeof r&&r()):d.isObject(p.para.app_js_bridge)&&p.para.app_js_bridge.is_mui?d.isObject(window.plus)&&window.plus.SDAnalytics&&window.plus.SDAnalytics.trackH5Event?(window.plus.SDAnalytics.trackH5Event(data),"function"==typeof r&&r()):d.isObject(p.para.app_js_bridge)&&!0===p.para.app_js_bridge.is_send?t.prepareServerUrl(e):"function"==typeof r&&r():(p.debug.apph5({data:s,step:"1",output:"code"}),t.prepareServerUrl(e))},app_js_bridge_v1:function(){var r=null,a=null;window.sensorsdata_app_js_bridge_call_js=function(e){r=e,d.isJSONString(r)&&(r=JSON.parse(r)),a&&(a(r),r=a=null)},p.getAppStatus=function(e){var t;if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&((t=document.createElement("iframe")).setAttribute("src","sensorsanalytics://getAppInfo"),document.documentElement.appendChild(t),t.parentNode.removeChild(t)),"object"==typeof window.SensorsData_APP_JS_Bridge&&window.SensorsData_APP_JS_Bridge.sensorsdata_call_app&&(r=SensorsData_APP_JS_Bridge.sensorsdata_call_app(),d.isJSONString(r)&&(r=JSON.parse(r))),!e)return r;null===r?a=e:(e(r),r=null)}},supportAppCallJs:function(){window.sensorsdata_app_call_js=function(e,t){e in window.sensorsdata_app_call_js.modules&&window.sensorsdata_app_call_js.modules[e](t)},window.sensorsdata_app_call_js.modules={}}},p.JSBridge=function(e){this.list={},this.type=e.type,this.app_call_js=d.isFunction(e.app_call_js)?e.app_call_js:function(){},this.init()},p.JSBridge.prototype.init=function(){var t=this;window.sensorsdata_app_call_js.modules[this.type]||(window.sensorsdata_app_call_js.modules[this.type]=function(e){t.app_call_js(e)})},p.JSBridge.prototype.jsCallApp=function(e){e={callType:this.type,data:e};if(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage)window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(e));else{if(!d.isObject(window.SensorsData_APP_New_H5_Bridge)||!window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)return p.log("数据发往App失败，App没有暴露bridge"),!1;window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(JSON.stringify(e))}},p.JSBridge.prototype.hasAppBridge=function(){return window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage?"ios":d.isObject(window.SensorsData_APP_New_H5_Bridge)&&window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app?"android":(p.log("App端bridge未暴露"),!1)},p.JSBridge.prototype.requestToApp=function(e){var t=this,r=d.isObject(e.data)?e.data:{};d.isFunction(e.callback)||(e.callback=function(){}),d.isObject(e.timeout)&&d.isNumber(e.timeout.time)&&(d.isFunction(e.timeout.callback)||(e.timeout.callback=function(){}),e.timer=setTimeout(function(){e.timeout.callback(),delete t.list[a]},e.timeout.time));var a=(new Date).getTime().toString(16)+"-"+String(d.getRandom()).replace(".","").slice(1,8);this.list[a]=e;r={callType:this.type,data:r};if(r.data.message_id=a,window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.sensorsdataNativeTracker&&window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage)window.webkit.messageHandlers.sensorsdataNativeTracker.postMessage(JSON.stringify(r));else{if(!d.isObject(window.SensorsData_APP_New_H5_Bridge)||!window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app)return p.log("数据发往App失败，App没有暴露bridge"),!1;window.SensorsData_APP_New_H5_Bridge.sensorsdata_js_call_app(JSON.stringify(r))}},p.JSBridge.prototype.double=function(e){var t;!e.message_id||(t=this.list[e.message_id])&&(t.timer&&clearTimeout(t.timer),t.callback(e),delete this.list[e.message_id])};var u=p.heatmap={otherTags:[],getTargetElement:function(e,t){var r=this,a=e;if("object"!=typeof a)return null;if("string"!=typeof a.tagName)return null;var i=a.tagName.toLowerCase();if("body"===i.toLowerCase()||"html"===i.toLowerCase())return null;if(!a||!a.parentNode||!a.parentNode.children)return null;var n=a.parentNode,s=r.hasElement(t.originalEvent||t),o=p.para.heatmap.track_attr,e=r.otherTags;if("a"===i||"button"===i||"input"===i||"textarea"===i||d.hasAttributes(a,o))return a;if(-1<d.indexOf(e,i))return a;if(r.canCollect(t))return r.canCollect(t);if("area"===i&&"map"===n.tagName.toLowerCase()&&d.ry(n).prev().tagName&&"img"===d.ry(n).prev().tagName.toLowerCase())return d.ry(n).prev();if(s)return s;if("div"===i&&p.para.heatmap.collect_tags.div&&r.isDivLevelValid(a))return 1<(p.para.heatmap&&p.para.heatmap.collect_tags&&p.para.heatmap.collect_tags.div&&p.para.heatmap.collect_tags.div.max_level||1)||r.isCollectableDiv(a)?a:null;if(r.isStyleTag(i)&&p.para.heatmap.collect_tags.div){a=r.getCollectableParent(a);if(a&&r.isDivLevelValid(a))return a}return null},getDivLevels:function(e,t){var t=u.getElementPath(e,!0,t).split(" > "),r=0;return d.each(t,function(e){"div"===e&&r++}),r},isDivLevelValid:function(e){for(var t=p.para.heatmap&&p.para.heatmap.collect_tags&&p.para.heatmap.collect_tags.div&&p.para.heatmap.collect_tags.div.max_level||1,r=e.getElementsByTagName("div"),a=r.length-1;0<=a;a--)if(u.getDivLevels(r[a],e)>t)return!1;return!0},getElementPath:function(e,t,r){for(var a=[];e.parentNode;){if(e.id&&!t&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.id)){a.unshift(e.tagName.toLowerCase()+"#"+e.id);break}if(r&&e===r){a.unshift(e.tagName.toLowerCase());break}if(e===document.body){a.unshift("body");break}a.unshift(e.tagName.toLowerCase()),e=e.parentNode}return a.join(" > ")},getClosestLi:function(e){return function(e,t){for(;e&&e!==document&&1===e.nodeType;e=e.parentNode)if(e.tagName.toLowerCase()===t)return e;return null}(e,"li")},getElementPosition:function(e,t,r){var a=p.heatmap.getClosestLi(e);if(!a)return null;var i=e.tagName.toLowerCase(),n=a.getElementsByTagName(i),s=n.length,o=[];if(1<s){for(var c=0;c<s;c++)p.heatmap.getElementPath(n[c],r)===t&&o.push(n[c]);if(1<o.length)return d.indexOf(o,e)}return function(e){if(e.tagName.toLowerCase(),!e.parentNode)return"";if(1===d.ry(e).getSameTypeSiblings().length)return 0;for(var t=0,r=e;d.ry(r).previousElementSibling().ele;r=d.ry(r).previousElementSibling().ele,t++);return t}(a)},setNotice:function(e){p.is_heatmap_render_mode=!0,p.para.heatmap||(p.errorMsg="您SDK没有配置开启点击图，可能没有数据！"),e&&e[0]&&e[1]&&"http:"===e[1].slice(0,5)&&"https:"===location.protocol&&(p.errorMsg="您的当前页面是https的地址，神策分析环境也必须是https！"),p.para.heatmap_url||(p.para.heatmap_url=location.protocol+"//static.sensorsdata.cn/sdk/"+p.lib_version+"/heatmap.min.js")},getDomIndex:function(e){if(!e.parentNode)return-1;for(var t=0,r=e.tagName,a=e.parentNode.children,i=0;i<a.length;i++)if(a[i].tagName===r){if(e===a[i])return t;t++}return-1},selector:function(e,t){var r=e.parentNode&&9==e.parentNode.nodeType?-1:this.getDomIndex(e);return e.getAttribute&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))&&(!p.para.heatmap||p.para.heatmap&&"not_use_id"!==p.para.heatmap.element_selector)&&!t?"#"+e.getAttribute("id"):e.tagName.toLowerCase()+(~r?":nth-of-type("+(r+1)+")":"")},getDomSelector:function(e,t,r){if(!e||!e.parentNode||!e.parentNode.children)return!1;t=t&&t.join?t:[];var a=e.nodeName.toLowerCase();return e&&"body"!==a&&1==e.nodeType?(t.unshift(this.selector(e,r)),e.getAttribute&&e.getAttribute("id")&&/^[A-Za-z][-A-Za-z0-9_:.]*$/.test(e.getAttribute("id"))&&p.para.heatmap&&"not_use_id"!==p.para.heatmap.element_selector&&!r?t.join(" > "):this.getDomSelector(e.parentNode,t,r)):(t.unshift("body"),t.join(" > "))},na:function(){var e=document.documentElement.scrollLeft||window.pageXOffset;return parseInt(isNaN(e)?0:e,10)},i:function(){var t=0;try{t=o.documentElement&&o.documentElement.scrollTop||m.pageYOffset,t=isNaN(t)?0:t}catch(e){t=0}return parseInt(t,10)},getBrowserWidth:function(){var e=window.innerWidth||document.body.clientWidth;return isNaN(e)?0:parseInt(e,10)},getBrowserHeight:function(){var e=window.innerHeight||document.body.clientHeight;return isNaN(e)?0:parseInt(e,10)},getScrollWidth:function(){var e=parseInt(document.body.scrollWidth,10);return isNaN(e)?0:e},W:function(e){var t=parseInt(+e.clientX+Number(this.na()),10),e=parseInt(+e.clientY+Number(this.i()),10);return{x:isNaN(t)?0:t,y:isNaN(e)?0:e}},getEleDetail:function(e){var t=this.getDomSelector(e),r=d.getEleInfo({target:e});r.$element_selector=t||"",r.$element_path=p.heatmap.getElementPath(e,p.para.heatmap&&"not_use_id"===p.para.heatmap.element_selector);e=p.heatmap.getElementPosition(e,r.$element_path,p.para.heatmap&&"not_use_id"===p.para.heatmap.element_selector);return d.isNumber(e)&&(r.$element_position=e),r},start:function(e,t,r,a,i){var n=d.isObject(a)?a:{},s=d.isFunction(i)?i:d.isFunction(a)?a:void 0;if(p.para.heatmap&&p.para.heatmap.collect_element&&!p.para.heatmap.collect_element(t))return!1;i=this.getEleDetail(t);p.para.heatmap&&p.para.heatmap.custom_property&&(a=p.para.heatmap.custom_property(t),d.isObject(a)&&(i=d.extend(i,a))),i=d.extend(i,n),"a"===r&&p.para.heatmap&&!0===p.para.heatmap.isTrackLink?d.trackLink({event:e,target:t},"$WebClick",i):p.track("$WebClick",i,s)},hasElement:function(e){var t=e._getPath?e._getPath():u.getElementPath(e.target,!0).split(" > ");if(d.isArray(t)&&0<t.length)for(var r=0;r<t.length;r++)if(t[r]&&t[r].tagName&&"a"===t[r].tagName.toLowerCase())return t[r];return!1},canCollect:function(e){var t=e._getPath?e._getPath():u.getElementPath(e.target,!0).split(" > ");if(d.isArray(t)&&0<t.length)for(var r=0;r<t.length;r++)if(t[r]&&t[r].tagName){if("button"===t[r].tagName.toLowerCase())return!t[r].disabled&&t[r];if("a"===t[r].tagName.toLowerCase()||d.hasAttributes(t[r],p.para.heatmap.track_attr))return t[r]}return!1},isStyleTag:function(e,t){return!(-1<d.indexOf(["a","div","input","button","textarea"],e))&&(!t||p.para.heatmap&&p.para.heatmap.collect_tags&&p.para.heatmap.collect_tags.div?!!(d.isObject(p.para.heatmap)&&d.isObject(p.para.heatmap.collect_tags)&&d.isObject(p.para.heatmap.collect_tags.div)&&-1<d.indexOf(p.para.heatmap.collect_tags.div.ignore_tags,e)):-1<d.indexOf(["mark","/mark","strong","b","em","i","u","abbr","ins","del","s","sup"],e))},isCollectableDiv:function(e,t){try{if(0===e.children.length)return!0;for(var r=0;r<e.children.length;r++)if(1===e.children[r].nodeType){var a=e.children[r].tagName.toLowerCase(),i=p.para&&p.para.heatmap&&p.para.heatmap.collect_tags&&p.para.heatmap.collect_tags.div&&p.para.heatmap.collect_tags.div.max_level;if(!("div"===a&&1<i||this.isStyleTag(a,t)))return!1;if(!this.isCollectableDiv(e.children[r],t))return!1}return!0}catch(e){p.log(e)}return!1},getCollectableParent:function(e,t){try{var r=e.parentNode,a=r?r.tagName.toLowerCase():"";if("body"===a)return!1;var i=p.para&&p.para.heatmap&&p.para.heatmap.collect_tags&&p.para.heatmap.collect_tags.div&&p.para.heatmap.collect_tags.div.max_level;if(a&&"div"===a&&(1<i||this.isCollectableDiv(r,t)))return r;if(r&&this.isStyleTag(a,t))return this.getCollectableParent(r,t)}catch(e){p.log(e)}return!1},initScrollmap:function(){if(!d.isObject(p.para.heatmap)||"default"!==p.para.heatmap.scroll_notice_map)return!1;function e(){return!(p.para.scrollmap&&d.isFunction(p.para.scrollmap.collect_url)&&!p.para.scrollmap.collect_url())}var t,r,a=((r={}).timeout=(t={timeout:1e3,func:function(e,t){var r=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,a=new Date,i=a-this.current_time;(i>p.para.heatmap.scroll_delay_time&&r-e.$viewport_position!=0||t)&&(e.$url=d.isDecodeURI(p.para.url_is_decode,location.href),e.$title=document.title,e.$url_path=location.pathname,e.event_duration=Math.min(p.para.heatmap.scroll_event_duration,parseInt(i)/1e3),p.track("$WebStay",e)),this.current_time=a}}).timeout||1e3,r.func=t.func,r.hasInit=!1,r.inter=null,r.main=function(e,t){this.func(e,t),this.inter=null},r.go=function(e){var t={};this.inter||(t.$viewport_position=document.documentElement&&document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop||0,t.$viewport_position=Math.round(t.$viewport_position)||0,t.$viewport_height=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0,t.$viewport_width=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0,e?r.main(t,!0):this.inter=setTimeout(function(){r.main(t)},this.timeout))},r);a.current_time=new Date,d.addEvent(window,"scroll",function(){return!!e()&&void a.go()}),d.addEvent(window,"unload",function(){return!!e()&&void a.go("notime")})},initHeatmap:function(){var i=this;return!(!d.isObject(p.para.heatmap)||"default"!==p.para.heatmap.clickmap)&&(!(d.isFunction(p.para.heatmap.collect_url)&&!p.para.heatmap.collect_url())&&("all"===p.para.heatmap.collect_elements?p.para.heatmap.collect_elements="all":p.para.heatmap.collect_elements="interact",void("all"===p.para.heatmap.collect_elements?d.addEvent(document,"click",function(e){var t=e||window.event;if(!t)return!1;var r=t.target||t.srcElement;if("object"!=typeof r)return!1;if("string"!=typeof r.tagName)return!1;var a=r.tagName.toLowerCase();if("body"===a||"html"===a)return!1;if(!r||!r.parentNode||!r.parentNode.children)return!1;e=r.parentNode.tagName.toLowerCase();"a"===e||"button"===e?i.start(t,r.parentNode,e):i.start(t,r,a)}):d.addEvent(document,"click",function(e){var t=e||window.event;if(!t)return!1;var r=t.target||t.srcElement,e=p.heatmap.getTargetElement(r,e);e?i.start(t,e,e.tagName.toLowerCase()):d.isElement(r)&&"div"===r.tagName.toLowerCase()&&d.isObject(p.para.heatmap)&&p.para.heatmap.get_vtrack_config&&0<p.unlimitedDiv.events.length&&p.unlimitedDiv.isTargetEle(r)&&i.start(t,r,r.tagName.toLowerCase(),{$lib_method:"vtrack"})}))))}};p.unlimitedDiv={events:[],init:function(){this.filterWebClickEvents()},filterWebClickEvents:function(e){this.events=p.vtrackcollect.getAssignConfigs(function(e){return!(!d.isObject(e)||!0!==e.event.unlimited_div||"webclick"!==e.event_type)},e)},isTargetEle:function(e){var t=p.heatmap.getEleDetail(e);if(!d.isObject(t)||!d.isString(t.$element_path))return!1;for(var r=0;r<this.events.length;r++)if(d.isObject(this.events[r])&&d.isObject(this.events[r].event)&&p.vtrackcollect.configIsMatch(t,this.events[r].event))return!0;return!1}},p.customProp={events:[],configSwitch:!1,collectAble:function(){return this.configSwitch&&d.isObject(p.para.heatmap)&&p.para.heatmap.get_vtrack_config},updateEvents:function(){this.events=p.vtrackcollect.getAssignConfigs(function(e){return!!(d.isObject(e)&&d.isArray(e.properties)&&0<e.properties.length)}),this.events.length?this.configSwitch=!0:this.configSwitch=!1},getVtrackProps:function(e){var t={};return this.collectAble()?"clickmap"===e.event?this.clickCustomPropMaker(e,this.events):t:{}},clickCustomPropMaker:function(t,e,r){var a=this,r=r||this.filterConfig(t,e,p.vtrackcollect.url_info.page_url),i={};return r.length?(d.each(r,function(e){d.isArray(e.properties)&&0<e.properties.length&&d.each(e.properties,function(e){e=a.getProp(e,t);d.isObject(e)&&d.extend(i,e)})}),i):{}},getProp:function(t,e){if(!d.isObject(t))return!1;if(!(d.isString(t.name)&&0<t.name.length))return p.log("----vtrackcustom----属性名不合法,属性抛弃",t.name),!1;var r,a,i={};if("content"!==t.method)return p.log("----vtrackcustom----属性不支持此获取方式",t.name,t.method),!1;if(d.isString(t.element_selector)&&0<t.element_selector.length)s=d.getDomBySelector(t.element_selector);else{if(!d.isString(t.list_selector))return p.log("----vtrackcustom----属性配置异常，属性抛弃",t.name),!1;var n=d.getDomBySelector(e.properties.$element_selector);if(!n)return p.log("----vtrackcustom----点击元素获取异常，属性抛弃",t.name),!1;var n=p.heatmap.getClosestLi(n),s=this.getPropElInLi(n,t.list_selector)}if(!s||!d.isElement(s))return p.log("----vtrackcustom----属性元素获取失败，属性抛弃",t.name),!1;if("input"===s.tagName.toLowerCase()?r=s.value||"":"select"===s.tagName.toLowerCase()?(n=s.selectedIndex,d.isNumber(n)&&d.isElement(s[n])&&(r=p._.getElementContent(s[n],"select"))):r=d.getElementContent(s,s.tagName.toLowerCase()),t.regular){try{a=new RegExp(t.regular).exec(r)}catch(e){return p.log("----vtrackcustom----正则处理失败，属性抛弃",t.name),!1}if(null===a)return p.log("----vtrackcustom----属性规则处理，未匹配到结果,属性抛弃",t.name),!1;if(!d.isArray(a)||!d.isString(a[0]))return p.log("----vtrackcustom----正则处理异常，属性抛弃",t.name,a),!1;r=a[0]}if("STRING"===t.type)i[t.name]=r;else if("NUMBER"===t.type){if(r.length<1)return p.log("----vtrackcustom----未获取到数字内容，属性抛弃",t.name,r),!1;if(isNaN(Number(r)))return p.log("----vtrackcustom----数字类型属性转换失败，属性抛弃",t.name,r),!1;i[t.name]=Number(r)}return i},getPropElInLi:function(e,t){if(!(e&&d.isElement(e)&&d.isString(t)))return null;if("li"!==e.tagName.toLowerCase())return null;var r=p.heatmap.getDomSelector(e);if(r){e=d.getDomBySelector(r+t);return e||null}return p.log("----vtrackcustom---获取同级属性元素失败，selector信息异常",r,t),null},filterConfig:function(r,e,a){var i=[];if(!a){var t=p.vtrackcollect.initUrl();if(!t)return[];a=t.page_url}return"$WebClick"===r.event&&d.each(e,function(e,t){d.isObject(e)&&"webclick"===e.event_type&&d.isObject(e.event)&&e.event.url_host===a.host&&e.event.url_path===a.pathname&&p.vtrackcollect.configIsMatch(r.properties,e.event)&&i.push(e)}),i}},p.vtrackcollect={unlimitedDiv:p.unlimitedDiv,config:{},storageEnable:!0,storage_name:"webjssdkvtrackcollect",para:{session_time:18e5,timeout:5e3,update_interval:18e5},url_info:{},timer:null,update_time:null,customProp:p.customProp,initUrl:function(){var e,t,r,a={server_url:{project:"",host:""},page_url:{host:"",pathname:""},api_url:""};if(!d.isString(p.para.server_url))return p.log("----vtrackcollect---server_url必须为字符串"),!1;try{e=d.URL(p.para.server_url),a.server_url.project=e.searchParams.get("project")||"default",a.server_url.host=e.host}catch(e){return p.log("----vtrackcollect---server_url解析异常",e),!1}try{t=d.URL(location.href),a.page_url.host=t.hostname,a.page_url.pathname=t.pathname}catch(e){return p.log("----vtrackcollect---页面地址解析异常",e),!1}try{(r=new d.urlParse(p.para.server_url))._values.Path="/config/visualized/Web.conf",a.api_url=r.getUrl()}catch(e){return p.log("----vtrackcollect---API地址解析异常",e),!1}return this.url_info=a},init:function(){return!(!d.isObject(p.para.heatmap)||!p.para.heatmap.get_vtrack_config)&&(d.localStorage.isSupport()||(this.storageEnable=!1),this.initUrl()?(this.storageEnable?(e=d.localStorage.parse(this.storage_name),d.isObject(e)&&d.isObject(e.data)&&this.serverUrlIsSame(e.serverUrl)?(this.config=e.data,this.update_time=e.updateTime,this.updateConfig(e.data),e=(new Date).getTime()-this.update_time,d.isNumber(e)&&0<e&&e<this.para.session_time?(e=this.para.update_interval-e,this.setNextFetch(e)):this.getConfigFromServer()):this.getConfigFromServer()):this.getConfigFromServer(),void this.pageStateListenner()):(p.log("----vtrackcustom----初始化失败，url信息解析失败"),!1));var e},serverUrlIsSame:function(e){return!!d.isObject(e)&&(e.host===this.url_info.server_url.host&&e.project===this.url_info.server_url.project)},getConfigFromServer:function(){var a=this;this.sendRequest(function(e,t){a.update_time=(new Date).getTime();var r={};200===e?t&&d.isObject(t)&&"Web"===t.os&&a.updateConfig(r=t):205===e?a.updateConfig(r):304===e?r=a.config:(p.log("----vtrackcustom----数据异常",e),a.updateConfig(r)),a.updateStorage(r),a.setNextFetch()},function(e){a.update_time=(new Date).getTime(),p.log("----vtrackcustom----配置拉取失败",e),a.setNextFetch()})},setNextFetch:function(e){var t=this;this.timer&&(clearTimeout(this.timer),this.timer=null),e=e||this.para.update_interval,this.timer=setTimeout(function(){t.getConfigFromServer()},e)},pageStateListenner:function(){var t=this;d.listenPageState({visible:function(){var e=(new Date).getTime()-t.update_time;d.isNumber(e)&&0<e&&e<t.para.update_interval?(e=t.para.update_interval-e,t.setNextFetch(e)):t.getConfigFromServer()},hidden:function(){t.timer&&(clearTimeout(t.timer),t.timer=null)}})},updateConfig:function(e){if(!d.isObject(e))return!1;this.config=e,this.customProp.updateEvents(),this.unlimitedDiv.init(e)},updateStorage:function(e){if(!this.storageEnable)return!1;if(!d.isObject(e))return!1;if(this.url_info.server_url)r=this.url_info.server_url;else{var t=p.vtrackcollect.initUrl();if(!t)return!1;r=t.server_url}var r={updateTime:(new Date).getTime(),data:e,serverUrl:r};d.localStorage.set(this.storage_name,JSON.stringify(r))},sendRequest:function(r,t){var e={app_id:this.url_info.page_url.host};this.config.version&&(e.v=this.config.version),d.jsonp({url:this.url_info.api_url,callbackName:"saJSSDKVtrackCollectConfig",data:e,timeout:this.para.timeout,success:function(e,t){r(e,t)},error:function(e){t(e)}})},getAssignConfigs:function(t,e){if(!this.url_info.server_url&&!this.initUrl())return[];var r=this,a=[];return(e=e||this.config).events=e.events||e.eventList,d.isObject(e)&&d.isArray(e.events)&&0<e.events.length?(d.each(e.events,function(e){d.isObject(e)&&d.isObject(e.event)&&e.event.url_host===r.url_info.page_url.host&&e.event.url_path===r.url_info.page_url.pathname&&t(e)&&a.push(e)}),a):[]},isDiv:function(e){if(e.element_path){e=e.element_path.split(">");if("div"!==d.trim(e.pop()).slice(0,3))return!1}return!0},configIsMatch:function(e,t){if(!t.element_path)return!1;if(t.limit_element_content&&t.element_content!==e.$element_content)return!1;if(t.limit_element_position&&t.element_position!==String(e.$element_position))return!1;if(void 0!==e.$element_position){if(t.element_path!==e.$element_path)return!1}else if(p.vtrackcollect.isDiv({element_path:t.element_path})){if(e.$element_path.indexOf(t.element_path)<0)return!1}else if(t.element_path!==e.$element_path)return!1;return!0}},p.init=function(e){if(p.readyState&&p.readyState.state&&2<=p.readyState.state)return!1;p.setInitVar(),p.readyState.setState(2),p.initPara(e),p.bridge.supportAppCallJs(),p.detectMode(),p.initError(),p._.isIOS()&&p._.getIOSVersion()&&p._.getIOSVersion()<13&&(p.para.heatmap&&p.para.heatmap.collect_tags&&p.para.heatmap.collect_tags.div&&p._.setCssStyle("div, [data-sensors-click] { cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }"),p.para.heatmap&&p.para.heatmap.track_attr&&p._.setCssStyle("["+p.para.heatmap.track_attr.join("], [")+"] { cursor: pointer; -webkit-tap-highlight-color: rgba(0,0,0,0); }"))};if(d.each(["getAppStatus","track","quick","register","registerPage","registerOnce","trackSignup","setProfile","setOnceProfile","appendProfile","incrementProfile","deleteProfile","unsetProfile","identify","login","logout","trackLink","clearAllRegister","clearPageRegister","trackCustom","initError"],function(e){var t=p[e];p[e]=function(){if(p.readyState.state<3)return d.isArray(p._q)||(p._q=[]),p._q.push([e,arguments]),!1;if(p.readyState.getState())return t.apply(p,arguments);try{console.error("请先初始化神策JS SDK")}catch(e){p.log(e)}}}),"string"!=typeof window.sensorsDataAnalytic201505)return void 0===window.sensorsDataAnalytic201505?window.sensorsDataAnalytic201505=p:window.sensorsDataAnalytic201505;p.setPreConfig(window[sensorsDataAnalytic201505]),window[sensorsDataAnalytic201505]=p,(window.sensorsDataAnalytic201505=p).init()}catch(e){if("object"==typeof console&&console.log)try{console.log(e)}catch(e){p.log(e)}}function _(){for(var e=+new Date,t=0;e==+new Date;)t++;return e.toString(16)+t.toString(16)}var f,g,h,v,y,w,b,S,k,j,O,P});