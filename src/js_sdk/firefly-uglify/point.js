// 引入埋点sdk
import './FireFly_JS_SDK_uglify.min.js'

// (function(para) {
//   var p = para.sdk_url,
//     n = para.name,
//     w = window,
//     d = document,
//     s = "script",
//     x = null,
//     y = null;
//   if (typeof w["sensorsDataAnalytic201505"] !== "undefined") {
//     return false;
//   }
//   w["sensorsDataAnalytic201505"] = n;
//   w[n] =
//     w[n] ||
//     function(a) {
//       return function() {
//         (w[n]._q = w[n]._q || []).push([a, arguments]);
//       };
//     };
//   var ifs = [
//     "track",
//     "quick",
//     "register",
//     "registerPage",
//     "registerOnce",
//     "clearAllRegister",
//     "trackSignup",
//     "trackAbtest",
//     "setProfile",
//     "setOnceProfile",
//     "appendProfile",
//     "incrementProfile",
//     "deleteProfile",
//     "unsetProfile",
//     "identify",
//     "login",
//     "logout",
//     "trackLink",
//     "deleteItem",
//     "setItem",
//     "trackCustom",
//     "initError"
//   ];
//   for (var i = 0; i < ifs.length; i++) {
//     w[n][ifs[i]] = w[n].call(null, ifs[i]);
//   }
//   if (!w[n]._t) {
//     (x = d.createElement(s)), (y = d.getElementsByTagName(s)[0]);
//     x.async = 1;
//     x.src = p;
//     x.setAttribute("charset", "UTF-8");
//     w[n].para = para;
//     y.parentNode.insertBefore(x, y);
//   }
// })({
//   sdk_url: './FireFly_JS_SDK_uglify.min.js',
//   name: "sensors",
//   server_url: "http://192.168.50.151:8080",
//   is_track_single_page: true, // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
//   use_client_time: true,
//   app_js_bridge: true, //配置H5与APP打通，如果H5工程集成在APP中，推荐开启
//   firefly_system_number: "system_number", //接入方在ITA中的模块编号，必传项
//   firefly_apc_type: 1, //H5默认1，必传项
//   firefly_model_name: "model_name", //接入方模块名称，必传项
//   heatmap: {
//     //是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
//     clickmap: "not_collect"
//   }
// });

window.sensors = window["sensorsDataAnalytic201505"]

let sensorsServerUrl = ''
if (process.env.NODE_ENV === 'development') {
  sensorsServerUrl = 'https://firefly.test.abchina.com:8090/nginx/firefly-collection/Collect/MobileData'
} else {
  sensorsServerUrl = 'https://firefly.abchina.com.cn/firefly-collection/Collect/MobileData'
}

export const  initSensors =  function(modelName) {
  console.log("modelName", modelName);
  if(!modelName) {
    return 
  }
  sensors.init({
    server_url: sensorsServerUrl,
    is_track_single_page: true, // 单页面配置，默认开启，若页面中有锚点设计，需要将该配置删除，否则触发锚点会多触发 $pageview 事件
    app_js_bridge: true, // 打通app内嵌h5
    firefly_system_number: "GDZHST002PS", //接入方在ITA中的模块编号，必传项
    firefly_apc_type: 1, //H5默认1，必传项
    firefly_model_name: modelName, //接入方模块名称，必传项
    use_client_time:true,
    heatmap: {
    //是否开启点击图，default 表示开启，自动采集 $WebClick 事件，可以设置 'not_collect' 表示关闭。
    clickmap: "not_collect"
    }
  })
}


