<template>
  <view class="jiaofei-electronic-certificate p-20">
    <image style="width: 100%; height: 200px; background-color: #eeeeee;" :src="image"></image>
    <!-- Footer 底部 Start -->
		<view class="footer">
			<u-button size="small" :customStyle="customBtnStyle" text="发送至邮箱" @click="showModal = true"></u-button>
		</view>
		<!-- Footer End -->

    <u-modal
      :show="showModal"
      :title="'接收邮箱'"
      width="280px"
      :showCancelButton="true"
      :confirmColor="variables.colorPrimary"
      :cancelText="'取消'"
      @confirm="confirmModal"
      @cancel="cancelModal"
      class="confirm-modal"
      >
				<view class="slot-content">
          <u-form
            labelPosition="top"
            :model="emailForm"
            :rules="rules"
            ref="emailFormRef"
          >
            <u-form-item label="" prop="email">
              <u-input v-model="emailForm.email"></u-input>
            </u-form-item>
          </u-form>
				</view>
		</u-modal>
  </view>
</template>

<script>
import Cache from '@/utils/cache'
import {apiBookingJiaofeiSendEmailImgUrl } from '@/api/jiaofei.js'

export default {
  data() {
    return {
      image: '',
      showModal: false,
      customBtnStyle: {
        minWidth: '120rpx',
        height: '60rpx',
        lineHeight: '60rpx',
        backgroundColor: '#11E69E',
        color: '#ffffff',
        padding: '40rpx 0rpx',
        borderRadius: '16rpx'
      },
      emailForm: {
        email: ''
      },
      rules: {
        email: [
          {
            required: true,
            message: '请输入邮箱',
            trigger: ['blur', 'change']
          },
          {
            pattern: /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/,
            message: '请输入正确的邮箱格式',
            trigger: ['blur', 'change']
          }
        ],
      }
    }
  },
  onShow() {
    this.image = Cache.get('electronic_certificate').img_url
    console.log(this.image)
  },
  methods: {
    async confirmModal() {
      this.$refs.emailFormRef.validate().then(res => {
        console.log('通过了')
        this.sendToEmail()
			})
    },
    sendToEmail() {
      apiBookingJiaofeiSendEmailImgUrl({
        img_url: this.image,
        send_email: this.emailForm.email
      }).then(res => {
        if (res.code === 0) {
          uni.$u.toast('发送成功')
          this.$refs.emailFormRef.resetFields()
          this.showModal = false
          this.$miRouter.back()
        } else {
          uni.$u.toast(res.msg)
        }
      })
    },
    cancelModal() {
      this.$refs.emailFormRef.resetFields()
      this.showModal = false
    }
  }
}
</script>

<style lang="scss" scoped>
.jiaofei-electronic-certificate {
  .footer {
    width: 100%;
    position: fixed;
    bottom: 40rpx;
    left: 0;
    padding: 0 40rpx;
    padding-bottom: env(safe-area-inset-bottom);
  }
}
</style>