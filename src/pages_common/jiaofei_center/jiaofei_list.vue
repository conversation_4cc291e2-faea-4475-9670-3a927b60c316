<template>
  <view :style="theme.style">
    <jiaofei-list v-if="jiaofeiLists && Number(jiaofeiLists.length)" :list="jiaofeiLists" type="jiaofeiList" :jiaofeiType="jiaofeiType"></jiaofei-list>
  </view>
</template>

<script>
  import jiaofeiList from '../components/jiaofei/list.vue'
  export default {
    components: {
      jiaofeiList
    },
    data() {
      return {
        jiaofeiLists: [],
        jiaofeiType: ''
      }
    },
    onLoad() {
      if (this.$Route.query.data) {
        this.jiaofeiLists = this.$Route.query.data
        this.jiaofeiType = this.$Route.query.type
        uni.setNavigationBarTitle({
          title: this.$Route.query.name
        })
      }
    },
    methods: {
      
    }
  }
</script>

<style>

</style>
