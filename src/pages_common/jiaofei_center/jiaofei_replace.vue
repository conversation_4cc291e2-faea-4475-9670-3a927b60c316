<template>
  <view :style="theme.style" class="jiaofei-replace">
    <view class="person-info">
      <u-cell title="姓名">
        <view class="" slot="value">
          <u-input
            placeholder="请输入姓名"
            input-align="right"
            border="none"
            v-model="name"
            maxlength="20"
          ></u-input>
        </view>
      </u-cell>
      <u-cell title="人员编号">
        <view class="" slot="value">
          <u-input
            placeholder="请输入人员编号"
            input-align="right"
            border="none"
            v-model="personNo"
            maxlength="20"
          ></u-input>
        </view>
      </u-cell>
      <u-cell title="手机号" :border="false" >
        <view class="" slot="value">
          <u-input
            placeholder="请输入手机号"
            input-align="right"
            border="none"
            v-model="phone"
            maxlength="11"
          ></u-input>
        </view>
      </u-cell>
    </view>
    <view class="tips">
      <view>温馨提示：</view>
      <view>1.输入你要代缴的人员信息（确保信息与系统记录的一致）；</view>
      <view>2.替他人代缴费用只会记录他人的缴费信息；</view>
    </view>
    <view class="confirm-btn" @click="getUserJiaofeiList">
      <u-button text="确认" shape="circle" :color="variables.bgLinearGradient1"></u-button>
    </view>
  </view>
</template>

<script>
  import { apiUserJiaofeiList } from '@/api/jiaofei.js'
  import Cache from '@/utils/cache'
  export default {
    data() {
      return {
        name: '',
        personNo: '',
        phone: '',
        companyId: '',
        userId: ''
      }
    },
    onLoad() {
      const userInfo = Cache.get('userInfo')
      this.companyId = userInfo.company_id
      this.userId = userInfo.user_id
    },
    methods: {
      async getUserJiaofeiList() {
        let reg = /^\d{8,11}$/
        if (!this.name) {
          return uni.$u.toast('请输入姓名')
        } else if (!this.personNo) {
          return uni.$u.toast('请输入人员编号')
        } else if (!this.phone) {
          return uni.$u.toast('请输入手机号')
        } else if (!reg.test(this.phone)) {
          return uni.$u.toast('请输入8-11位的手机号')
        }
        this.$showLoading({
        	title: '加载中...',
        	mask: true
        })
        apiUserJiaofeiList({
        	company_id: this.companyId,
        	person_no: this.personNo,
        	name: this.name,
        	phone: this.phone,
          user_id: this.userId
        })
        	.then(res => {
        		uni.hideLoading()
        		if (res.code == 0) {
              if (res.data.length) {
                this.$miRouter.push({
                  path: '/pages_common/jiaofei_center/jiaofei_list',
                  query: {
                    name: '缴费',
                    type: 'daijiaofei',
                    // #ifdef MP-ALIPAY
                    data: this.$encodeQuery(res.data),
                    // #endif
                    // #ifndef MP-ALIPAY
                    data: res.data,
                    // #endif
                  }
                })
              } else {
                uni.$u.toast('该用户暂无缴费信息')
              }
        		} else {
        			uni.$u.toast(res.msg)
        		}
        	})
        	.catch(err => {
        		uni.hideLoading()
        		uni.$u.toast(err.message)
        	})
      },
    }
  }
</script>

<style lang="scss" scoped>
.jiaofei-replace{
  padding: 30rpx;
  .person-info{
    background-color: #FFF;
    padding: 0 20px;
    border-radius: 20rpx;
    ::v-deep.u-cell .u-cell__body{
      padding: 10px 0px;
    }
  }
  .tips{
    color: #999999;
    font-size: 26rpx;
    margin: 30rpx 20rpx;
  }
  .confirm-btn{
    position: fixed;
    left: 0;
    bottom: 40rpx;
    width: 100%;
    padding: 0 40rpx;
  }
}
</style>
