<template>
  <view :style="theme.style" class="jiaofei-detail p-40">
    <view class="">
      <view class="detail-header" v-if="jiaofeiOrderInfo.order_type_alias==='缴费类'">
        <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_pass"></u-image>
        <text class="success-color m-l-15">订单已缴费</text>
      </view>
      <view class="detail-header" v-if="jiaofeiOrderInfo.order_type_alias==='退款类'">
        <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_refund" ></u-image>
        <text class="refund-color m-l-15">订单已退款</text>
      </view>
      <view class="order-info m-b-20" v-if="jiaofeiOrderInfo.order_type_alias==='退款类'">
        <view class="info-header">退款信息</view>
        <view class="info-item">
          <view class="flex row-between m-t-30">
            <view class="muted">退款订单：</view>
            <view>{{jiaofeiOrderInfo.order_trade_no}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">退款类型：</view>
            <view>{{jiaofeiOrderInfo.refund_type_alias}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">退款金额：</view>
            <view>￥{{ (jiaofeiOrderInfo.pay_fee/ 100).toFixed(2)}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">退款方式：</view>
            <view>{{jiaofeiOrderInfo.pay_method_alias}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">创建时间：</view>
            <view>{{jiaofeiOrderInfo.create_time}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">退款时间：</view>
            <view>{{jiaofeiOrderInfo.create_time}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">到账时间：</view>
            <view>{{jiaofeiOrderInfo.finish_time}}</view>
          </view>
        </view>
      </view>
      <view class="order-info m-b-20">
        <view class="info-header">人员信息</view>
        <view class="info-item">
          <view class="flex row-between m-t-30">
            <view class="muted">用户姓名：</view>
            <view>{{jiaofeiOrderInfo.payer_name}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">人员编号：</view>
            <view>{{jiaofeiOrderInfo.payer_person_no}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">部门：</view>
            <view>{{jiaofeiOrderInfo.payer_depart_name}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">手机号码：</view>
            <view>{{jiaofeiOrderInfo.payer_phone}}</view>
          </view>
        </view>
      </view>
      <view class="order-info m-b-20">
        <view class="info-header">缴费信息</view>
        <view class="info-item">
          <view class="flex row-between m-t-30">
            <view class="muted">缴费订单：</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='缴费类'">{{jiaofeiOrderInfo.order_trade_no}}</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='退款类'">{{jiaofeiOrderInfo.jiaofei_trade_no}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">缴费项目：</view>
            <view>{{jiaofeiOrderInfo.jiaofei_name}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">缴费类型：</view>
            <view>{{jiaofeiOrderInfo.jiaofei_type_alias}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">缴费金额：</view>
            <view>￥{{ (jiaofeiOrderInfo.pay_fee/ 100).toFixed(2)}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">下单时间：</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='缴费类'">{{jiaofeiOrderInfo.create_time}}</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='退款类'">{{jiaofeiOrderInfo.jiaofei_create_time}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">支付时间：</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='缴费类'">{{jiaofeiOrderInfo.pay_time}}</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='退款类'">{{jiaofeiOrderInfo.jiaofei_pay_time}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">到账时间：</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='缴费类'">{{jiaofeiOrderInfo.finish_time}}</view>
            <view v-if="jiaofeiOrderInfo.order_type_alias==='退款类'">{{jiaofeiOrderInfo.jiaofei_finish_time}}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        imgPath: this.$imgPath,
        jiaofeiOrderInfo: {}
      }
    },
    onLoad() {
      if (this.$Route.query.data) {
        this.jiaofeiOrderInfo = this.$Route.query.data
      }
    },
    methods: {
      
    }
  }
</script>

<style lang="scss" scoped>
.jiaofei-detail{
  .detail-header{
    background-color: #FFFFFF;
    border-radius: 20rpx;
    padding: 0 30rpx;
    font-size: 40rpx;
    height: 100rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    .order-status-img{
      padding-right: 20rpx;
    }
    .success-color{
      color: #11E69E;
    }
    .refund-color{
      color: #F9A63C;
    }
  }
  
  .order-info{
    background-color: #FFFFFF;
    border-radius: 20rpx;
    padding: 0 30rpx 30rpx;
    .info-header {
      border-bottom: 1px solid $border-color-base;
      height: 90rpx;
      line-height: 90rpx;
    }
  }
}

</style>
