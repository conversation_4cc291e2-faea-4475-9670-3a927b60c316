<template>
  <view :style="theme.style" class="jiaofei">
    <view class="jiaofei-center" v-if="jiaofeiType==='jiaofeiCenter'">
      <image class="jiaofei-bg" :src="themeImgPath.img_jiaofei_bg"></image>
      <view class="jiaofei-bg-text">
        <view class="font-size-48 f-w-600">缴费中心</view>
        <view class="xxs text-color m-t-20">查看当前缴费项目和进度</view>
      </view>
      <!-- 满意度 -->
      <view v-if="jiaofeiType==='jiaofeiCenter' && isShowStatisfaction" style="width:100%;position: absolute;text-align:right;top: 240rpx;">
        <image :src="themeImgPath.img_satisfaction_star" style="width: 148rpx;height: 172rpx;" @click="onEditorSatisfaction" />
      </view>
      <view class="m-l-30 m-r-30">
        <view class="font-size-38 f-w-600 jiaofei-bg-title">{{params.company_name}}</view>
        <view class="jiaofei-list" :key="listKey">
          <view class="jiaofei-item" v-for="(item,index) in jiaofeiTypeList" :key="index" @click="gotoJiaofeiList(item)">
            <u-image class="jiaofei-item-img" width="100rpx" height="100rpx" :src="item.icon"></u-image>
            <view class="">
              <view class="lg">{{item.name}}</view>
              <view v-if="item.type === 'daijiaofei'" class="mini m-t-6 status-color">替他人缴费</view>
              <view v-else class="mini m-t-6 status-color">{{item.status?'待缴费':'暂无缴费'}}</view>
            </view>
            <view v-if="item.status" class="status-point"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 评价弹窗 -->
    <statisfaction-modal ref="statisfactionMoadlRef" :tips="statisfactionTips" :tags="tags" :satisfactionForm="satisfactionForm" :visible.sync="sModalVisible" @onModalSuccess="onModalSuccess" />
    
    <view  v-if="jiaofeiType==='jiaofeiRecord'" class="jiaofei-record">
      <view class="top-warp">
        <u-tabs
          :list="recordTabs"
          :current="currentRecordTabs"
          :line-color="variables.colorPrimary"
          :active-style="{ fontWeight: 500, color: '#101010' }"
          :scrollable="false"
          @change="recordTabsChange"
        ></u-tabs>
      </view>
      <!-- top="xxx"下拉布局往下偏移,防止被悬浮菜单遮住 -->
      <mescroll-uni
        ref="mescrollRef"
        @init="mescrollInit"
        top="88"
        @down="downCallback"
        @up="upCallback"
        @emptyclick="emptyClick"
      >
        <!-- 塞选条件 -->
        <view ref="dropdownRef" class="bg-white plug-election" style="margin-bottom: 10rpx" :key="recordType">
          <dropdown menu-icon="arrow-down-fill" menu-icon-size="10rpx" @open="changeDropdown(true)" @close="changeDropdown(false)">
            <!-- <template> -->
            <dropdown-item
              v-model="recordDropdownData.valueDate"
              :title="recordDropdownData.valueDateTitle"
              :options="optionsDate"
              @change="changeMenuEvent($event, 'date')"
            ></dropdown-item>
            <dropdown-item
              v-model="recordDropdownData.valueType"
              :title="recordDropdownData.valueTypeTitle"
              :options="optionsType"
              @change="changeMenuEvent($event, 'type')"
            ></dropdown-item>
            <!-- </template> -->
          </dropdown>
        </view>
        <!-- 数据列表 -->
        <record-list v-if="recordLists && recordLists.length" :list="recordLists" type="recordList"></record-list>
      </mescroll-uni>
    </view>
    <u-tabbar :value="jiaofeiTabbar" :fixed="true" :activeColor="variables.colorPrimary">
    	<u-tabbar-item v-for="item in tabList" :text="item.name" @click="clickJiaofeiTabbar($event, item)" >
        <image
          class="u-page__item__slot-icon img-filter"
          slot="active-icon"
          :src="item.activeIcon"
        ></image>
        <image
          class="u-page__item__slot-icon"
          slot="inactive-icon"
          :src="item.inactiveIcon"
        ></image>
      </u-tabbar-item>
    </u-tabbar>
    <!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
  </view>
</template>

<script>
  import { apiUserJiaofeiList, apiJiaofeiOrderList } from '@/api/jiaofei.js'
  import Cache from '@/utils/cache'
  import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
  import recordList from '../components/jiaofei/list.vue'
  import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'
  import StatisfactionModal from '@/components/statisfaction-modal/statisfaction-modal.vue'
  import appMixins from '@/mixins/app.js'
  // import {setStorage, getStorage} from '@/utils/storage'
  export default {
    mixins: [MescrollMixin, appMixins],
    components: {
      recordList,
      FloatingPopup,
      StatisfactionModal
    },
    data() {
      return {
        // 满意度
        statisfactionTips: '',
        tags: [],
        isShowStatisfaction: false,
        satisfactionForm: {
          is_satisfied: true,
          reason: '',
          module_key: 'jiaofei'
        },
        sModalVisible: false,
        
        imgPath: this.$imgPath,
        jiaofeiTabbar: 0,
        jiaofeiType: 'jiaofeiCenter',
        // tabList: [
        //   {
        //     name: '缴费中心',
        //     activeIcon: this.$imgPath.img_jiaofei_active,
        //     inactiveIcon: this.$imgPath.img_jiaofei_inactive,
        //     type: 'jiaofeiCenter'
        //   },
        //   {
        //     name: '缴费记录',
        //     activeIcon: this.$imgPath.img_record_active,
        //     inactiveIcon: this.$imgPath.img_record_inactive,
        //     type: 'jiaofeiRecord'
        //   }
        // ],
        jiaofeiTypeList: [],
        // [
        //   {
        //     type: 'xuezafei',
        //     name: '学杂费',
        //     icon: this.$imgPath.img_xuezafei,
        //     status: 0
        //   },{
        //     type: 'shuifei',
        //     name: '水费',
        //     icon: this.$imgPath.img_shuifei,
        //     status: 0
        //   },{
        //     type: 'dianfei',
        //     name: '电费',
        //     icon: this.$imgPath.img_dianfei,
        //   },{
        //     type: 'zhusufei',
        //     name: '住宿费',
        //     icon: this.$imgPath.img_zhusufei,
        //     status: 0
        //   },{
        //     type: 'huoshifei',
        //     name: '伙食费',
        //     icon: this.$imgPath.img_huoshifei,
        //     status: 0
        //   },{
        //     type: 'xiaofufei',
        //     name: '校服费',
        //     icon: this.$imgPath.img_xiaofufei,
        //     status: 0
        //   },{
        //     type: 'daijiaofei',
        //     name: '代缴费',
        //     icon: this.$imgPath.img_daijiaofei,
        //     status: 0
        //   },{
        //     type: 'qitashoufei',
        //     name: '其他收费',
        //     icon: this.$imgPath.img_qitashoufei,
        //     status: 0
        //   }
        // ],
        listKey: 0,
        jiaofeiList: [],
        params: {},
        // 消费记录
        currentRecordTabs: 0,
        recordType: '',
        recordTabs: [
          {
            name: '全部',
            type: ''
          },
          {
            name: '缴费',
            type: 6
          },
          {
            name: '退款',
            type: 1
          },
          {
            name: '退款审批',
            type: -1
          }
        ],
        recordDropdownData: {
          valueDate: '一个月', //日期
          valueDateTitle: '一个月',
          valueType: '', // 类型
          valueTypeTitle: '全部类型',
        },
        optionsDate: [
          {
            label: '全部时间',
            value: '全部时间',
            start_time: uni.$u.timeFormat(new Date('2021/01/01').getTime(), 'yyyy-mm-dd 00:00:00')
          },
          {
            label: '三天内',
            value: '三天',
            start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 3, 'yyyy-mm-dd 00:00:00')
          },
          {
            label: '一周内',
            value: '一周',
            start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 7, 'yyyy-mm-dd 00:00:00')
          },
          {
            label: '一个月内',
            value: '一个月',
            start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 30, 'yyyy-mm-dd 00:00:00')
          },
          {
            label: '三个月内',
            value: '三个月',
            start_time: uni.$u.timeFormat(new Date().getTime() - 86400000 * 90, 'yyyy-mm-dd hh:MM:ss')
          }
        ],
        optionsType: [
          {
            value: '',
            label: '全部类型',
          },
          {
            value: 'xuezafei',
            label: '学杂费',
          },
          {
            value: 'shuifei',
            label: '水费',
          },
          {
            value: 'dianfei',
            label: '电费',
          },
          {
            value: 'zhusufei',
            label: '住宿费',
          },
          {
            value: 'huoshifei',
            label: '伙食费',
          },
          {
            value: 'xiaofufei',
            label: '校服费',
          },
          {
            value: 'qitashoufei',
            label: '其他',
          }
        ],
        openDropdown: false, // 是否打开Dropdown
        recordLists: [],
        startTime: uni.$u.timeFormat(new Date().getTime() - 86400000 * 30, 'yyyy-mm-dd 00:00:00'),
        canReset: false,
        floatingPopupShow: false
      }
    },
    computed: {
      tabList() {
        return [
          {
            name: '缴费中心',
            activeIcon: this.themeImgPath.img_jiaofei_active,
            inactiveIcon: this.themeImgPath.img_jiaofei_inactive,
            type: 'jiaofeiCenter'
          },
          {
            name: '缴费记录',
            activeIcon: this.themeImgPath.img_record_active,
            inactiveIcon: this.themeImgPath.img_record_inactive,
            type: 'jiaofeiRecord'
          }
        ]
      },
    },
    watch: {
      themeImgPath: {
        deep: true,
        handler(value) {
          this.initJiaofeiTypeList()
        }
      }
    },
    onLoad() {
      this.initJiaofeiTypeList()
    },
    async onShow() {
      const userInfo = Cache.get('userInfo')
      this.params.company_id = userInfo.company_id
      this.params.company_name = userInfo.company_name
      this.params.person_no = userInfo.person_no
      this.params.name = userInfo.card_name
      this.params.phone = userInfo.phone
      this.params.org_id = userInfo.company_id
      this.params.user_id = userInfo.user_id
      this.getUserJiaofeiList()
      
      // 申诉成功的话,切换到退款审批,刷新下页面
      this.$eventbus.$on('jiaofeiRefund', val => {
        this.canReset = val
      })
      if (this.canReset) {
        this.currentRecordTabs = 3
        this.recordType = -1
        this.mescroll.resetUpScroll() // 重置列表数据为第一页
        this.mescroll.scrollTo(0, 0) // 重置列表数据为第一页时,建议把滚动条也重置到顶部,避免无法再次翻页的问题
      }
      this.canReset = false
      this.floatingPopupShow = !this.floatingPopupShow
      
      // 判断是否显示评价弹窗
      const moduleKey = this.satisfactionForm.module_key // key
      // const today = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd') // 获取当天时间
      // const isModuleDay = getStorage(moduleKey) // 获取当天弹窗时间
      const result = await this._appGetSatisfaction(moduleKey) // 获取记录
      if(result) {
        // if(isModuleDay && isModuleDay === today) { // 一天内只弹一次
        //   this.sModalVisible = false
        // } else { // 主动弹窗
        //   setStorage({name: moduleKey, content:today}) // 记录当天弹窗时间
        //   this.sModalVisible = true
        // }
        this.isShowStatisfaction = true
        this.statisfactionTips = result.tips
        this.tags = result.tags
        
        this.satisfactionForm.module_key = result.module_key
        // this.satisfactionForm.is_satisfied = result.is_satisfied
        this.satisfactionForm.reason = result.reason
      }
      
    },
    methods: {
      // 提交评价回调
      onModalSuccess() {
        console.log('onModalSuccess----');
      },
      onEditorSatisfaction() {
        this.sModalVisible = true
      },
      initJiaofeiTypeList() {
        this.jiaofeiTypeList = [
          {
            type: 'xuezafei',
            name: '学杂费',
            icon: this.themeImgPath.img_xuezafei,
            status: 0
          },{
            type: 'shuifei',
            name: '水费',
            icon: this.themeImgPath.img_shuifei,
            status: 0
          },{
            type: 'dianfei',
            name: '电费',
            icon: this.themeImgPath.img_dianfei,
          },{
            type: 'zhusufei',
            name: '住宿费',
            icon: this.themeImgPath.img_zhusufei,
            status: 0
          },{
            type: 'huoshifei',
            name: '伙食费',
            icon: this.themeImgPath.img_huoshifei,
            status: 0
          },{
            type: 'xiaofufei',
            name: '校服费',
            icon: this.themeImgPath.img_xiaofufei,
            status: 0
          },{
            type: 'daijiaofei',
            name: '代缴费',
            icon: this.themeImgPath.img_daijiaofei,
            status: 0
          },{
            type: 'qitashoufei',
            name: '其他收费',
            icon: this.themeImgPath.img_qitashoufei,
            status: 0
          }
        ]
      },
      clickJiaofeiTabbar(val, item) {
        this.jiaofeiTabbar = val
        this.jiaofeiType = item.type
      },
      // 缴费中心----start
      async getUserJiaofeiList() {
        this.$showLoading({
        	title: '加载中...',
        	mask: true
        })
        apiUserJiaofeiList({
        	company_id: this.params.company_id,
        	person_no: this.params.person_no,
        	name: this.params.name,
        	phone: this.params.phone,
          user_id: this.params.user_id
        })
        	.then(res => {
        		uni.hideLoading()
        		if (res.code == 0) {
              this.jiaofeiList = res.data
              this.jiaofeiTypeList.map(jiaofei => {
                let list = this.jiaofeiList.filter(item => item.jiaofei_type === jiaofei.type)
                if (list.length) {
                  jiaofei.status = 1
                } else {
                  jiaofei.status = 0
                }
              })
              this.listKey = Math.random()
        		} else {
        			uni.$u.toast(res.msg)
        		}
        	})
        	.catch(err => {
        		uni.hideLoading()
        		uni.$u.toast(err.message)
        	})
      },
      gotoJiaofeiList(data) {
        let list = []
        list = this.jiaofeiList.filter(item => item.jiaofei_type === data.type)
        if (data.type === 'daijiaofei') {
          this.$miRouter.push('/pages_common/jiaofei_center/jiaofei_replace')
        } else if (data.status) {
          this.$miRouter.push({
            path: '/pages_common/jiaofei_center/jiaofei_list',
            query: {
              name: data.name,
              type: 'jiaofei',
              // #ifdef MP-ALIPAY
              data: this.$encodeQuery(list),
              // #endif
              // #ifndef MP-ALIPAY
              data: list,
              // #endif
            }
          })
        } else {
          uni.$u.toast('暂无缴费信息')
        }
      },
      // 缴费中心----end
      // 缴费记录----start
      upCallback(page) {
        if (this.openDropdown) {
          // 当筛选框dropdown弹出时禁止触发拉取数据
          return
        }
        this.getJiaofeiOrderList(page)
      },
      async getJiaofeiOrderList(page) {
        this.$showLoading({
        	title: '加载中...',
        	mask: true
        })
        let params = {
          page: page.num,
          page_size: page.size,
          start_time: this.startTime,
          end_time: uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd hh:MM:ss'),
          company_id: this.params.company_id,
          jiaofei_type: this.recordDropdownData.valueType,
        }
        if (this.recordType) {
          params.order_type = this.recordType
        }
        apiJiaofeiOrderList(params)
        	.then(res => {
        		uni.hideLoading()
        		if (res.code == 0) {
              const results = res.data.results
              const count = res.data.count
              const page_size = res.data.page_size
              // 如果是第一页需手动置空列表
              if (page.num == 1) this.recordLists = []
              // 追加新数据
              this.recordLists = [...this.recordLists, ...results]
              //方法二(推荐): 后台接口有返回列表的总数据量 count, 判断是否有下一页
              let pageLength = 0
              if (results) {
                pageLength = results.length
              } else {
                pageLength = 0
              }
              this.mescroll.endBySize(pageLength, count)
        		} else {
        			uni.$u.toast(res.msg)
        		}
        	})
        	.catch(err => {
        		uni.hideLoading()
        		uni.$u.toast(err.message)
        	})
      },
      recordTabsChange(e) {
        this.recordLists = []
        this.recordType = e.type
        this.upCallback({ num: 1, size: 10 })
      },
      changeMenuEvent(e, type) {
        if (type == 'date') {
          this.optionsDate.map(v => {
            if (v.value == e) {
              this.startTime = v.start_time
              this.recordDropdownData.valueDateTitle = e
            }
          })
        }else if (type == 'type') {
        this.recordDropdownData.valueTypeTitle = this.optionsType.find(n => n.value == e).label
      }
        this.upCallback({ num: 1, size: 10 })
      },
      
      changeDropdown(data) {
        this.openDropdown = data
        if (data) {
          this.mescroll.optUp.isLock = true
          this.mescroll.optDown.isLock = true
        } else {
          this.mescroll.lockUpScroll(false)
          this.mescroll.lockDownScroll(false)
        }
      },
      // 缴费记录----end
    }
  }
</script>

<style lang="scss" scoped>
.jiaofei{
  .jiaofei-center{
    .jiaofei-bg{
      width: 100%;
      height: 340rpx;
    }
    .jiaofei-bg-text{
      position: absolute;
      top: 90rpx;
      left: 30rpx;
      .text-color{
        color: #3F584B;
      }
    }
    .jiaofei-bg-title{
      max-width: 500rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .jiaofei-list{
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      margin-top: 30rpx;
      margin-bottom: 30rpx;
      .jiaofei-item{
        width: 48%;
        display: flex;
        align-items: center;
        background-color: #FFFFFF;
        padding: 36rpx 30rpx;
        margin-bottom: 30rpx;
        border-radius: 20rpx;
        position: relative;
        .jiaofei-item-img{
          margin-right: 30rpx;
        }
        .status-color{
          color: #a5a7aa;
        }
        .status-point{
          width: 20rpx;
          height: 20rpx;
          background-color: red;
          position: absolute;
          top: -2rpx;
          right: -2rpx;
          border-radius: 10rpx;
        }
      }
    }
  }
  .jiaofei-record{
    height: 100%;
    .top-warp {
      z-index: 20;
      position: fixed;
      top: --window-top; /* css变量 */
      left: 0;
      width: 100%;
      height: 88upx;
      background-color: white;
    }
    .plug-election {
      border-top: $border-base;
      padding: 10rpx 0;
    }
  }
  ::v-deep.u-tabbar-item{
    flex-direction: row;
    .u-tabbar-item__icon{
      width: 50rpx!important;
      .u-page__item__slot-icon{
        width: 50rpx;
        height: 50rpx;
      }
    }
    .u-tabbar-item__text{
      font-size: 32rpx;
      line-height: 32rpx;
      margin-left: 10rpx;
    }
  }
  ::v-deep.upwarp-nodata{
    margin-bottom: 100rpx;
  }
}
</style>
