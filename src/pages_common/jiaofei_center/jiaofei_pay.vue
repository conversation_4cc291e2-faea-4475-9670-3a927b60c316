<template>
  <view :style="theme.style" class="jiaofei-pay p-t-30 p-r-30 p-l-30">
    <view class="person-info m-b-20">
      <view class="info-header">人员信息</view>
      <view class="info-item">
        <view class="flex row-between m-b-30">
          <view class="muted">用户姓名：</view>
          <view>{{jiaofeiInfo.name}}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">人员编号：</view>
          <view>{{jiaofeiInfo.person_no}}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">部门：</view>
          <view>{{jiaofeiInfo.department_group}}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">手机号码：</view>
          <view>{{jiaofeiInfo.phone}}</view>
        </view>
      </view>
    </view>
    <view class="jiaofei-info">
      <view class="info-header">缴费信息</view>
      <view class="info-item">
        <view class="flex row-between m-b-30">
          <view class="muted">收费来源：</view>
          <view>{{jiaofeiInfo.org_name}}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">缴费项目：</view>
          <view>{{jiaofeiInfo.jiaofei_name}}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">缴费金额：</view>
          <view>￥{{ (jiaofeiInfo.jiaofei_money/ 100).toFixed(2)}}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">缴费到期：</view>
          <view>{{jiaofeiInfo.end_time}}</view>
        </view>
      </view>
    </view>
    <view class="pay-btn" @click="rechargeSettingShow = true">
      <u-button text="支付" shape="circle" :color="variables.bgLinearGradient1"></u-button>
    </view>
    <!-- 支付方式 -->
    <u-modal
      :show="rechargeSettingShow"
      :title="'支付方式'"
      confirmColor="#5A6080"
      confirmText="支付"
      @confirm="confirmPay()"
      :showCancelButton="true"
      cancelText="取消"
      @cancel="rechargeSettingShow = false"
    >
      <u-radio-group placement="column" v-model="radioPayinfoId" size="35" iconPlacement="right">
        <u-radio
          :customStyle="{ marginBottom: '16px' }"
          v-for="(item, index) in payinfosList"
          :key="index"
          :activeColor="variables.colorPrimary"
          :label="item.payway_alias"
          :name="item.id"
        ></u-radio>
      </u-radio-group>
    </u-modal>
  </view>
</template>

<script>
  import { getApiRechargeGetSettings, queryOrderInfo } from '@/api/user.js'
  import { apiJiaofeiOrderCreate } from '@/api/jiaofei.js'
  import { payMpRequest, checkWxBridgeReady, setWxJssdkConfig, payWxJssdkRequest } from '@/utils/payment'
  import { isBankAbcClient, debounce } from '@/utils/util'
  import Cache from '@/utils/cache'

  
  export default {
    data() {
      return {
        jiaofeiInfo: {},
        payinfosList: [],
        radioPayinfoId: '',
        rechargeSettingShow: false,
        tradeNo: '',
        jiaofeiType: ''
        
      }
    },
    onLoad() {
      if (this.$Route.query.data) {
        this.jiaofeiInfo = this.$Route.query.data
        if (this.jiaofeiInfo.id) {
          this.getRechargeGetSettings()
        }
      }
      if (this.$Route.query.type) {
        this.jiaofeiType = this.$Route.query.type
      }
      if (uni.getStorageSync('jiaofeiOrderTradeNo') && isBankAbcClient()) {
        this.getOrderDetail(uni.getStorageSync('jiaofeiOrderTradeNo'))
      }
    },
    methods: {
      getRechargeGetSettings() {
        this.$showLoading({
          title: '获取中....',
          mask: true
        })
        getApiRechargeGetSettings({
          org_id: this.jiaofeiInfo.org_id
        })
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              // this.payinfosList = res.data.payinfos
              // 判断payway是否重复
            let checkKey = {}
            res.data.payinfos.map(v => {
              if (!checkKey[v.payway]) {
                checkKey[v.payway] = 1
              } else {
                checkKey[v.payway] += 1
              }
            })
            this.payinfosList = res.data.payinfos.map(v => {
              if (checkKey[v.payway] > 1) {
                v.payway_alias = v.payway_alias + '-' + v.sub_payway_alias
              }
              return v
            })
              if (this.payinfosList && this.payinfosList.length >= 1) {
                this.radioPayinfoId = this.payinfosList[0].id
              }
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.hideLoading()
            uni.$u.toast(err)
          })
      },
      confirmPay: debounce(function() {
        if (!this.radioPayinfoId) {
          return uni.$u.toast('获取缴费配置失败！请联系客服')
        }
        this.$showLoading({
          title: '支付中....',
          mask: true
        })
        let params = {
          amount: this.jiaofeiInfo.jiaofei_money,
          payinfo_id: this.radioPayinfoId,
          jiaofei_detail_id: this.jiaofeiInfo.id,
          org_id: this.jiaofeiInfo.org_id,
          person_no: this.jiaofeiInfo.person_no,
          // #ifdef H5
          return_url: window.location.origin + `/pages/index/index`
          // #endif
        }
        let originBackUrl = Cache.get('originBackUrl')
        if (originBackUrl) {
          params.backUrl = originBackUrl
        }
        apiJiaofeiOrderCreate(params)
          .then(res => {
            this.tradeNo = ''
            if (res.code == 0) {
              this.rechargeSettingShow = false
              this.tradeNo = res.data.trade_no
              // #ifdef H5
              if (res.data.extra && res.data.extra.redirect) {
                uni.hideLoading()
                window.location.href = res.data.extra.redirect
                return
              }
              // #endif
              if (res.data.sub_payway == 'fastepay') {
                uni.hideLoading()
                if (isBankAbcClient()) {
                  uni.setStorageSync('jiaofeiOrderTradeNo', res.data.trade_no)
                }
                window.location.href = res.data.extra.redirect
                return
              } else if (res.data.sub_payway == 'jsapi' || (res.data.payway == 'AliPay' && res.data.sub_payway === 'miniapp')) { // 貌似微信支付是走的jsapi的
                uni.hideLoading()
                this.jsapiChooseWXPay(res.data.extra)
              } else if ((res.data.payway == 'AliPay' || res.data.payway == 'sub_paypay' || res.data.payway == 'ShouqianbaPay') && res.data.sub_payway === 'h5') { // h5走location
                uni.hideLoading()
                window.location.href = res.data.extra
              } else {
                uni.hideLoading()
              }
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.hideLoading()
            uni.$u.toast(err)
          })
      }, 1000),
      jsapiChooseWXPay(params) {
        let _this = this
        // #ifdef H5
        payWxJssdkRequest(params).then(({res}) => {
          uni.hideLoading()
          uni.showToast({
            title: '缴费成功',
            icon: 'success',
            success: () => {
              _this.gotoJiaofeiCenter()
            }
          })
        }).catch(({res}) => {
          uni.hideLoading()
          uni.showToast({
            title: '缴费失败',
            icon: 'none',
            success: () => {
            }
          })
        })
        // #endif
        // #ifdef MP-WEIXIN || MP-ALIPAY
        payMpRequest(params).then(({res, provider}) => {
          uni.hideLoading()
          if (provider === 'alipay') { // 当为支付宝支付时需要额外判断状态码
            switch (res.resultCode) {
              case '9000': // 订单处理成功。
                uni.showToast({
                  title: '缴费成功',
                  icon: 'success',
                  success: () => {
                    _this.gotoJiaofeiCenter()
                  }
                })
                break;
              case '6001': // 用户中途取消
                uni.showToast({
                  title: '用户中途取消',
                  icon: 'fail',
                  success: () => {
                  }
                })
                break;
              case '8000': // 正在处理中。支付结果未知（有可能已经支付成功）。
                // uni.showToast({
                //   title: '正在处理中',
                //   icon: 'fail'
                // })
                if (params.trade_no) {
                  // this.$miRouter.push({
                  //   path: '/pages_bundle/recharge/recharge_status',
                  //   query: {
                  //     trade_no: params.trade_no
                  //   }
                  // })
                  uni.showToast({
                    title: '正在处理中',
                    icon: 'success',
                    success: () => {
                      _this.gotoJiaofeiCenter()
                    }
                  })
                } else {
                  uni.showToast({
                    title: '正在处理中',
                    icon: 'fail',
                    success: () => {
                    }
                  })
                }
                break;
              case '6002': // 网络连接出错
                uni.showToast({
                  title: '网络连接出错',
                  icon: 'fail',
                  success: () => {
                  }
                })
                break;
              case '6004': // 处理结果未知（有可能已经成功）
                // uni.showToast({
                //   title: '处理结果未知',
                //   icon: 'fail'
                // })
                uni.showToast({
                  title: '正在处理中',
                  icon: 'success',
                  success: () => {
                    _this.gotoJiaofeiCenter()
                  }
                })
                break;
              case '4': // 无权限调用
                uni.showToast({
                  title: '无权限调用',
                  icon: 'fail',
                  success: () => {
                  }
                })
                break;
              default:
              break;
            }
          } else {
            uni.showToast({
              title: '缴费成功',
              icon: 'success',
              success: () => {
                _this.gotoJiaofeiCenter()
              }
            })
          }
        }).catch(({res, provider}) => {
          uni.hideLoading()
          uni.showToast({
            title: '缴费失败',
            icon: 'none',
            success: () => {
            }
          })
        })
        // #endif
      },
      gotoJiaofeiCenter() {
        if (this.jiaofeiType === 'daijiaofei') {
          this.$miRouter.back(3)
        } else {
          this.$miRouter.back(2)
        }
      },
      getOrderDetail(trade_no) {
        this.$showLoading({
          title: '获取中....',
          mask: true
        })
        queryOrderInfo({
          trade_no
        })
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              if (res.data.order_status === 'ORDER_SUCCESS') {
                this.gotoJiaofeiCenter()
              }
              uni.removeStorageSync('jiaofeiOrderTradeNo')
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.hideLoading()
            uni.$u.toast(err)
          })
      }
    }
  }
</script>

<style lang="scss" scoped>
.jiaofei-pay{
  .person-info, .jiaofei-info{
    background-color: #FFFFFF;
    border-radius: 20rpx;
    padding: 0 30rpx 10rpx;
    .info-header {
    	border-bottom: 1px solid $border-color-base;
    	height: 90rpx;
    	line-height: 90rpx;
      margin-bottom: 30rpx;
    }
  }
  .pay-btn{
    position: fixed;
    left: 0;
    bottom: 40rpx;
    width: 100%;
    padding: 0 40rpx;
  }
}
</style>
