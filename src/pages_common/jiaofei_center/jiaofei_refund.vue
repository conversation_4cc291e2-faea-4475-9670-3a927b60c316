<template>
  <view :style="theme.style + `${type==='apply'?'padding-bottom: 120rpx;':''}`" class="jiaofei-refund">
    <view class="refund-steps">
      <u-steps>
        <u-steps-item class="pass" :activeStepsTextSize="28" :inactiveStepsTextSize="27" title="提交申请">
          <view class="slot-icon" slot="icon">
            <!-- <image :src="themeImgPath.img_pass" @click="hidePopup"></image> -->
            <image width="40rpx" height="40rpx" :src="themeImgPath.img_pass"></image>
          </view>
        </u-steps-item>
        <u-steps-item class="unpass" :activeStepsTextSize="28" :inactiveStepsTextSize="27" title="退款审批" v-if="!jiaofeiRefundInfo.is_approval">
          <view class="slot-icon" slot="icon">
            <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_unpass"></u-image>
          </view>
        </u-steps-item>
        <u-steps-item class="pass" :activeStepsTextSize="28" :inactiveStepsTextSize="27" title="审批中" v-if="jiaofeiRefundInfo.approval_status==='applying'">
          <view class="slot-icon" slot="icon">
            <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_pass"></u-image>
          </view>
        </u-steps-item>
        <u-steps-item class="pass" :activeStepsTextSize="28" :inactiveStepsTextSize="27" title="退款审批" v-if="jiaofeiRefundInfo.approval_status === 'agree'">
          <view class="slot-icon" slot="icon">
            <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_pass"></u-image>
          </view>
        </u-steps-item>
        <u-steps-item class="failed" :activeStepsTextSize="28" :inactiveStepsTextSize="27" title="已拒绝" v-if="jiaofeiRefundInfo.approval_status==='refuse'">
          <view class="slot-icon" slot="icon">
            <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_failed"></u-image>
          </view>
        </u-steps-item>
        <u-steps-item class="unpass" :activeStepsTextSize="28" :inactiveStepsTextSize="27" title="退款到账" v-if="jiaofeiRefundInfo.approval_status!=='agree'">
          <view class="slot-icon" slot="icon">
            <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_unpass"></u-image>
          </view>
        </u-steps-item>
        <u-steps-item class="pass" :activeStepsTextSize="28" :inactiveStepsTextSize="27" title="退款到账" v-if="jiaofeiRefundInfo.approval_status==='agree'">
          <view class="slot-icon" slot="icon">
            <u-image width="40rpx" height="40rpx" :src="themeImgPath.img_pass"></u-image>
          </view>
        </u-steps-item>
      </u-steps>
    </view>
    <view class="m-40">
      <view class="refund-info m-b-20 p-b-30" v-if="jiaofeiRefundInfo.approval_status==='refuse'||jiaofeiRefundInfo.approval_status === 'agree'">
        <view class="info-header">审批结果</view>
        <view class="info-item">
          <view class="flex row-between m-t-30">
            <view class="muted">审批内容：</view>
            <view class="text-over" v-if="jiaofeiRefundInfo.approval_status==='refuse'">{{jiaofeiRefundInfo.approval_reason}}</view>
            <view v-if="jiaofeiRefundInfo.approval_status === 'agree'">同意退款</view>
          </view>
        </view>
      </view>
      <view v-if="type==='apply'" class="refund-info m-b-20">
        <view class="info-header">退款申请</view>
        <view class="info-item">
          <u-cell
            title="退款类型:"
            is-link
            :border="false"
            :value="refundTypeAlias"
            @click="showRefundTypeList=true"
            :class="[refundTypeAlias==='请选择退款类型'?'value-placeholder':'']">
          </u-cell>
          <u-cell title="退款金额:" :border="false" >
            <view class="" slot="value">
              <template v-if="refundType==='ORDER_ALL_REFUND'">
                <view>￥{{ (jiaofeiRefundInfo.pay_fee/ 100).toFixed(2)}}</view>
              </template>
              <template v-else>
                <view>
                  <u-input
                    placeholder="请输入退款金额"
                    input-align="right"
                    border="none"
                    v-model="refundFee"
                  ></u-input>
                </view>
              </template>
            </view>
          </u-cell>
          <u-cell
            title="退款原因:"
            is-link
            :border="false"
            :value="refundReason"
            @click="showRefundReasonList=true"
            :class="[refundReason==='请选择退款原因'?'value-placeholder':'']">
          </u-cell>
        </view>
      </view>
      <view v-if="type==='detail'" class="refund-info m-b-20 p-b-30">
        <view class="info-header">退款申请</view>
        <view class="info-item">
          <view class="flex row-between m-t-30">
            <view class="muted">退款类型：</view>
            <view>{{jiaofeiRefundInfo.refund_type_alias}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">退款金额：</view>
            <view style="color: #FE5858;">￥{{ (jiaofeiRefundInfo.pay_fee/ 100).toFixed(2)}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">退款原因：</view>
            <view class="text-over">{{jiaofeiRefundInfo.refund_reason}}</view>
          </view>
        </view>
      </view>
      <view class="refund-info p-b-30 m-b-20">
        <view class="info-header">人员信息</view>
        <view class="info-item">
          <view class="flex row-between m-t-30">
            <view class="muted">用户姓名：</view>
            <view>{{jiaofeiRefundInfo.payer_name}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">人员编号：</view>
            <view>{{jiaofeiRefundInfo.payer_person_no}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">部门：</view>
            <view>{{jiaofeiRefundInfo.payer_depart_name}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">手机号码：</view>
            <view>{{jiaofeiRefundInfo.payer_phone}}</view>
          </view>
        </view>
      </view>
      <view class="refund-info p-b-30 m-b-20">
        <view class="info-header">缴费信息</view>
        <view class="info-item">
          <view class="flex row-between m-t-30">
            <view class="muted">缴费订单：</view>
            <view>{{jiaofeiRefundInfo.order_trade_no}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">缴费项目：</view>
            <view>{{jiaofeiRefundInfo.jiaofei_name}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">缴费类型：</view>
            <view>{{jiaofeiRefundInfo.jiaofei_type_alias}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">缴费金额：</view>
            <view v-if="type==='apply'">￥{{ (jiaofeiRefundInfo.pay_fee/ 100).toFixed(2)}}</view>
            <view v-if="type==='detail'">￥{{ (jiaofeiRefundInfo.jiaofei_fee/ 100).toFixed(2)}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">下单时间：</view>
            <view>{{jiaofeiRefundInfo.create_time}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">支付时间：</view>
            <view>{{jiaofeiRefundInfo.pay_time}}</view>
          </view>
          <view class="flex row-between m-t-30">
            <view class="muted">到账时间：</view>
            <view>{{jiaofeiRefundInfo.finish_time}}</view>
          </view>
        </view>
      </view>
      <view class="pay-btn" v-if="type==='apply'">
        <u-button text="提交申请" shape="circle" @click="checkRefund" :color="variables.bgLinearGradient1"></u-button>
      </view>
    </view>
    <u-picker
      :show="showRefundTypeList"
      :columns="refundTypeList"
      keyName="label"
      @confirm="confirmRefundType"
      @cancel="showRefundTypeList = false"
    ></u-picker>
    <u-picker
      :show="showRefundReasonList"
      :columns="refundReasonList"
      @confirm="confirmRefundReason"
      @cancel="showRefundReasonList = false"
    ></u-picker>
    <u-modal
      :show="showConfirmModal"
      showCancelButton
      @confirm="refundApply"
      @cancel="showConfirmModal = false"
      title="提示"
      content='确定要进行退款申请吗？确认后不可撤销'
      width="500rpx"
      :confirmColor="variables.colorPrimary">
    </u-modal>
  </view>
</template>

<script>
  import { apiJiaofeiRefundApproval } from '@/api/jiaofei.js'
  export default {
    data() {
      return {
        imgPath: this.$imgPath,
        type: '',
        jiaofeiRefundInfo: {},
        refundType: '',
        refundTypeAlias: '请选择退款类型',
        refundFee: '',
        refundReason: '请选择退款原因',
        showRefundTypeList: false,
        refundTypeList: [
          [{
            label: '全额退款',
            key: 'ORDER_ALL_REFUND'
          },
          {
            label: '部分退款',
            key: 'ORDER_PART_REFUND'
          }]
        ],
        showRefundReasonList: false,
        refundReasonList: [
          ['重复缴费', '协商退费', '退学退费']
        ],
        showConfirmModal: false
      }
    },
    onLoad() {
      if (this.$Route.query.data) {
        this.jiaofeiRefundInfo = this.$Route.query.data
        this.type = this.$Route.query.type
      }
    },
    methods: {
      confirmRefundType(e) {
        this.showRefundTypeList = false
        this.refundType = e.value[0].key
        this.refundTypeAlias = e.value[0].label
      },
      confirmRefundReason(e) {
        this.showRefundReasonList = false
        this.refundReason = e.value[0]
      },
      checkRefund() {
        let reg = /^-?(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/
        if (!this.refundType.length) {
          return uni.$u.toast('请选择退款类型')
        }
        if (this.refundType==='ORDER_PART_REFUND') {
          if (!this.refundFee.length) {
            return uni.$u.toast('请输入退款金额')
          }
          if (this.refundFee===0) {
            return uni.$u.toast('退款金额不能为零')
          }
          if (!reg.test(this.refundFee)) {
            return uni.$u.toast('请输入正确的金额')
          }
          if (Number(this.refundFee)*100 > this.jiaofeiRefundInfo.pay_fee) {
            return uni.$u.toast('退款金额不能大于订单金额')
          }
        }
        if (this.refundReason==='请选择退款原因') {
          return uni.$u.toast('请选择退款原因')
        }
        this.showConfirmModal = true
      },
      async refundApply() {
        this.$showLoading({
          title: '提交中....',
          mask: true
        })
        apiJiaofeiRefundApproval({
          order_jiaofei_id: this.jiaofeiRefundInfo.order_jiaofei_id,
          refund_type: this.refundType,
          refund_fee: this.refundType==='ORDER_PART_REFUND'?Number(this.refundFee)*100:this.jiaofeiRefundInfo.pay_fee,
          refund_reason: this.refundReason
        })
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              uni.hideLoading()
              uni.$u.toast('提交成功')
              this.$eventbus.$emit('jiaofeiRefund', true)
              this.$miRouter.back()
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.hideLoading()
            uni.$u.toast(err)
          })
      }
    }
  }
</script>

<style lang="scss" scoped>
.jiaofei-refund{
  .refund-steps{
    background-color: #FFFFFF;
    padding: 20rpx;
    .u-steps-item .u-steps-item__content .u-text .u-text__value--main{
      font-size: 28rpx !important;
      color: red !important;
    }
    ::v-deep.unpass .u-text__value{
      color: #bdbaba;
    }
    ::v-deep.pass .u-text__value{
      color: #11e69e;
    }
    ::v-deep.failed .u-text__value{
      color: #ff5757;
    }
    .slot-icon{
      image{
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
  .refund-info{
    background-color: #FFFFFF;
    border-radius: 20rpx;
    padding-left: 30rpx;
    padding-right: 30rpx;
    .info-header {
      border-bottom: 1px solid $border-color-base;
      height: 90rpx;
      line-height: 90rpx;
      margin-top: 30rpx;
    }
    .text-over{
      max-width: 420rpx;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    ::v-deep.u-cell__body{
      padding: 0;
      margin-bottom: 30rpx;
      .u-cell__title-text{
        color: #999999;
        font-size: 28rpx;
      }
      .u-cell__value{
        color: #000000;
      }
    }
    ::v-deep.value-placeholder{
      .u-cell__value{
        color: #c0c4cc!important;
      }
    }
  }
  .pay-btn{
    position: fixed;
    left: 0;
    bottom: 30rpx;
    width: 100%;
    padding: 0 40rpx;
  }
}
</style>
