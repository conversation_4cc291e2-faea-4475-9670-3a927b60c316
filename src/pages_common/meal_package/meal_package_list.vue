<template>
  <view :style="theme.style" class="meal-package-list">
    <view class="" style="background-color: #fff;">
			<u-tabs
				:list="tabs"
				:line-color="variables.colorPrimary"
				:active-style="{ fontWeight: 500, color: '#101010' }"
				@change="tabsChange"
			></u-tabs>
		</view>
    <view class="package-list m-t-30" v-if="reportMealPackList.length">
      <view v-for="(item, index) in reportMealPackList" :key="index" class="package-item">
        <view class="package-item-card">
          <view class="flex row-between package-title" :style="current === 0 ? 'background-color: #6cf6e1;' : 'background-color: #ffe8c8;'">
            <view class="">{{item.report_meal_pack_settingsname}}</view>
          </view>
          <view class="package-info">
            <view class="flex row-between m-b-20">
              <view class="muted">配餐日期：</view>
              <view>{{item.start_date}} ~ {{item.end_date}}</view>
            </view>
            <view class="flex row-between m-b-20">
              <view class="muted">餐段：</view>
              <view>{{item.meal_type_name}}</view>
            </view>
            <view class="package-num-price">
              <text>共{{item.meal_days}}天，{{item.meal_num}}份，￥{{ item.price }}</text>
            </view>
          </view>
        </view>
        <view class="package-btn-wrap">
          <view class="package-btn package-detail-btn" @click="gotoDetail(item)">详情</view>
          <view v-if="current === 0" class="package-btn package-stop-btn" @click="gotoResumeStop('stop', item)">停餐申请</view>
          <view v-if="current === 0" class="package-btn package-resume-btn" @click="gotoResumeStop('resume', item)">恢复就餐</view>
        </view>
      </view> 
		</view>
    <view v-else class="no-data">
      <img class="no-data-img" :src="themeImgPath.img_pack_no_data"/>
      <view class="xxl f-w-600 m-t-30">当前未购买餐包</view>
      <view class="muted m-t-10 m-b-40">请点击下方“购买餐包”按钮前往购买</view>
    </view>
    <view class="buy-btn-wrap">
      <view class="to-buy-btn" @click="gotoBuyPack">购买餐包</view>
    </view>
  </view>
</template>

<script>
import { getApiMealPackOrderList } from '@/api/report_meal.js'
import Cache from '@/utils/cache'
export default {
  data() {
    return {
      current: 0,
      tabs: [
				{
					name: '当前餐包',
					type: 'useing'
				},
				{
					name: '历史餐包',
					type: 'history'
				}
			],
      reportMealPackList: [],
			paramsData: {},
    }
  },
  onLoad() {
    this.paramsData = this.$store.state.appoint.select
    this.getReportMealPackList()
  },
  methods: {
    tabsChange({ index }) {
      this.reportMealPackList = []
			this.current = index
      this.getReportMealPackList()
		},
    getReportMealPackList() {
			this.$showLoading({
				mask: true
			})
			getApiMealPackOrderList({
        meal_pack_status: this.current === 0 ?  'use' : 'history',
				person_no: this.paramsData.person.person_no,
        company_id: Cache.get('userInfo').company_id
      })
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.reportMealPackList = res.data.results.map(item => {
              item.meal_type_name = item.meal_type_alias.join('、')
              item.price = (item.real_fee/100).toFixed(2)
              return item
            })
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
    gotoBuyPack() {
      this.$miRouter.replace({
        path: '/pages_bundle/select/select_diner',
        query: {
          type: 'report_meal',
          meal_pack: 1
        }
      })
    },
    gotoResumeStop(type, data) {
      this.$miRouter.push({
        path: '/pages_common/meal_package/meal_package_resume_stop',
        query: {
          data: this.$encodeQuery({
						id: data.id,
						report_meal_pack_settings_id: data.report_meal_pack_settings_id,
            organization: data.organization,
            startDate: data.start_date,
            endDate: data.end_date,
            type
					})
        }
      })
    },
    gotoDetail(data) {
      this.$miRouter.push({
        path: '/pages_common/meal_package/meal_package_detail',
        query: {
          id: data.id,
          start_date: data.start_date,
          end_date: data.end_date
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

.meal-package-list {
  .package-list{
    padding: 0 30rpx 120rpx;
    .package-item{
      border-radius: 24rpx;
      background-color: #FFF;
      margin-bottom: 20rpx;
      overflow: hidden;
      .package-item-card{
        position: relative;
        height: 300rpx;
      }
      .package-title{
        padding: 22rpx 20rpx 40rpx ;
        background-color: #ffe8c8;
        color: #000;
        font-size: 30rpx;
      }
      .package-info{
        position: absolute;
        top: 80rpx;
        left: 0;
        right: 0;
        z-index: 9997;
        background: #fff;
        padding: 30rpx;
        border-radius: 20rpx;
      }
      
      .package-num-price{
        font-size: 32rpx;
      }

      .package-btn-wrap{
        border-top: 1px solid #E4E4E4;
        padding: 20rpx 0;
        margin: 0 30rpx;
        display: flex;
        justify-content: right;
        .package-btn{
          text-align: center;
          height: 50rpx;
          line-height: 50rpx;
          border-radius: 8rpx;
          margin-left: 20rpx;
          font-size: 26rpx;
        }
        .package-resume-btn{
          width: 146rpx;
          color: #FFF;
          background-color: $color-primary;
        }
        .package-stop-btn{
          width: 146rpx;
          color: $color-primary;
          border: 1px solid $color-primary;
        }
        .package-detail-btn{
          width: 110rpx;
          color: #000;
          border: 1px solid #E4E4E4;
        }
      }
    }
  }
  .no-data{
    margin: 30rpx;
    background: #fff;
    border-radius: 30rpx;
    text-align: center;
    padding: 120rpx 0;
    .no-data-img{
      width: 240rpx;
    }
  }
  .buy-btn-wrap{
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 20rpx 40rpx;
    background: #fff;
    z-index: 9999;
    .to-buy-btn{
      background-color: $color-primary;
      color: #FFF;
      text-align: center;
      font-size: 30rpx;
      padding: 20rpx 0;
      border-radius: 100rpx;
    }
  }
}
</style>
