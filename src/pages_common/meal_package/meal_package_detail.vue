<template>
  <view :style="theme.style" class="meal-package">
    <!-- 顶部筛选 -->
    <view class="filter-bar" @click="showFilterPopup">
      <view class="filter-content">
        <text class="filter-text m-r-10">筛选</text>
        <u-icon v-if="showFilter" name="arrow-up" size="32" color="#666"></u-icon>
        <u-icon v-else name="arrow-down" size="32" color="#666"></u-icon>
      </view>
    </view>

    <!-- 列表 -->
     <mescroll-uni
      v-if="showList"
      ref="mescrollRef"
      @init="mescrollInit"
      top="110"
      @down="downCallback"
      @up="upCallback"
      @emptyclick="emptyClick"
      :isShowEmpty="false"
      class="list"
    >
      <!-- 数据列表 -->
      <view
        v-for="(item, index) in mealList"
        :key="index"
        class="list-item"
      >
        <view class="item-header">
          <view class="date-info">
            <text class="date">{{ item.report_date }}</text>
            <text class="type">{{ item.meal_type_alias }}</text>
          </view>
          <view class="status" :class="item.statusClass">
            <text class="dot"></text>
            <text class="status-text">{{ item.meal_pack_status }}</text>
          </view>
        </view>
        <view v-if="item.meal_pack_status === '未取餐'" class="item-action">
          <view
            class="action-btn"
            @click="handleAction(item)"
          >
            取餐码
          </view>
        </view>
      </view>
    </mescroll-uni>
    <view v-else class="no-data">
      <img class="no-data-img" :src="themeImgPath.img_pack_no_data"/>
      <view class="xxl f-w-600 m-t-30">暂无数据</view>
      <view class="muted m-t-10 m-b-40">您当前暂无餐包数据</view>
      <view class="go-back" @click="$miRouter.back()">返回</view>
    </view>

    <!-- 筛选弹窗 -->
    <filter-popup
      :show.sync="showFilter"
      :start-date="startDate"
      :end-date="endDate"
      @confirm="confirmFilterPopup"
    ></filter-popup>

  </view>
</template>

<script>
import { getApiMealPackOrderDetail } from '@/api/report_meal.js'
import filterPopup from '../components/filter-popup.vue'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
export default {
  mixins: [MescrollMixin],
  components: {
    filterPopup
	},
  data() {
    return {
      showFilter: false,
      selectedStatus: '',
      selectedDate: '',
      selectedMeal: '',
      orderInfoId: '',
      startDate: '',
      endDate: '',
      mealList: [],
      showList: true

    };
  },
  onLoad() {
    // 从路由参数获取日期范围，如果没有则使用默认值
    this.orderInfoId = this.$Route.query.id
    this.startDate = this.$Route.query.start_date;
    this.endDate = this.$Route.query.end_date;
  },
  methods: {
    upCallback(page) {
      this.getReportMealPackDetail(page)
    },
    showFilterPopup() {
      this.showFilter = !this.showFilter;
    },
    confirmFilterPopup(e) {
      this.selectedStatus = e.status;
      this.selectedDate = e.date;
      this.selectedMeal = e.meal;
      this.showFilter = false;
      this.upCallback({ num: 1, size: 10 })
    },
    getReportMealPackDetail(page) {
			this.$showLoading({
				mask: true
			})
      let params = {
        order_report_meal_pack_id: this.orderInfoId,
        page: page.num,
        page_size: page.size,
      }
      if (this.selectedDate.length) {
        params.start_date = this.selectedDate[0]
        params.end_date = this.selectedDate[1]
      } else {
        params.start_date = this.startDate
        params.end_date = this.endDate
      }
      if (this.selectedMeal) {
        params.meal_type = this.selectedMeal
      }
      if (this.selectedStatus === 'no_take') { // 未取餐
        params.take_meal_status = ['no_take']
        params.dining_status = ['dining']
      } else if (this.selectedStatus === 'take_out') { // 已取餐
        params.take_meal_status = ['take_out']
        params.dining_status = ['dining']
      } else if (this.selectedStatus === 'time_out') { // 已过期
        params.take_meal_status = ['time_out']
        params.dining_status = ['dining']
      } else if (this.selectedStatus === 'cancel') { // 已退款
        params.take_meal_status = ['cancel']
      } else if (this.selectedStatus === 'stop') { // 已停餐待处理
        params.take_meal_status = ['no_take', 'time_out']
        params.dining_status = ['stop']
      }
			getApiMealPackOrderDetail(params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						const results = res.data.results.map(item => {
              if (item.meal_pack_status === '已停餐待退款') {
                item.meal_pack_status = '已停餐待处理'
              }
              if (item.meal_pack_status === '未取餐') {
                item.statusClass = "orange"
              } else if (item.meal_pack_status === '已过期') {
                item.statusClass = "gray"
              } else if (item.meal_pack_status === '已停餐待处理' || item.meal_pack_status === '停餐审核中' || item.meal_pack_status === '已退款') {
                item.statusClass = "red"
              } else if (item.meal_pack_status === '已取餐') {
                item.statusClass = "green"
              }
              return item
            })
            const count = res.data.count
            const page_size = res.data.page_size
            // 如果是第一页需手动置空列表
            if (page.num == 1) this.mealList = []
            // 追加新数据
            this.mealList = [...this.mealList, ...results]
            //方法二(推荐): 后台接口有返回列表的总数据量 count, 判断是否有下一页
            let pageLength = 0
            if (results) {
              pageLength = results.length
            } else {
              pageLength = 0
            }
            if (this.mealList.length === 0) {
              this.showList = false
            } else {
              this.showList = true
            }
            this.mescroll.endBySize(pageLength, count)
					} else {
            this.showList = false
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
    handleAction(item) {
      this.$miRouter.push({
				path: '/pages_bundle/meal_public/meal_code',
				query: {
					data: this.$encodeQuery({
						trade_no: item.trade_no,
						meal_number_qrcode: item.trade_no,
						meal_number: item.trade_no
					})
				}
			})
    },
  },
};
</script>

<style lang="scss" scoped>
.meal-package {
  background: #f5f5f5;
  min-height: 100vh;
  .filter-bar {
    position: fixed;
    top: 82rpx;
    width: 100%;
    background: #fff;
    margin-bottom: 20rpx;
    padding: 24rpx 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    .filter-content {
      display: flex;
      align-items: center;

      .filter-text {
        color: #333;
        font-size: 30rpx;
        font-weight: 500;
      }
    }
  }

  .list {
    margin-top: 106rpx;
    padding: 0 20rpx;
    .list-item {
      background: #fff;
      border-radius: 16rpx;
      padding: 32rpx 24rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
      margin: 0 30rpx 30rpx;

      .item-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .date-info {
          display: flex;
          align-items: center;

          .date {
            color: #333;
            font-size: 32rpx;
            font-weight: 500;
            margin-right: 20rpx;
          }

          .type {
            color: #666;
            font-size: 28rpx;
          }
        }

        .status {
          display: flex;
          align-items: center;
          font-size: 28rpx;

          .dot {
            width: 12rpx;
            height: 12rpx;
            border-radius: 50%;
            margin-right: 8rpx;
          }

          .status-text {
            font-size: 28rpx;
          }

          &.gray {
            color: #999;
            .dot {
              background: #999;
            }
          }

          &.red {
            color: #ff4757;
            .dot {
              background: #ff4757;
            }
          }

          &.green {
            color: #2ed573;
            .dot {
              background: #2ed573;
            }
          }

          &.orange {
            color: #ffa502;
            .dot {
              background: #ffa502;
            }
          }
        }
      }

      .item-action {
        margin-top: 30rpx;
        padding-top: 30rpx;
        display: flex;
        justify-content: flex-end;
        border-top: 1px solid #E4E4E4;

        .action-btn {
          line-height: 50rpx;
          padding: 0 32rpx;
          border-radius: 8rpx;
          font-size: 24rpx;
          text-align: center;
          min-width: 120rpx;
          background: $color-primary;
          color: #fff;
        }
      }
    }
  }
  .no-data{
    margin: 106rpx 30rpx;
    background: #fff;
    border-radius: 30rpx;
    text-align: center;
    padding: 120rpx 0;
    .no-data-img{
      width: 240rpx;
    }
    .go-back{
      color: #fff;
      text-align: center;
      padding: 20rpx;
      border-radius: 50rpx;
      margin: 0 240rpx;
      background: linear-gradient(90deg, #11E69E 0%, #11E6C5 98.46%);
    }
  }
}
</style>