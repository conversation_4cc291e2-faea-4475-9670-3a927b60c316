<template>
  <view :style="theme.style" class="meal-package">
    <view class="header">
			<!-- #ifndef MP-ALIPAY -->
			<u-navbar bg-color="transparent" left-icon-color="#fff" auto-back>
				<view class="white lg f-w-500" slot="center">餐包购买</view>
			</u-navbar>
			<!-- #endif -->
			<view class="canteen flex col-center row-between white">
				<router-link to="/pages_bundle/select/select_diner?type=report_meal&meal_pack=1">
					<view class="flex col-center xxl">
						<u-icon name="map" color="#fff" size="38rpx"></u-icon>
						<text class="bold line-2">{{ org.org_name }}</text>
						<u-icon name="arrow-right" color="#fff" size="28rpx"></u-icon>
					</view>
				</router-link>
			</view>
		</view>
    <data-select v-if="false" :localdata="canteenList" :value="canteenValue" size="16px" class="data-select" @change="changeCanteenType"></data-select>
		<view class="package-list m-t-30" v-if="reportMealPackList.length">
      <view v-for="(item, index) in reportMealPackList" :key="index" class="package-item">
        <view class="package-item-card" :style="item.remark ? 'height: 340rpx;' : 'height: 292rpx;'">
          <view class="flex row-between package-title">
            <view class="">{{item.name}}</view>
            <view class="flex col-center xs m-l-50">
              <view class="sm">购买须知</view>
              <u-icon @click="openBuyInfo(item)" name="question-circle-fill" color="#f1f1f1" size="32rpx"></u-icon>
            </view>
          </view>
          <view class="package-info">
            <view class="flex row-between m-b-20">
              <view class="muted">配餐日期：</view>
              <view>{{item.start_date}} ~ {{item.end_date}}</view>
            </view>
            <view class="flex row-between m-b-20">
              <view class="muted">餐段：</view>
              <view>{{item.meal_type_name}}</view>
            </view>
            <view v-if="item.remark" class="flex row-between m-b-20">
              <view class="muted">备注：</view>
              <zb-tooltip :content="item.remark">{{ nameFormat(item.remark, 15) }}</zb-tooltip>
            </view>
            <view class="flex row-between">
              <view class="muted">餐包总数：</view>
              <view>共{{item.meal_days}}天，{{item.meal_num}}份</view>
            </view>
          </view>
        </view>
        <view class="package-btn-wrap">
          <view class="package-fee">
            <price-format :price="item.fee" :size="36" color="#ff5650"></price-format>
          </view>
          <view class="package-buy-btn" @click="gotoConfirm(item)">购买</view>
        </view>
      </view> 
		</view>
    <view class="package-list m-t-30" v-else>
      <view class="flex flex-center">
        <u-image width="200rpx" height="200rpx" src="/static/images/mescroll-empty.png"></u-image>
      </view>
      <view class="muted text-center m-t-40">~ 空空如也 ~</view>
    </view>
    <u-modal :show="showBuyInfo" class="buy-info-modal" :title="'购买须知'" @confirm="showBuyInfo = false" :confirmColor="variables.colorPrimary" confirmText="关闭">
      <view class="slot-content m-t-20 m-b-20">
        <view class="title">停餐时间说明</view>
        <view class="info">
          <view v-for="(item, index) in mealPackInfo.meal_type_alias" :key="index">
            {{ item }}：开餐前{{ mealPackInfo[mealPackInfo.meal_type[index] + '_ahead_stop'] }}小时内，不可申请停餐
          </view>
        </view>
        <view class="title">恢复就餐时间说明</view>
        <view class="info">
          <view v-for="(item, index) in mealPackInfo.meal_type_alias" :key="index">
            {{ item }}：开餐前{{ mealPackInfo[mealPackInfo.meal_type[index] + '_ahead_resume'] }}小时内，不可申请恢复就餐
          </view>
        </view>
        <view class="title">退费说明</view>
        <view class="info">
          <view v-if="mealPackInfo.refund_type === 'manual'">工作人员审批通过后即可退款</view>
          <view v-if="mealPackInfo.refund_type === 'meal_end'">餐费将在{{ mealPackInfo.manual_day }}天后自动退回</view>
          <view v-if="mealPackInfo.refund_type === 'specify_date'">
            <view v-if="mealPackInfo.specify_date_json.specify_type === 'week'">
              餐费将在每
              <text v-for="(item,index) in mealPackInfo.specify_date_json.week" :key="index">
                周{{formatWeek(item)}}
                <text v-if="index < mealPackInfo.specify_date_json.week.length-1">、</text>
              </text>
              统一退回
            </view>
            <view v-if="mealPackInfo.specify_date_json.specify_type === 'month'">
              餐费将在每月
              <text v-for="(item,index) in mealPackInfo.specify_date_json.days" :key="index">
                <text v-if="item !==-1 ">{{item}}日</text>
                <text v-else>最后一日</text>
                <text v-if="index < mealPackInfo.specify_date_json.days.length-1">、</text>
              </text>
              统一退回
            </view>
            <view v-if="mealPackInfo.specify_date_json.specify_type === 'fixed'">
              餐费将在
              <text v-for="(item,index) in mealPackInfo.specify_date_json.days" :key="index">
                {{item}}
                <text v-if="index < mealPackInfo.specify_date_json.days.length-1">、</text>
              </text>
              统一退回
            </view>
          </view>
          <view>如有疑问，请联系现场工作人员</view>
        </view>
      </view>
    </u-modal>
  </view>
</template>

<script>
import { getApiReportMealPackList } from '@/api/report_meal.js'
import Cache from '@/utils/cache'
export default {
  data() {
    return {
      reportMealPackList: [],
			paramsData: {},
      canteenList: [{
        text: '全部',
        value: 0
      }, {
        text: '待审批',
        value: 'PENDING'
      }, {
        text: '审批中',
        value: 'PROCESSING'
      }],
      canteenValue: 0,
      shadowStyle: {
        backgroundImage: "none",
        marginTop: "10rpx"
      },
      mealPackInfo: {},
      showBuyInfo: false
    }
  },
  onShow() {
    this.paramsData = this.$store.state.appoint.select
    this.getReportMealPackList()
	},
  onLoad() {
  },
  computed: {
		org() {
			// 用户进入页面前选择的组织
			const state = this.$store.state.appoint
			return state.select.org || {}
		},
  },
  methods: {
    getReportMealPackList(data, clean_org_all) {
			this.$showLoading({
				mask: true
			})
			getApiReportMealPackList({
				person_no: this.paramsData.person.person_no,
        company_id: Cache.get('userInfo').company_id
      })
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.reportMealPackList = res.data.results.map(item => {
              item.meal_type_name = item.meal_type_alias.join('、')
              return item
            })
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
    openBuyInfo(data) {
      this.showBuyInfo = true
      this.mealPackInfo = data
    },
    changeCanteenType() {

    },
    gotoConfirm(data) {
      if (data.can_buy) {
        this.$miRouter.replace({
          path: '/pages_common/meal_package/meal_package_confirm',
          query: {
            // #ifdef MP-ALIPAY
            data: this.$encodeQuery(data),
            // #endif
            // #ifndef MP-ALIPAY
            data: data,
            // #endif
          }
        })
      }
    },
    formatWeek(num) {
      let weeks = ['一', '二', '三', '四', '五', '六', '日']
      return weeks[num - 1]
    },
    // 格式化文字 超过多少显示...
		nameFormat(name,number) {
			if(!name) return
			let subStr = name.slice(0, number)
			subStr = subStr + (name.length > number ? '...' : '')
			return subStr
		},
  }
}
</script>

<style lang="scss" scoped>

.meal-package {
  .header{
    background: #38ccc6 $bg-linear-gradient-1;
    .canteen {
      padding: 28rpx 36rpx;
    }
  }
  .data-select{
    width: 180rpx;
    padding: 15rpx 30rpx;
    flex: inherit!important;
  }
  .package-list{
    padding: 0 30rpx 30rpx;
    .package-item{
      border-radius: 20rpx;
      background-color: #FFF;
      margin-bottom: 20rpx;
      overflow: hidden;
      .package-item-card{
        position: relative;
        height: 320rpx;
      }
      .package-title{
        padding: 22rpx 20rpx 40rpx ;
        background-color: $color-primary;
        color: #FFF;
        font-size: 30rpx;
      }
      .package-info{
        position: absolute;
        top: 80rpx;
        left: 0;
        right: 0;
        z-index: 9999;
        background: #fff;
        padding: 30rpx;
        border-radius: 20rpx;
      }

      .package-btn-wrap{
        border-top: 1px solid #E4E4E4;
        padding: 20rpx 0;
        margin: 0 30rpx;
        display: flex;
        justify-content: space-between;
        .package-buy-btn{
          width: 140rpx;
          text-align: center;
          color: #FFF;
          padding: 6rpx 10rpx;
          border-radius: 10rpx;
          background-color: $color-primary;
        }
      }
    }
  }
  .buy-info-modal{
    text-align: left;
    font-size: 28rpx;
    ::v-deep .u-modal__content{
      justify-content: flex-start!important;
    }
    .title{
      font-weight: bold;
      margin-bottom: 20rpx;
    }
    .info{
      margin-left: 30rpx;
      margin-bottom: 30rpx;
    }
  }
}
</style>
