<template>
  <view :style="theme.style" class="meal-package-resume">
    <!-- #ifndef MP-ALIPAY -->
    <u-navbar left-icon-color="#000" auto-back class="navbar-header">
      <view class="lg f-w-600" slot="center">{{ packType === 'stop' ? '停餐申请' : '恢复就餐' }}</view>
    </u-navbar>
    <!-- #endif -->
    <!-- 顶部筛选 -->
    <view class="filter-bar" @click="showFilterPopup">
      <view class="filter-content">
        <text class="filter-text m-r-10">筛选</text>
        <u-icon v-if="showFilter" name="arrow-up" size="32" color="#666"></u-icon>
        <u-icon v-else name="arrow-down" size="32" color="#666"></u-icon>
      </view>
    </view>
    <view v-if="packType === 'stop'" class="meal-tips">选择你要停餐的餐段信息</view>
    <view v-else class="meal-tips">选择你要恢复就餐的餐段信息，仅展示可恢复的餐段</view>
    <view class="meal-list" v-if="showList">
      <u-checkbox-group v-model="checkedItem" @change="changeItem">
        <mescroll-uni
          ref="mescrollRef"
          @init="mescrollInit"
          top="260"
          bottom="120"
          @down="downCallback"
          @up="upCallback"
          @emptyclick="emptyClick"
          :isShowEmpty="false"
          class="list"
        >
        <view v-for="(item, index) in packList" :key="index" class="meal-item">
          <u-checkbox v-if="packType === 'stop'" :activeColor="variables.colorPrimary" :name="item.trade_no" :key="item.trade_no" :disabled="!item.can_stop"></u-checkbox>
          <u-checkbox v-else :activeColor="variables.colorPrimary" :name="item.trade_no" :key="item.trade_no" :disabled="!item.can_resume"></u-checkbox>
          <view>{{timeFormat(new Date(item.report_date), 'mm月dd日 星期w')}} {{item.meal_type_alias}}</view>
        </view>
        </mescroll-uni>
      </u-checkbox-group>
    </view>
    <view v-else class="no-data">
      <img class="no-data-img" :src="themeImgPath.img_pack_no_data"/>
      <view class="xxl f-w-600 m-t-30">无可申请订单</view>
      <view v-if="packType === 'stop'" class="muted m-t-10 m-b-40">您当前暂无可以申请停餐的订单</view>
      <view v-else class="muted m-t-10 m-b-40">您当前暂无可以申请恢复就餐的订单</view>
      <view class="go-back" @click="$miRouter.back()">返回</view>
    </view>
    <view class="footer">
      <view class="bg-white footer--warpper flex row-between col-center">
        <u-checkbox-group v-model="checkedAll" @change="changeAll">
          <u-checkbox :activeColor="variables.colorPrimary" :disabled="checkAllDisable" name="all" key="all">全选</u-checkbox>
        </u-checkbox-group>
        <view v-if="checkedItem.length">
          <text class="muted">已选</text>
          <text class="xl f-w-600 m-l-5 m-r-5">{{checkedItem.length}}</text>
          <text class="muted">餐</text>
        </view>
        <view class="submit-btn white flex flex-center" :class="!checkedItem.length ? 'un-submit-btn' : ''" @click="stopDiningPackOrder(packType)">
          {{ packType === 'stop' ? '申请停餐' : '恢复就餐'}}
        </view>
      </view>
    </view>
    <!-- 筛选弹窗 -->
    <filter-popup
      :show.sync="showFilter"
      :start-date="orderInfo.startDate"
      :end-date="orderInfo.endDate"
      :is-show-status="false"
      @confirm="confirmFilterPopup"
    ></filter-popup>
  </view>
</template>

<script>
import {
  getApiMealPackOrderStopList,
  getApiMealPackOrderRefundList,
  getApiMealPackOrderResume,
  getApiReportMealPackList,
  getApiMealPackOrderDiningStatus
} from '@/api/report_meal.js'
import { timeFormat } from '@/utils/date.js'
import filterPopup from '../components/filter-popup.vue'
import Cache from '@/utils/cache'
import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
export default {
  mixins: [MescrollMixin],
  components: {
    filterPopup
	},
  data() {
    return {
      paramsData: {},
      orderInfo: {},
      packType: '',
      selectedDate: '',
      selectedMeal: '',
      packList: [],
      loading: false,
      showFilter: false,
      checkedAll: [],
      checkedItem: [],
      packOrderSettingInfo: {},
      showList: true,
      showModal: false
    }
  },
  onShow() {
	},
  computed: {
    // 控制全选按钮是否禁用
    checkAllDisable() {
      // 如果列表为空，禁用全选
      if (!this.packList || this.packList.length === 0) {
        return true;
      }

      // 检查是否所有项目都被禁用
      if (this.packType === 'resume') {
        // 恢复就餐：如果没有任何可恢复的项目，禁用全选
        return !this.packList.some(item => item.can_resume);
      } else {
        // 停餐申请：如果没有任何可停餐的项目，禁用全选
        return !this.packList.some(item => item.can_stop);
      }
    }
  },
  onLoad() {
    this.paramsData = this.$store.state.appoint.select
    this.orderInfo = this.$decodeQuery(this.$Route.query.data)
    this.packType = this.orderInfo.type
  },
  methods: {
    upCallback(page) {
      this.getReportMealPackList(page)
    },
    timeFormat,
    getReportMealPackList(page) {
			this.$showLoading({
				mask: true
			})
      let api
      let params = {
        order_report_meal_pack_id: this.orderInfo.id,
        page: page.num,
        page_size: page.size,
      }
      if (this.selectedDate.length) {
        params.start_date = this.selectedDate[0]
        params.end_date = this.selectedDate[1]
      } else {
        params.start_date = this.orderInfo.startDate
        params.end_date = this.orderInfo.endDate
      }
      if (this.selectedMeal) {
        params.meal_type = this.selectedMeal
      }
      if (this.packType === 'resume') {
        api = getApiMealPackOrderRefundList
        params.org_id = this.orderInfo.organization
        params.report_meal_pack_settings_id = this.orderInfo.report_meal_pack_settings_id
      } else {
        api = getApiMealPackOrderStopList
        params.dining_status = ['dining']
        params.take_meal_status = ['no_take']
      }
			api(params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						const results = res.data.results.map(item => {
              item.checked = false
              return item
            })
            const count = res.data.count
            const page_size = res.data.page_size
            // 如果是第一页需手动置空列表
            if (page.num == 1) this.packList = []
            // 追加新数据
            this.packList = [...this.packList, ...results]
            //方法二(推荐): 后台接口有返回列表的总数据量 count, 判断是否有下一页
            let pageLength = 0
            if (results) {
              pageLength = results.length
            } else {
              pageLength = 0
            }
            if (this.packList.length === 0) {
              this.showList = false
            } else {
              this.showList = true
            }
            this.mescroll.endBySize(pageLength, count)

            if (this.showModal) {
              uni.showToast({
                title: `${this.packType === 'stop' ? '停餐申请' : '恢复就餐'}成功`,
                icon: 'success'
              })
              this.showModal = false
            }
					} else {
            this.showList = false
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
    showFilterPopup() {
      this.showFilter = !this.showFilter;
    },
    confirmFilterPopup(e) {
      this.selectedDate = e.date;
      this.selectedMeal = e.meal;
      this.showFilter = false;
      this.upCallback({ num: 1, size: 10 })
    },
    changeAll(e) {
      this.checkedAll = e
      if (this.checkedAll.length) {
        this.checkedItem = []
        this.packList.map(item => {
          if (this.packType === 'resume') {
            if (item.can_resume) this.checkedItem.push(item.trade_no)
          } else {
            if (item.can_stop) this.checkedItem.push(item.trade_no)
          }
        })
      } else {
        this.checkedItem = []
      }
    },
    changeItem(e) {
      this.checkedItem = e
      let length = 0
      this.packList.map(item => {
        if (this.packType === 'resume') {
          if (item.can_resume) length++
        } else {
          if (item.can_stop) length++
        }
      })
      if (this.checkedItem.length === length) {
        this.checkedAll = ['all']
      } else {
        this.checkedAll = []
      }
    },
    // 恢复就餐 申请停餐
		stopDiningPackOrder(type) {
      if (!this.checkedItem.length) return uni.$u.toast('请选择餐包订单')
			if (type === 'stop') {
				this.getDiningPackOrderSetting(type)
			} else if (type === 'resume') {
				let modalText = `是否确认恢复就餐`
				this.stopDiningPackModal('恢复就餐', modalText, type)
			}
		},
		async getDiningPackOrderSetting(type) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiReportMealPackList({
				order_report_meal_pack_id: this.orderInfo.id,
        person_no: Cache.get('userInfo').person_no
      })
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						if (res.data && res.data.results.length) {
							this.packOrderSettingInfo = res.data.results[0]
						}
						let modalText = `是否申请停餐？`
						if (this.packOrderSettingInfo.refund_type === 'manual') {
							modalText = `是否申请停餐？餐费将在工作人员审批后退回。如有疑问，请联系现场工作人员`
						} else if (this.packOrderSettingInfo.refund_type === 'meal_end') {
							modalText = `是否申请停餐？餐费将在${this.packOrderSettingInfo.manual_day}天后自动退回，如有疑问，请联系现场工作人员`
						} else if (this.packOrderSettingInfo.refund_type === 'specify_date') {
							if (this.packOrderSettingInfo.specify_date_json.specify_type === 'week') {
								modalText = `是否申请停餐？餐费将在每`
								this.packOrderSettingInfo.specify_date_json.week.map((item, index) => {
									modalText += `周${item}`
									if (index < this.packOrderSettingInfo.specify_date_json.week.length - 1) {
										modalText += '、'
									}
								})
								modalText += `统一退回`
							} else if (this.packOrderSettingInfo.specify_date_json.specify_type === 'month') {
								modalText = `是否申请停餐？餐费将在每月`
								this.packOrderSettingInfo.specify_date_json.days.map((item, index) => {
									if( item !== -1 ) {
										modalText += `${item}日`
									} else {
										modalText += `最后一日`
									}
									if (index < this.packOrderSettingInfo.specify_date_json.days.length - 1) {
										modalText += '、'
									}
								})
								modalText += `统一退回`
							} else if (this.packOrderSettingInfo.specify_date_json.specify_type === 'fixed') {
								modalText = `是否申请停餐？餐费将在`
								this.packOrderSettingInfo.specify_date_json.days.map((item, index) => {
									modalText += `${item}`
									if (index < this.packOrderSettingInfo.specify_date_json.days.length - 1) {
										modalText += '、'
									}
								})
								modalText += `统一退回`
							}
						} else if (this.packOrderSettingInfo.refund_type === 'immediate_refund') {
              modalText = `是否申请停餐？餐费将在申请后退回，如有疑问，请联系现场工作人员`
            }
						this.stopDiningPackModal('申请停餐', modalText, type)
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
		},
		async stopDiningPackModal(modalTitle, modalText, type) {
			let _this = this
			uni.showModal({
				title: modalTitle,
				content: modalText,
				confirmText: '确认',
				cancelText: '取消',
				success: function(res) {
					if (res.confirm) {
						_this.handleMealPackageResume()
					} else if (res.cancel) {
						// console.log('用户点击取消')
					}
				}
			})
		},
    handleMealPackageResume() {
      this.$showLoading({
				mask: true
			})
      let api
      let params = {
        trade_nos: this.checkedItem,
        person_no: Cache.get('userInfo').person_no
      }
      if (this.packType === 'resume') {
        api = getApiMealPackOrderResume
        params.report_meal_pack_settings_id = this.orderInfo.report_meal_pack_settings_id
        params.org_id = this.orderInfo.organization
      } else {
        api = getApiMealPackOrderDiningStatus
        params.dining_status = "stop"
      }
			api(params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
            this.showModal = true
            this.upCallback({ num: 1, size: 10 })
            this.checkedItem = []
            this.checkedAll = []
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(error => {
					uni.$u.toast(error.message)
				})
    }
  }
}
</script>

<style lang="scss">
.meal-package-resume {
  .navbar-header{
    position: fixed;
    top: 0;
    width: 100%;
  }
  .filter-bar {
    position: fixed;
    top: 82rpx;
    width: 100%;
    background: #fff;
    padding: 24rpx 32rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    .filter-content {
      display: flex;
      align-items: center;

      .filter-text {
        color: #333;
        font-size: 30rpx;
        font-weight: 500;
      }
    }
  }

  .meal-tips{
    margin-top: 166rpx;
    background: #FFF3E3;
    padding: 20rpx 30rpx;
  }

  .meal-list{
    padding: 30rpx;
    padding-bottom: 120rpx;
    .u-checkbox-group{
      display: block;
    }
  }
  .meal-item{
    display: flex;
    line-height: 54rpx;
    background: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    margin: 0 30rpx 30rpx;
  }
  .footer {
    width: 100%;
    position: fixed;
    z-index: 999;
    bottom: 0;
    left: 0;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 100rpx;
      padding-left: 38rpx;
    }

    .submit-btn {
      width: 280rpx;
      background-color: $color-primary;
      margin: 12rpx 20rpx;
      border-radius: 60rpx;
      font-size: 32rpx;
      padding: 20rpx 0;
    }
    .un-submit-btn{
      background-color: $color-primary-light-4;
    }
  }
  .no-data{
    margin: 30rpx;
    background: #fff;
    border-radius: 30rpx;
    text-align: center;
    padding: 120rpx 0;
    .no-data-img{
      width: 240rpx;
    }
    .go-back{
      color: #fff;
      text-align: center;
      padding: 20rpx;
      border-radius: 50rpx;
      margin: 0 240rpx;
      background: linear-gradient(90deg, #11E69E 0%, #11E6C5 98.46%);
    }
  }
}
</style>
