<template>
  <view :style="theme.style" class="car-order">
    <view class="no-card" v-if="!carInfo.car_no">
      <view class="flex flex-center">
        <u-image width="200rpx" height="158rpx" :src="themeImgPath.img_car_warn"></u-image>
      </view>
      <view class="xl f-w-600 text-center m-t-40">暂无车辆信息</view>
      <view class="muted text-center m-t-20">请先联系管理员录入车俩信息，再绑定</view>
    </view>
    <view class="" v-else>
      <view class="car-info">
        <view class="">
          <view class="md f-w-600">{{carInfo.car_no}}</view>
          <!-- <view class="muted xs m-t-12">长租车（2023-5-29~2023-6-29）</view> -->
        </view>
        <view class="muted sm flex" @click="gotoSelectCar">
          <text>换车</text>
          <u-icon color="#acafb1" name="arrow-right" size="28rpx"></u-icon>
        </view>
      </view>
      <view class="subsection-wrap">
        <u-subsection
          :list="typelist"
          :current="currentTypeIndex"
          :activeColor="variables.colorPrimary"
          mode="subsection"
          @change="typeChange">
        </u-subsection>
      </view>
      <view class="select-wrap">
        <data-select :localdata="timeTypeList" :value="timeValue" class="data-select m-r-30" @change="changeTimeType"></data-select>
        <data-select v-if="currentType==='order'" :localdata="orderTypeList" :value="orderValue" class="data-select" @change="changeOrderType"></data-select>
      </view>
      <view class="no-data" v-if="!orderList.length">
        <view class="flex flex-center">
          <u-image width="200rpx" height="200rpx" :src="themeImgPath.img_no_data"></u-image>
        </view>
        <view class="muted text-center m-t-40">暂无记录</view>
      </view>
      <view class="">
        <mescroll-uni
          ref="mescrollRef"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
          @emptyclick="emptyClick"
          top="330"
        >
          <list :type="currentType" :list="orderList"></list>
        </mescroll-uni>
      </view>
    </view>
  </view>
</template>

<script>
  import { apiQueryCar, apiCarPassRecord, apiCarOrderPassRecord } from '@/api/car.js'
  import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
  import list from '../components/car_admin/list.vue'
  import Cache from '@/utils/cache'
import { encodeQuery } from '../../utils/util'
  export default {
    mixins: [MescrollMixin],
    components: { list },
    data() {
      return {
        imgPath: this.$imgPath,
        userinfo: {},
        carInfo: {},
        currentType: 'record',
        currentTypeIndex: 0,
        typelist: [{
          name: '通行记录',
          value: 'record'
        },{
          name: '通行订单',
          value: 'order'
        }],
        timeValue: 'week',
        timeTypeList: [{
          text: '本周',
          value: 'week'
        }, {
          text: '本月',
          value: 'month'
        }],
        startDate: '',
        endDate: uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd hh:MM:ss'),
        orderValue: 0,
        orderTypeList: [{
          text: '全部',
          value: 0
        }, {
          text: '消费',
          value: 'payment'
        }, {
          text: '退款',
          value: 'refund'
        }],
        orderList: []
      }
    },
    onShow() {
      this.carInfo =  {}
      this.getQueryCar()
      this.userinfo = Cache.get('userInfo')
      // 计算当前周周一
      let before = new Date(new Date().getTime()).getDay()
      let date_before = new Date().getTime() - 24 * 60 * 60 * 1000 * (before - 1)
      this.startDate = uni.$u.timeFormat(date_before, 'yyyy-mm-dd hh:MM:ss')
    },
    methods: {
      typeChange(e) {
        this.currentTypeIndex = e
        this.currentType = this.typelist[e].value
        this.orderList = []
        this.reloadData()
      },
      changeTimeType(e) {
        this.timeValue = e
        
        if (this.timeValue === 'week') {
          // 计算当前周周一
          let before = new Date(new Date().getTime()).getDay()
          let date_before = new Date().getTime() - 24 * 60 * 60 * 1000 * (before - 1)
          this.startDate = uni.$u.timeFormat(date_before, 'yyyy-mm-dd hh:MM:ss')
        } else {
          this.startDate = uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm') + '-01 00:00:00'
        }
        this.reloadData()
      },
      changeOrderType(e) {
        this.orderValue = e
        this.reloadData()
      },
      reloadData() {
        this.upCallback({
          num: 1,
          size: 10
        })
        // this.mescroll.resetUpScroll() // 重置列表数据为第一页
        // this.mescroll.scrollTo(0, 0) // 重置列表数据为第一页时,建议把滚动条也重置到顶部,避免无法再次翻页的问题
      },
      upCallback(page) {
        this.getCarRecord(page)
      },
      getCarRecord(page) {
        this.$showLoading({
          title: '获取中....',
          mask: true
        })
        console.log(this.userinfo)
        let params = {
          page: page.num,
          page_size: page.size,
          start_time: this.startDate,
          end_time: this.endDate,
          person_no: this.userinfo.person_no,
          // car_no: this.carInfo.car_no
        }
        let api
        if (this.currentType === 'record') {
          api = apiCarPassRecord
        } else {
          api = apiCarOrderPassRecord
          if (this.orderValue) {
            params.order_type = this.orderValue
          }
        }
        api(params)
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              // const results = [{
              //   name: '粤A754788',
              //   status_alias: '进',
              //   time: '2023-06-29 08:00:00',
              //   door: '西门',
              //   order_status: '1',
              //   order_status_alis: '通行消费'
              // }]
              const results = res.data.results
              const count = res.data.count
              // 如果是第一页需手动置空列表
              if (page.num == 1) this.orderList = []
              // 追加新数据
              this.orderList = [...this.orderList, ...results]
              //后台接口有返回列表的总数据量 count, 判断是否有下一页
              let pageLength = 0
              if (results) {
                pageLength = results.length
              } else {
                pageLength = 0
              }
              this.mescroll.endBySize(pageLength, count)

            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err)
          })
      },
      getQueryCar() {
        apiQueryCar()
          .then(res => {
            if (res.code == 0) {
              this.carList = res.data
              if (this.carList.length) {
                let data
                if (uni.getStorageSync('carId')) {
                  data = this.carList.find(item => item.id === uni.getStorageSync('carId'))
                }
                if (data && data.id) {
                  this.carInfo = data
                } else {
                  this.carInfo = this.carList[0]
                  uni.setStorageSync('carId', this.carList[0].id)
                }
              }
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err)
          })
      },
      gotoSelectCar() {
        this.$miRouter.push({
          query: {
            data: encodeQuery(this.carList)
          },
          path: '/pages_common/car_admin/car_select'
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.car-order{
  .no-card{
    margin-top: 180rpx;
  }
  .car-info{
    padding: 30rpx 40rpx;
    background-color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .subsection-wrap{
    background-color: #FFFFFF;
    margin: 40rpx 230rpx;
  }
  .select-wrap{
    margin: 0 40rpx;
    display: flex;
    .data-select{
      width: 120rpx;
      padding: 0 10rpx;
      flex: inherit!important;
    }
  }
  ::v-deep.mescroll-upwarp{
    display: none;
  }
  .no-data{
    background-color: #FFF;
    border-radius: 30rpx;
    margin: 20rpx 40rpx;
    padding: 100rpx;
  }
}
</style>
