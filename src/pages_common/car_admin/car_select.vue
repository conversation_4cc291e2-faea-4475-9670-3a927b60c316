<template>
  <view :style="theme.style" class="payment">
    <!-- Section 主体 Start -->
    <scroll-view scroll-y="true">
      <view class="section bg-white">
        <block v-for="(item, index) in itemsData" :key="index">
          <view class="item flex row-between col-center" @click="selectCar(item)">
            <view class="black">{{ item.car_no }}</view>
            <u-icon v-if="item.id === carId" name="checkmark" :color="variables.colorPrimary" size="36rpx"></u-icon>
          </view>
        </block>
      </view>
    </scroll-view>
    <!-- Section End -->

    <!-- Footer 底部 Start -->
    <!-- <view class="footer bg-white">
      <view class="footer--warpper flex flex-center">
        <u-button
          text="添加项目"
          color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)"
          @click="toAddItemsFunc"
        ></u-button>
      </view>
    </view> -->
    <!-- Footer End -->
  </view>
</template>

<script>
import { decodeQuery } from '../../utils/util'
export default {
  // Data Start
  data() {
    return {
      carId: '',
      itemsData: []
    }
  },
  // Data End

  // Methods Start
  methods: {
    selectCar(item) {
      uni.setStorageSync('carId', item.id)
      this.$miRouter.back()
    },
    toAddItemsFunc() {
      this.$miRouter.push('/pages_bundle/switch_items/add_items')
    },
  },
  // Methods End

  // Life Cycle Start
  onLoad() {
    this.itemsData = decodeQuery(this.$Route.query.data)
    this.carId = uni.getStorageSync('carId')
  },
  onShow() {
  }
  // Life Cycle End
}
</script>

<style lang="scss">
.payment {
  .ls-card {
    border-radius: 20rpx;
  }

  .section {
    padding: 0 40rpx;
    // margin-bottom: 200rpx;
    margin-bottom: calc(200rpx + env(safe-area-inset-bottom));
    border-top: $border-base;

    .item {
      padding: 40rpx 0;
    }
  }

  .footer {
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 0 40rpx;
    border-top: 1px solid #eaecee;
    padding-bottom: env(safe-area-inset-bottom);

    &--warpper {
      height: 190rpx;
    }
  }
}
</style>
