<template>
  <view :style="theme.style" class="question-list">
    <u-navbar bg-color="#FFF" left-icon-color="#000" @leftClick="leftClick">
			<view class="lg f-w-500" slot="center">调查问卷</view>
		</u-navbar>
    <view class="list">
      <view class="question-item" v-for="item in questionList" :key="item.id" @click="gotoMainQuestion(item)">
        <img :src="themeImgPath.img_question_icon" alt="">
        <view class="question-item-info">
          <view class="title">{{item.name}}</view>
          <view class="time muted">截止日期：{{item.end_date}}</view>
        </view>
        <u-icon color="#999" name="arrow-right" size="24rpx"></u-icon>
        <view class="right-label" v-if="item.survey_feedbacks.length">已完成答题</view>
      </view>
    </view>
  </view>
</template>

<script>
  import { apiGetSurveyInfoList } from '@/api/question'
  export default {
    data() {
      return {
        questionList: []
      }
    },
    onShow() {
      this.getQuestionList()
    },
    onLoad() {
      this.questionList = this.$Route.query.data
    },
    methods: {
      leftClick() {
        this.$miRouter.replaceAll({ path: '/pages/index/index' })
      },
      getQuestionList() {
        apiGetSurveyInfoList()
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              this.questionList = res.data.results.map(item => {
                if ((new Date(item.end_date).getTime() + 86400000) < new Date().getTime()) {
                  item.status = "expire"
                }
                return item
              })
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.hideLoading()
            uni.$u.toast(err.message)
          })
      },
      gotoMainQuestion(data) {
        if (data.status === 'expire' && !data.survey_feedbacks.length) return uni.$u.toast("问卷已过期")
        let path = '/pages_common/question/main_question'
        if (data.survey_feedbacks.length && data.status ==='expire') {
          path = '/pages_common/question/doing_question'
        }
        uni.setStorageSync('doing_question', data)
        this.$miRouter.push({
          path,
          query: {
            // data: this.$encodeQuery(data)
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.question-list{
  .list{
    padding: 30rpx;
  }
  .question-item{
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    img{
      width: 90rpx;
    }
    .question-item-info{
      width: 100%;
      margin-left: 20rpx;
      .title{
        margin-bottom: 15rpx;
        font-weight: bold;
        font-size: 29rpx;
        width: 450rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
      }
      .time{
        font-size: 24rpx;
      }
    }
    .right-label{
      position: absolute;
      top: 0;
      right: 0;
      padding: 6rpx 15rpx;
      background: $color-primary-light-8;
      border-radius: 0px 20rpx 0px 20rpx;
      overflow: hidden;
      font-size: 22rpx;
      color: $color-primary;
    }
  }
}
</style>
