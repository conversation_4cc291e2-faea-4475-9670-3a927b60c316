<template>
  <view :style="theme.style + `background-image: url(${themeImgPath.img_question_top_bg});`" class="doing-question">
    <u-navbar bg-color="transparent" left-icon-color="#000" @leftClick="leftClick">
      <view class="lg f-w-500" slot="center">调查问卷</view>
    </u-navbar>
    <view class="question-top">
      <view class="question-title">{{ questionInfo.name }}</view>
    </view>
    <view class="question-wrap">
      <view class="question-type" v-if="currentInfo.type === 'CHOICE' && !currentInfo.multiple_choice">单选题</view>
      <view class="question-type" v-if="currentInfo.type === 'CHOICE' && currentInfo.multiple_choice">多选题</view>
      <view class="title">{{ currentInfo.content }}</view>
      <view class="option-wrap" v-if="currentInfo.type === 'CHOICE'">
        <view
          v-for="opt in currentInfo.options"
          :key="opt.options"
          class="option-item flex row-between"
          :class="optionsActive(opt.options)"
          @click="clickOptions(opt.options)"
        >
          {{ opt.options }}、{{ opt.content }}
          <u-icon v-if="iconActive(opt)" :color="variables.colorPrimary" name="checkmark" size="40rpx"></u-icon>
        </view>
      </view>
      <view v-if="currentInfo.type === 'SCORE'">
        <view class="muted">分数越高代表评价越好</view>
        <view class="score-list flex">
          <view
            v-for="item in 10"
            :key="item"
            class="score-item"
            :class="optionsScoreActive(item)"
            @click="clickScoreOptions(item)"
          >
            {{ item }}
          </view>
        </view>
      </view>
      <u-textarea
        v-if="currentInfo.type === 'ANSWER'"
        :disabled="isCompleted"
        class="reason-text"
        v-model="answerContent"
        height="150"
        placeholder="请输入您的建议"
        count
        maxlength="50"
        @input="changeAnswerContent"
      ></u-textarea>
    </view>
    <view class="btn-wrap" v-if="!isCompleted">
      <view v-if="current" class="save-btn" @click="preQuestion">上一题</view>
      <view v-if="current + 1 < questionInfo.survey_questions.length" class="save-btn" @click="checkQuestion('next')">下一题</view>
      <view v-if="current + 1 === questionInfo.survey_questions.length" class="save-btn" @click="checkQuestion('last')">完成答题</view>
    </view>
    <view class="btn-wrap" v-else >
      <view class="save-btn" @click="leftClick">返回</view>
      <view v-if="current + 1 < questionInfo.survey_questions.length" class="save-btn" @click="checkQuestion('next')">下一题</view>
    </view>
    <u-modal
			:show="showModal"
			title="提示"
			confirmColor="#ff6b6c"
			confirmText="确定"
			@confirm="clickConfirmQuit"
			:showCancelButton="true"
			cancelText="取消"
			@cancel="showModal = false"
			class="modal-box"
		>
			<view class="p-b-20">您还没有完成答题，确定要退出吗？</view>
		</u-modal>
  </view>
</template>

<script>
import { apiAddSurveyInfo } from '@/api/question'
export default {
  data() {
    return {
      questionInfo: {}, // 当前问卷的所有信息
      anonymous: false,
      current: 0,
      currentInfo: {}, // 当前问题
      surveyFeedbacks: [], // 当前问卷的所有问题
      answerContent: '',
      isCompleted: false, // 过期已填写
      showModal: false
    }
  },
  onLoad() {
    // this.questionInfo = this.$Route.query.data
    this.questionInfo = uni.getStorageSync('doing_question')
    this.currentInfo = this.questionInfo.survey_questions[this.current]
    this.questionInfo.survey_questions.map(item => {
      let data = {
        id: item.id,
        type: item.type
      }
      if (item.type === 'CHOICE') {
        data.options_data = []
      } else if (item.type === 'ANSWER') {
        data.content = ''
      } else if (item.type === 'SCORE') {
        data.score = ''
      }
      this.surveyFeedbacks.push(data)
    })
    this.anonymous = this.$Route.query.anonymous
    this.isCompleted = !!(this.questionInfo.survey_feedbacks.length && data.status ==='expire')
  },
  methods: {
    leftClick() {
      this.showModal = true
    },
    clickConfirmQuit() {
      this.$miRouter.back()
    },
    preQuestion() {
      this.current--
      this.currentInfo = this.questionInfo.survey_questions[this.current]
      if (this.currentInfo.type === 'ANSWER') {
        this.surveyFeedbacks.map(item => {
          if (item.id === this.currentInfo.id) {
            this.answerContent = item.content
          }
        })
      }
    },
    checkQuestion(type) {
      let data = this.surveyFeedbacks.find(item => item.id === this.currentInfo.id)
      if (this.currentInfo.type === 'CHOICE' && !data.options_data.length) {
        return uni.$u.toast("请选择")
      } else if (this.currentInfo.type === 'ANSWER' && !data.content) {
        return uni.$u.toast("请填写建议")
      } else if (this.currentInfo.type === 'SCORE' && !data.score) {
        return uni.$u.toast("请选择分数")
      }
      if (type === 'next') {
        this.nextQuestion()
      } else if (type === 'last') {
        this.finishQuestion()
      }
    },
    nextQuestion() {
      this.current++
      this.currentInfo = this.questionInfo.survey_questions[this.current]
      if (this.currentInfo.type === 'ANSWER') {
        this.surveyFeedbacks.map(item => {
          if (item.id === this.currentInfo.id) {
            this.answerContent = item.content
          }
        })
      }
    },
    finishQuestion() {
      apiAddSurveyInfo({
        survey_info_id: this.questionInfo.id,
        anonymous: this.anonymous,
        survey_questions: this.surveyFeedbacks
      })
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            this.$miRouter.push({
              path: '/pages_common/question/question_result',
              query: {
                data: this.$encodeQuery(this.questionInfo),
                anonymous: this.anonymous
              }
            })
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    optionsActive(opt) {
      console.log('opt', opt)
      let className = ''
      this.surveyFeedbacks.map(item => {
        if (item.id === this.currentInfo.id && item.options_data.indexOf(opt) !== -1) {
          className = 'option-item-active'
        }
      })
      return className
    },
    iconActive(opt) {
      let flag = false
      this.surveyFeedbacks.map(item => {
        if (item.id === this.currentInfo.id && item.options_data.indexOf(opt) !== -1) {
          flag = true
        }
      })
      return flag
    },
    clickOptions(opt) {
      if (this.isCompleted) return
      this.surveyFeedbacks.map(item => {
        if (item.id === this.currentInfo.id && this.currentInfo.multiple_choice) {
          if (item.options_data.indexOf(opt) === -1) {
            item.options_data.push(opt)
          } else {
            item.options_data.splice(item.options_data.indexOf(opt), 1)
          }
        }
        if (
          item.id === this.currentInfo.id &&
          item.options_data.indexOf(opt) === -1 &&
          !this.currentInfo.multiple_choice
        ) {
          item.options_data = [opt]
        }
      })
    },
    changeAnswerContent(aa) {
      this.surveyFeedbacks.map(item => {
        if (item.id === this.currentInfo.id) {
          item.content = this.answerContent
        }
      })
    },
    optionsScoreActive(num) {
      let className = ''
      this.surveyFeedbacks.map(item => {
        if (item.id === this.currentInfo.id && Number(num) === Number(item.score)) {
          className = 'score-item-active'
        }
      })
      return className
    },
    clickScoreOptions(num) {
      if (this.isCompleted) return
      this.surveyFeedbacks.map(item => {
        if (item.id === this.currentInfo.id) {
          item.score = num
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.doing-question {
  height: 100vh;
  // background-image: url('./top_bg.png');
  background-size: 100% 700rpx;
  background-repeat: no-repeat;
  .question-top {
    margin: 30rpx;
    .question-title {
      font-size: 36rpx;
      font-weight: bold;
    }
  }
  .question-wrap {
    margin: 0 30rpx;
    background: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    .question-type {
      background: $color-primary;
      border-radius: 10rpx;
      padding: 0 12rpx;
      width: 100rpx;
      line-height: 45rpx;
      text-align: center;
      color: #fff;
      font-size: 24rpx;
      margin-bottom: 30rpx;
    }
    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin: 0 0 20rpx;
    }
    .option-wrap {
      .option-item {
        color: #ffffff;
        background-color: #f6f7fb;
        font-size: 29rpx;
        padding: 20rpx 40rpx;
        border-radius: 100rpx;
        color: #000;
        margin-bottom: 30rpx;
      }
      .option-item-active {
        background-color: $color-primary-light-8;
        color: $color-primary;
      }
    }
    .score-list {
      margin-top: 40rpx;
      flex-wrap: wrap;
      justify-content: space-between;
      .score-item {
        width: 18%;
        height: 70rpx;
        line-height: 70rpx;
        font-size: 32rpx;
        font-weight: bold;
        text-align: center;
        color: rgb(79, 79, 79);
        background-color: #f6f7fb;
        margin-bottom: 20rpx;
        border-radius: 50rpx;
      }
      .score-item-active {
        color: #fff;
        background-color: $color-primary;
      }
    }
  }
  .btn-wrap {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 2;
    .save-btn {
      width: 100%;
      margin: 30rpx;
      color: #ffffff;
      background-color: $color-primary;
      text-align: center;
      font-size: 30rpx;
      padding: 20rpx 0;
      border-radius: 100rpx;
    }
  }
}
</style>
