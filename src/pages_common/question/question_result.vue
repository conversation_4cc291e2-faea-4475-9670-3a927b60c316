<template>
  <view :style="theme.style" class="main-question">
    <view class="question-content flex flex-col flex-center">
			<u-icon :color="variables.colorPrimary" name="checkmark-circle-fill" :size="200"></u-icon>
      <view class="question-title">{{questionName}}</view>
      <view class="muted">您已完成所有答题，感谢您的配合！</view>
    </view>
    <view class="btn-wrap">
      <view class="save-btn" @click="gotoDoingQuestion">修改</view>
      <view class="save-btn" @click="gotoMainQuestion">确定</view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        questionName: ''
      }
    },
    onLoad() {
      this.questionName = this.$Route.query.name
    },
    methods: {
      leftClick() {
        this.$miRouter.back()
      },
      gotoDoingQuestion() {
        this.$miRouter.back()
      },
      gotoMainQuestion() {
        uni.removeStorageSync('doing_question')
        this.$miRouter.replaceAll({
        path: '/pages_common/question/question_list'
      })
      }
    }
  }
</script>

<style lang="scss" scoped>
.main-question{
  padding: 40rpx;
  .question-content{
    padding: 40rpx;
    border-radius: 20rpx;
    background: #FFF;
    text-align: center;
    .question-title {
      font-size: 36rpx;
      font-weight: bold;
      margin: 30rpx 0;
    }
  }
  .btn-wrap{
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    .save-btn {
      width: 100%;
      margin: 30rpx;
      color: #ffffff;
      background-color: $color-primary;
      text-align: center;
      font-size: 30rpx;
      padding: 20rpx 0;
      border-radius: 100rpx;
    }
  }
}

</style>
