<template>
  <view :style="theme.style + `background-image: url(${themeImgPath.img_question_top_bg});`" class="main-question">
    <u-navbar bg-color="transparent" left-icon-color="#000" @leftClick="leftClick">
			<view class="lg f-w-500" slot="center">调查问卷</view>
		</u-navbar>
    <view class="question-content">
      <img class="question-img" :src="themeImgPath.img_question_img" alt="">
      <view class="question-title">{{questionInfo.name}}</view>
      <view class="question-tips">
        <view class="flex col-center m-b-25"><view class="tips-dot"></view>完成问卷大概需要3-5分钟</view>
        <view class="flex col-center"><view class="tips-dot"></view>请您认真阅读题目并根据实际情况填写</view>
      </view>
      <view class="btn-wrap">
        <view class="save-btn" @click="gotoDoingQuestion">开始答题</view>
      </view>
      <u-checkbox-group class="anonymous-checked" v-if="questionInfo.anonymous" v-model="anonymousChecked" placement="row" iconPlacement="left" size="25">
        <u-checkbox :activeColor="variables.colorPrimary" name="anonymous" label="匿名">
          <text class="checkbox-text muted">匿名提交</text>
        </u-checkbox>
      </u-checkbox-group>
    </view>
    <view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        questionInfo: [],
        anonymousChecked: []
      }
    },
    onLoad() {
      // this.questionInfo = this.$Route.query.data
      this.questionInfo = uni.getStorageSync('doing_question')
    },
    methods: {
      leftClick() {
        this.$miRouter.back()
      },
      gotoDoingQuestion() {
        this.$miRouter.push({
          path: '/pages_common/question/doing_question',
          query: {
            // data: this.$encodeQuery(this.questionInfo),
            anonymous: this.questionInfo.anonymous ? !!this.anonymousChecked.length : false
          }
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
.main-question{
  height: 100vh;
  // background-image: url('./top_bg.png');
  background-size: 100% 700rpx;
  background-repeat: no-repeat;
  .question-content{
    margin: 80rpx 40rpx;
    padding: 40rpx;
    background-image: linear-gradient(180deg, $color-primary-light-7 0%, #FFF 12%, #FFF 100%);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 20rpx 80rpx 20rpx 20rpx;
    position: relative;
    .question-img{
      position: absolute;
      right: 0;
      top: -40rpx;
      width: 300rpx;
      z-index: 1;
    }
    .question-title{
      width: 300rpx;
      font-size: 42rpx;
      font-weight: bold;
      margin-bottom: 60rpx;
    }
    .question-tips{
      background: #F6F7FB;
      border-radius: 15rpx 45rpx 15rpx 15rpx;
      padding: 40rpx 30rpx;
      position: relative;
      z-index: 10;
      margin-bottom: 60rpx;
      .tips-dot{
        width: 14rpx;
        height: 14rpx;
        border-radius: 10rpx;
        background: $color-primary;
        margin-right: 15rpx;
      }
    }
    .btn-wrap{
      background-color: #FFF;
      z-index: 2;
      .save-btn {
        color: #ffffff;
        background-color: $color-primary;
        text-align: center;
        font-size: 30rpx;
        padding: 20rpx 0;
        border-radius: 100rpx;
      }
    }
    .anonymous-checked{
      display: flex;
      justify-content: space-around;
      margin: 30rpx 0 10rpx;
    }
  }
}

</style>
