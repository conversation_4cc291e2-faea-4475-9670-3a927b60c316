<template>
  <view class="add-meal-apply">
    <view class="apply-form">
      <view class="card-wrap">
        <view class="card-item flex row-between bb">
          <view style="width: 180rpx" class="md">
            就餐日期
            <text class="m-l-5 green-text">*</text>
          </view>
          <view class="flex row-right" @click="showCalendar = true">
            <view :class="['md', !formData.mealDateText ? 'value-placeholder' : 'black']">
              {{ formData.mealDateText ? formData.mealDateText : '请选择日期' }}
            </view>
            <u-icon color="#999" name="arrow-right" size="24rpx"></u-icon>
          </view>
        </view>
      </view>
      <view class="card-wrap">
        <view class="card-item flex row-between">
          <view class="md">申请人<text class="m-l-5 green-text">*</text></view>
          <u-input placeholder="请输入姓名" :disabled="!isMealVisitor" border="none" inputAlign="right" v-model="formData.name" maxlength="10"></u-input>
        </view>
        <view class="card-item flex row-between">
          <view class="md">手机号码<text class="m-l-5 green-text">*</text></view>
          <u-input placeholder="请输入手机号码" border="none" inputAlign="right" v-model="formData.phone" maxlength="11"></u-input>
        </view>
      </view>
      <view class="card-wrap">
        <view class="card-item flex row-between bb">
          <view style="width: 180rpx" class="md">
            申请类型
            <text class="m-l-5 green-text">*</text>
          </view>
          <view class="flex row-right" @click="showApplyType = true">
            <view :class="['md', !formData.applyType ? 'value-placeholder' : 'black']">
              {{ formData.applyType ? formData.applyType : '请选择类型' }}
            </view>
            <u-icon color="#999" name="arrow-right" size="24rpx"></u-icon>
          </view>
        </view>
      </view>
      <view class="card-wrap">
        <view class="card-item flex row-between bb">
          <view style="min-width: 140rpx" class="md">
            就餐食堂
            <text class="m-l-5 green-text">*</text>
          </view>
          <view class="flex row-right">
            <view v-if="formData.canteenList.length" class="flex flex-wrap row-right">
              <view v-for="(item, index) in formData.canteenList" :key="index" class="flex card-right-label m-l-12">
                <text>{{ item.name }}</text>
                <u-icon class="m-l-10" color="#11e69e" name="close" size="24rpx" @click="delCanteenLabel(index, item)"></u-icon>
              </view>
            </view>
            <view v-else class="value-placeholder" @click="openSelectPopup('canteen')">请选择食堂</view>
            <u-icon color="#999" name="arrow-right" size="24rpx" @click="openSelectPopup('canteen')"></u-icon>
          </view>
        </view>
        <view class="card-item flex row-between bb">
          <view style="min-width: 140rpx" class="md">
            就餐餐段
            <text class="m-l-5 green-text">*</text>
          </view>
          <view class="flex row-right">
            <view v-if="formData.mealList.length" class="flex flex-wrap row-right">
              <view v-for="(item, index) in formData.mealList" :key="index" class="flex card-right-label m-l-12">
                <text>{{ item.label }}</text>
                <u-icon class="m-l-10" color="#11e69e" name="close" size="24rpx" @click="delMealLabel(index, item)"></u-icon>
              </view>
            </view>
            <view v-else class="value-placeholder" @click="openSelectPopup('meal')">请选择餐段</view>
            <u-icon color="#999" name="arrow-right" size="24rpx" @click="openSelectPopup('meal')"></u-icon>
          </view>
        </view>
        <view class="card-item flex row-between bb">
          <view style="width: 180rpx" class="md">
            就餐人数
            <text class="m-l-5 green-text">*</text>
          </view>
          <view class="flex row-right" @click="showSelectNumPopup = true">
            <view :class="['md', !formData.count ? 'value-placeholder' : 'black']">
              {{ formData.count ? formData.count : '请选择人数' }}
            </view>
            <u-icon color="#999" name="arrow-right" size="24rpx" @click="showSelectNumPopup = true"></u-icon>
          </view>
        </view>
      </view>
      <view class="card-wrap">
        <view class="card-item">
          <view class="m-b-20 md">备注<text v-if="formData.applyType === '其他' || approveRule.need_remark" class="m-l-5 green-text">*</text></view>
          <u-textarea class="reason-text" v-model="formData.remark" height="150" placeholder="请写下你的申请理由" count maxlength="50"></u-textarea>
        </view>
      </view>
    </view>
    <view class="btn-wrap">
      <view class="flex-1  m-t-20" v-if="approveRule.pay_method === 'PayAtSight'">
        <text class="md muted m-r-10">合计金额</text>
        <price-format :price="totalFee" :size="40" color="#000" ></price-format>
      </view>
      <view class="flex-1" @click="checkData()">
        <u-button text="提交申请" shape="circle" :color="variables.bgLinearGradient1"></u-button>
      </view>
    </view>
    <select-popup
		  :show-select.sync="showSelectPopup"
			:list="selectList"
			:value="selectValue"
			:title="selectTitle"
			:valueKey="selectValueKey"
			:valueLabel="selectValueLabel"
			@change="changeSelect"
		>
		</select-popup>
    <select-number
		  :show-select.sync="showSelectNumPopup"
			:value="selectNumValue"
      :max-num="50"
			@confirm="confirmCount"
		>
		</select-number>
    <u-picker
      :show="showApplyType"
      :columns="applyTypeList"
      @confirm="confirmApplyType"
      @cancel="showApplyType = false"
    ></u-picker>
    <u-calendar
      :show="showCalendar"
      mode="range"
      color="#11e69e"
      maxRange="2"
      month-num="2"
      rangePrompt="最多可选3天"
      :min-date="today"
      :max-date="maxMealDate"
      :allow-same-day="true"
      :defaultDate="[]"
      @confirm="confirmDate"
      @close="showCalendar = false"
    ></u-calendar>
    <u-modal
      :show="showMealCountModal"
      title="请选择餐段数"
      cancelText="取消"
      cancelColor="#8d8f92"
      confirmText="确定"
      showCancelButton
      confirmColor="#11e69e"
      @cancel="showMealCountModal = false"
      @confirm="clickConfirmButton"
    >
      <view slot="default" class="meal-count-modal">
        <scroll-view style="max-height: 500rpx" :scroll-y="true">
          <view v-for="(date, dateIndex) in mealCountList" :key="dateIndex" class="md date-item">
            <view>{{ date.date }}</view>
            <view v-for="(canteen, canteenIndex) in date.canteenInfo" :key="canteenIndex">
              <view class="canteen-title">{{ canteen.canteenName }}</view>
              <view v-for="(meal, mealIndex) in canteen.mealInfo" :key="mealIndex" class="meal-item">
                <view class="meal-item-title">{{ meal.mealName }}</view>
                <view v-if="approveRule.pay_method === 'PayAtSight'">
                  <price-format
                    :price="meal.fee"
                    :size="30"
                    color="#a1a1a1"
                  ></price-format>
                  <text class="muted">/人次</text>
                </view>
                <view>
                  <number-box
                    v-model="meal.count"
                    :async-change="true"
                    :disabled="isDisabled(date.date, canteen.canteenid, meal.mealKey)"
                    @change="changeNumberBox($event, meal, isDisabled(date.date, canteen.canteenid, meal.mealKey))"
                  ></number-box>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </u-modal>
    <pay-way :money="totalFee" @select="selectPayWay" :personNo="userInfo.person_no" :orgId="formData.canteenIds[0]" :show-pay="showPayPopup" @showPayClose="showPayClose"></pay-way>
  </view>
</template>

<script>
import { deepClone } from '../../utils/util'
import SelectPopup from '../components/meal_apply/select-popup.vue'
import SelectNumber from '../components/meal_apply/select-number.vue'
import PayWay from '../components/meal_apply/pay-way.vue'
import { apiCreateApproveOrder, apiOrderVisitorPay } from '@/api/meal_apply.js'
import { checkClient, formateMealVisitorParams, getBaseUrl } from '@/utils/util.js'
import { getApiWechatCongfigGet, getQywechatConfigGet } from '@/api/app'
import Cache from '@/utils/cache'
import { payMpRequest, checkWxBridgeReady, onBridgeReady } from '@/utils/payment'
import { timeFormat } from '@/utils/date'
import { mapGetters, mapActions } from 'vuex'
// #ifdef H5
var jweixin = null
// #endif
export default {
  components: {
	  SelectPopup,
	  SelectNumber,
    PayWay
	},
  data() {
    return {
      isLoading: false,
      approveRule: {},
      paramsData: {},
      formData: {
        mealDateList: [],
        mealDateText: '',
        name: '',
        phone: '',
        applyType: '',
        applyTypeText: '',
        canteenIds: [],
        canteenList: [],
        mealList: [],
        mealKeys: [],
        count: '',
        remark: ''
      },
      mealCountList: [],
      oldMealCountList: [],
      orgList: [],
      allMealTypeList: [
        { label: '早餐', value: 'breakfast' },
        { label: '午餐', value: 'lunch' },
        { label: '下午茶', value: 'afternoon' },
        { label: '晚餐', value: 'dinner' },
        { label: '夜宵', value: 'supper' },
        { label: '凌晨餐', value: 'morning' }
      ],
      showSelectPopup: false,
      selectPopupType: '',
      selectTitle: '',
      selectList: [],
      selectValue: '',
      selectValueKey: '',
      selectValueLabel: '',
      showCalendar: false,
      today: uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd'),
      maxMealDate: '',
      showMealCountModal: false,
      showApplyType: false,
      applyTypeList: [
        [{
          label: '访客记账',
          id: 'Accounting'
        },
        {
          label: '即付',
          id: 'PayAtSight'
        }]
      ],
      selectNumValue: '',
      showSelectNumPopup: false,
      showPayPopup: false,
      payWay: {},
      orderInfo: {},
      userInfo: {},
      platform: checkClient() // 平台， 微信or支付宝
    }
  },
  onLoad() {
    this.approveRule = this.$Route.query.rule
    this.userInfo = Cache.get('userInfo')
    this.formData.name = this.userInfo.card_name
    this.formData.phone = this.userInfo.phone
    // #ifdef H5
		if (this.platform === 'wechat') {
			jweixin = require('jweixin-module')
		}else if(this.platform === 'wxwork'){
			//企业微信
			jweixin = require('weixin-jweixin').default
		}
    if (this.platform === 'wechat' || this.platform === 'wxwork') {
			// 因为企业微信需要初始化
      this.getWechatCongfigGet()
    }
    // 支付宝h5支付成功后跳回来会带上out_trade_no的
		// let tradeNo = Cache.get('PAYORDERTRADENO')
		if (this.$Route.query.out_trade_no) {
			this.$showLoading({
				title: '支付中....',
				mask: true
			})
			this.gotoPayResult(this.$Route.query.out_trade_no, true)
			return
		}
		// #endif
    if (this.$Route.query.type === 'again') {
      this.applyData = this.$Route.query.data
      this.formData.name = this.applyData.name
      this.formData.phone = this.applyData.phone
      this.formData.applyType = this.applyData.apply_type
      this.formData.count = this.applyData.meal_num
      this.formData.remark = this.applyData.remark
      this.formData.canteenList = []
      this.formData.canteenIds = []
      this.approveRule.org_list.map(item => {
        if (this.applyData.org_name_list.indexOf(item.name) !== -1) {
          this.formData.canteenIds.push(item.id)
          this.formData.canteenList.push(item)
        }
      })
      this.formData.mealList = []
      this.formData.mealKeys = []
      this.allMealTypeList.map(item => {
        if (this.applyData.meal_type_alias_list.indexOf(item.label) !== -1) {
          this.formData.mealKeys.push(item.value)
          this.formData.mealList.push(item)
        }
      })
    }
    this.applyTypeList[0] = this.approveRule.apply_type_list
    let time = ((this.approveRule.can_approve_day - 1) * 24 * 60 * 60 * 1000) + new Date().getTime()
    this.maxMealDate = uni.$u.timeFormat(time, 'yyyy-mm-dd')
    console.log('this.maxMealDate', this.maxMealDate)
  },
  computed: {
    ...mapGetters(['isMealVisitor']),
    totalFee() {
      let fee = 0
      this.mealCountList.map(date => {
        date.canteenInfo.map(canteen => {
          canteen.mealInfo.map(meal => {
            if (meal.count) {
              fee += (meal.fee * meal.count)
            }
          })
        })
      })
      if (this.formData.count) {
        fee = fee * Number(this.formData.count)
      }
      return fee
    }
  },
  methods: {
    ...mapActions({
      setMealVisitor: 'setMealVisitor'
    }),
    checkData() {
      console.log(this.formData)
      if (!this.formData.mealDateList.length) return uni.$u.toast('请选择就餐日期')
      if (!this.formData.name) return uni.$u.toast('请填写就餐申请人')
      if (!this.formData.phone) return uni.$u.toast('请填写手机号码')
      let reg = /^1\d{10}$/
      if (!reg.test(this.formData.phone)) {
        return uni.$u.toast("请输入正确的手机号码")
      }
      if (!this.formData.applyType) return uni.$u.toast('请选择申请类型')
      if (!this.formData.canteenIds.length) return uni.$u.toast('请选择就餐食堂')
      if (!this.formData.mealKeys.length) return uni.$u.toast('请选择就餐餐段')
      if (!this.formData.count) return uni.$u.toast('请输入就餐人数')
      if (!this.formData.remark && (this.formData.applyType === '其他' || this.approveRule.need_remark)) return uni.$u.toast('请输入备注')
      this.paramsData = {
        company_id: this.userInfo.company_id,
        person_no: this.userInfo.person_no,
        pay_method: this.approveRule.pay_method,
        name: this.formData.name,
        phone: this.formData.phone,
        apply_type: this.formData.applyType,
        org_ids: this.formData.canteenIds,
        meal_type_list: this.formData.mealKeys,
        meal_num: this.formData.count,
        date_list: this.formData.mealDateList,
        remark: this.formData.remark
      }
      this.showMealCountModal = true
      // if (this.approveRule.pay_method === 'PayAtSight') {
      //   this.showMealCountModal = true
      // } else {
      //   this.gotoAddApply()
      // }
      
    },
    async gotoAddApply() {
      this.$showLoading({
        title: '加载中...',
        mask: true
      })
      apiCreateApproveOrder(formateMealVisitorParams(this.paramsData))
        .then(async res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (this.approveRule.pay_method === 'PayAtSight') {
              this.orderInfo = res.data
              this.showPayPopup = true
            } else {
              uni.$u.toast('成功')
              await this.$sleep(500)
              this.gotoPayResult()
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    showPayClose(value) {
			this.showPayPopup = value
		},
    selectPayWay(value, org) {
      this.payWay = value
      console.log("(this.isLoading", this.isLoading, this.payWay);
      if (this.isLoading) return 
      if (this.payWay.payway === 'PushiPay' && !this.payWay.balance_type) return
      this.$showLoading({
				title: '支付中...',
				mask: true
			})
			this.isLoading = true
			let params = {
        name: this.formData.name,
        phone: this.formData.phone,
				balance_type: this.payWay.balance_type,
				wallet_id: this.payWay.wallet_id,
				payinfo_id: this.payWay.payinfo_id,
				source_organization_id: org,
				approve_no: this.orderInfo.approve_no,
				company_id: this.userInfo.company_id,
				user_id: this.userInfo.user_id,
				// #ifdef H5
				return_url: getBaseUrl() + 'pages_common/meal_apply/apply_list' // 支付完成跳转的地址针对location.href这种方式的充值
				// #endif
			}
			let originBackUrl = Cache.get('originBackUrl')
      if (originBackUrl) {
        params.backUrl = originBackUrl
      }
			apiOrderVisitorPay(formateMealVisitorParams(params, true))
				.then(res => {
					if (res.code == 0) {
						this.tradeNo = res.data.trade_no

						// 成功的订单，已签约免密支付的会走这一步
						if (res.data.pay_result && res.data.pay_result.order_status === 'ORDER_SUCCESS') {
							// uni.hideLoading()
							uni.showToast({
								title: '支付成功',
								icon: 'success',
								success: () => {
									this.gotoPayResult()
								}
							})
							return
						}
						// 支付成功
						if (res.data.order_status === 'ORDER_SUCCESS') {
							uni.showToast({
								title: '支付成功',
								icon: 'success',
								success: () => {
									this.gotoPayResult()
								}
							})
							return
						}
						// #ifdef H5
						if (res.data.pay_result.extra && res.data.pay_result.extra.redirect) {
              window.location.href = res.data.pay_result.extra.redirect
              return
            }
						// #endif

						if (
							res.data.pay_result.payway === 'WechatPay' ||
							res.data.pay_result.payway === 'QyWechatPay' ||
							(res.data.pay_result.payway == 'AliPay' && res.data.pay_result.sub_payway === 'miniapp')
						) {
							this.jsapiChooseWXPay(res.data.pay_result.extra)
						} else if ((res.data.pay_result.payway === 'AliPay' || res.data.pay_result.payway === 'WXYFPay') && res.data.pay_result.sub_payway === 'h5') {
							// Cache.set('PAYORDERTRADENO', this.tradeNo)
							uni.hideLoading()
							this.isLoading = false
							window.location.href = res.data.pay_result.extra
						} else {
							uni.hideLoading()
							this.isLoading = false
							// uni.showToast({
							// 	title: '支付成功',
							// 	icon: 'success',
							// 	// duration: 2000,
							// 	success: () => {
							// 		this.gotoPayResult()
							// 	}
							// })
						}
					} else if (res.code === 1) {
						this.$miRouter.replace({
							path: '/pages_common/meal_apply/apply_payment_result',
							query: {
								statusType: 'error',
								statusMsg: res.msg
							}
						})
					} else {
						uni.hideLoading()
						this.isLoading = false
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					this.isLoading = false
					uni.$u.toast(err.message)
					uni.hideLoading()
				})
		},
    async gotoPayResult(tradeNo, sleep) {
      if (sleep) {
				await this.$sleep(1000)
			}
			this.isLoading = false
			uni.hideLoading()
			this.$miRouter.replace({
				path: '/pages_common/meal_apply/apply_payment_result',
				query: {
					// #ifdef MP-ALIPAY
					// data: this.$encodeQuery(res.data),
					// #endif
					// #ifndef MP-ALIPAY
					// data: res.data,
					// #endif
					trade_no: tradeNo ? tradeNo : this.tradeNo,
          type: this.approveRule.pay_method
				}
			})
    },
    confirmDate(value) {
      if (value.length === 2 && value[0] === value[1]) {
        this.formData.mealDateList = [value[0]]
        this.formData.mealDateText = value[0]
      } else {
        this.formData.mealDateList = value
        if (value.length > 1) {
          this.formData.mealDateText = `${value[0]}~${value[value.length - 1]}`
        }
      }
      this.showCalendar = false
      // 处理数据
      this.mealCountList = []
      this.formData.mealDateList.map(date => {
        let arr = this.oldMealCountList.filter(oldDate => oldDate.date === date)
        if (arr.length) {
          this.mealCountList.push(arr[0])
        } else {
          let canteenInfo = []
          this.formData.canteenList.map(canteen => {
            let mealInfo = []
            this.formData.mealList.map(meal => {
              let mealFee = null
              if (this.approveRule?.fee_dict.length) {
                this.approveRule.fee_dict.map(fee => {
                  if (fee.org_id === canteen.id) {
                    mealFee = fee[meal.value]
                  }
                })
              }
              mealInfo.push({
                mealName: meal.label,
                mealKey: meal.value,
                count: 0,
                fee: mealFee,
                date: date,
                canteenName: canteen.name,
                canteenid: canteen.id
              })
            })
            canteenInfo.push({
              canteenName: canteen.name,
              canteenid: canteen.id,
              mealInfo
            })
          })
          this.mealCountList.push({
            date,
            dayCount: 0,
            canteenInfo
          })
        }
      })
      this.oldMealCountList = deepClone(this.mealCountList)
      console.log(this.mealCountList)
    },
    openSelectPopup(type) {
      this.selectPopupType = type
      if (type === 'canteen') {
        this.selectTitle = '请选择就餐食堂'
        this.selectList = this.approveRule.org_list
        this.selectValue = this.formData.canteenIds
        this.selectValueKey = 'id'
        this.selectValueLabel = 'name'
      } else if (type === 'meal') {
        this.selectTitle = '请选择餐段'
        this.selectList = this.approveRule.meal_type_list.map(item =>{
          let data = this.allMealTypeList.filter(meal => meal.value === item)
          return data[0]
        })
        this.selectValue = this.formData.mealKeys
        this.selectValueKey = 'value'
        this.selectValueLabel = 'label'
      }
      this.showSelectPopup = true
    },
    changeSelect(data) {
      if (this.selectPopupType === 'canteen') {
        this.formData.canteenList = data
        this.formData.canteenIds = data.map(item => {
          return item.id
        })
        // 处理数据
        this.mealCountList.map(date => {
          date.canteenInfo = [] // 先重置所有canteenInfo
          let list = this.oldMealCountList.filter(old => old.date === date.date) // 先找到当前日期的所有旧的食堂
          this.formData.canteenList.map(canteen => { // 来给日期挨个添加食堂了
            let arr = []
            if (list.length) {
              arr = list[0].canteenInfo.filter(oldCan => oldCan.canteenid === canteen.id)
            }
            if (arr.length) { // 组织id是唯一的，筛选出来的arr一般最多只有一个
              date.canteenInfo.push(arr[0])
            } else {
              let mealInfo = []
              this.formData.mealList.map(meal => {
                let mealFee = null
                if (this.approveRule?.fee_dict.length) {
                  this.approveRule.fee_dict.map(fee => {
                    if (fee.org_id === canteen.id) {
                      mealFee = fee[meal.value]
                    }
                  })
                }
                mealInfo.push({
                  mealName: meal.label,
                  mealKey: meal.value,
                  count: 0,
                  fee: mealFee,
                  canteenName: canteen.name,
                  canteenid: canteen.id,
                  date: date.date,
                })
              })
              date.canteenInfo.push({
                canteenName: canteen.name,
                canteenid: canteen.id,
                mealInfo
              })
            }
          })
        })
        this.oldMealCountList = deepClone(this.mealCountList)
      } else if (this.selectPopupType === 'meal') {
        this.formData.mealList = data
        this.formData.mealKeys = data.map(item => {
          return item.value
        })
        // 处理数据
        this.mealCountList.map(date => {
          date.canteenInfo.map(canteen => {
            canteen.mealInfo = [] // 先重置所有mealInfo
            let list = []
            this.oldMealCountList.map(oldDate => { // 先找到当前日期、食堂的所有旧的餐段
              if (oldDate.date === date.date) {
                list = oldDate.canteenInfo.filter(oldCan => oldCan.canteenid === canteen.canteenid)
              }
            })
            this.formData.mealList.map(meal => { // 来给食堂挨个添加餐段了
              let mealFee = null
              if (this.approveRule?.fee_dict.length) {
                this.approveRule.fee_dict.map(fee => {
                  if (fee.org_id === canteen.canteenid) {
                    mealFee = fee[meal.value]
                  }
                })
              }
              let arr = []
              if (list.length) {
                arr = list[0].mealInfo.filter(oldMeal => oldMeal.mealKey === meal.value)
              }
              if (arr.length) { // 组织id是唯一的，筛选出来的arr一般最多只有一个
                canteen.mealInfo.push(arr[0])
              } else {
                canteen.mealInfo.push({
                  mealName: meal.label,
                  mealKey: meal.value,
                  count: 0,
                  fee: mealFee,
                  date: date.date,
                  canteenName: canteen.canteenid,
                  canteenid: canteen.canteenName,
                })
              }

            })
          })
        })
        this.oldMealCountList = deepClone(this.mealCountList)
      }
      console.log('this.mealCountList', this.mealCountList)
    },
    delCanteenLabel(index, item) {
      this.formData.canteenList.splice(index, 1)
      this.formData.canteenIds.splice(index, 1)
      this.selectPopupType = 'canteen'
      this.changeSelect(this.formData.canteenList)
    },
    delMealLabel(index, item) {
      this.formData.mealList.splice(index, 1)
      this.formData.mealKeys.splice(index, 1)
      this.selectPopupType = 'meal'
      this.changeSelect(this.formData.mealList)
    },
    changeNumberBox(e, info, disabled) {
      if (disabled) return uni.$u.toast(`当前餐段已过点餐时间`)
      this.mealCountList.map(item => {
        item.canteenInfo.map(canteen => {
          canteen.mealInfo.map(meal => {
            console.log(meal.canteenid, info)
            if (meal.date === info.date && meal.canteenid === info.canteenid && meal.mealKey === info.mealKey) {
              console.log(info.canteenid)
              if (this.approveRule.limit.type === 'meal') { // 单人单餐限制
                let allCount = 0
                item.canteenInfo.map(canteenitem => {
                  canteenitem.mealInfo.map(mealitem => {
                    if (mealitem.mealKey === info.mealKey) {
                      allCount += mealitem.count
                    }
                  })
                })
                if (meal.count < e) {
                  if ( Number(allCount) < Number(this.approveRule.limit.num)) {
                    meal.count ++
                  } else {
                    uni.$u.toast(`单人单餐限制点餐次数为${this.approveRule.limit.num}次`)
                  }
                } else {
                  meal.count --
                }
              } else if (this.approveRule.limit.type === 'day') { // 单人每天限制
                if (meal.count < e) {
                  if ( item.dayCount < Number(this.approveRule.limit.num)) {
                    meal.count ++
                    item.dayCount ++
                  } else {
                    uni.$u.toast(`单人每天限制点餐次数为${this.approveRule.limit.num}次`)
                  }
                } else {
                  meal.count --
                  item.dayCount --
                }
              } else if (this.approveRule.limit.type === 'limitless') {
                if (meal.count < e) {
                  meal.count ++
                } else {
                  meal.count --
                }
              }
            }
          })
        })
      })
      this.oldMealCountList = deepClone(this.mealCountList)
      console.log(this.mealCountList, 'mealCountList')
    },
    plusNumberBox(e) {
      console.log(e)
    },
    confirmApplyType(e) {
      this.formData.applyType = e.value[0]
      this.showApplyType = false
    },
    confirmCount(e) {
      this.formData.count = e.data
      this.showSelectNumPopup = false
    },
    openMealCountModal() {
      this.showMealCountModal = true
    },
    clickConfirmButton() {
      this.showMealCountModal = false
      let mealNumInfo = []
      this.mealCountList.map(date => {
        date.canteenInfo.map(canteen => {
          canteen.mealInfo.map(meal => {
            let info = {
              date: date.date,
              org_id: canteen.canteenid
            }
            info[meal.mealKey] = meal.count
            mealNumInfo.push(info)
          })
        })
      })
      this.paramsData.meal_info = mealNumInfo
      this.gotoAddApply()
    },
    isDisabled(date, canteen, meal) {
      let index = this.approveRule.org_list.findIndex(orgItem => orgItem.id === canteen)
      let mealdate = new Date(this.approveRule.org_list[index].meal_type_info[meal]).getTime()
      let nowDate = new Date().getTime()
      console.log(mealdate, nowDate)
      let flag = false
      if (mealdate < nowDate && timeFormat(new Date().getTime(), 'yyyy-mm-dd') === date) {
        flag = true
      }
      return flag
    },
    jsapiChooseWXPay(params) {
			let _this = this
			let paymentOrderType = this.$store.state.appoint.select.payment_order_type
			// #ifdef H5
				// 企业微信 和微信支付共用
				onBridgeReady(params, function({res}) {
					if (res.err_msg == 'get_brand_wcpay_request:ok') {
						// 使用以上方式判断前端返回,微信团队郑重提示：
						//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							// duration: 2000,
							success: () => {
								// _this.$miRouter.back(2)
								uni.hideLoading()
								_this.isLoading = false
								_this.gotoPayResult()
							}
						})
					} else {
						uni.showToast({
							title: '支付失败',
							icon: 'none',
							success: () => {
								_this.isLoading = false
								uni.hideLoading()
							}
						})
					}
				})
			// #endif
			// #ifdef MP-WEIXIN || MP-ALIPAY
			payMpRequest(params)
				.then(({ res, provider }) => {
					uni.hideLoading()
					if (provider === 'alipay') {
						// 当为支付宝支付时需要额外判断状态码
						switch (res.resultCode) {
							case '9000': // 订单处理成功。
								uni.showToast({
									title: '支付成功',
									icon: 'success',
									success: () => {
										// _this.$miRouter.back(2)
										_this.isLoading = false
										_this.gotoPayResult()
									}
								})
								break
							case '6001': // 用户中途取消
								uni.showToast({
									title: '用户中途取消',
									icon: 'fail'
								})
								_this.isLoading = false
								break
							case '8000': // 正在处理中。支付结果未知（有可能已经支付成功）。
								uni.showToast({
									title: '正在处理中',
									icon: 'success',
									success: () => {
										// _this.$miRouter.back(2)
										_this.isLoading = false
										_this.gotoPayResult()
									}
								})
								break
							case '6002': // 网络连接出错
								_this.isLoading = false
								uni.showToast({
									title: '网络连接出错',
									icon: 'fail'
								})
								break
							case '6004': // 处理结果未知（有可能已经成功）
								uni.showToast({
									title: '正在处理中',
									icon: 'success',
									success: () => {
										// _this.$miRouter.back(2)
										_this.isLoading = false
										_this.gotoPayResult()
									}
								})
								break
							case '4': // 无权限调用
								_this.isLoading = false
								uni.showToast({
									title: '无权限调用',
									icon: 'fail'
								})
								break
							default:
								uni.showToast({
									title: '支付失败',
									icon: 'none'
								})
								break
						}
					} else {
						uni.hideLoading()
						uni.showToast({
							title: '支付成功',
							icon: 'success',
							success: () => {
								// _this.$miRouter.back(2)
								_this.isLoading = false
								_this.gotoPayResult()
							}
						})
					}
				})
				.catch((res, provider) => {
					_this.isLoading = false
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					})
				})
			// #endif
		},
    // 初始化支付
    async getWechatCongfigGet() {
      let params = {
        appid: uni.getStorageSync('appid') || this.userInfo.appid,
        company_id: this.userInfo.company_id,
        url: window.location.href.split('#')[0]
      }
			if (this.isMealVisitor) {
				params.appid = uni.getStorageSync('appidVisitor')
			}
			let res = null
			try {
				if(this.platform === 'wechat'){
					res = await getApiWechatCongfigGet(formateMealVisitorParams(params))
				}else if(this.platform === 'wxwork'){
					res = await getQywechatConfigGet(params)
				}
				uni.hideLoading()
				if (res.code == 0) {
					if (res.data) {
						jweixin.config({
							beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
							debug: false,
							appId: res.data.appid,
							timestamp: res.data.timestamp,
							nonceStr: res.data.noncestr,
							signature: res.data.signature,
							jsApiList: ['checkJsApi', 'chooseWXPay','getBrandWCPayRequest'] // 把支付也初始化
						})
						jweixin.error(function(res){
							console.log('error~'+res)
						});
					}
				} else {
					uni.$u.toast(res.msg)
				}
			} catch (error) {
				uni.hideLoading()
				uni.$u.toast(error.message)
			}
    }
  }
}
</script>

<style lang="scss" scoped>
.add-meal-apply {
  .apply-form{
    .u-input{
      background-color: transparent!important;
    }
    padding: 0 30rpx 140rpx;
    .card-wrap {
      background-color: #fff;
    	padding: 0 30rpx;
      border-radius: 20rpx;
      margin-top: 20rpx;
    	.card-item {
        border-bottom: 1rpx #f3f3f3 solid;
    		padding: 32rpx 0;
    		box-sizing: border-box;
        .value-placeholder{
          color: #c0c4cc;
        }
        .card-right-label{
          color: #11e69e;
          background-color: #11e69e18;
          font-size: 24rpx;
          padding: 8rpx 20rpx;
          border-radius: 40rpx;
          margin-bottom: 10rpx;
        }
    	}
      .card-item:nth-child(n+2) {
        border-top: 1rpx solid #f1f1f1;
      }
      .green-text{
        color: $color-primary;
      }
    }
  }
  .btn-wrap {
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 20rpx 30rpx;
    .save-btn {
      margin: 30rpx;
      color: #ffffff;
      background-color: #11e69e;
      text-align: center;
      font-size: 30rpx;
      padding: 20rpx 0;
      border-radius: 100rpx;
    }
  }
  .meal-count-modal{
    width: 100%;
    .date-item{
      padding: 30rpx 0 20rpx;
      border-bottom: 2rpx solid #f2f2f2;
      &:last-child{
       border: none;
      }
      .canteen-title{
        border-left: 8rpx solid #11e69e;
        padding-left: 16rpx;
        font-weight: bold;
        line-height: 32rpx;
        margin: 30rpx 0 30rpx;
      }
      .meal-item{
        margin-top: 20rpx;
        display: flex;
        justify-content: space-between;
        .meal-item-title{
          width: 100rpx;
        }
      }
    }
    
  }
}
</style>
