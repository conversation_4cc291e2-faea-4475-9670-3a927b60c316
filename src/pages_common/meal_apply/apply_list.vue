<template>
  <view :style="theme.style">
    <!-- #ifndef MP-ALIPAY -->
		<u-navbar bg-color="#fff" left-icon-color="#000" @leftClick="leftClick">
			<view class="lg f-w-500" slot="center">申请列表</view>
		</u-navbar>
		<!-- #endif -->
    <view class="apply-list">
      <view class="select-wrap">
        <data-select :localdata="orderTypeList" :value="orderValue" size="16px" class="data-select" @change="changeOrderType"></data-select>
      </view>
      <!-- <view class="no-data" v-if="!applyList.length">
        <view class="flex flex-center">
          <u-image width="200rpx" height="200rpx" :src="themeImgPath.img_no_data"></u-image>
        </view>
        <view class="muted text-center m-t-40">暂无申请记录</view>
      </view> -->
      <view class="p-b-30">
        <mescroll-uni
          ref="mescrollRef"
          @init="mescrollInit"
          @down="downCallback"
          @up="upCallback"
          @emptyclick="emptyClick"
          top="180"
          bottom="160"
        >
          <view class="apply-list-item" v-for="(item, index) in applyList" :key="index">
            <view class="flex row-between m-b-20">
              <view class="item-title md">{{item.approve_no}}</view>
              <view v-if="item.is_all_refund === 1" :class="['type-label', setStatusColor('is_all_refund')]">已退款</view>
              <view v-else :class="['type-label', setStatusColor(item.approve_status_alias)]">{{item.approve_status_alias}}</view>
            </view>
            <view class="item-info">
              <view class="flex m-t-20">
                <view class="muted">就餐日期：</view>
                <view class="">{{item.date_range_str}}</view>
              </view>
              <view class="flex m-t-20">
                <view class="muted">申请类型：</view>
                <view class="">{{item.apply_type}}</view>
              </view>
              <view class="flex m-t-20">
                <view class="muted">就餐食堂：</view>
                <view class="">{{ item.orgs }}</view>
              </view>
              <view class="flex m-t-20">
                <view class="muted">就餐餐段：</view>
                <view class="">{{ item.meals }}</view>
              </view>
              <view class="flex m-t-20">
                <view class="muted">就餐人数：</view>
                <view class="">{{ item.meal_num }}</view>
              </view>
            </view>
            <view class="flex row-right col-center m-t-30">
              <view class="flex-1 flex" v-if="item.approve_status_alias === '拒绝申请'">
                <view class="muted">拒绝原因：</view>
                <zb-tooltip :content="item.reject_reason">{{ nameFormat(item.reject_reason, 11) }}</zb-tooltip>
              </view>
              <view class="flex row-right col-center">
                <view class="m-l-20" v-if="item.approve_status_alias === '审批通过' && item.is_all_refund === 0">
                  <u-button size="small"  :customStyle="customBtnStyle" type="primary" text="访客码" @click="gotoCode(item)"></u-button>
                </view>
                <view class="m-l-20 apply-btn" v-if="item.approve_status_alias === '审批中' || item.approve_status_alias === '待审批'">
                  <u-button size="small" plain :customStyle="customBtnStyleAgain" text="撤销申请" @click="cancelOrder(item)"></u-button>
                </view>
                <view class="m-l-20 apply-btn" v-else>
                  <u-button size="small" plain :customStyle="customBtnStyleAgain" text="再次申请" @click="applyAgain(item)"></u-button>
                </view>
              </view>
            </view>
          </view>
        </mescroll-uni>
      </view>
      <view class="btn-wrap">
        <view class="save-btn" @click="gotoAddApply">发起申请</view>
      </view>
    </view>
    
    <u-modal
      :show="showModal"
      title="提示"
      :content="modalText"
      width="280px"
      :showCancelButton="true"
      :confirmColor="variables.colorPrimary"
      @confirm="confirmModal"
      @cancel="cancelModal"
      class="confirm-modal"
      >
		</u-modal>
  </view>
</template>

<script>
  import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
  import { apiGetApproveOrderRuleInfo, apiGetApproveList, apiRevokeApproveOrder } from '@/api/meal_apply.js'
  import Cache from '@/utils/cache'
  import { getApiUserGetProjectCardUserList, apiGetUserOpenid, apiGetWechatValidate, setApiChangeProjectPoint } from '@/api/app'
  import { formateMealVisitorParams } from '@/utils/util.js'
  import { apiQueryUserinfo } from '@/api/user.js'
  import { mapGetters, mapActions } from 'vuex'
  export default {
    mixins: [MescrollMixin],
    data() {
      return {
        approveRule: {},
        imgPath: this.$imgPath,
        userinfo: {},
        carInfo: {},
        orderValue: 0,
        orderTypeList: [{
          text: '全部',
          value: 0
        }, {
          text: '待审批',
          value: 'PENDING'
        }, {
          text: '审批中',
          value: 'PROCESSING'
        }, {
          text: '审批通过',
          value: 'AGREE'
        }, {
          text: '拒绝申请',
          value: 'REJECT'
        }, {
          text: '撤销申请',
          value: 'REVOKE'
        }, {
          text: '已退款',
          value: 'refund'
        }],
        applyList: [],
        customBtnStyle: {
          minWidth: '120rpx',
          height: '60rpx',
          lineHeight: '60rpx',
        },
        customBtnStyleAgain: {
          minWidth: '120rpx',
          height: '60rpx',
          lineHeight: '60rpx',
        },
        isShowList: false,
        codeCompanyId: '',
        showModal: false,
        modalText: ''
      }
    },
    async onLoad(option) {
      // #ifdef H5
      if (option.state === 'snsapi_base_get_openid' && option.code) {
        console.log(option.code)
        this.setMealVisitor(1)
        await this.getSnsapiBaseOpenid(option.code)
      }
      if (this.$Route.query.user_type === 'outsiders') {
        this.showModal = true
        this.modalText = '是否需要进行访客申请？'
        this.codeCompanyId = Number(this.$Route.query.company_id)
      } else if (this.$Route.query.user_type === 'insider') {
        this.codeCompanyId = Number(this.$Route.query.company_id)
        this.queryUserinfo('check')
        this.setMealVisitor(0) // 清除访客餐游客缓存
      } else if (uni.getStorageSync('applyVisitorInfo')) {
        this.codeCompanyId = Number(uni.getStorageSync('applyVisitorInfo').company_id)
        console.log(uni.getStorageSync('applyVisitorInfo').type)
        if (uni.getStorageSync('applyVisitorInfo').user_type === 'insider') {
          uni.removeStorageSync('applyVisitorInfo')
          this.getApproveRule()
          // this.upCallback({
          //   num: 1,
          //   size: 10
          // })
        }
      } else {
        this.getApproveRule()
        //   this.upCallback({
        //   num: 1,
        //   size: 10
        // })
      }
      // #endif
    },
    onShow() {
      // this.getApproveRule()
      // this.upCallback({
      //   num: 1,
      //   size: 10
      // })
    },
    computed: {
    ...mapGetters(['isMealVisitor']),
    },
    methods: {
      ...mapActions({
        setUserInfo: 'setUserInfo',
        setMealVisitor: 'setMealVisitor'
      }),
      async getApproveRule() {
        this.$showLoading({
          title: '加载中...',
          mask: true
        })
        apiGetApproveOrderRuleInfo(formateMealVisitorParams({
          company_id: Cache.get('userInfo').company_id
        }))
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              this.approveRule = res.data
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.hideLoading()
            uni.$u.toast(err.message)
          })
      },
      changeOrderType(e) {
        this.orderValue = e
        this.upCallback({
          num: 1,
          size: 10
        })
      },
      upCallback(page) {
        this.getApproveList(page)
      },
      getApproveList(page) {
        this.$showLoading({
          title: '获取中....',
          mask: true
        })
        let params = {
          company_id: Cache.get('userInfo').company_id,
          page: page.num,
          page_size: page.size
        }
        if (this.orderValue) { params.approve_status = this.orderValue }
        apiGetApproveList(formateMealVisitorParams(params))
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              this.isShowList = true
              const results = res.data.results.map(item => {
                item.orgs = item.org_name_list.join('、')
                item.meals = item.meal_type_alias_list.join('、')
                return item
              })
              const count = res.data.count
              const page_size = res.data.page_size
              // 如果是第一页需手动置空列表
              if (page.num == 1) this.applyList = []
              // 追加新数据
              this.applyList = [...this.applyList, ...results]
              //后台接口有返回列表的总数据量 count, 判断是否有下一页
              let pageLength = 0
              if (results) {
                pageLength = results.length
              } else {
                pageLength = 0
              }
              this.mescroll.endBySize(pageLength, count)

            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err)
          })
      },
      setStatusColor(status) {
        if (status === '拒绝申请') {
          return 'red'
        } else if (status === '审批中') {
          return 'yelow'
        } else if (status === '审批通过') {
          return 'green'
        } else if (status === '撤销申请') {
          return 'revoke'
        } else if (status === '待审批') {
          return 'yelow'
        } else if (status === 'is_all_refund') {
          return 'red'
        }
      },
      cancelOrder(info) {
        let _that = this
        uni.showModal({
          title: '撤销申请',
          content: `确定撤销本次访客餐申请吗？`,
          /*  #ifndef MP-ALIPAY */
          cancelColor: "#11e69e",
          confirmColor: "#FF6E6E",
          /*  #endif  */
          confirmText: '撤销',
          cancelText: '我再想想',
          success: function(res) {
            if (res.confirm) {
              _that.getApproveRevoke(info)
            } else if (res.cancel) {
              // console.log('用户点击取消')
            }
          }
        })
      },
      async getApproveRevoke(data) {
        this.$showLoading({
          title: '加载中...',
          mask: true
        })
        apiRevokeApproveOrder(formateMealVisitorParams({
          company_id: Cache.get('userInfo').company_id,
          id: data.id
        }))
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              this.upCallback({
                num: 1,
                size: 10
              })
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.hideLoading()
            uni.$u.toast(err.message)
          })
      },
      gotoAddApply() {
        if (Object.keys(this.approveRule).length) {
          this.$miRouter.push({
            path: '/pages_common/meal_apply/add_meal_apply',
            query: {
              rule: this.$encodeQuery(this.approveRule),
              type: 'add'
            }
          })
        } else {
          uni.$u.toast('当前不存在申请规则')
        }
      },
      applyAgain(item) {
        if (Object.keys(this.approveRule).length) {
          this.$miRouter.push({
            path: '/pages_common/meal_apply/add_meal_apply',
            query: {
              rule: this.$encodeQuery(this.approveRule),
              data: this.$encodeQuery(item),
              type: 'again'
            }
          })
        } else {
          uni.$u.toast('当前不存在申请规则')
        }
      },
      gotoCode(item) {
        this.$miRouter.push({
          path: '/pages_common/meal_apply/apply_meal_code',
          query: {
            data: this.$encodeQuery(item),
          }
        })
      },
      async confirmModal() {
        this.showModal = false
        this.$showLoading({
          title: '加载中...',
          mask: true
        })
        uni.setStorageSync('applyVisitorInfo', {
          type: this.$Route.query.type,
          company_id: this.$Route.query.company_id,
          id: this.$Route.query.id,
          user_type: this.$Route.query.user_type
        })
        if (this.$store.getters.cookie) {
          this.$store.dispatch('setLogOut')
        }
        uni.setStorageSync('companyId', this.$Route.query.company_id)
        await this.$sleep(500)
        this.getWechatValidate()
      },
      cancelModal() {
        this.showModal = false
        this.$miRouter.replaceAll('/pages/login/login')
      },
      async getWechatValidate() {
        await apiGetWechatValidate({
          company_id: uni.getStorageSync('applyVisitorInfo').codeCompanyId
        })
          .then(res => {
            if (res.code == 0) {
              uni.hideLoading()
              uni.setStorageSync('appidVisitor', res.data.appid)
              let redirectUrl = this.$encodeQuery(`${res.data.redirect_uri}/pages_common/meal_apply/apply_list`)
              window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.data.appid}&redirect_uri=${redirectUrl}&response_type=code&scope=snsapi_base&state=snsapi_base_get_openid#wechat_redirect`
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err)
          })
      },
      // 游客获取openid
      async getSnsapiBaseOpenid(code) {
        await apiGetUserOpenid({
          code,
          appid: uni.getStorageSync('appidVisitor'),
          company_id: Number(uni.getStorageSync('applyVisitorInfo').company_id)
        })
          .then(res => {
            if (res.code == 0) {
              uni.setStorageSync('codeOpenid', res.data.openid)
              uni.removeStorageSync('applyVisitorInfo')
              this.getApproveRule()
              this.upCallback({
                num: 1,
                size: 10
              })
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err)
          })
      },
      // 获取用户已绑定的项目点
      getUserGetProjectCardUserList() {
        getApiUserGetProjectCardUserList()
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              this.companyUserInfo = res.data.find(item => item.company_id === this.codeCompanyId)
              if (this.companyUserInfo) { // 用户存在该项点
                this.changeCompany()
              } else {
                uni.$u.toast('您未存在该项目点，无法申请')
              }
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err.message)
          })
      },
      // 切换项目点
      changeCompany() {
        setApiChangeProjectPoint({
          company_id: this.companyUserInfo.company_id,
          company_name: this.companyUserInfo.company_name,
          name: this.companyUserInfo.name,
          person_no: this.companyUserInfo.person_no
        })
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              this.queryUserinfo('save')
            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err.message)
          })
      },
      // 重新登录然后保存个人信息
      queryUserinfo(type) {
        apiQueryUserinfo()
          .then(res => {
            if (res.code == 0) {
              if (type === 'check') {
                this.checkCompany()
              } else {
                this.setUserInfo(res.data)
                uni.setStorageSync('applyVisitorInfo', {
                  type: this.$Route.query.type,
                  company_id: this.$Route.query.company_id,
                  id: this.$Route.query.id,
                  user_type: this.$Route.query.user_type
                })
                // // 去首页重新登陆授权，获取当前项目点的配置
                this.$miRouter.replaceAll({ path: `/pages/index/index?company_id=${res.data.company_id}` })
              }
            } else if (res.code === -1) {
              uni.$u.toast('请登录且绑定对应项目点后重新扫码申请')
              setTimeout(() => {
                this.$miRouter.replaceAll({ path: '/pages/login/login' })
              }, 1000)
            } else {
              
            }
          })
          .catch(err => {
            console.log(err)
          })
      },
      checkCompany() {
        if (this.codeCompanyId !== Number(uni.getStorageSync('companyId'))) {
          this.getUserGetProjectCardUserList()
        } else {
          this.getApproveRule()
          this.upCallback({
            num: 1,
            size: 10
          })
        }
      },
      leftClick() {
        this.setMealVisitor(0) // 清除访客餐游客缓存
        this.$miRouter.pushTab({ path: '/pages/index/index' })
      },
      // 格式化文字 超过多少显示...
      nameFormat(name,number) {
        if(!name) return
        let subStr = name.slice(0, number)
        subStr = subStr + (name.length > number ? '...' : '')
        return subStr
      },
    }
  }
</script>

<style lang="scss" scoped>
.apply-list{
  padding: 30rpx 40rpx;
  .select-wrap{
    display: flex;
    flex-direction: row-reverse;
    margin-right: 15rpx;
    .data-select{
      width: 180rpx;
      padding: 0 10rpx;
      flex: inherit!important;
    }
  }
  ::v-deep.mescroll-upwarp{
    display: none;
  }
  .no-data{
    background-color: #FFF;
    border-radius: 20rpx;
    margin-top: 20rpx;
    padding: 100rpx;
  }
  .apply-list-item{
    background-color: #FFFFFF;
    padding:30rpx;
    border-radius: 20rpx;
    position: relative;
    margin: 0 40rpx 30rpx;
    .item-info{
      padding-bottom: 20rpx;
      border-top: 2rpx solid #f0f0f0;
      border-bottom: 2rpx solid #f0f0f0;
    }
    .type-label{
      line-height: 40rpx;
      padding-left: 40rpx;
      padding-right: 20rpx;
      border-radius: 20rpx 0 0 20rpx;
      color: #FFFFFF;
      position: absolute;
      right: 0;
      &::before{
        content: '';
        display: block;
        height: 12rpx;
        width: 12rpx;
        position: absolute;
        left: 20rpx;
        top: 15rpx;
        border-radius: 50%;
        
      }
    }
    .red{
      color: red;
      &::before{
        background-color: red;
      }
    }
    .yelow{
      color: #F9A63C;
      &::before{
        background-color: #F9A63C;
      }
    }
    .green{
      color: #11e69e;
      &::before{
        background-color: #11e69e;
      }
    }
    .revoke{
      color: #999999;
      &::before{
        background-color: #999999;
      }
    }
    .apply-btn .u-button{
      border-color: $color-primary;
      color: $color-primary;
    }
  }
  .btn-wrap{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #FFF;
    z-index: 2;
    .save-btn {
      margin: 30rpx;
      color: #ffffff;
      background-color: $color-primary;
      text-align: center;
      font-size: 30rpx;
      padding: 20rpx 0;
      border-radius: 100rpx;
    }
  }
}
</style>
