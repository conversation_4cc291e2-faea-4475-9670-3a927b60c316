<template>
  <view :style="theme.style" class="meal-apply-code">
    <view class="apply-code-info item" id="codewrap">
      <view class="code-wrap item-header">
        <view class="xxl m-b-40">{{mealCodeList[currentIndex].scan_code}}</view>
        <uqrcode v-if="mealCodeList[currentIndex].scan_code" ref="uQRCode" :text="mealCodeList[currentIndex].scan_code" :size="170" />
      </view>
      <view class="p-t-60 p-b-30">
        <view class="flex row-between m-b-30">
          <view class="muted">有效时间：</view>
          <view>{{ dataInfo.date_range_str }}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">适用消费点：</view>
          <view>{{ dataInfo.orgs }}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">适用餐段：</view>
          <view>{{ dataInfo.meals }}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">访客码可用次数：</view>
          <view>{{mealCodeList[currentIndex].total_use_meal_num}}</view>
        </view>
        <view class="flex row-between m-b-30">
          <view class="muted">已使用次数：</view>
          <view>{{mealCodeList[currentIndex].use_meal_num}}</view>
        </view>
      </view>
    </view>
    <view class="flex switch-img img-filter">
      <img src="./icon_left.png" alt="" srcset="" @click="preImg">
      <view>{{currentIndex + 1}} /<span class="muted m-l-15">{{mealCodeList.length}}</span></view>
      <img src="./icon_right.png" alt="" srcset="" @click="nextImg">
    </view>
    <view class="btn-wrap">
      <view class="save-btn" @click="clickSave">保存图片</view>
    </view>
  </view>
</template>

<script>
import html2canvas from 'html2canvas'
import { download } from '@/utils/util'
  export default {
    data() {
      return {
        dataInfo: {},
        currentIndex: 0,
        mealCodeList: [],
        imgUrl: null
      }
    },
    onLoad() {
      this.dataInfo = this.$Route.query.data
      this.mealCodeList = this.dataInfo.meal_info
    },
    methods: {
      /**
     * 点击保存图片按钮
     */
      clickSave(Url) {
        // #ifdef H5
        uni.showLoading({ title: '正在生成图片...' })
        // this.downloadLoading = true
        this.$nextTick(_ => {
          html2canvas(document.querySelector('#codewrap'), { useCORS: true }).then(canvas => {
            this.imgUrl = canvas.toDataURL('image/png') // 将canvas转换成img的src流
            //  || this.platform === 'alipay'
            if (this.platform === 'wechat') {
              this.showOverylay = true
              uni.$u.toast('请长按图片进行保存，或在浏览器中打开！')
            } else {
              download(this.imgUrl, false)
            }
            // this.showOverylay = true
            // this.downloadLoading = false
            uni.hideLoading()
          })
        })

        //#endif
        // #ifndef H5
        uni.saveImageToPhotosAlbum({
          filePath: this.imgUrl,
          success: function () {
            uni.hideLoading()
          uni.$u.toast('图片保存成功')
          },
          fail: function () {
            uni.hideLoading()
          uni.$u.toast('图片保存失败')
          },
          complete: function () {
            uni.hideLoading()
          }
        });
        //#endif
      },
      preImg() {
        if (!this.currentIndex) return
        this.currentIndex -= 1
      },
      nextImg() {
        if (this.currentIndex + 1 === this.mealCodeList.length) return
        this.currentIndex += 1
      }
    }
  }
</script>

<style lang="scss" scoped>
.meal-apply-code{
  margin: 30rpx;
  .apply-code-info{
    background-color: #FFF;
    border-radius: 20rpx;
    .code-wrap{
      padding: 80rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
  .item {
    border-radius: 20rpx;
    background-color: #fff;
    position: relative;
    padding: 0 30rpx;
    margin-bottom: 20rpx;
    
    &::before,
    &::after {
      content: '';
      display: block;
      height: 30rpx;
      width: 30rpx;
      background-color: $background-color;
      position: absolute;
      border-radius: 50%;
      top: 552rpx;
      transform: translateY(-50%);
    }
    
    &::before {
      left: -15rpx;
    }
    
    &::after {
      right: -15rpx;
    }

    .item-header {
      border-bottom: 1px dashed $border-color-base;
    }
  }
  .switch-img{
    justify-content: space-between;
    align-items: center;
    font-size: 36rpx;
    img{
      width: 100rpx;
    }
  }
  .btn-wrap{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    // background-color: #FFF;
    z-index: 2;
    .save-btn {
      margin: 30rpx;
      color: #ffffff;
      background-color: $color-primary;
      text-align: center;
      font-size: 30rpx;
      padding: 20rpx 0;
      border-radius: 100rpx;
    }
  }
}
</style>
