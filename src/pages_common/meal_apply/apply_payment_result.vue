<template>
	<!-- 支付结果页面 -->
	<view :style="theme.style" class="payment-result">
		<view v-if="progressStatus === 2" class="payment-success">
			<view class="flex flex-col flex-center">
				<u-icon color="#11E69E" name="checkmark-circle-fill" :size="200"></u-icon>
				<view class="font-size-42 f-w-500">支付成功</view>
			</view>
			<view class="payment-info">
				<view class="info-item">
					<view class="muted">就餐人：</view>
					<view>{{ resultInfo.name }}</view>
				</view>
				<view class="info-item">
					<view class="muted">订单原金额：</view>
          <!-- <view>￥{{getOrderOriginFee()}}</view> -->
					<price-format :price="getOrderOriginFee()"></price-format>
        </view>
				<view class="info-item">
					<view class="muted">支付总金额：</view>
					<price-format :price="resultInfo.pay_total_fee"></price-format>
				</view>
				<view class="info-item" v-if="resultInfo.discount_total_fee">
					<view class="muted">优惠金额：</view>
					<view>
						<span class="red">-</span>
						<price-format :price="resultInfo.discount_total_fee" color="red"></price-format>
					</view>
					<!-- <view class="red">-￥{{ resultInfo.discount_total_fee ? resultInfo.discount_total_fee / 100 : 0 }}</view> -->
				</view>
				<view class="info-item">
					<view class="muted">下单时间：</view>
					<view>{{ resultInfo.pay_time }}</view>
				</view>
				<view class="info-item" v-if="resultInfo.rate_fee">
					<view class="muted">扣款手续费：</view>
					<price-format :price="resultInfo.rate_fee"></price-format>
				</view>
				<view class="info-item">
					<view class="muted">线上支付金额：</view>
					<price-format :price="resultInfo.online_fee"></price-format>
				</view>
				<view class="info-item">
          <view class="muted">线下支付金额：</view>
					<price-format :price="resultInfo.offline_fee"></price-format>
        </view>
				<view class="info-item" @click="gotoOrder">
					<view class="muted">总订单号：</view>
					<view class="flex flex-center">
						{{ resultInfo.trade_no }}
						<u-icon color="#999" name="arrow-right" :size="28"></u-icon>
					</view>
				</view>
				<!-- 
        <view class="info-item">
          <view class="muted">线下支付订单号：</view>
          <view class="flex">12345679898798 <u-icon color="#999" name="arrow-right" :size="28"></u-icon></view>
        </view> -->
			</view>
			<!-- <router-link to="/pages_bundle/appoint/user_appoint">
        <u-button shape="circle" color="linear-gradient(90deg, #A9FED5 0%, #11E69E 0%, #11E69E 0%, #11E6C5 100%, #11E6C5 100%)" text="确定"></u-button>
      </router-link> -->
			<view class="" @click="gotoOrder">
				<u-button
					shape="circle"
					:color="variables.bgLinearGradient1"
					text="确定"
				></u-button>
			</view>
		</view>
		<view v-else-if="progressStatus === 1" class="payment-fail m-t-50">
			<view class="flex flex-col flex-center">
				<u-icon color="#11E69E" name="clock-fill" :size="150"></u-icon>
				<!-- <image color="#11E69E" name="@/static/icons/canteen_more.png" :size="150"></image> -->
				<!-- <image style="width: 150rpx; height: 150rpx;" :src="$imgPath.img_icon_my_fuwu_notice"></image> -->
				<view class="font-size-38 f-w-500 m-t-30">查询中</view>
			</view>
		</view>
		<view v-else-if="progressStatus === 3" class="payment-fail m-t-50">
			<view class="flex flex-col flex-center">
				<u-icon color="red" name="close-circle" :size="150"></u-icon>
				<view class="font-size-38 f-w-100 red m-t-30">{{ progressText }}</view>
			</view>
			<view class="flex flex-center m-t-50">
				<view class="m-r-20" style="width: 30%;" @click="gotoIndex"><u-button plain text="返回首页"></u-button></view>
				<view class="m-l-20" style="width: 30%;" @click="gotoOrder">
					<u-button
					:color="variables.bgLinearGradient1"
						text="查看订单"
					></u-button>
				</view>
			</view>
		</view>
		<view v-else-if="progressStatus === 4" class="payment-fail m-t-50">
			<view class="flex flex-col flex-center">
				<u-icon color="#11E69E" name="checkmark-circle-fill" :size="200"></u-icon>
				<view class="font-size-42 f-w-500 m-t-30">提交成功</view>
			</view>
			<view class="m-t-60" @click="gotoOrder">
				<u-button
					shape="circle"
					:color="variables.bgLinearGradient1"
					text="确定"
				></u-button>
			</view>
		</view>
	</view>
</template>

<script>
import { queryOrderInfo } from '@/api/user.js'
import { plus } from '@/utils/util.js'

export default {
	data() {
		return {
			resultInfo: {},
			paymentOrderType: '',
			tradeNo: '', // 订单号
			progressStatus: 1, // 1查询中，2成功，3失败
			progressText: '查询失败，请联系客服',
			queryHandle: null // 定时查询订单结果任务
		}
	},
	methods: {
		queryResult() {
			this.$showLoading({
				title: '查询中....',
				mask: true
			})
			this.queryHandle = setInterval(() => {
				this.queryPayResult()
				// clearInterval(this.queryHandle);
			}, 1000)
		},
		// 查询支付结果
		queryPayResult() {
			if (this.tradeNo) {
				queryOrderInfo({
					trade_no: this.tradeNo
				})
					.then(res => {
						if (res.code === 0) {
							if (res.data.order_status === 'ORDER_SUCCESS') {
								this.progressStatus = 2
								this.resultInfo = res.data
								uni.hideLoading()
								if (this.queryHandle) {
									clearInterval(this.queryHandle)
								}
							}
						} else {
							this.progressStatus = 3
							uni.hideLoading()
							if (this.queryHandle) {
								clearInterval(this.queryHandle)
							}
							this.progressText = res.msg
							uni.$u.toast(res.msg)
						}
					})
					.catch(err => {
						this.progressStatus = 3
						uni.hideLoading()
						if (this.queryHandle) {
							clearInterval(this.queryHandle)
						}
					})
			} else {
				uni.hideLoading()
				uni.$u.toast('订单号不能为空！')
				if (this.queryHandle) {
					clearInterval(this.queryHandle)
				}
			}
		},
		gotoOrder() {
			this.$miRouter.replace({
				path: '/pages_common/meal_apply/apply_list'
			})
		},
		gotoIndex() {
			this.$miRouter.replace({
				path: '/pages/index/index'
			})
		},
		plus,
    getOrderOriginFee() {
      if (this.resultInfo.discount_total_fee) {
        return this.plus(this.resultInfo.pay_total_fee, this.resultInfo.discount_total_fee)
      } else {
        return this.resultInfo.pay_total_fee
      }
    }
	},
	onShow() {},
	onLoad(options) {
		this.paymentOrderType = this.$store.state.appoint.select.payment_order_type
		if (this.$Route.query.type === 'Accounting') {
			this.progressStatus = 4
		} else if (this.$Route.query.statusType === 'error') {
			this.progressText = this.$Route.query.statusMsg
			this.progressStatus = 3
		} else {
			// 支付宝h5支付有out_trade_no和trade_no，先判断out_trade_no
			this.tradeNo = this.$Route.query.out_trade_no || this.$Route.query.trade_no
			// this.resultInfo = this.$Route.query.data
			if (this.tradeNo) {
				// 当有订单号时使用定时器查询订单
				this.queryResult()
			}
			this.progressStatus = 1
		}
	},
	onUnload() {
		if (this.queryHandle) {
			clearInterval(this.queryHandle)
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #ffffff;
}

.payment-result {
	padding: 20rpx 70rpx;
	.payment-info {
		margin-bottom: 30rpx;
		.info-item {
			display: flex;
			justify-content: space-between;
			font-size: $font-size-md;
			padding: 30rpx 0;
			&:not(:last-of-type) {
				border-bottom: $border-base;
			}
		}
	}
}
</style>
