<template>
  <view class="">
    <view class="p-t-30 p-r-30 p-l-30" v-for="(item, index) in list" :key="index">
    <view class="item">

      <view v-if="type==='jiaofeiList'" class="">
        <view class="item-header flex col-center">
          <view class="lg flex-1">{{item.jiaofei_type_alias}}</view>
          <view class="muted">{{ item.start_time }}</view>
        </view>
        <view class="item-info p-t-30">
          <view class="flex row-between m-b-30">
            <view class="muted">收费来源：</view>
            <view>{{ item.org_name }}</view>
          </view>
          <view class="flex row-between m-b-30">
            <view class="muted">缴费项目：</view>
            <view>{{ item.jiaofei_name }}</view>
          </view>
          <view class="flex row-between m-b-30">
            <view class="muted">缴费金额：</view>
            <view>￥{{ (item.jiaofei_money / 100).toFixed(2) }}</view>
          </view>
          <view class="flex row-between m-b-30">
            <view class="muted">缴费到期：</view>
            <view>{{ item.end_time }}</view>
          </view>
        </view>
      </view>
      <!--order_type 6：缴费，1：退款，-1：审批 -->
      <view v-if="type==='recordList'" class="">
        <view class="item-header flex col-center">
          <view class="lg flex-1" v-if="item.order_type===-1">申请时间：{{item.create_time}}</view>
          <view class="lg flex-1" v-else>创建时间：{{item.create_time}}</view>
          <view :class="[orderStatusStyle(item)]">{{orderStatusAlias(item)}}</view>
        </view>
        <view class="item-info p-t-30">
          <view class="flex row-between m-b-30" v-if="item.order_type===1">
            <view class="muted">退款订单：</view>
            <view>{{ item.order_trade_no }}</view>
          </view>
          <view class="flex row-between m-b-30" v-if="item.order_type!==6">
            <view class="muted">退款类型：</view>
            <view>{{ item.refund_type_alias }}</view>
          </view>
          <view class="flex row-between m-b-30" v-if="item.order_type!==6">
            <view class="muted">退款金额：</view>
            <view>￥{{ (item.pay_fee / 100).toFixed(2) }}</view>
          </view>
          <view class="flex row-between m-b-30" v-if="item.order_type!==1">
            <view class="muted">缴费订单：</view>
            <view>{{ item.order_trade_no }}</view>
          </view>
          <view class="flex row-between m-b-30">
            <view class="muted">缴费项目：</view>
            <view>{{ item.jiaofei_name }}</view>
          </view>
          <view class="flex row-between m-b-30">
            <view class="muted">缴费类型：</view>
            <view>{{ item.jiaofei_type_alias }}</view>
          </view>
          <view class="flex row-between m-b-30">
            <view class="muted">缴费金额：</view>
            <view>￥{{ (item.pay_fee / 100).toFixed(2) }}</view>
          </view>
          <view class="flex row-between m-b-30" v-if="item.order_type===6">
            <view class="muted">支付时间：</view>
            <view>{{ item.pay_time }}</view>
          </view>
          <view class="flex row-between m-b-30" v-if="item.order_type===1">
            <view class="muted">退款时间：</view>
            <view>{{ item.pay_time }}</view>
          </view>
          <view class="flex row-between m-b-30" v-if="item.order_type!==-1">
            <view class="muted">到账时间：</view>
            <view>{{ item.finish_time }}</view>
          </view>
        </view>
      </view>
      <view class="item-footer flex row-right col-center">
        <view v-if="type==='recordList'" class="m-l-20">
          <u-button size="small" :color="variables.colorPrimary" :customStyle="customBtnStyle" text="电子凭证" @click="gotoElectronicCertificate(item)"></u-button>
        </view>
        <view v-if="type==='jiaofeiList'" class="m-l-20">
          <u-button size="small" :color="variables.colorPrimary" :customStyle="customBtnStyle" text="去缴费" @click="gotoPay(item)"></u-button>
        </view>
        <!-- 后台设置允许退款、没有申诉过、并且是线上订单才允许退款 -->
        <view v-if="type==='recordList'&&item.can_refund&&!item.is_approval&&item.consume_type==='online'&&!item.img_url" class="m-l-20">
          <u-button size="small" plain :customStyle="customBtnStyle" text="退款" @click="gotoRefund(item, 'apply')"></u-button>
        </view>
        <!-- 跳转到订单详情 -->
        <view v-if="type==='recordList'&&item.order_type!==-1" class="m-l-20">
          <u-button size="small" :color="variables.colorPrimary" :customStyle="customBtnStyle" text="查看详情" @click="jiaefeiOrderDetail(item)"></u-button>
        </view>
        <!-- 退款审批的订单，详情跳转到审批详情 -->
        <view v-if="type==='recordList'&&item.order_type===-1" class="m-l-20">
          <u-button size="small" :color="variables.colorPrimary" :customStyle="customBtnStyle" text="查看详情" @click="gotoRefund(item, 'detail')"></u-button>
        </view>
      </view>
     </view>
    </view>
  </view>
</template>

<script>
import Cache from '@/utils/cache'
  export default {
    props: {
      list: Array,
      type: String,
      jiaofeiType: String
    },
    data() {
      return {
        customBtnStyle: {
          minWidth: '120rpx',
          height: '60rpx',
          lineHeight: '60rpx',
        }
      }
    },
    methods: {
      orderStatusAlias(data) {
        if (data.order_type_alias === '退款申请类') {
          return data.approval_status_alias
        } else if (data.order_type_alias === '退款类') {
          return data.refund_type_alias
        } else if (data.order_type_alias === '缴费类')  {
          if (data.order_status==='ORDER_SUCCESS') {
            return '已缴费'
          }
        }
      },
      orderStatusStyle(data) {
        if (data.order_type_alias === '退款申请类') {
          if (data.approval_status === 'applying') {
            return 'orange'
          } else if (data.approval_status === 'agree') {
            return 'green'
          } else if (data.approval_status === 'refuse') {
            return 'red'
          }
          return data.approval_status_alias
        } else if (data.order_type_alias === '退款类') {
          return 'orange'
        } else if (data.order_type_alias === '缴费类')  {
          if (data.order_status==='ORDER_SUCCESS') {
            return 'muted'
          }
        }
      },
      gotoPay(data) {
        this.$miRouter.push({
          path: '/pages_common/jiaofei_center/jiaofei_pay',
          query: {
            type: this.jiaofeiType,
            // #ifdef MP-ALIPAY
            data: this.$encodeQuery(data),
            // #endif
            // #ifndef MP-ALIPAY
            data: data
            // #endif
          }
        })
      },
      jiaefeiOrderDetail(data) {
        this.$miRouter.push({
          path: '/pages_common/jiaofei_center/jiaofei_detail',
          query: {
            // #ifdef MP-ALIPAY
            data: this.$encodeQuery(data),
            // #endif
            // #ifndef MP-ALIPAY
            data: data
            // #endif
          }
        })
      },
      gotoRefund(data, type) {
        this.$miRouter.push({
          path: '/pages_common/jiaofei_center/jiaofei_refund',
          query: {
            // #ifdef MP-ALIPAY
            data: this.$encodeQuery(data),
            // #endif
            // #ifndef MP-ALIPAY
            data: data,
            // #endif
            type
          }
        })
      },
      gotoElectronicCertificate(data) {
        if (data.img_url) {
          Cache.set('electronic_certificate', data)
          this.$miRouter.push({
            path: '/pages_common/jiaofei_center/jiaofei_electronic_certificate',
          })
        } else {
          this.$u.toast('暂无电子凭证')
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
@import '/item.scss';
.green{
  color: #11E69E;
}
.orange{
  color: #F9A63C;
}
.red{
  color: #FE5858;
}
</style>
