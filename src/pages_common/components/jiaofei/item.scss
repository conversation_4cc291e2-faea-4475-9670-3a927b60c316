.item {
	border-radius: 20rpx;
	background-color: #fff;
	position: relative;
	padding: 0 30rpx;
	margin-bottom: 20rpx;
	
	&::before,
	&::after {
		content: '';
		display: block;
		height: 30rpx;
		width: 30rpx;
		background-color: $background-color;
		position: absolute;
		border-radius: 50%;
		top: 90rpx;
		transform: translateY(-50%);
	}
	
	&::before {
		left: -15rpx;
	}
	
	&::after {
		right: -15rpx;
	}
	
	.item-header {
		border-bottom: 1px dashed $border-color-base;
		height: 90rpx;
	}
	.item-info {
		border-top: $border-base;
		border-bottom: $border-base;
	}
	.item-footer {
		height: 100rpx;
	}
}