<template>
  <view class="order-list">
    <view class="order-list-item" v-for="(item, index) in list" :key="index">
      <view class="" v-if="type === 'record'">
        <view class="flex row-between">
          <view class="item-title md">{{item.name}}</view>
          <view :class="['type-label', setStatusColor(item.in_out_direction)]">{{item.in_out_direction_alias}}</view>
        </view>
        <view class="face-img">
          <u-image width="100%" height="250rpx" :src="item.pass_img"></u-image>
        </view>
        <view class="flex row-between m-t-20">
          <view class="muted">通行时间：</view>
          <view class="">{{item.create_time}}</view>
        </view>
        <view class="flex row-between m-t-20">
          <view class="muted">通行道口：</view>
          <view class="">{{item.cross_name}}</view>
        </view>
      </view>
      <view class="" v-if="type === 'order'">
        <view class="flex row-between">
          <view class="item-title md">{{item.car_no}}</view>
          <view :class="[setStatusColor(item.order_type)]">{{item.order_type === 'payment' ?  '通行消费' : '通行退款'}}</view>
        </view>
        <view v-if="item.order_type === 'payment'">
          <view class="order-info-item muted">支付时间：{{item.create_time}}</view>
          <view class="order-info-item muted">支付渠道：{{item.payway_alias}}</view>
          <view class="order-info-item muted">支付方式：{{item.sub_payway_alias}}</view>
          <view class="order-info-item muted">订单金额：<price-format :price="item.origin_fee" :size="30"></price-format>元</view>
          <view class="order-info-item muted">支付金额：<price-format :price="item.pay_fee" :size="30"></price-format>元</view>
          <view class="order-info-item muted">订单号：{{item.trade_no}}</view>
        </view>
        <view v-if="item.order_type === 'refund'">
          <view class="order-info-item muted">退款时间：{{item.time}}</view>
          <view class="order-info-item muted">退款渠道：{{item.refund_payway_alias}}</view>
          <view class="order-info-item muted">原支付金额：<price-format :price="item.origin_fee" :size="30"></price-format>元</view>
          <view class="order-info-item muted">退款金额：<price-format :price="item.pay_fee" :size="30"></price-format>元</view>
          <view class="order-info-item muted">退款订单号：{{item.trade_no}}</view>
          <view class="order-info-item muted">原订单号：{{item.origin_trade_no}}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      type: String,
      list: Array
    },
    data() {
      return {
        
      }
    },
    methods: {
      setStatusColor(status) {
        if (status === 'in') {
          return 'in'
        } else if (status === 'out') {
          return 'out'
        } else if (status === 'payment') {
          return 'xiaofei'
        } else if (status === 'refund') {
          return 'muted'
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
.order-list{
  padding: 0 40rpx;
  .order-list-item{
    background-color: #FFFFFF;
    padding:30rpx;
    border-radius: 20rpx;
    margin-top: 20rpx;
    position: relative;
    .type-label{
      line-height: 40rpx;
      padding-left: 40rpx;
      padding-right: 20rpx;
      border-radius: 20rpx 0 0 20rpx;
      color: #FFFFFF;
      position: absolute;
      right: 0;
      &::before{
        content: '';
        display: block;
        height: 12rpx;
        width: 12rpx;
        position: absolute;
        left: 20rpx;
        top: 15rpx;
        border-radius: 50%;
        background-color: #FFFFFF;
      }
    }
    .xiaofei{
      color: #FFB142;
    }
    .in{
      background-color: #11E69E;
    }
    .out{
      background-color: #FFB142;
    }
    .item-title{
      line-height: 40rpx;
    }
    .face-img{
      width: 100%;
      height: 250rpx;
      margin-top: 30rpx;
      border-radius: 20rpx;
      position: relative;
      overflow: hidden;
    }
    .order-info-item{
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      margin-top: 20rpx;
    }
  }
}

  
</style>
