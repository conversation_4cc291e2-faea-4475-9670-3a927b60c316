<template>
  <view :style="theme.style" class="filter-popup">
    <view v-if="showMask" class="filter-mask" :class="[ isClosing ? 'mask-closing' : '' ]" @click="closeFilter">
      <view class="filter-container" @click.stop>
        <view v-if="showFilter" class="filter-popup" :class="{ closing: isClosing }">
          <!-- <view class="popup-header">
            <text class="popup-title">筛选</text>
            <u-icon name="arrow-down" @click="closeFilter" color="#666"></u-icon>
          </view> -->

          <!-- 日期筛选 -->
          <view class="filter-section" v-if="isShowDate">
            <text class="section-title">日期</text>
            <view class="filter-tags">
              <view
                v-for="date in dateOptions"
                :key="date.value"
                class="filter-tag"
                :class="{ active: selectedDate === date.value }"
                @click="selectDate(date.value)"
              >
                <text v-if="date.value !== 'custom'" class="tag-text">{{ date.label }}</text>
                <text v-else class="tag-text">{{ mealDateList.length ? mealDateText : '自定义' }}</text>
                <view class="check-icon" v-if="selectedDate === date.value">
                  <u-icon name="checkmark" size="6" color="#fff"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <!-- 餐段筛选 -->
          <view class="filter-section" v-if="isShowMeal">
            <text class="section-title">餐段</text>
            <view class="filter-tags">
              <view
                v-for="meal in mealOptions"
                :key="meal.value"
                class="filter-tag"
                :class="{ active: selectedMeal === meal.value }"
                @click="selectMeal(meal.value)"
              >
                <text class="tag-text">{{ meal.label }}</text>
                <view class="check-icon" v-if="selectedMeal === meal.value">
                  <u-icon name="checkmark" size="6" color="#fff"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <!-- 状态筛选 -->
          <view class="filter-section" v-if="isShowStatus">
            <text class="section-title">状态</text>
            <view class="filter-tags">
              <view
                v-for="status in statusOptions"
                :key="status.value"
                class="filter-tag"
                :class="{ active: selectedStatus === status.value }"
                @click="selectStatus(status.value)"
              >
                <text class="tag-text">{{ status.label }}</text>
                <view class="check-icon" v-if="selectedStatus === status.value">
                  <u-icon name="checkmark" size="6" color="#fff"></u-icon>
                </view>
              </view>
            </view>
          </view>

          <!-- 底部按钮 -->
          <view class="popup-footer">
            <view class="btn-reset" @click="resetFilter">重置</view>
            <view class="btn-confirm" @click="confirmFilter">确定</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 日期弹窗 -->
    <view v-if="showCustomDate" class="date-popup" :class="{ closing: isCustomDateClosing }">
      <view class="date-calendar">
        <mealPackageCalendar
          :show="true"
          :showTitle="false"
          mode="range"
          :color="variables.colorPrimary"
          month-num="2"
          :min-date="today"
          :allow-date="allowDate"
          :allow-same-day="true"
          :defaultDate="[]"
          @confirm="confirmDate"
        ></mealPackageCalendar>
      </view>
    </view>
  </view>
</template>

<script>
import mealPackageCalendar from '../components/calendar-range/meal-package-calendar.vue'
export default {
  name: 'filter-popup',
  components: {
    mealPackageCalendar
	},
	// Props Start
	props: {
		// 控制弹窗显示/隐藏
		show: {
			type: Boolean,
			default: true
		},
		// 日期范围 - 开始日期 (YYYY-MM-DD格式)
		startDate: {
			type: String,
			default: ''
		},
		// 日期范围 - 结束日期 (YYYY-MM-DD格式)
		endDate: {
			type: String,
			default: ''
		},
		// 是否显示日期筛选区域
		isShowDate: {
			type: Boolean,
			default: true
		},
		// 是否显示餐段筛选区域
		isShowMeal: {
			type: Boolean,
			default: true
		},
		// 是否显示状态筛选区域
		isShowStatus: {
			type: Boolean,
			default: true
		}
	},
	// Props End

  data() {
    return {
      showFilter: false,
      // showMask: false,
      isClosing: false,
      selectedStatus: '',
      selectedDate: '',
      selectedMeal: '',
      oldSelectedStatus: '',
      oldSelectedDate: '',
      oldSelectedMeal: '',
      // 日期筛选选项
      dateOptions: [
        { label: '全部', value: '' },
        { label: '近3天', value: '3' },
        { label: '近7天', value: '7' },
        { label: '近1个月', value: '30' },
        { label: '近3个月', value: '90' },
        { label: '自定义', value: 'custom' }
      ],
      // 餐段筛选选项
      mealOptions: [
        { label: '全部', value: '' },
        { label: '早餐', value: 'breakfast' },
        { label: '午餐', value: 'lunch' },
        { label: '下午茶', value: 'afternoon' },
        { label: '晚餐', value: 'dinner' },
        { label: '夜宵', value: 'supper' },
        { label: '凌晨餐', value: 'morning' },
      ],
      // 状态筛选选项
      statusOptions: [
        { label: '全部', value: '' },
        { label: '未取餐', value: 'no_take' },
        { label: '已过期', value: 'time_out' },
        { label: '已退款', value: 'cancel' },
        { label: '已取餐', value: 'take_out' },
        { label: '已停餐待处理', value: 'stop' },
        // { label: '停餐审核中', value: 'processing' }
      ],
      // 自定义日期
      showCustomDate: false,
      isCustomDateClosing: false,
      today: uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd'),
      mealDateList: [],
      mealDateText: '',
    }
  },

  computed: {
		showMask: {
			get() {
				return this.show
			},
			set(val) {
				this.$emit('update:show', val)
			}
		},
		// 根据 startDate 和 endDate 计算 allowDate
		allowDate() {
			if (!this.startDate || !this.endDate) {
				console.warn('startDate 或 endDate 为空，返回空数组');
				return [];
			}

			const dateList = [];
			const start = new Date(this.startDate);
			const end = new Date(this.endDate);

			// 检查日期有效性
			if (isNaN(start.getTime()) || isNaN(end.getTime())) {
				console.error('日期格式无效:', this.startDate, this.endDate);
				return [];
			}

			// 如果开始日期大于结束日期，交换它们
			if (start > end) {
				console.warn('开始日期大于结束日期，已自动交换');
				const temp = start;
				start = end;
				end = temp;
			}

			// 遍历生成日期数组
			const currentDate = new Date(start);
			while (currentDate <= end) {
				// 格式化为 YYYY-MM-DD 格式
				const year = currentDate.getFullYear();
				const month = String(currentDate.getMonth() + 1).padStart(2, '0');
				const day = String(currentDate.getDate()).padStart(2, '0');
				dateList.push(`${year}-${month}-${day}`);

				// 增加一天
				currentDate.setDate(currentDate.getDate() + 1);
			}

			return dateList;
		}
	},

  watch: {
		showMask(val) {
			if (val) {
				this.showFilter = true
        this.isClosing = false
        this.selectedStatus = this.oldSelectedStatus;
        this.selectedDate = this.oldSelectedDate;
        this.selectedMeal = this.oldSelectedMeal;
			} else {
				// 当外部设置 show=false 时，播放关闭动画
				if (this.showFilter) {
					this.isClosing = true
					setTimeout(() => {
						this.showFilter = false
						this.isClosing = false
					}, 300)
				}
			}
		}
	},

  onLoad() {
  },
  methods: {
    closeFilter() {
      // 直接设置 showMask 为 false，让 watch 处理动画
      this.showMask = false;
    },
    selectStatus(value) {
      this.selectedStatus = value;
    },
    selectDate(value) {
      this.selectedDate = value;
      if (value === 'custom') {
        // 点击自定义时，显示自定义日期选择器
        this.showCustomDatePicker();
      }
    },
    showCustomDatePicker() {
      this.showCustomDate = true;
      this.isCustomDateClosing = false;
      this.showFilter = false;
    },
    closeCustomDate() {
      this.isCustomDateClosing = true;
      this.showFilter = true;
      setTimeout(() => {
        this.showCustomDate = false;
        this.isCustomDateClosing = false;
      }, 300);
    },
    confirmDate(value) {
      if (value.length === 2 && value[0] === value[1]) {
        this.mealDateList = [value[0]]
        this.mealDateText = value[0]
      } else {
        this.mealDateList = value
        if (value.length > 1) {
          // 截取掉时间
          let start = value[0]
          let end = value[value.length - 1]
          this.mealDateText = `${start} ~ ${end}`
        }
      }
      this.closeCustomDate()
    },
    selectMeal(value) {
      this.selectedMeal = value;
    },
    resetFilter() {
      this.selectedStatus = '';
      this.selectedDate = '';
      this.selectedMeal = '';
    },
    confirmFilter() {
      this.oldSelectedStatus = this.selectedStatus;
      this.oldSelectedDate = this.selectedDate;
      this.oldSelectedMeal = this.selectedMeal;

      let dateList = ''
      if (this.selectedDate === 'custom') {
        dateList = [this.mealDateList[0], this.mealDateList[this.mealDateList.length - 1]]
      } else {
        if (this.selectedDate === '3') {
          dateList = [uni.$u.timeFormat(new Date().getTime() - 86400000 * 3, 'yyyy-mm-dd'), this.today]
        } else if (this.selectedDate === '7') {
          dateList = [uni.$u.timeFormat(new Date().getTime() - 86400000 * 7, 'yyyy-mm-dd'), this.today]
        } else if (this.selectedDate === '30') {
          dateList = [uni.$u.timeFormat(new Date().getTime() - 86400000 * 30, 'yyyy-mm-dd'), this.today]
        } else if (this.selectedDate === '90') {
          dateList = [uni.$u.timeFormat(new Date().getTime() - 86400000 * 90, 'yyyy-mm-dd'), this.today]
        }
      }

      this.$emit('confirm', {
        status: this.selectedStatus,
        date: dateList,
        meal: this.selectedMeal
      })

      // 关闭弹窗
      this.closeFilter();
    },
  }
}
</script>

<style lang="scss" scoped>

.filter-popup {
  // 筛选
  .filter-mask {
    position: fixed;
    top: 166rpx;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: flex;
    flex-direction: column;
    transition: opacity 0.3s ease;

    &.mask-closing {
      opacity: 0;
    }

    .filter-container {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    // 筛选弹窗样式
    .filter-popup {
      background: #fff;
      border-radius: 0 0 20rpx 20rpx;
      padding: 32rpx 0 0 0;
      overflow-y: auto; // 内容过多时可滚动
      animation: slideDown 0.3s ease-out;
      transform-origin: top; // 设置变换原点为顶部

      &.closing {
        animation: slideUp 0.3s ease-in;
      }

      .popup-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 32rpx 32rpx 0 32rpx;
        margin-bottom: 32rpx;

        .popup-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
        }
      }

      .filter-section {
        padding: 0 32rpx;
        margin-bottom: 40rpx;

        .section-title {
          font-size: 28rpx;
          color: #000;
          font-weight: 500;
          margin-bottom: 24rpx;
          display: block;
        }

        .filter-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 16rpx;

          .filter-tag {
            position: relative;
            padding: 14rpx 42rpx;
            border: 2rpx solid #f0f3f5;
            border-radius: 12rpx;
            background: #f0f3f5;
            display: flex;
            align-items: center;
            gap: 8rpx;
            transition: all 0.3s ease;

            .tag-text {
              font-size: 28rpx;
              color: #1d201e;
              transition: color 0.3s ease;
            }
            .check-icon{
              position: absolute;
              right: 0;
              bottom: 0;
              background: $color-primary;
              padding: 8rpx 2rpx 8rpx 4rpx;
              border-radius: 10rpx 0 12rpx 0;
            }

            &.active {
              border-color: $color-primary;
              background: $color-primary-light-9;

              .tag-text {
                color: $color-primary;
              }
            }

            // 特殊样式处理
            &:first-child.active {
              border-color: $color-primary;
            }
          }
        }
      }

      .popup-footer {
        display: flex;
        gap: 20rpx;
        padding: 40rpx 32rpx 32rpx 32rpx;
        border-top: 1rpx solid #f0f0f0;
        margin-top: 20rpx;

        .btn-reset,
        .btn-confirm {
          flex: 1;
          padding: 24rpx;
          border-radius: 48rpx;
          text-align: center;
          font-size: 30rpx;
          font-weight: 500;
          border: none;
        }

        .btn-reset {
          background: #f8f8f8;
          color: #666;
        }

        .btn-confirm {
          background: $color-primary;
          color: #fff;
        }
      }
    }
  }

  // 日期
  .date-popup{
    position: fixed;
    top: 166rpx;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #fff;
    border-radius: 0 0 20rpx 20rpx;
    overflow-y: auto; // 内容过多时可滚动
    animation: slideDown 0.3s ease-out;
    transform-origin: top; // 设置变换原点为顶部
    .date-calendar{
      border-top: 1rpx solid #f0f0f0;
    }
    &.closing {
      animation: slideUp 0.3s ease-in;
    }
  }

  // 动画效果
  @keyframes slideDown {
    from {
      transform: scaleY(0);
      opacity: 0;
    }
    to {
      transform: scaleY(1);
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: scaleY(1);
      opacity: 1;
    }
    to {
      transform: scaleY(0);
      opacity: 0;
    }
  }
}
</style>
