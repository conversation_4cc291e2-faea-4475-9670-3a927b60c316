# Filter-Popup 组件使用说明

## 📋 组件概述

`filter-popup` 是一个通用的筛选弹窗组件，支持日期、餐段、状态三种筛选类型，每种筛选都可以独立控制显示/隐藏。

## 🎯 Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `show` | Boolean | `true` | 控制弹窗显示/隐藏 |
| `startDate` | String | `''` | 日期范围开始日期 (YYYY-MM-DD格式) |
| `endDate` | String | `''` | 日期范围结束日期 (YYYY-MM-DD格式) |
| `isShowDate` | Boolean | `true` | 是否显示日期筛选区域 |
| `isShowMeal` | Boolean | `true` | 是否显示餐段筛选区域 |
| `isShowStatus` | Boolean | `true` | 是否显示状态筛选区域 |

## 🔧 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `confirm` | `{ date, meal, status }` | 确认筛选时触发 |
| `update:show` | Boolean | 弹窗显示状态变化时触发 |

## 💡 使用示例

### 1. 完整筛选 (默认)
```vue
<template>
  <filter-popup
    :show.sync="showFilter"
    :start-date="startDate"
    :end-date="endDate"
    @confirm="handleFilterConfirm"
  />
</template>

<script>
export default {
  data() {
    return {
      showFilter: false,
      startDate: '2025-06-01',
      endDate: '2025-06-30'
    }
  },
  methods: {
    handleFilterConfirm(filterData) {
      console.log('筛选结果:', filterData);
      // filterData = { date: [...], meal: 'breakfast', status: 'completed' }
    }
  }
}
</script>
```

### 2. 只显示日期和餐段筛选
```vue
<template>
  <filter-popup
    :show.sync="showFilter"
    :start-date="startDate"
    :end-date="endDate"
    :is-show-date="true"
    :is-show-meal="true"
    :is-show-status="false"
    @confirm="handleFilterConfirm"
  />
</template>
```

### 3. 只显示状态筛选
```vue
<template>
  <filter-popup
    :show.sync="showFilter"
    :start-date="startDate"
    :end-date="endDate"
    :is-show-date="false"
    :is-show-meal="false"
    :is-show-status="true"
    @confirm="handleFilterConfirm"
  />
</template>
```

### 4. 只显示日期筛选
```vue
<template>
  <filter-popup
    :show.sync="showFilter"
    :start-date="startDate"
    :end-date="endDate"
    :is-show-date="true"
    :is-show-meal="false"
    :is-show-status="false"
    @confirm="handleFilterConfirm"
  />
</template>
```

## 🎨 筛选选项说明

### 日期筛选选项
- **全部**: 不限制日期范围
- **近3天**: 最近3天的数据
- **近7天**: 最近7天的数据
- **近1个月**: 最近30天的数据
- **近3个月**: 最近90天的数据
- **自定义**: 弹出日历选择具体日期范围

### 餐段筛选选项
- **全部**: 不限制餐段
- **早餐**: 只显示早餐数据
- **午餐**: 只显示午餐数据
- **晚餐**: 只显示晚餐数据

### 状态筛选选项
- **全部**: 不限制状态
- **未取餐**: 未取餐状态
- **已过期**: 已过期状态
- **已退款**: 已退款状态
- **已取餐**: 已取餐状态
- **已停餐待处理**: 已停餐待处理状态
- **停餐审核中**: 停餐审核中状态

## 🔄 实际应用场景

### 餐包详情页面 (全筛选)
```vue
<!-- 显示所有筛选选项 -->
<filter-popup
  :show.sync="showFilter"
  :start-date="startDate"
  :end-date="endDate"
  :is-show-date="true"
  :is-show-meal="true"
  :is-show-status="true"
  @confirm="confirmFilterPopup"
/>
```

### 停餐/恢复页面 (部分筛选)
```vue
<!-- 只显示日期和餐段筛选，不显示状态筛选 -->
<filter-popup
  :show.sync="showFilter"
  :start-date="startDate"
  :end-date="endDate"
  :is-show-date="true"
  :is-show-meal="true"
  :is-show-status="false"
  @confirm="confirmFilterPopup"
/>
```

## 📱 响应式设计

组件支持移动端响应式设计，在不同屏幕尺寸下都能正常显示和操作。

## 🎯 注意事项

1. **日期格式**: `startDate` 和 `endDate` 必须使用 `YYYY-MM-DD` 格式
2. **日期范围**: 组件会自动根据 `startDate` 和 `endDate` 生成可选日期列表
3. **默认显示**: 所有筛选区域默认都显示，可根据需要隐藏
4. **事件处理**: 确认筛选后会返回包含所有筛选条件的对象
