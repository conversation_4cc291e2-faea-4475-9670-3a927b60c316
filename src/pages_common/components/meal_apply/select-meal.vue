<template>
	<view class="num-btn-wrap">
		<view v-for="(item, index) in mealList" :key="index" @click="setActive(index)">
			<view :class="['num-btn', item.isActive ? 'active-num' : '']">
				{{item.label}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			value: [Array, String, Number],
			maxNum: {
				type: Number,
				default: 50
			},
			list: Array,
		},
		data() {
			return {
        allMealTypeList: [
					{ label: '早餐', value: 'breakfast', isActive: false },
					{ label: '午餐', value: 'lunch', isActive: false },
					{ label: '下午茶', value: 'afternoon', isActive: false },
					{ label: '晚餐', value: 'dinner', isActive: false },
					{ label: '夜宵', value: 'supper', isActive: false },
					{ label: '凌晨餐', value: 'morning', isActive: false }
				],
				selectMealList: [],
				mealList: [] // 后台传来的
			}
		},
    created() {
			this.mealList = this.list.map(item =>{
				let data = this.allMealTypeList.filter(meal => meal.value === item)
				return data[0]
			})
			this.mealList.map(item => {
				if (this.value.indexOf(item.value) !== -1) {
					item.isActive = true
					this.selectMealList.push(item)
				}
			})
    },
    watch: {
    },
		methods: {
			setActive(index) {
        this.mealList.map((item, i) => {
          if (index === i) {
            item.isActive = !item.isActive
						if (item.isActive) {
							this.selectMealList.push(item)
						} else {
							this.selectMealList.splice(this.selectMealList.findIndex(meal => meal.value === item.value), 1)
						}
          }
        })
				this.$emit('confirm', {
					data: this.selectMealList
				})
      }
		},
	}
</script>

<style lang="scss">
.num-btn-wrap{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-bottom: 20rpx;
	.num-btn{
		width: 200rpx;
		height: 80rpx;
		text-align: center;
		margin: 10rpx 0;
		padding: 20rpx 0;
		border: 2rpx solid #dadada;
		border-radius: 14rpx;
		font-size: 30rpx;
	}
	.active-num{
		border: 2rpx solid $color-primary !important;
		color: $color-primary !important;
		background-color: $color-primary-light-9; // rgba(17, 230, 158, 0.0941176471)!important;
	}
}
</style>