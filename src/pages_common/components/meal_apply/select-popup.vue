<template>
	<view>
		<u-popup :show="visible" mode="bottom" round="16" @close="closePopup">
		  <view class="select-popup">
				<view class="select-popup-top">
					<view class="title">{{ title }}</view>
        	<u-icon	class="close-icon" name="close" size="28rpx" color="#a2a2a2" @click="closePopup"></u-icon>
				</view>
				<u-checkbox-group 
					v-model="checked"
					iconPlacement="right" 
					placement="column"
					@change="checkboxChange">
					<u-checkbox
						class="select-checkbox"
						v-for="(item, index) in list"
						:key="index"
						:activeColor="variables.colorPrimary"
						:name="item[valueKey]"
						:label="item[valueLabel]">
						<view>
							{{ item[valueLabel] }}
						</view>
					</u-checkbox>
				</u-checkbox-group>
		  </view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		props: {
			showSelect: {
				type: <PERSON>olean,
				default: false
			},
			list: Array,
			value: [Array, String, Number],
			valueKey: String,
			valueLabel: String,
			title: {
				type: String,
				default: '请选择'
			}
		},
		data() {
			return {
        checked: []
			}
		},
    created() {
    },
    watch: {
			visible() {
				this.checked = this.value
			}
    },
    computed: {
      visible: {
        get() {
          return this.showSelect
        },
        set(val) {
          this.$emit('update:showSelect', val)
        }
      }
    },
		methods: {
      closePopup() {
        this.visible = false
      },
			checkboxChange(e) {
				let data = this.list.filter(item => e.indexOf(item[this.valueKey]) !== -1)
				this.$emit('change', data)
			}
		},
	}
</script>

<style lang="scss">
	.select-popup {
		position: relative;
		width: 100%;
		height: 100%;
		padding: 0 40rpx 30rpx;

		.select-popup-top{
			position: relative;
			.title {
				padding: 30rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: bold;
			}
			.close-icon{
				position: absolute;
				right: -2rpx;
				top: 36rpx;
			}
		}
		

		.select-checkbox{
			margin-bottom: 30rpx;
		}
	}
</style>