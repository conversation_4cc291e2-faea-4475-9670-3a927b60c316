<template>
	<view class="pay_way">
		<!-- Component Start -->
		<u-popup :show="show" @close="showClose" :round="10" :closeable="true">
			<view class="container">
				<view class="title f-w-500 text-center"><text>支付方式</text></view>
				<scroll-view :scroll-y="true" style="height: 700rpx">
					<view class="" v-for="(item, index) in walletData" :key="index">
					<view class="org-name">{{item.org_name}}</view>
						<radio-group @change="radioChange($event, item.org_id)" class="p-t-30 p-l-30 reason" v-model="num">
							<view class="" v-for="(payitem, payindex) in item.pay_info" :key="payindex">
								<!-- 一级钱包 -->
								<label class="reason-item flex row-between" @click="showPop = false">
									<view class="black lg f-w-500">{{ funPayName(payitem) }}</view>
									<radio
										style="transform: scale(0.7)"
										color="#11E69E"
										:value="String(payitem.index)"
										:checked="num == String(payitem.index)"
									></radio>
								</label>
								<!-- 二级钱包 -->
								<view class="" v-if="String(payitem.index) == num && payitem.payway != 'WechatPay' && payitem.payway != 'AliPay' && payitem.payway != 'QyWechatPay' && payitem.pay_way != 'WXYFPay'">
									<!-- 钱包类型 -->
									<view class="m-t-50 m-b-28 xs muted">请选择钱包类型</view>
									<!-- 1. cannot 样式为余额不足||无法选择   2. active 样式为当前选择 -->
									<block v-for="(itemT, indexT) in payitem.wallet_list" :key="indexT">
										<view
											class="item flex flex-col flex-center"
											:class="{ active: currentIndex == itemT.balance_type, cannot: itemT.blance == 0 }"
											@click="selectWalletType(itemT, payitem)"
										>
											<price-format :price="itemT.blance" :size="36" :weight="500"></price-format>
											<view class="name nr">{{ itemT.name }}</view>
										</view>
									</block>
								</view>
							</view>
						</radio-group>
					</view>
				</scroll-view>
			</view>
		</u-popup>

		<!-- Components End -->
	</view>
</template>

<script>
import { apiGetMealApplyPayMethod } from '@/api/meal_apply.js'
import Cache from '@/utils/cache'
import { formateMealVisitorParams } from '@/utils/util.js'
export default {
	name: 'address-card',

	// Props Start
	props: {
		showPay: {
			type: Boolean,
			default: true
		},
		money: {
			type: Number,
			default: 0
		},
		orgId: {
			type: Number,
			default: 0
		},
		personNo: {
			type: String,
			default: ''
		},
	},
	// Props End

	// Data Start
	data() {
		return {
			num: '', // 选中的一级标签

			walletData: {},

			currentIndex: '',
			walletType: [
				{
					money: '560.00',
					name: '账户钱包',
					type: 1
				},
				{
					money: '322.00',
					name: '农行电子账户钱包',
					type: 1
				},
				{
					money: '50.00',
					name: '赠送钱包',
					type: 0
				},
				{
					money: '366.00',
					name: '赠送钱包',
					type: 1
				}
			],

			formData: {
				// company_id: '',
				// user_id: '',
				// person_no: '',

				// receipt_name: '233', // 收货姓名
				// receipt_phone: '15216949486', // 收货电话
				// receipt_adders: '收货地址', // 收货地址
				// take_meal_type: 'on_scene', //  消费订单类型
				// // on_scene-堂食
				// // waimai-外卖
				// // bale-堂食自提
				// // cupboard-取餐柜

				// payment_order_type: 'reservation', // 取餐类型 reservation-预约订单, report_meal-报餐
				org_id: '101' // 食堂ID
			},

			selectInfo: {
				payway: '', // 一级支付方式
				payway_alias: '', // 一级支付方式描述

				// 二级
				name: '', // 钱包名
				balance_type: '', // 钱包类型
				wallet_type: '' // 钱包类型
			}, // 选中的支付方式
			walletOrg: '',
			oldSelectId: ''
		}
	},
	// Data End
	computed: {
		show: {
			get() {
				return this.showPay
			},
			set(val) {
				this.$emit('update:showPay', val)
			}
		}
	},
	// Methods Start
	methods: {
		funPayName(data) {
			let name = ''
			if (data.payway == 'WechatPay' || data.payway == 'AliPay' ) {
				name = data.payway_alias
			} else {
				name = data.payway_alias
				// name = data.name + '-' + data.payway_alias
			}
			return name
		},
		// 一级选择钱包
		radioChange(e, org_id) {
			this.walletOrg = org_id
			this.num = e.detail.value
			// console.log('一级选择钱包', e)
			this.walletData.forEach(orgitem => {
				if (orgitem.org_id === org_id) {
					orgitem.pay_info.map(item => {
						if (String(item.index) == e.detail.value) {
							this.selectInfo.payway_alias = item.payway_alias
							this.selectInfo.payinfo_id = item.payinfo_id
							this.selectInfo.payway = item.payway
						}
					})
				}
			})
			// 如果是微信支付 支付宝，直接关闭弹窗
			if (this.selectInfo.payway === 'WechatPay' || this.selectInfo.payway === 'AliPay' || this.selectInfo.payway === 'QyWechatPay' || this.selectInfo.payway === 'WXYFPay') {
				console.log("this.oldSelectId", this.oldSelectId, this.selectInfo.payinfo_id);
				if(this.oldSelectId === this.selectInfo.payinfo_id) {
					this.$emit('select', this.selectInfo, this.walletOrg)
				}
				this.show = false
				this.selectInfo.name = ''
				this.$emit('showPayClose', false)
			}
		},
		// 二级-选择钱包
		selectWalletType(item, parent) {
			if (item.blance == 0) return
			let canSelect = true
			// 如果储值钱包余额充足，且赠送钱包余额也充足时，用户点击赠送钱包，不会选中赠送钱包
			if (parent.payway === 'PushiPay' && item.balance_type === 'complimentary') {
				for (let index = 0; index < parent.wallet_list.length; index++) {
					const current = parent.wallet_list[index];
					if (current.balance_type === "store") {
						// 当钱包余额不足时提示下
						if (item.blance < this.money) {
							uni.$u.toast('当前钱包余额不足，请选择其它钱包')
							canSelect = false
							break;
						}
						// 当储值钱包余额充足并且赠送钱包余额也充足时，优先使用储值钱包
						if ((current.blance >= this.money) && (item.blance >= this.money)) {
							uni.$u.toast('当前储值钱包余额充足，请先使用储值钱包进行支付')
							canSelect = false
							break;
						}
					}
				}
			}
			if (!canSelect) return
			this.currentIndex = item.balance_type
			this.selectInfo.name = item.name
			this.selectInfo.wallet_type = item.wallet_type
			this.selectInfo.balance_type = item.balance_type
			this.selectInfo.wallet_id = item.wallet_id
			this.show = false
			this.$emit('showPayClose', false)
		},

		// 获取支付方式列表
		getPayMethodList() {
			this.formData.org_id = this.orgId
			apiGetMealApplyPayMethod(formateMealVisitorParams(this.formData, true))
				.then(res => {
					if (res.code == 0) {
						this.walletData = res.data
						// 如果只有一条就默认拿第一个
						if (res.data.length === 1 && res.data[0]?.pay_info.length === 1) {
							this.walletOrg = res.data[0].org_id || ''
							this.num = res.data[0].index
							this.selectInfo.payway_alias = res.data[0].pay_info[0].payway_alias
							this.selectInfo.payinfo_id = res.data[0].pay_info[0].payinfo_id
							this.selectInfo.payway = res.data[0].pay_info[0].payway
							// 如果是微信支付，直接关闭弹窗
							if (this.selectInfo.payway === 'WechatPay' || this.selectInfo.payway === 'AliPay' || this.selectInfo.payway === 'QyWechatPay' || this.selectInfo.payway === 'WXYFPay') {
								console.log("this.oldSelectId", this.oldSelectId, this.selectInfo.payinfo_id);
								if(this.oldSelectId === this.selectInfo.payinfo_id) {
									this.$emit('select', this.selectInfo, this.walletOrg)
								}
								this.show = false;
								this.$emit('showPayClose', false);
							}
						}
					} else {
						console.log(res)
					}
				})
				.catch(err => {
					console.log('err', err)
				})
		},
		showClose() {
			this.show = false
			this.$emit('showPayClose', false)
		}
	},
	watch: {
		selectInfo: {
			handler(newVal) {
				this.$emit('select', newVal, this.walletOrg)
				this.oldSelectId = newVal.payinfo_id
			},
			deep: true
		},
		showPay(val) {
			console.log(val)
			// this.show = val;
			this.getPayMethodList()
			if (!val) {
      	this.num = -1
			}
		}
	},
	// Methods End

	// Life Cycle Start
	created() {
		this.formData.user_id = Cache.get('userInfo').user_id || ''
		this.formData.person_no = this.personNo ? this.personNo : this.$store.state.appoint.select.person.person_no
		this.formData.company_id = Cache.get('userInfo').company_id || this.$store.state.appoint.select.address_info.company_id
		this.formData.org_id = this.orgId ? this.orgId : this.$store.state.appoint.select.org.org_id
		console.log('this.$store.state.appoint.select', this.$store.state.appoint.select)

		// this.formData.payment_order_type = this.$store.state.appoint.payment_order_type
		// this.formData.take_meal_type = this.$store.state.appoint.select.take_meal_type
		// this.formData.receipt_name = this.$store.state.appoint.select.person.name
		// this.formData.person_no = this.$store.state.appoint.select.person.person_no
	}
	// Life Cycle End
}
</script>

<style lang="scss" scoped>
.pay_way {
	.ls-card {
		border-radius: 20rpx;
		background-color: #ffffff;
	}

	.main {
		padding: 32rpx 30rpx;
		box-sizing: border-box;

		.name {
			width: 400rpx;
		}
	}

	.container {
		padding: 0 40rpx;

		.title {
			padding: 30rpx 0;
		}

		.org-name{
			border-left: 5px solid $color-primary;
			padding-left: 20rpx;
			font-size: 36rpx;
		}

		.reason {
			.reason-item:first-child {
				border-bottom: $border-base;
				margin-bottom: 20rpx;
				padding-bottom: 20rpx;
			}
		}

		// 当前选择的钱包类型
		.active {
			color: $color-primary !important;
			border: 1px solid $color-primary !important;
		}

		.item:nth-child(2n) {
			margin-left: 0;
		}

		.item {
			display: inline-block;
			width: 300rpx;
			padding: 46rpx 0;
			border-radius: 8rpx;
			margin-left: 30rpx;
			margin-bottom: 30rpx;
			border: $border-base;
			text-align: center;
		}

		// 无法选择，余额不足
		.cannot {
			color: $color-text-secondary !important;
			position: relative;
		}

		.cannot::after {
			content: '无法选择';
			position: absolute;
			top: 0;
			right: 0;
			padding: 6rpx 14rpx;
			font-size: $font-size-xxs;
			background-color: $background-color;
			border-radius: 0rpx 8rpx;
		}
	}
}
</style>
