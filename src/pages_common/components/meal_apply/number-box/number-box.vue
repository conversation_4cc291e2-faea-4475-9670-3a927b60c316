<template>
  <view class="number-box">
    <uu-number-box
      v-model="valueNum"
      :min="min"
      :max="max"
      :asyncChange="asyncChange"
      @change="handleChange"
      @plus="handlePlus"
      @minus="handleMinus"
    >
      <view v-show="valueNum > min" slot="minus">
        <view class="minus">
          <u-icon name="minus" color="inherit" size="20rpx"></u-icon>
        </view>
      </view>
      <!-- 支付宝小程序中text不能用v-show指令，可用view替换text -->
      <!-- <view v-show="valueNum" slot="input" class="input text-center">{{ valueNum }}</view> -->
      <view slot="input">
        <view v-if="!disabled" :class="['num-btn', errorInput || overNumber ? 'error-num' : '']">
          <u-input v-model.number="valueNum" :formatter="formatterInput" class="input-all" input-align="center" type="number"/>
        </view>
      </view>
      <view slot="plus" class="plus" :class="disabled ? 'disabled' : '' ">
        <u-icon name="plus" color="inherit" size="20rpx"></u-icon>
      </view>
    </uu-number-box>
    <view v-if="errorInput" class="red-text">请输入正整数</view>
  </view>
</template>

<script>
import UuNumberBox from "./uu-number-box.vue"
export default {
  components: {
    UuNumberBox
  },
  props: {
    value: {
      type: Number
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 999
    },
    asyncChange: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      valueNum: 0,
      errorInput: false,
      overNumber: false
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val === this.valueNum) return
        this.valueNum = val
      },
      immediate: true
    },
    valueNum(val) {
      let reg = /^[0-9]*[1-9][0-9]*$/
      if (!reg.test(this.valueNum) && this.valueNum) {
        this.errorInput = true
        this.$emit('error', true)
        return
        // return uni.$u.toast("请输入正整数")
      } else {
        this.errorInput = false
        this.$emit('error', false)
      }
      this.$emit('input', val)
    }
  },
  methods: {
    formatterInput(e) {
      let num = Number(e)
      if (!e) num = 0
      return num
    },
    handleChange(e) {
      this.$emit('change', e.value)
    },
    handlePlus() {
      this.$emit('plus', this.valueNum)
    },
    handleMinus(e) {
      this.$emit('minus')
    }
  }
}
</script>

<style lang="scss">
.number-box{
  position: relative;
  .minus,
  .plus {
    color: $color-primary;
    width: 32rpx;
    height: 32rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid $color-primary;
    border-radius: 4rpx;
  }

  .plus {
    background-color: $color-primary;
    color: #fff;
    border-color: $color-primary;
  }
  .disabled{
    background-color: #d4d4d4;
    border-color: #d4d4d4;
  }

  .input-all {
    width: 90rpx;
    height: 50rpx;
    margin: 0 10rpx;
  }
  .red-text{
    color: red;
    font-size: 24rpx;
    text-align: center;
    position: absolute;
    bottom: -35rpx;
    left: 16rpx;
  }
}


</style>
