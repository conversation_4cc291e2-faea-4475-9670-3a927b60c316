<template>
	<view class="num-btn-wrap">
		<view v-for="(item, index) in tableNumList" :key="index" @click="setActive(index)">
			<view v-if="item.num==='custom'">
				<view :class="['num-btn', item.isActive ? (errorInput || overNumber) ? 'error-num' : 'active-num' : '']">
					<input type="text" v-model="customNum" @input="changeInput" placeholder="自定义人数"/>
				</view>
				<view v-if="errorInput" class="red-text">请输入正整数</view>
				<view v-if="overNumber" class="red-text">数量不能大于{{maxNum}}</view>
			</view>
			<view v-else :class="['num-btn', item.isActive ? 'active-num' : '']">
				{{item.num}}人
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			value: [Array, String, Number],
			maxNum: {
				type: Number,
				default: 50
			}
		},
		data() {
			return {
        tableNumList: [{
          num: 1,
          isActive: false
        },{
          num: 2,
          isActive: false
        },{
          num: 3,
          isActive: false
        },{
          num: 4,
          isActive: false
        },{
          num: 5,
          isActive: false
        },{
          num: 'custom',
          isActive: false
        }],
				customNum: '',
				number: '',
        isBtnActive: false,
        errorInput: false,
        overNumber: false,
			}
		},
    created() {
			let flag = true
			this.tableNumList.map(item => {
				if (item.num === Number(this.value)) {
					item.isActive = true
					flag = false
				}
				if (flag && item.num === 'custom') {
					item.isActive = true
					this.customNum = Number(this.value)
				}
			})
    },
    watch: {
    },
		methods: {
			setActive(index) {
        this.tableNumList.map((item, i) => {
          if (index === i) {
            item.isActive = true
          } else {
            item.isActive = false
          }
          if (item.isActive && item.num !== 'custom') {
            this.number = item.num
          }
          if (item.isActive && item.num === 'custom' && /^[1-9]{1,3}$/.test(this.customNum)) {
            this.number = this.customNum
          }
          if (!item.isActive && item.num === 'custom' && !/^[1-9]{1,3}$/.test(this.customNum)) {
            this.customNum = ''
						this.errorInput = false
						this.overNumber = false
          }
        })
				this.confirm()
      },
			changeInput(e) {
				this.number = e.detail.value
				let reg = /^[0-9]*[1-9][0-9]*$/
				if (!reg.test(this.number) && this.number) {
					this.errorInput = true
					// return uni.$u.toast("请输入正整数")
				} else {
					this.errorInput = false
				}
				if (Number(this.number) > this.maxNum) {
					this.overNumber = true
					// return uni.$u.toast(`数量不能大于${this.maxNum}`)
				} else {
					this.overNumber = false
				}
				this.confirm()
			},
			confirm(e) {
				if (this.overNumber || this.errorInput) {
					this.$emit('error', true)
				} else {
					this.$emit('error', false)
				}
				this.$emit('confirm', {
					data: this.number
				})
			}
		},
	}
</script>

<style lang="scss">
.num-btn-wrap{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-bottom: 20rpx;
	.num-btn{
		width: 200rpx;
		height: 80rpx;
		text-align: center;
		margin: 10rpx 0;
		padding: 20rpx 0;
		border: 2rpx solid #dadada;
		border-radius: 14rpx;
		font-size: 30rpx;
	}
	.active-num{
		border: 2rpx solid $color-primary !important;
		color: $color-primary !important;
		// background-color: rgba(17, 230, 158, 0.0941176471)!important;
		// background-color: rgba($color: #12e294, $alpha: 0.15);
	}
	.error-num{
		border: 2rpx solid red!important;
		color: red!important;
	}
	.red-text{
		color: red;
		font-size: 24rpx;
		text-align: center;
	}
}
</style>