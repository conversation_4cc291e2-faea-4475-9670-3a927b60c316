<template>
	<view>
		<u-popup :show="visible" mode="bottom" round="16" @close="closePopup">
		  <view class="select-popup">
				<view class="select-popup-top">
					<view class="title">{{ title }}</view>
        	<u-icon	class="close-icon" name="close" size="28rpx" color="#a2a2a2" @click="closePopup"></u-icon>
				</view>
				<view class="num-btn-wrap">
					<view v-for="(item, index) in tableNumList" :key="index" @click="setActive(index)">
						<view v-if="item.num==='custom'" :class="['num-btn', item.isActive ? 'active-num' : '']">
							<input type="text" v-model="customNum" @input="changeInput" placeholder="自定义人数"/>
						</view>
						<view v-else :class="['num-btn', item.isActive ? 'active-num' : '']">
							{{item.num}}人
						</view>
					</view>
				</view>
				<u-button text="确定" shape="circle" :color="variables.bgLinearGradient1" @click="confirm"></u-button>
		  </view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		props: {
			showSelect: {
				type: Boolean,
				default: false
			},
			list: Array,
			value: [Array, String, Number],
			valueKey: String,
			valueLabel: String,
			title: {
				type: String,
				default: '请选择'
			},
			maxNum: {
				type: Number,
				default: 50
			}
		},
		data() {
			return {
        tableNumList: [{
          num: 1,
          isActive: false
        },{
          num: 2,
          isActive: false
        },{
          num: 3,
          isActive: false
        },{
          num: 4,
          isActive: false
        },{
          num: 5,
          isActive: false
        },{
          num: 'custom',
          isActive: false
        }],
				customNum: '',
				number: '',
        isBtnActive: false,
			}
		},
    created() {
    },
    watch: {
    },
    computed: {
      visible: {
        get() {
          return this.showSelect
        },
        set(val) {
          this.$emit('update:showSelect', val)
        }
      }
    },
		methods: {
			setActive(index) {
        this.tableNumList.map((item, i) => {
          if (index === i) {
            item.isActive = true
          } else {
            item.isActive = false
          }
          if (item.isActive && item.num !== 'custom') {
            this.number = item.num
            this.isBtnActive = true
          }
          if (item.isActive && item.num === 'custom' && /^[1-9]{1,3}$/.test(this.customNum)) {
            this.isBtnActive = true
            this.number = this.customNum
          }else if (item.isActive && item.num === 'custom' && !/^[0-9]{1,3}$/.test(this.customNum)) {
            this.isBtnActive = false
          }
        })
      },
      closePopup() {
        this.visible = false
      },
			changeInput(e) {
				this.number = e.detail.value
			},
			confirm(e) {
				let reg = /^[0-9]*[1-9][0-9]*$/
				if (!reg.test(this.number)) {
					return uni.$u.toast("请输入正整数")
				}
				if (Number(this.number) > this.maxNum){
					return uni.$u.toast(`数量不能大于${this.maxNum}`)
				}
				this.$emit('confirm', {
					data: this.number
				})
			}
		},
	}
</script>

<style lang="scss">
	.select-popup {
		position: relative;
		width: 100%;
		height: 100%;
		padding: 0 40rpx 30rpx;

		.select-popup-top{
			position: relative;
			.title {
				padding: 30rpx;
				text-align: center;
				font-size: 32rpx;
				font-weight: bold;
			}
			.close-icon{
				position: absolute;
				right: -2rpx;
				top: 36rpx;
			}
		}
		

		.num-btn-wrap{
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			margin-bottom: 20rpx;
			.num-btn{
				width: 200rpx;
				height: 90rpx;
				text-align: center;
				margin: 10rpx 0;
				padding: 25rpx 0;
				border: 2rpx solid #dadada;
				border-radius: 14rpx;
				font-size: 30rpx;
			}
			.active-num{
				border: 2rpx solid $color-primary !important;
				color: #FFF!important;
				background-color: $color-primary !important;
			}
		}

	}
</style>