<template>
  <view class="record-list">
    <view class="record-list-item" v-for="(item, index) in list">
      <view v-if="type==='attendance'" :class="['type-label', setStatusColor(item.punch_status_alias)]">{{item.punch_status_alias}}</view>
      <view v-if="type==='accessControl'" :class="['type-label', setStatusColor(item.pass_type_alias)]">{{item.pass_type_alias}}</view>
      <view class="item-title md">
        <text v-if="type==='attendance'">打卡时间：</text>
        <text v-else-if="type==='accessControl'">通行时间：</text>
        {{item.create_time}}
      </view>
      <view class="record-info-wrap">
        <view class="face-img">
          <u-image width="100%" height="250rpx" :src="item.face_url"></u-image>
          <!-- <view class="face-img-text">刷脸记录成功</view> -->
        </view>
        <view class="record-info">
          <view class="record-info-item">
            <text class="muted">姓名：</text>{{item.person_name}}
          </view>
          <view class="record-info-item">
            <text class="muted">所属组织：</text>{{item.primary}}
          </view>
          <view class="record-info-item">
            <text class="muted">通行设备：</text>{{item.device_name}}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      type: String,
      list: Array
    },
    data() {
      return {
        
      }
    },
    methods: {
      setStatusColor(status) {
        if (status === '签到') {
          return 'qiandao'
        } else if (status === '签退') {
          return 'qiantui'
        } else if (status === '迟到') {
          return 'chidao'
        } else if (status === '早退') {
          return 'zaotui'
        } else if (status === '缺卡') {
          return 'zaotui'
        } else if (status === '进') {
          return 'jin'
        } else if (status === '出') {
          return 'chu'
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
.record-list{
  padding: 0 40rpx;
  .record-list-item{
    background-color: #FFFFFF;
    padding:30rpx;
    height: 400rpx;
    border-radius: 20rpx;
    margin-top: 20rpx;
    position: relative;
    .type-label{
      line-height: 40rpx;
      padding-left: 40rpx;
      padding-right: 20rpx;
      border-radius: 20rpx 0 0 20rpx;
      color: #FFFFFF;
      position: absolute;
      right: 0;
      &::before{
        content: '';
        display: block;
        height: 12rpx;
        width: 12rpx;
        position: absolute;
        left: 20rpx;
        top: 15rpx;
        border-radius: 50%;
        background-color: #FFFFFF;
      }
    }
    .qiandao, .jin{
      background-color: #11E69E;
    }
    .qiantui, .chu{
      background-color: #FFB142;
    }
    .chidao{
      background-color: #9F9F9F;
    }
    .zaotui{
      background-color: #FF5757;
    }
    .item-title{
      line-height: 40rpx;
    }
    .record-info-wrap{
      display: flex;
      justify-content: space-between;
      align-items: center;
      .face-img{
        width: 35%;
        height: 250rpx;
        margin-top: 30rpx;
        border-radius: 20rpx;
        position: relative;
        overflow: hidden;
        // .face-img-text{
        //   position: absolute;
        //   bottom: 0;
        //   background-color: #11e69e7a;
        //   width: 100%;
        //   line-height: 50rpx;
        //   padding-left: 15rpx;
        //   color: #FFFFFF;
        // }
      }
      .record-info{
        width: 60%;
        .record-info-item{
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          margin-top: 40rpx;
        }
      }
    }
  }
}

  
</style>
