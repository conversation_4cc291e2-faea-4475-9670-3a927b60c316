<template>
  <view :style="theme.style" class="access-record">
    <view class="access-record-top">
      <view class="flex xl" @click="showDatePicker = true">
        <text>{{selectDate}}</text>
        <u-icon color="#000000" name="arrow-down" size="32rpx"></u-icon>
      </view>
    </view>
    <mescroll-uni
      ref="mescrollRef"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @emptyclick="emptyClick"
      top="80"
    >
      <record-list type="accessControl" :list="recordList"></record-list>
    </mescroll-uni>
    <u-datetime-picker
      :show="showDatePicker"
      :value="selectDate"
      @confirm="confirmDate"
      @cancel="showDatePicker = false"
      mode="date"
    ></u-datetime-picker>
  </view>
</template>

<script>
  import { apiGetAccessControlRecord } from '@/api/control_attendance.js'
  import MescrollMixin from '@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins'
  import { timeFormat } from '@/utils/date';
  import recordList from '../components/control_attendance/record_list.vue'
  export default {
    mixins: [MescrollMixin],
    components: { recordList },
    data() {
      return {
        showDatePicker: false,
        selectDate: timeFormat(new Date().getTime(), 'yyyy-mm-dd'),
        recordList: []
      }
    },
    methods: {
      confirmDate(e) {
        this.selectDate = timeFormat(e.value, 'yyyy-mm-dd')
        this.showDatePicker = false
        this.upCallback({ num: 1, size: 10 })
      },
      upCallback(page) {
        this.getAccessControlRecord(page)
      },

      getAccessControlRecord(page) {
        this.$showLoading({
          title: '获取中....',
          mask: true
        })
        apiGetAccessControlRecord({
          page: page.num,
          page_size: page.size,
          date: this.selectDate
        })
          .then(res => {
            uni.hideLoading()
            if (res.code == 0) {
              const results = res.data.results
              const count = res.data.count
              // 如果是第一页需手动置空列表
              if (page.num == 1) this.recordList = []
              // 追加新数据
              this.recordList = [...this.recordList, ...results]
              //后台接口有返回列表的总数据量 count, 判断是否有下一页
              let pageLength = 0
              if (results) {
                pageLength = results.length
              } else {
                pageLength = 0
              }
              this.mescroll.endBySize(pageLength, count)

            } else {
              uni.$u.toast(res.msg)
            }
          })
          .catch(err => {
            uni.$u.toast(err)
          })
      },
    }
  }
</script>

<style lang="scss" scoped>
.access-record{
  padding: 30rpx 40rpx;
}
</style>
