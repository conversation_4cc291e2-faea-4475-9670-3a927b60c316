<template>
  <view :style="theme.style" class="control-attendance">
    <view class="menu-list flex flex-wrap img-filter">
      <view
        class="menu-item m-b-40 flex flex-col col-center"
        v-for="(item, index) in menuList"
        :key="index"
        @click="handleClickMenu(item)"
      >
        <u-image width="60rpx" height="60rpx" :src="item.icon"></u-image>
        <view class="m-t-16 xs">{{ item.name }}</view>
      </view>
    </view>
    <!-- 弹窗 -->
    <popup></popup>
    <floating-popup :floatingPopupShow="floatingPopupShow"></floating-popup>
  </view>
</template>

<script>
  import FloatingPopup from '@/components/floating-popup/floatingPopup.vue'
  export default {
    components: { FloatingPopup },
    data() {
      return {
        // menuList: [
        //   {
        //     icon: this.$imgPath.img_access_record,
        //     name: '进出记录',
        //     path: '/pages_common/control_attendance/access_record',
        //     type: 'access_record'
        //   },
        //   {
        //     icon: this.$imgPath.img_leave_apply,
        //     name: '请假申请',
        //     path: '',
        //     type: 'leave_apply'
        //   },
        //   {
        //     icon: this.$imgPath.img_repair_apply,
        //     name: '补卡申请',
        //     path: '',
        //     type: 'repair_apply'
        //   },
        //   {
        //     icon: this.$imgPath.img_attendance_statistics,
        //     name: '考勤记录',
        //     path: '/pages_common/control_attendance/attendance_record',
        //     type: 'attendance_statistics'
        //   }
        // ],
        floatingPopupShow: false
      }
    },
    computed: {
      menuList() {
        return [
          {
            icon: this.themeImgPath.img_access_record,
            name: '进出记录',
            path: '/pages_common/control_attendance/access_record',
            type: 'access_record'
          },
          {
            icon: this.themeImgPath.img_leave_apply,
            name: '请假申请',
            path: '',
            type: 'leave_apply'
          },
          {
            icon: this.themeImgPath.img_repair_apply,
            name: '补卡申请',
            path: '',
            type: 'repair_apply'
          },
          {
            icon: this.themeImgPath.img_attendance_statistics,
            name: '考勤记录',
            path: '/pages_common/control_attendance/attendance_record',
            type: 'attendance_statistics'
          }
        ]
      }
    },
    onShow() {
      this.floatingPopupShow = !this.floatingPopupShow
    },
    methods: {
      handleClickMenu(item) {
        if (item.path) {
          this.$miRouter.push(item.path)
        } else {
          this.$toast({
            title: '请期待'
          })
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
.control-attendance{
  padding: 40rpx;
  .menu-list{
    background-color: #FFFFFF;
    padding: 50rpx 30rpx;
    height: 200rpx;
    border-radius: 20rpx;
    .menu-item {
      width: 25%;
    }
  }
}
</style>
