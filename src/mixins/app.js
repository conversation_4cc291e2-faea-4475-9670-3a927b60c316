import { copy } from '@/utils/util.js'
import { getSatisfactionRecord } from '@/api/app.js'
export default {
  data() {
    return {}
  },
  methods: {
    // 全局复制方法
    copy(str) {
      copy(str.toString())
    },
    // 获取评价弹窗是否显示
    async _appGetSatisfaction(val) {
      const [err, res] = await this.$to(getSatisfactionRecord({
        module_key: val
      }))
      if (err) {
        uni.$u.toast(err.message)
        return null
      }
      if (res && res.code === 0) {
        return res.data
      } else {
        // uni.$u.toast(res.msg || '出错啦！')
        return null
      }
    }
  },
  computed: {
    $vars() {
      return this.$store.getters.vars
    }
  }
}
