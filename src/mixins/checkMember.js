import Cache from '@/utils/cache'
import { getUserIsMember } from '@/utils/member'
import store from '@/store'
import { mapActions } from 'vuex'
export default {
  data() {
    return {
      memberInfo: {}, // 会员信息
      membershipPermissions: [], // 会员权限
      modalContent: '',
      showConfirmbtn: false,
      showMember: false
    }
  },
  methods: {
    ...mapActions({
      setIsPermissions: 'setIsPermissions'
    }),
    async checkMemberPermissions(permissions, modal) {
      console.log('准备获取memberInfo')
      let flag = false
      this.memberInfo = await getUserIsMember(Cache.get('userInfo').user_id)
      console.log('获取到的memberInfo', this.memberInfo)
      // 这里需判断用户是否有权限(尚未确定菜谱那属于什么固定权限还是键值)
      if (modal) {
        if (this.memberInfo.is_member_status) {
          flag = true
        } else {
          if (!this.memberInfo.healthy_info) return uni.$u.toast('请先完善健康档案')
          this.showMember = true
          if (!this.memberInfo.is_member_status) {
            this.modalContent = '你当前属于非会员状态，请成为会员后查看'
            this.showConfirmbtn = true
          }
        }
      }
      return flag
    },
    // 通过键值判断用户是否有权限查看照片
    // isPermissionsByKey() {
    //   let flag = true
    //   let membershipPermissions = Cache.get('membershipRights') ? Cache.get('membershipRights') : {member_has_permissions: []}
    //   if ('member_has_permissions' in membershipPermissions && membershipPermissions.member_has_permissions.length!==0) {
    //     // 循环判断
    //     for (let item of membershipPermissions.member_has_permissions) {
    //       if (item.type==='fixed' && item.permission_key==='picture') {
    //         // 如果是固定模块且模块为图片
    //         // 判断是否有会员
    //         if (!membershipPermissions.is_member_status) {
    //           return flag = false
    //         }
    //       }
    //     }
    //   }
    //   return flag
    // },
    // 判断isPermissions
    isPermissions() {
      // 获取vux中的isPermissions
      console.log('isPermissions里的noPermissions', store.getters.noPermissions)
      let flag = store.getters.noPermissions
      // 如果是false的话弹窗
      if (!flag) {
        this.showMember = true
        this.modalContent = '你当前属于非会员状态，请成为会员后查看'
        this.showConfirmbtn = true
      }
    },
    // 设置会员标志
    setShowMember(value) {
      this.showMember = value
      // 重置
      this.setIsPermissions(true)
      console.log('noPermissions', store.getters.noPermissions)
    }
  }
}
