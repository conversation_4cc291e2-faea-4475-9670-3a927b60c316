<template>
  <div>
    <!-- 评价弹窗 -->
    <u-modal :show="visibleCur" confirmColor="#5A6080" class="satisfaction-modal" closeOnClickOverlay @close="visibleCur = false">
      <template>
        <view class="title">功能满意度评价</view>
      </template>
      
      <view v-if="tips" class="txt">{{ tips }}</view>
      <view v-else class="txt">请对{{ module_key_obj[satisfactionForm.module_key] }}功能的流程进行评价:</view>
      
      <view style="margin: 32rpx 0 40rpx 0; display: flex; justify-content: space-between">
        <template v-if="satisfactionForm.is_satisfied">
          <image
            :src="themeImgPath.img_banner_satisfaction"
            style="width: 270rpx; height: 102rpx"
            @click="onSetSatisfaction(true)"
          />
          <image
            :src="themeImgPath.img_banner_not_satisfaction"
            style="width: 270rpx; height: 102rpx"
            @click="onSetSatisfaction(false)"
          />
        </template>
        <template v-else>
          <image
            :src="themeImgPath.img_banner_not_satisfaction_v2"
            style="width: 270rpx; height: 102rpx"
            @click="onSetSatisfaction(true)"
          />
          <image
            :src="themeImgPath.img_banner_satisfaction_v2"
            style="width: 270rpx; height: 102rpx"
            @click="onSetSatisfaction(false)"
          />
        </template>
      </view>
      <template v-if="!satisfactionForm.is_satisfied">
        <!-- 输入改进为 标签选择 -->
        <view v-if="tags.length" style="font-size: 32rpx; line-height: 120%; color: #898989">您不满意的类型是：</view>
        <view v-if="tags.length" class="label-list">
          <view v-for="(item, index) in tags" :key="index" 
            @click="toggleLabel(item)"
            class="label-item"
            :class="selectedLabels.includes(item) ? 'label-item--active' : '' "
          >
            {{ item }}
          </view>
        </view>
        <!-- <view v-if="!tags.length" style="font-size: 32rpx; line-height: 120%; color: #1d201e">请简要说明不满意的情况，以便我们后期调整</view> -->
        <u--textarea
          v-model="satisfactionForm.reason"
          style="margin-top: 20rpx"
          placeholder="请说您不满意的原因，以便我们提供更好的产品体验"
          maxlength="100"
          count
        ></u--textarea>
      </template>
      <view slot="confirmButton">
        <u-button
          :text="'提交评价'"
          shape="circle"
          color="#11E69E"
          @click="onPushSatisfaction"
          :customStyle="pushBtnStyle"
        ></u-button>
      </view>
    </u-modal>
  </div>
</template>

<script>
import { addSatisfactionRecord } from '@/api/app.js'
export default {
  props: {
    satisfactionForm: {
      type: Object,
      default: () => ({
        is_satisfied: true, // 是否满意
        reason: '', // 不满意原因
        module_key: '', // 模块key
        tags: [] // 评价标签
      })
    },
    visible: {
      type: Boolean,
      value: false
    },
    tips: {
      type: String,
      value: 'tips'
    },
    tags : {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      module_key_obj: {
        charge: '充值',
        report: '报餐',
        report_meal: '报餐', // 兼容
        reservation: '预约',
        jiaofei: '缴费中心'
      },
      pushBtnStyle: {
        width: '560rpx',
        height: '72rpx'
      },
      selectedLabels: [] // 存储选中的标签值
    }
  },
  computed: {
    visibleCur: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  mounted() {},
  methods: {
    // 切换评价
    onSetSatisfaction(val) {
      this.$nextTick(() => {
        this.satisfactionForm.is_satisfied = val
      })
    },
    toggleLabel(value) {
      if (this.selectedLabels.includes(value)) {
        this.selectedLabels = this.selectedLabels.filter(v => v !== value)
      } else {
        this.selectedLabels.push(value)
      }
    },
    // 提交评价
    async onPushSatisfaction() {
      if( this.satisfactionForm.is_satisfied === false && this.tags.length && this.selectedLabels <= 0){
        uni.$u.toast('请选择您不满意的类型')
        return
      }
      this.$showLoading({
        title: '获取中....',
        mask: true
      })

      const params = {
        ...this.satisfactionForm,
        tags: this.selectedLabels
      }
      if (this.satisfactionForm.is_satisfied) {
        delete params.reason
      }

      // 特殊处理 report_meal = report
      if (params.module_key === 'report_meal') {
        params.module_key = 'report'
      }
      // console.log('params', params)
      // return
      const [err, res] = await this.$to(addSatisfactionRecord(params))
      if (err) {
        return uni.$u.toast(err.message)
      }
      if (res && res.code === 0) {
        uni.hideLoading()
        uni.$u.toast('提交成功')
        this.$emit('onModalSuccess')
        this.visibleCur = false
      } else {
        uni.hideLoading()
        uni.$u.toast(res.msg || '出错啦！')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  font-size: 36rpx;
  line-height: 100%;
  letter-spacing: 0%;
  text-align: center;
  color: #1d201e;
  margin-bottom: 40rpx;
  font-weight: bold;
}
.satisfaction-modal {
  .txt {
    font-size: 32rpx;
    line-height: 32rpx;
    color: #1d201e;
  }
  ::v-deep .u-modal__content {
    display: block;
    color: red;
    // flex-direction: column;
  }
}
.label-list{
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 20rpx;
}
.label-item {
  color: $color-primary;
  margin:0 10rpx 10rpx 0;
  display: inline-block;
  border: 1rpx solid $color-primary;
  padding: 10rpx 15rpx;
  border-radius: 10rpx;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  flex-shrink: 0; // 防止被压缩
  max-width: 100%; // 最大宽度为容器宽度
  white-space: nowrap; // 禁止换行
  overflow: hidden; // 超出部分隐藏
  text-overflow: ellipsis; // 超出部分显示省略号

  &--active {
    background-color: $color-primary;
    color: #fff;
  }
}
</style>
