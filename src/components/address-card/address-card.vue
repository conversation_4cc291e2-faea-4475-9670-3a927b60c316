<template>
  <view class="address-info">
    <!-- Main Start -->
    <view class="main ls-card" @click="$miRouter.push('/pages_bundle/select/select_address')">
      <view class="flex row-between">
        <view class="">
          <slot name="title"></slot>
          <slot name="user-info"></slot>
        </view>
        <u-icon name="arrow-right" size="24rpx"></u-icon>
      </view>
    </view>
    <!-- Main End -->
  </view>
</template>

<script>
export default {
  name: 'address-card',

  // Props Start
  // props: {
  //     addressInfo: {
  //     	type: Object,
  //     	default: ()=>({})
  //     },
  // },
  // Props End

  // Data Start
  data() {
    return {}
  },
  // Data End

  // Methods Start
  methods: {},
  // Methods End

  // Life Cycle Start
  created() {}
  // Life Cycle End
}
</script>

<style lang="scss" scoped>
.address-info {
  .ls-card {
    border-radius: 20rpx;
    background-color: #ffffff;
  }

  .main {
    padding: 40rpx 30rpx;
    box-sizing: border-box;
  }
}
</style>
