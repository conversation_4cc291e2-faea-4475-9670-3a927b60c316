<template>
  <view>
    <canvas
      :style="{width: width + 'px', height: height + 'px'}"
    	:canvas-id="canvasImgId"></canvas>
  </view>
</template>

<script>
  export default {
    props: {
      canvasImgId: String,
    	url: String,
    	width: String,
    	height: String,
    	roi: {
    		type: Object,
    		default: () => {}
    	}
    },
    data() {
      return {
        imageContext: null
      }
    },
    mounted() {
      this.imageContext = uni.createCanvasContext(this.canvasImgId, this);
      
      // var img = new Image()
      // img.crossOrigin = ''
      // let that = this
      // img.onload = function() {
      //   that.imageContext.drawImage(this, that.roi.x1, that.roi.y1, that.roi.x2 - that.roi.x1, that.roi.y2 - that.roi.y1, 0, 0, that.width, that.height);
      //   // that.imageContext.getImageData(0, 0, that.width, that.height)
      // }
      // img.src = this.url
      let img = String(this.url)
      let url
      if (img.indexOf('debug') !== -1) {
        url = process.env.VUE_APP_MINAPP + img.split('https://cashier-v4.debug.packertec.com')[1]
      }else {
        url = process.env.VUE_APP_MINAPP + img.split('https://cashier-v4.packertec.com')[1]
      }
      this.imageContext.drawImage(url, this.roi.x1, this.roi.y1, this.roi.x2 - this.roi.x1, this.roi.y2 - this.roi.y1, 0, 0, this.width, this.height);
      this.imageContext.draw()
    },
    methods: {
      
    }
  }
</script>

<style>
.{
  overflow: hidden;
}
</style>
