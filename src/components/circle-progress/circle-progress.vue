<template>
	<view
		class="charts-box"
		:style="{
			width: widthPx + 'px',
			height: widthPx + 'px'
		}"
	>
		<canvas
			:canvas-id="canvasId"
			:id="canvasId"
			type="2d"
			class="charts"
			:style="{
				zIndex: '100',
				width: widthPx + 'px',
				height: widthPx + 'px'
			}"
		/>
		<view 
			v-if="bgShow"
			:class="['bg', bgImgShow]"
		></view>
	</view>
</template>

<script>
import uCharts from '@/uni_modules/qiun-data-charts/js_sdk/u-charts/u-charts.js'
export default {
	name: 'circle-progress',
	props: {
		// 圆环进度百分比值
		percent: {
			type: Number,
			default: 0,
			// 值在0到100之间
			validator: val => {
				return val >= 0 && val <= 100
			}
		},
		// 圆环底色（灰色的圆环）
		inactiveColor: {
		  type: String,
		  default: '#ececec'
		},
		// 圆环激活部分的颜色
		activeColor: {
			type: String,
			default: '#009dff'
		},
		// 圆环线条的宽度
		borderWidth: {
			type: [Number, String],
			default: 14
		},
		// 整个圆形的宽度，单位rpx
		width: {
			type: [Number, String],
			default: 200
		},
		// 文字
		title: {
			type: String,
			default: ''
		},
		// 文字
		titleSize: {
			type: [Number, String],
			default: 9
		},
		// 文字
		subtitle: {
			type: [Number, String],
			default: ''
		},
		subtitleSize: {
			type: [Number, String],
			default: 10
		},
		bgImg: {
			type: Number,
			default: 0
		},
		bgShow: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			uChartsInstance: {},
			pixelRatio: 2,
			cWidth: 0,
			cHeight: 0,
			chartData: {
				series: [
					{
						color: '#2fc25b',
						data: 0
					}
				]
			},
			canvasId: this.randomId(), //一个页面多个圆形进度
			widthPx: uni.upx2px(this.width) // 转成px后的整个组件的背景宽度
		}
	},
	computed: {
		bgImgShow() {
			console.log('this.bgImg', this.bgImg)
			switch (this.bgImg) {
				case 0:
					return 'unhot'
				case 1:
					return 'hot'
				case 2:
					return 'hot-2'
				case 3:
					return 'hot-3'
			}
		}
	},
	watch: {
		percent(nVal, oVal = 0) {
			// console.log(this.percent, '数据')
			// console.log(nVal, '数据2222')
			if (nVal >= 100) nVal = 100
			if (nVal <= 0) oVal = 0
			setTimeout(() => {
				if (this.uChartsInstance[this.canvasId]) {
					this.uChartsInstance[this.canvasId].updateData({
						series: [
							{
								color: this.activeColor,
								data: this.percent ? this.percent / 100 : 0
							}
						]
					})
				}
			}, 50)
		},
		subtitle() {
			setTimeout(() => {
				if (this.uChartsInstance[this.canvasId]) {
					this.uChartsInstance[this.canvasId].updateData({
						subtitle: {
							name: String(this.subtitle),
							fontSize: this.subtitleSize
							// color: "#666666"
						}
					})
				}
			}, 50)
		}
	},
	onReady() {},
	created() {
		this.pixelRatio = uni.getSystemInfoSync().pixelRatio
		this.cWidth = uni.upx2px(this.width)
		this.cHeight = uni.upx2px(this.width)
		// 圆环线条的宽度
		// 0-1
		setTimeout(() => {
		this.$nextTick(()=> {
			this.drawCharts(this.canvasId)
		})
		}, 500);
	},
	mounted() {},
	methods: {
		drawCharts(id) {
			console.log('123', this.bgImg)
			// 因为h5 拿不到res[0].node 分开两套
			// #ifdef MP-ALIPAY || MP-WEIXIN
			let query = uni.createSelectorQuery().in(this)
			query.select('#' + id).fields({ node: true, size: true }).exec(res => {
					if (res[0]) {
						let canvas = res[0].node
						let ctx = canvas.getContext('2d')
						canvas.width = res[0].width * this.pixelRatio
						canvas.height = res[0].height * this.pixelRatio
						// let mm = uni.$u.deepClone(cc)
						this.uChartsInstance[id] = new uCharts({
							context: ctx,
							type: 'arcbar',
							width: this.cWidth * this.pixelRatio,
							height: this.cHeight * this.pixelRatio,
							pixelRatio: this.pixelRatio,
							series: [
								{
									color: this.activeColor,
									data: this.percent ? this.percent / 100 : 0
								}
							],
							animation: true,
							padding: undefined,
							title: {
								name: this.title,
								fontSize: this.titleSize
								// color: "#2fc25b"
							},
							subtitle: {
								name: this.subtitle ? String(this.subtitle) : '0',
								fontSize: this.subtitleSize
								// color: "#666666"
							},
							extra: {
								arcbar: {
									type: 'circle',
									width: this.borderWidth,
									backgroundColor: this.inactiveColor,
									startAngle: 1.5,
									endAngle: 0.25,
									gap: 2
								}
							}
						})
					} else {
						console.error('[uCharts]: 未获取到 context')
					}
				})
			// #endif
			// #ifdef H5
			const ctx = uni.createCanvasContext(id, this)
			this.uChartsInstance[id] = new uCharts({
				type: 'arcbar',
				context: ctx,
				width: this.cWidth,
				height: this.cHeight,
				series: [
					{
						color: this.activeColor,
						data: this.percent ? this.percent / 100 : 0
					}
				],
				animation: true,
				padding: undefined,
				title: {
					name: this.title,
					fontSize: 9
					// color: "#2fc25b"
				},
				subtitle: {
					name: this.subtitle ? String(this.subtitle) : '0',
					fontSize: this.subtitleSize
					// color: "#666666"
				},
				extra: {
					arcbar: {
						type: 'circle',
						width: this.borderWidth,
						// backgroundColor: this.inactiveColor,
						startAngle: 1.5,
						endAngle: 0.25,
						gap: 2
					}
				}
			})
			// #endif
		},

		//一个页面多个progress时ID需不同
		randomId() {
			return 'progressId' + parseInt(Math.random() * 1000000)
		}
	}
}
</script>

<style lang="scss" scoped>
.charts-box {
	/* width: 130rpx; */
	/* height: 130rpx; */
	position: relative;
	.bg {
		z-index: 101;
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		top: 25%;
		left: 25%;
	}
	.hot {
		background-image: url($imgBasePath + '/icons/hot_1.png');
	}
	.unhot {
		background-image: url($imgBasePath + '/icons/hot_2.png');
	}
	.hot-2 {
		background-image: url($imgBasePath + '/icons/hot_3.png');
	}
	.hot-3 {
		background-image: url($imgBasePath + '/icons/hot_4.png');
	}
}
</style>
