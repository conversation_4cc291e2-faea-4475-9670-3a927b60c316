<template>
  <view class="number-box">
    <uu-number-box
      v-model="valueNum"
      :min="min"
      :max="max"
      :asyncChange="asyncChange"
      @change="handleChange"
      @plus="handlePlus"
      @minus="handleMinus"
    >
      <view v-show="valueNum > min" slot="minus">
        <view class="minus">
          <u-icon name="minus" color="inherit" size="20rpx"></u-icon>
        </view>
      </view>
      <!-- 支付宝小程序中text不能用v-show指令，可用view替换text -->
      <view v-show="valueNum" slot="input" class="input text-center">{{ valueNum }}</view>
      <view slot="plus" class="plus" :class="disabled ? 'disabled' : '' ">
        <u-icon name="plus" color="inherit" size="20rpx"></u-icon>
      </view>
    </uu-number-box>
  </view>
</template>

<script>
import UuNumberBox from "./uu-number-box.vue"
export default {
  components: {
    UuNumberBox
  },
  props: {
    value: {
      type: Number
    },
    min: {
      type: Number,
      default: 0
    },
    max: {
      type: Number,
      default: 999
    },
    asyncChange: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      valueNum: 0
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val === this.valueNum) return
        this.valueNum = val
      },
      immediate: true
    },
    valueNum(val) {
      this.$emit('input', val)
    }
  },
  methods: {
    handleChange(e) {
      this.$emit('change', e.value)
    },
    handlePlus() {
      this.$emit('plus', this.valueNum)
    },
    handleMinus(e) {
      this.$emit('minus')
    }
  }
}
</script>

<style lang="scss">
.minus,
.plus {
  color: $color-primary;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid $color-primary;
  border-radius: 4rpx;
}

.plus {
  background-color: $color-primary;
  color: #fff;
  border-color: $color-primary;
}
.disabled{
  background-color: #d4d4d4;
  border-color: #d4d4d4;
}

.input {
  width: 50rpx;
  height: 32rpx;
}
</style>
