<template>
	<u-modal
		:show="value"
		:title="title"
		:content="content"
		showCancelButton
		@confirm="confirmModal"
		@cancel="cancelModal"
		confirmColor="#11E69E"
		confirmText="成为会员"
		cancelText="我知道了"
	>
  <view class="text-center">{{content}}</view>
  </u-modal>
</template>

<script>
import Cache from '@/utils/cache'
export default {
  data() {
    return {
      value: false
    }
  },
  components: {},
  props: {
    // value: {
    //   type: Boolean
    // },
		title:  {
      type: String,
			default: '会员提醒'
    },
		content: {
      type: String,
			default: '你当前属于非会员状态，请成为会员后查看'
    },
    showMember:{
      type: Boolean,
      default: false
    }
  },
  // computed: {
  // 	showModal: {
  // 		get() {
  // 			return !this.value
  // 		},
  // 		set(val) {
  // 			this.$emit('input', val)
  // 		}
  // 	}
  // },
  watch:{
    showMember(newVal) {
      this.value = newVal
    }
  },
  methods: {
    confirmModal() {
      this.value = false
      this.$miRouter.push('/pages_member/member_center/VIP_page')
      this.$emit("setShowMember", this.value)
    },
    cancelModal() {
      this.value = false
      this.$emit("setShowMember", this.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.u-page__item__slot-icon {
	width: 45rpx;
	height: 45rpx;
}
</style>
