<template>
  <u-tabbar :zIndex="99" :value="current" :fixed="true" :placeholder="true" :safeAreaInsetBottom="true" :inactive-color="theme.tabBarColor" :active-color="theme.tabBarSelectedColor">
    <u-tabbar-item text="首页" @click="pathTabber('/pages/index/index', 0)">
      <image class="u-page__item__slot-icon" slot="inactive-icon" :src="themeImgPath.img_tab_home"></image>
      <image class="u-page__item__slot-icon" slot="active-icon" :src="themeImgPath.img_tab_home_s"></image>
    </u-tabbar-item>
    <!-- staging和dev可以进入h5版的健康中心，正式的要跳转小程序哦，领导要求的 -->
    <u-tabbar-item v-if="toMiniHealthy" text="健康中心"  @click="gotoMiniapp">
      <view class="wxOpenWeapp" slot="inactive-icon">
        <view class="wxOpenWeapp-box" v-if="platform === 'wechat' && isLoginStatus">
          <wx-open-launch-weapp
            id="launch-btn"
            appid="wx1ff45c78e3f8888f"
            :path="`pages/index/index?bg_sessionid=${authData.bg_sessionid}`"
            :env-version="envVersion"
          >
            <script type="text/wxtag-template">
              <style>
              .btn {
              	width: 50px;
              	height: 50px;
              }
              </style>
              <div class="btn"></div>
            </script>
          </wx-open-launch-weapp>
        </view>
        <image class="u-page__item__slot-icon" :src="themeImgPath.img_tab_healthy"></image>
      </view>
    </u-tabbar-item>
    <u-tabbar-item v-if="!toMiniHealthy" text="健康中心" @click="pathTabber('/pages/health_center/health_center', 1)">
      <image class="u-page__item__slot-icon" slot="inactive-icon" :src="themeImgPath.img_tab_healthy"></image>
      <image class="u-page__item__slot-icon" slot="active-icon" :src="themeImgPath.img_tab_healthy_s"></image>
    </u-tabbar-item>
    <u-tabbar-item text="个人中心" @click="pathTabber('/pages/user/user', 2)">
      <image class="u-page__item__slot-icon" slot="inactive-icon" :src="themeImgPath.img_tab_user"></image>
      <image class="u-page__item__slot-icon" slot="active-icon" :src="themeImgPath.img_tab_user_s"></image>
    </u-tabbar-item>
    <!-- <u-tabbar-item v-for="(item, index) in tabbarList" :key="index" :text="item.text" @click="pathTabber(item, index)">
        <image class="u-page__item__slot-icon" slot="inactive-icon" :src="item.iconPath"></image>
        <image class="u-page__item__slot-icon" slot="active-icon" :src="item.selectedIconPath"></image>
      </u-tabbar-item> -->
  </u-tabbar>
</template>

<script>
import { mapGetters } from 'vuex'
import { getApiIsMiniappAuth, getApiWechatCongfigGet } from '@/api/app.js'
import Cache from '@/utils/cache'
// #ifdef H5
var jweixin = require('jweixin-module')
// #endif
import { checkClient } from '@/utils/util.js'


export default {
  name: 'custom-tabar',
  props: {
    type: {
      type: String,
      default: 'user'
    }
  },
  data() {
    return {
      platform: checkClient(), // 平台， 微信or支付宝
      path: '',
      appid: '',
      tabbarList: [],
      // 没用
      list: [
        {
          type: 'index',
          pagePath: '/pages/index/index',
          iconPath: this.$imgPath.img_tab_home,
          selectedIconPath: this.$imgPath.img_tab_home_s,
          text: '首页'
        },
        // {
        //	type: 'discovery',
        // 	pagePath: '/pages/discovery/index',
        // 	iconPath: require('@/static/icons/discovery1.png'),
        // 	selectedIconPath: require('@/static/icons/discovery.png'),
        // 	text: '发现'
        // },
        // {
        // 	type: 'photo_recognition',
        // 	pagePath: '/pages/photo_recognition/photo_recognition',
        // 	iconPath: require('@/static/icons/tab_photo.png'),
        // 	selectedIconPath: require('@/static/icons/tab_photo_s.png'),
        // 	text: '饮食记录'
        // },
        // {
        // 	type: 'nutrition_circle',
        // 	pagePath: '/pages/nutrition_circle/nutrition_circle',
        // 	iconPath: require('@/static/icons/nutrition_circle_table.png'),
        // 	selectedIconPath: require('@/static/icons/nutrition_circle_table_s.png'),
        // 	text: '营养圈'
        // },
        {
          type: 'health_center',
          pagePath: '/pages/health_center/health_center',
          iconPath: this.$imgPath.img_tab_healthy,
          selectedIconPath: this.$imgPath.img_tab_healthy_s,
          text: '健康中心'
        },
        {
          type: 'user',
          pagePath: '/pages/user/user',
          iconPath: this.$imgPath.img_tab_user,
          selectedIconPath: this.$imgPath.img_tab_user_s,
          text: '个人中心'
        }
      ],
      authData: {},
      envVersion: '',
      toMiniHealthy: true
    }
  },
  computed: {
    ...mapGetters(['isAddressVisitor', 'isLoginStatus']),
    current() {
      let index = this.tabbarList.findIndex(item => item.type === this.type)
      return index
    }
  },
  watch: {
    isLoginStatus: {
			handler(newData, old) {
        if (newData) {
          var ua = window.navigator.userAgent.toLowerCase()
          if (ua.match(/MicroMessenger/i) == 'micromessenger') { 
            this.getWechatCongfigGet()
          }
          this.getIsMiniappAuth()
        }
			},
			// 开启深度监听：只要obj中的任何一个属性发生改变，都会触发相应的代码
			deep: true,
			immediate: true
		}
	},

  created() {
    if (process.env.NODE_ENV === 'development' || (location && location.href.indexOf('debug') > -1)) {
      this.envVersion = 'trial'
    } else {
      this.envVersion = 'release'
    }
    uni.hideTabBar()
    // this.userinfo = Cache.get('userInfo');
    if (this.isAddressVisitor) {
      this.tabbarList = this.list.filter(item => item.type === 'user')
    } else {
      this.tabbarList = this.list
    }
    // #ifdef H5
    // staging和dev可以进入h5版的健康中心，正式的要跳转小程序哦，领导要求的，要用来做演示哦 农行环境也不能跳小程序
    // 先用location.href做判断吧，不用location.origin，方便快捷调试
    if (process.env.NODE_ENV === 'development' || (location.href.indexOf('debug') > -1) || (location.href.indexOf('staging') > -1) || this.platform === 'abc') {
      this.toMiniHealthy = false
    }
    // #endif
  },
  mounted() {
    console.log('themeImgPath', this.themeImgPath)
  },
  methods: {
    getWechatCongfigGet() {
      let userInfo = Cache.get('userInfo') || {}
      let parmas = {
        appid: userInfo.appid,
        company_id: userInfo.company_id,
        url: window.location.href.split('#')[0]
      }
      getApiWechatCongfigGet(parmas)
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            if (res.data && res.data.appid) {
              jweixin.config({
                debug: false,
                appId: res.data.appid,
                timestamp: res.data.timestamp,
                nonceStr: res.data.noncestr,
                signature: res.data.signature,
                jsApiList: ['wx-open-launch-weapp', 'checkJsApi', 'scanQRCode', 'chooseWXPay'], // 把支付也初始化
                // jsApiList: ['wx-open-launch-weapp'], // 必填，需要使用的JS接口列表，这个地方必须至少写一个
                openTagList: ['wx-open-launch-weapp'] // 可选，需要使用的开放标签列表
              })
            }
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.message)
        })
    },
    getIsMiniappAuth() {
      let userInfo = Cache.get('userInfo') || {}
      if (!userInfo.user_id) {
        return 
        // this.$miRouter.replace({
        //   path: '/pages/login/login'
        // })
      }

      getApiIsMiniappAuth({
        user_id: userInfo.user_id,
        appid: 'wx1ff45c78e3f8888f' // 写死
        // secret: '45fd1815c38af427bda4389902d7db98' // 写死
      })
        .then(async res => {
          if (res.code === 0) {
            this.authData = res.data
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.$u.toast(err)
        })
    },
    gotoMiniapp() {
      // if (this.platform === 'wechat') {
      let hrefData = ''
      // if (this.authData.is_auth) {
      hrefData = `weixin://dl/business/?appid=wx1ff45c78e3f8888f&path=pages/index/index&query=bg_sessionid=${
        this.authData.bg_sessionid
      }&time=${new Date().getTime()}`
      // } else {
      //   hrefData = `weixin://dl/business/?appid=wx1ff45c78e3f8888f&path=pages/index/index`
      // }
      if (process.env.NODE_ENV === 'development' || (location && location.href.indexOf('debug') > -1)) {
        hrefData += '&env_version=trial'
      }
      window.location.href = hrefData
      // }
    },
    pathTabber(pagePath, index) {
      if (this.current == index) return
      this.$miRouter.pushTab({
        path: pagePath
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.u-page__item__slot-icon {
  width: 45rpx;
  height: 45rpx;
}
.wxOpenWeapp {
  position: relative;
  z-index: 222;
  .wxOpenWeapp-box {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -36%);
    z-index: 999999;
  }
}
</style>
