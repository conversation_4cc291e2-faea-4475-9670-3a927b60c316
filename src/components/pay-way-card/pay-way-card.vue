<template>
	<view class="pay_way">
		<!-- Main Start -->
		<view class="main ls-card" @click="show = true">
			<view class="flex row-between md">
				<view class="muted">支付方式：</view>
				<view class="flex row-right">
					<view class="name line-1">{{ selectInfo.payway_alias + ' ' + selectInfo.name }}</view>
					<u-icon color="#999" name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
		</view>
		<!-- Main End -->
		<!-- Component Start -->

		<u-popup :show="show" @close="showClose" :round="10" :closeable="true">
			<view class="container">
				<view class="title f-w-500 text-center"><text>支付方式</text></view>
				<scroll-view :scroll-y="true" style="height: 700rpx">
					<radio-group @change="radioChange" class="p-t-30 reason" v-model="num">
						<view class="" v-for="(item, index) in walletData" :key="index">
							<!-- 一级钱包 -->
							<label class="reason-item flex row-between" @click="showPop = false">
								<view class="black lg f-w-500 flex">{{ funPayName(item) }} <view  v-if="item.consume_tag &&  (item.wallet_list && item.wallet_list.length <= 0)" class="sm red-tag m-l-10">更多优惠</view></view>
								<radio
									style="transform: scale(0.7)"
									:color="variables.colorPrimary"
									:value="String(item.index)"
									:checked="num == String(item.index)"
								></radio>
							</label>
							<!-- 二级钱包 -->
							<view class="" v-if="String(item.index) == num && item.payway != 'WechatPay' && item.payway != 'AliPay' && item.payway != 'QyWechatPay' && item.pay_way != 'WXYFPay' && item.wallet_list.length > 0">
								<!-- 钱包类型 -->
								<view class="m-t-50 m-b-28 xs muted">请选择钱包类型</view>
								<!-- 1. cannot 样式为余额不足||无法选择   2. active 样式为当前选择 -->
								 <view class="flex flex-wrap">
										<block v-for="(itemT, indexT) in item.wallet_list" :key="indexT">
											<view
												class="item m-r-20"
												:class="{ active: currentIndex == itemT.balance_type }"
												@click="selectWalletType(itemT, item)"
											>
											<!-- 组合支付 不显示金额 -->
												<price-format  v-if="itemT.balance_type !== 'online_combine_wallet'" :price="itemT.blance" :size="36" :weight="500"></price-format>
												<view class="name nr">{{ itemT.name }}</view>
												<!-- 只有组合支付 需要展示的内容 -->
												<view class="muted" v-if="itemT.balance_type === 'online_combine_wallet'">单钱包余额不足时，才能选择。</view>
												<view v-if='itemT.consume_tag'  class="sm red-tag m-l-10 m-t-10">更多优惠</view>
											</view>
										</block>
									</view>
							</view>
						</view>
					</radio-group>
				</scroll-view>
			</view>
		</u-popup>

		<!-- Components End -->
	</view>
</template>

<script>
import { apiGetPayMethodList, getApiUserWalletList } from '@/api/user.js'
import Cache from '@/utils/cache'
import { formateVisitorParams } from '@/utils/util.js'
import { divide } from '../../utils/util'

export default {
	name: 'address-card',

	// Props Start
	props: {
		showPay: {
			type: Boolean,
			default: false
		},
		money: {
			type: Number,
			default: 0
		},
		orgId: {
			type: Number,
			default: 0
		},
		personNo: {
			type: String,
			default: ''
		},
		orderInfo: Object,
		paymentOrderType:{ //报餐类型
			type: String,
			default: ''
		}
	},
	// Props End

	// Data Start
	data() {
		return {
			show: false,
			num: '', // 选中的一级标签

			walletData: {},

			currentIndex: '',
			walletType: [
				{
					money: '560.00',
					name: '账户钱包',
					type: 1
				},
				{
					money: '322.00',
					name: '农行电子账户钱包',
					type: 1
				},
				{
					money: '50.00',
					name: '赠送钱包',
					type: 0
				},
				{
					money: '366.00',
					name: '赠送钱包',
					type: 1
				}
			],

			formData: {
				// company_id: '',
				// user_id: '',
				// person_no: '',

				// receipt_name: '233', // 收货姓名
				// receipt_phone: '15216949486', // 收货电话
				// receipt_adders: '收货地址', // 收货地址
				// take_meal_type: 'on_scene', //  消费订单类型
				// // on_scene-堂食
				// // waimai-外卖
				// // bale-堂食自提
				// // cupboard-取餐柜

				// payment_order_type: 'reservation', // 取餐类型 reservation-预约订单, report_meal-报餐
				org_id: '101' // 食堂ID
			},

			selectInfo: {
				payway: '', // 一级支付方式
				payway_alias: '', // 一级支付方式描述

				// 二级
				name: '', // 钱包名
				balance_type: '', // 钱包类型
				wallet_type: '' // 钱包类型
				
			}, // 选中的支付方式
			userInfo: {}
		}
	},
	// Data End

	// Methods Start
	methods: {
		funPayName(data) {
			let name = ''
			if (data.payway == 'WechatPay' || data.payway == 'AliPay' ) {
				name = data.payway_alias
			} else {
				name = data.name + '-' + data.payway_alias
			}
			return name
		},
		// 一级选择钱包
		radioChange(e) {
			this.num = e.detail.value
			console.log('一级选择钱包', e)
			this.walletData.forEach(item => {
				if (String(item.index) == e.detail.value) {
					this.selectInfo.payway_alias = item.payway_alias
					this.selectInfo.payinfo_id = item.payinfo_id
					this.selectInfo.payway = item.payway
					
					// 如果钱包列表是空就清空选择的钱包信息
					if (Reflect.has(item, "wallet_list") && (!item.wallet_list || item.wallet_list.length === 0)) {
						// 清空已选择的钱包信息,并且设置balance_type为空
						if (Reflect.has(this.selectInfo, 'wallet_id')) {
							delete this.selectInfo.wallet_id
							this.selectInfo.balance_type = ''
							this.currentIndex = ""
						}
					}
					console.log("item", item, this.selectInfo);
				} 
			})
			// 如果是微信支付 支付宝，直接关闭弹窗
			if (this.selectInfo.payway === 'WechatPay' || this.selectInfo.payway === 'AliPay' || this.selectInfo.payway === 'QyWechatPay' || this.selectInfo.payway === 'WXYFPay') {
				this.show = false
				this.selectInfo.name = ''
				this.$emit('showPayClose', false)
			}
			this.$emit('select', this.selectInfo)
		},
		// 二级-选择钱包
		selectWalletType(item, parent) {
			console.log("item",item);
			// if (item.blance == 0) return
			// let canSelect = true
			// // 如果储值钱包余额充足，且赠送钱包余额也充足时，用户点击赠送钱包，不会选中赠送钱包
			// if (parent.payway === 'PushiPay' && item.balance_type === 'complimentary') {
			// 	for (let index = 0; index < parent.wallet_list.length; index++) {
			// 		const current = parent.wallet_list[index];
			// 		if (current.balance_type === "store") {
			// 			// 当钱包余额不足时提示下
			// 			if (item.blance < this.money) {
			// 				uni.$u.toast('当前钱包余额不足，请选择其它钱包')
			// 				canSelect = false
			// 				break;
			// 			}
			// 			// 当储值钱包余额充足并且赠送钱包余额也充足时，优先使用储值钱包
			// 			if ((current.blance >= this.money) && (item.blance >= this.money)) {
			// 				uni.$u.toast('当前储值钱包余额充足，请先使用储值钱包进行支付')
			// 				canSelect = false
			// 				break;
			// 			}
			// 		}
			// 	}
			// }
			// if (!canSelect) return
			this.currentIndex = item.balance_type
			this.selectInfo.name = item.name
			this.selectInfo.wallet_type = item.wallet_type
			this.selectInfo.balance_type = item.balance_type
			this.selectInfo.wallet_id = item.wallet_id
			this.show = false
			this.$emit('showPayClose', false)
			this.$emit('select', this.selectInfo)
			console.log("item end",this.selectInfo);
		
		},

		// 获取支付方式列表
		getPayMethodList() {
			let api;
			let params = {}
			// 如果是美团就拿全局钱包接口
			if (this.paymentOrderType === 'meituan') {
				params = { person_no: this.userInfo.person_no }
				api = getApiUserWalletList
			}else {
			 	params = formateVisitorParams(this.formData, true)
				api = apiGetPayMethodList
			}
			api(params)
				.then(res => {
					if (res.code == 0) {
						var data = res.data || []
						if (this.paymentOrderType === 'meituan') { 
							this.walletData = data.wallet_list
						}else {
							this.walletData = data.map(v=> {
									// 需要后台开启了 线上组合支付 就push一个组合支付进去
								if (v.wallet_list.length && v.payway == 'PushiPay' && this.paymentOrderType === 'reservation' && v.online_combine_wallet_on) {
									let totalBlance = 0
									v.wallet_list.forEach(item=>{
										totalBlance += item.blance
									})
									v.wallet_list.push(
										{
											"payinfo_id": v.payinfo_id,
											"wallet_id": v.wallet_id,
											"name": "组合支付",
											"wallet_type": "wallet",
											"balance_type": "online_combine_wallet",
											// "org_name": "晓组织",
											// "org_id": v.org_id,
											"blance": 0,
											totalBlance: totalBlance,
											"consume_tag": false
										}
									)
								}
								return v
							})
						}
						console.log(this.walletData, 88888)

						// 美团默认只拿储值支付 -- 美团企业版
						if (this.paymentOrderType === 'meituan'){
							this.walletData = this.walletData.filter(item => item.payway === 'PushiPay');
							if (this.walletData.length <= 1) {
								// 全局钱包 没有 index 默认加一个
								this.walletData[0].index = 1
							}
						}
						// 如果只有一条就默认拿第一个
						if (this.walletData.length <= 1) {
							this.num = this.walletData[0].index
							this.selectInfo.payway_alias = this.walletData[0].payway_alias
							this.selectInfo.payinfo_id = this.walletData[0].payinfo_id
							this.selectInfo.payway = this.walletData[0].payway
							// 如果是微信支付，直接关闭弹窗
							if (this.selectInfo.payway === 'WechatPay' || this.selectInfo.payway === 'AliPay' || this.selectInfo.payway === 'QyWechatPay' || this.selectInfo.payway === 'WXYFPay') {
								this.show = false;
								this.$emit('showPayClose', false);
							}
							//  钱包只有一个的时候情况默认选择当前第一个钱包
							if (this.walletData[0] && Reflect.has(this.walletData[0], "wallet_list") && Array.isArray(this.walletData[0].wallet_list) && this.walletData[0].wallet_list.length === 1) {
								let walletListData = this.walletData[0].wallet_list[0]
								if (walletListData.blance == 0) {
									this.$emit('select', this.selectInfo)
									return
								}
								this.currentIndex = walletListData.balance_type
								this.selectInfo.name = walletListData.name
								this.selectInfo.wallet_type = walletListData.wallet_type
								this.selectInfo.balance_type = walletListData.balance_type
								this.selectInfo.wallet_id = walletListData.wallet_id
							}
							this.$emit('select', this.selectInfo)
						}
					} else {
						console.log(res)
					}
				})
				.catch(err => {
					console.log('err', err)
				})
		},
		showClose() {
			this.show = false
			this.$emit('showPayClose', false)
		},
		// 比较钱包的钱与线上支付的钱的大小
		compareMoney(itemData) {
			console.log("compareMoney", itemData, this.num, this.currentIndex, this.walletData);
			// var currentMoney = money
			if (this.selectInfo.payway === "PushiPay") {
				var walletData = this.walletData.find(item => { return item.index == this.num })
				console.log("walletData", walletData);
				if (walletData) {
					var walletList = walletData.wallet_list || []
					console.log("walletList", walletList);
					if (Array.isArray(walletList) && walletList.length > 0 && this.currentIndex) {
						const current = walletList.find(wallItem => {
							return wallItem.balance_type == this.currentIndex
						});
						// 储值跟补贴钱包的时候
						if (current && (current.balance_type === "store" || current.balance_type === 'subsidy')) {
							// 当钱包余额不足时提示下
							if (current.blance < itemData.data) {
								uni.$u.toast('当前钱包余额不足，请选择其它钱包')
								this.resetWallet()
							}
						}
						// 如果是赠送钱包的时候
						if (current && current.balance_type === "complimentary") {
							var storeWallet = walletList.find(wallItem => {
								return wallItem.balance_type == 'store'
							});
							console.log(walletList, 77777777)
							var storeMoney = storeWallet ? storeWallet.blance : 0
							console.log("storeMoney", storeMoney);
							if (storeMoney >= itemData.chuzhi_pay.data) {
								uni.$u.toast('当前储值钱包余额充足，请先使用储值钱包进行支付')
								this.resetWallet()
							}
							if (itemData.data > current.blance) {
								uni.$u.toast('当前钱包余额不足，请选择其它钱包')
								this.resetWallet()
							}
						}
						console.log("current111", current, current.balance_type);
						// 组合支付
						if (current.balance_type === "online_combine_wallet") {
							walletList.forEach(v=> {
								if(itemData[v.balance_type]) {
										if (v.blance >= itemData[v.balance_type].pay_fee) {
											uni.$u.toast('其他钱包余额充足，请先使用其他钱包进行支付')
											this.resetWallet()
										}
								}
							})
							// totalBlance 所有的钱包的合计，如果合计大于等于 当前支付金额 就先用其他钱包，不足就使用组合支付
							// if (current.totalBlance >= money.combine_pay_fee) {
							// 	uni.$u.toast('其他钱包余额充足，请先使用其他钱包进行支付')
							// 	this.resetWallet()
							// }
							console.log(itemData.combine_pay_fee , current.totalBlance, '你好你好你好')
							if (itemData.combine_pay_fee > current.totalBlance) {
								uni.$u.toast('余额不足,请先充值')
								this.resetWallet()
							}
						}
					}
				}
			}
		},
		// 重置一下选择的钱包
		resetWallet() {
			this.selectInfo.wallet_id = ''
			this.selectInfo.balance_type = ''
			this.currentIndex = ''
		}
	},
	watch: {
		selectInfo: {
			handler(newVal) {
				// this.$emit('select', newVal)
			},
			deep: true
		},
		showPay(val) {
			this.show = val;
		}
	},
	// Methods End

	// Life Cycle Start
	created() {
		this.userInfo = Cache.get('userInfo')
		this.formData.user_id = Cache.get('userInfo').user_id || ''
		this.formData.person_no = this.personNo ? this.personNo : this.$store.state.appoint.select.person.person_no
		this.formData.company_id = Cache.get('userInfo').company_id || this.$store.state.appoint.select.address_info.company_id
		this.formData.org_id = this.orgId ? this.orgId : this.$store.state.appoint.select.org.org_id
		this.formData.stall_ids = this.orderInfo.stall_ids
		this.getPayMethodList()
		console.log('this.$store.state.appoint.select', this.$store.state.appoint.select)

		// this.formData.payment_order_type = this.$store.state.appoint.payment_order_type
		// this.formData.take_meal_type = this.$store.state.appoint.select.take_meal_type
		// this.formData.receipt_name = this.$store.state.appoint.select.person.name
		// this.formData.person_no = this.$store.state.appoint.select.person.person_no
	}
	// Life Cycle End
}
</script>

<style lang="scss" scoped>
.pay_way {
	.ls-card {
		border-radius: 20rpx;
		background-color: #ffffff;
	}

	.main {
		padding: 32rpx 30rpx;
		box-sizing: border-box;

		.name {
			width: 400rpx;
		}
	}

	.container {
		padding: 0 40rpx;

		.title {
			padding: 30rpx 0;
		}

		.reason {
			.reason-item:first-child {
				border-bottom: $border-base;
				margin-bottom: 20rpx;
				padding-bottom: 20rpx;
			}
		}

		// 当前选择的钱包类型
		.active {
			color: $color-primary !important;
			border: 1px solid $color-primary !important;
		}

		.item:nth-child(2n) {
			margin-left: 0;
		}

		.item {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 300rpx;
			padding: 46rpx 40rpx;
			border-radius: 8rpx;
			margin-left: 30rpx;
			margin-bottom: 30rpx;
			border: $border-base;
			text-align: center;
		}

		// 无法选择，余额不足
		.cannot {
			color: $color-text-secondary !important;
			position: relative;
		}

		.cannot::after {
			content: '无法选择';
			position: absolute;
			top: 0;
			right: 0;
			padding: 6rpx 14rpx;
			font-size: $font-size-xxs;
			background-color: $background-color;
			border-radius: 0rpx 8rpx;
		}
	}
	.red-tag {
		width: 130rpx;
		height: 40rpx;
		text-align: center;
		line-height: 40rpx;
		background-color: #EC808D;
		color: #ffffff;
		border-radius: 5rpx;
		align-self: center;
	}
}
</style>
