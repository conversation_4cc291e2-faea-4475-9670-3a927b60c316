<template>
  <view
    class="trigonometry"
    :class="{
      rotate: direction == 'up'
    }"
  >
    <text
      class="arrow-down-fill inline"
      :style="{
        'border-left': size + ' solid transparent',
        'border-right': size + ' solid transparent',
        'border-top': size + ' solid #c0c1c3'
      }"
    ></text>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },

  components: {},
  props: {
    color: {
      type: String,
      default: ''
    },
    direction: {
      type: String,
      default: 'down'
    },
    size: {
      type: String,
      default: '10rpx'
    }
  },
  methods: {}
}
</script>
<style lang="scss">
.trigonometry {
  margin-left: 6rpx !important;
  transition: transform 0.3s;
  align-items: center;
  display: flex;

  &.rotate {
    transform: rotate(180deg);
  }

  .arrow-down-fill {
    width: 0;
    height: 0;
  }
}
</style>
