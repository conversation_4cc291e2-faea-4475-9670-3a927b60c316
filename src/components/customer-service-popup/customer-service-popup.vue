<template>
  <view :class="['custome-service-popup', isShow ? 'showHalf' : '']" @click="goToPage">
    <image class="img-logo" :src="themeImgPath.img_kefu" style="width: 98rpx; height: 118rpx;"></image>
  </view>
</template>

<script>
export default {
  props: {
    popupShow: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    popupShow: {
      handler: function(newVal, oldVal) {
        this.isShow = newVal
      },
      immediate: true
    }
  },
  data() {
    return {
      isShow: false
    }
  },
  methods: {
    goToPage() {
      this.$miRouter.push({
        path: '/pages_info/user_config/customer_service/customer_service'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.custome-service-popup {
  z-index: 1001;
  width: 98rpx;
  height: 114rpx;
  position: fixed;
  right: 0px;
  top: 55%;
  transition: transform 1 ease-in-out;
}
// .showAll {
//   transition: transform 0.5 ease;
// }
.showHalf {
  transform: translateX(60rpx);
}
</style>
