<template>
	<view class="scroll-choose-all">
		<view class="middleLine"></view>
		<view class="zz"></view>
		<scroll-view
			class="scroll-choose"
			scroll-x="true"
			upper-threshold="5"
			lower-threshold="5"
			:scroll-left="scrollLeftInit"
			show-scrollbar="false"
			@scroll="scroll"
			@scrolltoupper="upper"
			@scrolltolower="lower"
			scroll-with-animation="true"
		>
			<view class="scroll-con" :style="{ width: scrollWid }">
				<view class="topLine">
					<view
						class="allLine"
						:style="{ marginRight: maginL + 'px' }"
						:class="item.type"
						v-for="(item, index) in scrollList"
						:key="index"
					></view>
				</view>
				<view class="bottomNum" :style="{ paddingLeft: allNumLeft }">
					<text
						class="allNum"
						:class="{ active: scrollNum === item }"
						:style="{ width: (maginL + 3) * 10 + 'px', left: -((maginL + 3) * 5) + 'px' }"
						v-for="(item, index) in scrollNumList"
						:key="index"
					>
						{{ item }}
					</text>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import NP from '@/utils/np.js'
export default {
	name: 'scroll-choose',
	props: {
		//起始值和终止值差距不要过大，否则会影响页面性能，scrollUnit为0.1支持一位小数，目前仅支持一位小数
		/**
		 * 初始值（注意：初始值应在起始值和终止值之间）
		 */
		scrollLeft: {
			type: Number,
			default: 0
		},
		/**
		 * 滚动区域起始值（注意：起始值不能大于终止值）
		 */
		scrollStart: {
			type: Number,
			default: 0
		},
		/**
		 * 滚动区域终止值
		 */
		scrollEnd: {
			type: Number,
			default: 100
		},
		/**
		 * 线间距
		 */
		maginL: {
			type: Number,
			default: 5
		},
		scrollUnit: {
			// 最小刻度单位（最小格）仅支持传入  1 或 0.1 ，传入1支持整数，传入0.1支持小数点
			type: Number,
			default: 1
		}
	},
	data() {
		return {
			scrollList: [],
			scrollNumList: [],
			scrollWid: '100vw',
			scrollLeftInit: 0,
			allNumLeft: '',
			scrollNum: 0
		}
	},
	mounted() {
		this.init()
	},
	computed: {},
	watch: {
		// scrollLeft: function(val) {
		// 	// val && this.setNowLeft()
		// }
	},
	methods: {
		init() {
			for (let i = this.scrollStart; i < this.scrollEnd + 1; i++) {
				let j = i
				do {
					let _line = {
						value: j
					}
					if (j % (5 * this.scrollUnit) == 0) {
						if (j % (10 * this.scrollUnit) == 0) {
							this.scrollNumList.push(i)
							_line.type = 'LLine'
						} else {
							_line.type = 'MLine'
						}
					} else {
						_line.type = 'SLine'
					}
					this.scrollList.push(_line)
					j = NP.plus(j, this.scrollUnit)
				} while (j < i + 1 && j < this.scrollEnd)
			}
			this.scrollWid = uni.upx2px(750) + ((this.scrollEnd - this.scrollStart) * (this.maginL + 3)) / this.scrollUnit + 'px'
			if (this.scrollStart % (10 * this.scrollUnit) != 0) {
				if (this.scrollStart > 0) {
					this.allNumLeft = (10 - (this.scrollStart % (10 * this.scrollUnit))) * (this.maginL + 3) + uni.upx2px(372) + 'px'
				} else {
					this.allNumLeft = Math.abs(this.scrollStart % (10 * this.scrollUnit)) * (this.maginL + 3) + uni.upx2px(372) + 'px'
				}
			}
			setTimeout(() => {
				this.setNowLeft()
			}, 100)
		},
		setNowLeft() {
			this.scrollLeftInit = ((this.scrollLeft - this.scrollStart) * (this.maginL + 3)) / this.scrollUnit
		},
		upper: function(e) {
			setTimeout(() => {
				this.$emit('scroll', this.scrollStart)
			}, 50)
		},
		lower: function(e) {
			setTimeout(() => {
				this.$emit('scroll', this.scrollEnd)
			}, 50)
		},
		scroll: function(e) {
			// this.scrollNum = Math.round(e.detail.scrollLeft/(this.maginL + 2)) + this.scrollStart
			this.scrollNum = NP.times(Math.round(e.detail.scrollLeft / (this.maginL + 3)) , this.scrollUnit) + this.scrollStart
			if (this.scrollNum >= 0 && this.scrollNum <= this.scrollEnd) {
				this.$emit('scroll', this.scrollNum)
			}
		}
	}
}
</script>

<style lang="scss">
@charset "UTF-8";
.scroll-choose-all {
	width: 750rpx;
	height: 70px;
	background: #f8f8f8;
	margin: 10px 0;
	// border-bottom: 1px solid #ccc;
	// border-top: 1px solid #ccc;
	position: relative;
}
.middleLine {
	position: absolute;
	width: 4px;
	height: 30px;
	background: $color-primary-light-3;
	left: 374rpx;
	margin-left: -1px;
	z-index: 1;
}
.zz {
	position: absolute;
	top: -20rpx;
	left: 0;
	right: 0;
	width: 0px;
	margin: auto;
	border-top: 20rpx solid $color-primary-light-3;
	border-right: 20rpx solid transparent;
	border-bottom: 0rpx;
	border-left: 20rpx solid transparent;
	z-index: 999;
}
.scroll-choose {
	width: 750rpx;
	height: 70px;
	background-color: #fff;
	.scroll-con {
		height: 70px;
		overflow: hidden;
		.topLine {
			height: 30px;
			padding: 0 372rpx;
		}
		.bottomNum {
			height: 30px;
			padding: 0 0 0 372rpx;
			width: 100%;
			// display: flex;
			// flex-wrap: nowrap;
			.allNum {
				color: #9db8aa;
				display: inline-block;
				position: relative;
				// width: 70px;
				// left: -35px;
				text-align: center;
			}
			.active {
				color: $color-primary;
			}
		}
		.allLine {
			display: inline-block;
			// margin-right: 5px;
			width: 3px;
			background: #9db8aa;
			position: relative;
		}
		.allLine:last-child {
			margin-right: 0px !important;
		}
		.LLine {
			// width: 6rpx;
			height: 30px;
		}
		.MLine {
			height: 20px;
			top: -10px;
		}
		.SLine {
			height: 15px;
			top: -15px;
		}
	}
}
</style>
