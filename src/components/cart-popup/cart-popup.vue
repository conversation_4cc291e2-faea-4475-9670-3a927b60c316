<template>
	<view class="cart-popup">
		<u-popup :show="showPop" mode="bottom" :round="20" @close="showPop = false">
			<view class="popup-content">
				<view class="cart-header flex row-between">
					<text class="muted">购物车</text>
					<view class="muted flex flex-center" @click="clearCard">
						<u-icon name="trash" color="#999999" size="32"></u-icon>
						清空
					</view>
				</view>
				<scroll-view :scroll-y="true" style="height: 800rpx">
					<view class="cart-lists">
						<view class="cart-item m-b-40" v-for="(item, index) in lists" :key="index">
							<view class="cart-time xxl p-t-20">{{ item.date }}</view>
							<view v-for="(sItem, sIndex) in item.stall" :key="sIndex">
								<view class="cart-stall">
									<view class="cart-stall-header flex row-between lg">
										<view class="f-w-500">{{ sItem.stall_name }}</view>
										<view class="primary">{{ sItem.meal_type_alias }}</view>
									</view>
									<view class="cart-goods flex m-t-30" v-for="(fItem, fIndex) in sItem.food" :key="fIndex">
										<view class="goods-name  flex-6 f-w-500" v-if="fItem.obj_name == 'food'">
											<view class="">{{ fItem.food_name }}</view>
											<view class="text-ash xs  m-l-30 flex flex-wrap p-t-10">
												{{ fItem.spec_name }}{{ fItem.spec_name && fItem.taste_name ? '、' : '' }}{{ fItem.taste_name }}
											</view>
										</view>

										<view class="goods-name  flex-6 f-w-500" v-if="fItem.obj_name == 'set_meal'">
											<view class="">{{ fItem.set_meal_name }}</view>
											<view class="text-ash xs  m-l-20 flex flex-wrap p-t-10">
													<view class="" v-for="(setMealItem, setMealIndex) in fItem.set_meal_style_data" :key="setMealIndex">
														{{
															(setMealIndex == fItem.set_meal_style_data.length - 1 && setMealItem.food_name) ||
																setMealItem.food_name + '+'
														}}
												</view>
											</view>
										</view>
										<view class="flex-2 red">
											<price-format :price="fItem.obj_name == 'set_meal' ? fItem.price : fItem.spec_price"></price-format>
										</view>
										<number-box
											v-model="fItem.count"
											:async-change="true"
											@change="
												handleCountChange($event, fItem, {
													date: item.date,
													meal_type: sItem.meal_type
												}, sItem)
											"
										></number-box>
									</view>
								</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
export default {
	props: {
		value: Boolean,
		lists: {
			type: Array,
			default: () => []
		},
		time: String
	},
	data() {
		return {}
	},
	computed: {
		showPop: {
			get() {
				return this.value
			},
			set(val) {
				this.$emit('input', val)
			}
		}
	},
	methods: {
		clearCard() {
			this.$emit('clear')
		},
		handleCountChange(value, item, obj, stall) {
			this.$emit('countchange', {
				value,
				food: { ...item, ...obj },
				stall_id: stall.stall_id
			})
		}
	}
}
</script>

<style lang="scss">
.cart-popup {
	.popup-content {
		padding-bottom: 120rpx;

		.cart-header {
			padding: 30rpx;
		}

		.cart-lists {
			.cart-item {
				.cart-time {
					display: flex;
					align-items: center;
					padding: 0 40rpx;
					&::before {
						content: '';
						display: block;
						width: 6rpx;
						height: 28rpx;
						background: $color-primary;
						margin-right: 15rpx;
					}
				}

				.cart-stall {
					padding: 25rpx 40rpx;
					border-bottom: 10rpx solid #f0f3f5;
					.cart-stall-header {
						padding-bottom: 25rpx;
						border-bottom: $border-base;
					}
				}
			}
		}
		.text-ash {
			color: #9da2a7;
		}
	}
}
</style>
