<template>
	<view class="nutritionRatio">
		<view class="ls-card m-t-20">
			<view class="flex row-between md f-w-500 m-b-20">
				<text>饮食健康</text>
				<view class="xs muted flex col-center" @click="$miRouter.push('/pages_health/healthy/diet_healthy/index')">
					更多
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
			<view class="flex">
				<view class="arcbar-box">
					<canvas
						canvas-id="********************************"
						id="********************************"
						class="charts"
						@touchstart="touchstart"
						@touchmove="touchmove"
						@touchend="touchend"
					/>
					<view class="arcbar-title text-center">
						<view class="muted mini">今日摄入</view>
						<view>
							<text class="text">{{ dietIntake.energy_kcal }}</text>
							<text class="muted mini p-l-5">kcal</text>
						</view>
						<!-- <view>
							<text class="text">{{ dietIntake.energy_kcal }}</text>
							<text class="muted mini p-l-5">kcal</text>
						</view> -->
						<view class="muted mini">
							<view class="">推荐摄入</view>
							<text>{{ dietIntake.need_energy_kcal }}</text>
							<text class="muted mini p-l-5">kcal</text>
						</view>
					</view>
				</view>
				<view class="nutrition-meal m-t-25">
					<view class="flex col-center row-between p-b-20" v-for="(item, index) in mealList" :key="index">
						<view class="flex col-center row-center">
							<text
								class="marking-circular m-b-5 m-r-5"
								:style="{
									backgroundColor: nutritionCount(item.use_value, item.need_value).color
								}"
							></text>
							<text>{{ item.name }}</text>
						</view>
						<view>{{ item.use_value }} kcal</view>
					</view>
					<view class="diet-nutrition-marking flex row-between">
						<view>
							<text class="circular m-r-8"></text>
							不足
						</view>
						<view>
							<text class="circular m-r-8" style="backgroundColor:#18e6a2"></text>
							适宜
						</view>
						<view>
							<text class="circular m-r-8" style="backgroundColor:#ff5d5f"></text>
							超标
						</view>
					</view>
				</view>
			</view>
			<view class="record-utrition flex col-center row-between m-b-30">
				<text class="xs">有效的记录能更好地管理饮食健康</text>
				<view>
					<u-button
						text="记录体重"
						size="small"
						shape="circle"
						:color="variables.bgLinearGradient1"
						@click="clickManualRecord"
					>
						<view class="flex col-center p-r-20 p-l-20">
							<text class="xs">去记录</text>
							<!-- <u-icon name="arrow-right" color="#fff" size="28"></u-icon> -->
						</view>
					</u-button>
				</view>
			</view>
			<view class="dietary-structure-wrapp m-b-20">
				<view class="md f-w-500">
					<text>膳食结构</text>
					<text class="xs muted">（建议摄入每餐 4 种,每天 12 种以上的食物）</text>
				</view>
				<view class="flex row-between flex-wrap ">
					<view
						class="dietary-structure-content flex flex-col row-between col-center p-t-20 p-b-10 m-b-10"
						v-for="(item, index) in ingredientCategoryList"
						:key="index"
					>
						<u-image width="65rpx" height="65rpx" :src="item.icon"></u-image>
						<view class="xs p-t-10 p-b-5">{{ item.name }}</view>
						<view class="xs p-b-5">{{ item.count }}种</view>
						<view class="line m-b-10"></view>
						<view class="flex col-center">
							<text
								class="marking-circular m-b-5 m-r-5"
								:style="{
									backgroundColor: nutritionCount(item.use_value, item.need_value).color
								}"
							></text>
							<text class="muted xs">{{ item.use_value }}g</text>
						</view>
					</view>
				</view>
				<view class="text-center xs muted p-b-20 p-t-10" v-if="ingredientCategoryCountTotle < 12">
					今日摄入食物种类已经达到 {{ ingredientCategoryCountTotle }} 种，还需继续努力。
				</view>
				<view class="text-center xs muted p-b-20 p-t-10" v-if="ingredientCategoryCountTotle >= 12">
					今日摄入食物种类已经达到{{ ingredientCategoryCountTotle }}种，真是太棒了。
				</view>
			</view>
			<view class="flex col-center md f-w-500">
				<text class="p-r-10">膳食营养</text>
				<u-icon name="question-circle" color="#d2d2d2" size="28" @click="clickDietaryNutrition"></u-icon>
			</view>
			<view class="p-t-20">
				<view class="m-b-20" v-for="(item, index) in nutrientDietList" :key="index">
					<view class="flex col-center">
						<view class="flex-1">
							<view class="flex row-between sm flex-wrap p-b-10">
								<view>
									<text>{{ item.name }}</text>
								</view>
								<view>
									<text>{{ item.use_value }}</text>
									<text class="muted">/{{ item.need_value }}g</text>
								</view>
							</view>
							<u-line-progress
								:percentage="nutrientScale(item.use_value, item.need_value)"
								height="15"
								:showText="false"
								:activeColor="nutritionCount(item.use_value, item.need_value).color"
							></u-line-progress>
						</view>
						<view
							class="diet-marking xs m-l-20"
							:style="{
								color: nutritionCount(item.use_value, item.need_value).color
							}"
						>
							{{ nutritionCount(item.use_value, item.need_value).name }}
						</view>
					</view>
				</view>
			</view>
		</view>
		<u-modal
			:show="dietaryNutritionShowTips"
			title="膳食营养"
			confirmText="我知道了"
			:confirmColor="variables.colorPriamry"
			@confirm="dietaryNutritionShowTips = false"
		>
			<view slot="default">
				<text>合理搭配是膳食营养的保障。合理搭配是指食物种类和重量的合理化，膳食的营养价值通过合理搭配而提高和优化。</text>
				<view class="nutrition-show-tips-box diet-nutrition-marking flex row-between m-t-20 m-b-20">
					<view>
						<text class="circular m-r-8"></text>
						摄入不足
					</view>
					<view>
						<text class="circular m-r-8" style="backgroundColor:#18e6a2"></text>
						摄入适宜
					</view>
					<view>
						<text class="circular m-r-8" style="backgroundColor:#ff5d5f"></text>
						摄入超标
					</view>
				</view>
				<view class="table-box">
					<table width="100%" border="1" cellspacing="0" cellpadding="5">
						<thead>
							<tr>
								<th>提供能量的营养素</th>
								<th>主要食物来源</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td align="center">碳水化合物</td>
								<td align="center">谷物、薯类</td>
							</tr>
							<tr>
								<td align="center">蛋白质</td>
								<td align="center">畜禽肉类、鱼类、大豆</td>
							</tr>
							<tr>
								<td align="center">脂肪</td>
								<td align="center">植物油、动物油脂</td>
							</tr>
						</tbody>
					</table>
				</view>
				<view class="muted mini p-t-20">参考依据:《中国营养科学全书》《中国居民膳食指南2022版》</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
// import { checkClient } from '@/utils/util.js'
import uCharts from '@/uni_modules/qiun-data-charts/js_sdk/u-charts/u-charts.js'
var uChartsInstance = {}
export default {
	props: {
		healthyIndexData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			// canvas2dStatus: false,
			dietIntake: {},
			healthyDataObj: {},
			mealList: [
				{
					key: 'breakfast',
					name: '早餐',
					need_value: 0,
					use_value: 0
				},
				{
					key: 'lunch',
					name: '午餐',
					need_value: 0,
					use_value: 0
				},
				{
					key: 'dinner',
					name: '晚餐',
					need_value: 0,
					use_value: 0
				}
			],
			ingredientCategoryList: [
				{
					key: 'cereals_tubers',
					name: '谷物薯类',
					count: '',
					need_value: '',
					use_value: '',
					// icon: require('@/static/icons/icon01.png')
					icon: this.$imgPath.img_icon01
				},
				{
					key: 'eggsandmeat',
					name: '鱼禽蛋肉',
					count: '',
					need_value: '',
					use_value: '',
					// icon: require('@/static/icons/icon02.png')
					icon: this.$imgPath.img_icon02
				},
				{
					key: 'fruit_vegetable',
					name: '蔬菜水果',
					count: '',
					need_value: '',
					use_value: '',
					// icon: require('@/static/icons/food1.png')
					icon: this.$imgPath.img_food1
				},
				{
					key: 'dairy',
					name: '奶类豆类',
					count: '',
					need_value: '',
					use_value: '',
					// icon: require('@/static/icons/icon04.png')
					icon: this.$imgPath.img_icon04
				}
			],
			ingredientCategoryCountTotle: 0, // 膳食结构多少种
			// nutrientData: {},
			// 膳食营养
			nutrientDiet: {},
			nutrientDietList: [
				{
					name: '碳水化合物',
					key: 'carbohydrate',
					need_value: 0,
					use_value: 0
				},
				{
					name: '蛋白质',
					key: 'protein',
					need_value: 0,
					use_value: 0
				},
				{
					name: '脂肪',
					key: 'axunge',
					need_value: 0,
					use_value: 0
				}
			],
			arcbarData: {
				series: [
					{
						color: '#ff9656',
						data: 0
					}
				]
			},
			dietaryNutritionShowTips: false,
			cWidth: uni.upx2px(350),
			cHeight: uni.upx2px(260)
		}
	},
	watch: {
		healthyIndexData(newValue, oldValue) {
			this.dietIntake = newValue.intake
			this.healthyDataObj = newValue
			this.init()
		}
	},
	created() {
		//这里的 650 对应 css .charts 的 width
		// this.cWidth = uni.upx2px(350)
		//这里的 400 对应 css .charts 的 height
		// this.cHeight = uni.upx2px(260)
		// 先初始化
		this.$nextTick(function() {
			this.drawCharts('********************************', this.arcbarData)
		})
		// this.canvas2dStatus = this.platform === 'mp-alipay' ? false : true
	},
	onShow() {},
	mounted() {},
	methods: {
		drawCharts(id, data) {
			console.log(data,99999)
			const ctx = uni.createCanvasContext(id, this)
			uChartsInstance[id] = new uCharts({
				type: 'arcbar',
				context: ctx,
				width: this.cWidth,
				height: this.cHeight,
				series: data.series,
				animation: true,
				background: '#FFFFFF',
				enableScroll: false, // 是否允许滚动
				padding: [15, 15, 0, 5],
				title: {
					name: '',
					fontSize: 13,
					color: '#666666',
					offsetY: -10
				},
				subtitle: {
					name: '',
					fontSize: 20,
					color: 'black',
					offsetY: -10
				},
				extra: {
					arcbar: {
						type: 'default',
						width: 8,
						linearType: 'none',//关闭渐变
						backgroundColor: '#E9E9E9',
						startAngle: 0.75,
						endAngle: 0.25,
						gap: 2
					}
				}
			})
		},
		touchend(e) {
			uChartsInstance[e.target.id].scrollEnd(e)
			uChartsInstance[e.target.id].touchLegend(e)
			uChartsInstance[e.target.id].showToolTip(e)
		},
		touchstart(e) {
			uChartsInstance[e.target.id].scrollStart(e)
		},
		touchmove(e) {
			uChartsInstance[e.target.id].scroll(e)
		},
		clickManualRecord() {
			this.$miRouter.push({
				path: '/pages_health/record_food/manual_record'
			})
		},
		nutritionCount(value, needValue) {
			let scale = 0
			if (value / needValue) {
				scale = (value / needValue) * 100
			}
			let params = {
				color: '',
				name: '',
				key: ''
			}
			if (scale < 70) {
				params.color = '#ff9656'
				params.name = '不足'
				params.key = 'insufficient'
			} else if (scale >= 70 && scale < 110) {
				params.color = '#11e69e'
				;(params.name = '适宜'), (params.key = 'suitable')
			} else if (scale >= 110) {
				params.color = '#ff5d5f'
				params.name = '超标'
				params.key = 'extra'
			}
			return params
		},
		// 计算营养
		nutrientScale(value, needValue) {
			let scale = parseInt((value / needValue) * 100)
			return scale ? (scale <= 100 ? scale : 100) : 0
		},
		init() {
			// 头部饮食健康
			if (uChartsInstance['********************************']) {
				// 头部饮食健康
				let energyKcalScale =
					this.dietIntake.energy_kcal / this.dietIntake.need_energy_kcal
						? this.dietIntake.energy_kcal / this.dietIntake.need_energy_kcal
						: 0
				this.arcbarData.series[0].data = energyKcalScale < 1 ? energyKcalScale : 1
				this.arcbarData.series[0].color = this.nutritionCount(this.dietIntake.energy_kcal, this.dietIntake.need_energy_kcal).color
				uChartsInstance['********************************'].updateData({
					series: this.arcbarData.series
				})
			}
			this.mealList.forEach(v => {
				v.need_value = this.dietIntake['need_' + v.key]
				v.use_value = this.dietIntake[v.key]
			})
			// 膳食结构
			this.ingredientCategoryCountTotle = 0
			this.ingredientCategoryList.forEach(v => {
				v.count = this.healthyDataObj.ingredient_category[v.key].count
				v.need_value = this.healthyDataObj.ingredient_category[v.key].need_value
				v.use_value = this.healthyDataObj.ingredient_category[v.key].use_value
				this.ingredientCategoryCountTotle += v.count
			})
			// 膳食营养
			this.nutrientDiet = this.healthyDataObj.nutrient
			this.nutrientDietList.forEach(v => {
				v.need_value = this.nutrientDiet['need_' + v.key]
				v.use_value = this.nutrientDiet[v.key]
			})
		},
		clickDietaryNutrition() {
			this.dietaryNutritionShowTips = true
		}
	}
}
</script>

<style lang="scss" scoped>
.nutritionRatio {
	.ls-card {
		padding: 30rpx;
		border-radius: 20rpx;
		background-color: #ffffff;
	}
	.arcbar-box {
		width: 350rpx;
		height: 260rpx;
		position: relative;
		.arcbar-title {
			position: absolute;
			top: 46%;
			left: 50%;
			transform: translate(-50%, -50%);
			.text {
				font-size: 35rpx;
			}
		}
	}
	.marking-circular {
		display: inline-block;
		width: 12rpx;
		height: 12rpx;
		background-color: #11e69e;
		border-radius: 12rpx;
	}
	.nutrition-meal {
		width: 100%;
		font-size: 24rpx;
	}
	.diet-nutrition-marking {
		.circular {
			display: inline-block;
			width: 20rpx;
			height: 20rpx;
			background-color: #ff9656;
			border-radius: 6rpx;
		}
	}
	.record-utrition {
		padding: 20rpx 30rpx 20rpx 30rpx;
		background-color: #f6f7fb;
		border-radius: 16rpx;
	}
	.dietary-structure-wrapp {
		.dietary-structure-content {
			// width: 142rpx;
			// height: 225rpx;
			padding: 20rpx;
			background-color: #f6f7fb;
			border-radius: 16rpx;
			.line {
				width: 102rpx;
				height: 2rpx;
				background-color: #e7e8f0;
			}
		}
	}
	.diet-marking {
		margin-top: 20rpx;
		margin-left: 20rpx;
		padding: 10rpx 25rpx 10rpx 25rpx;
		color: #ff9656;
		background-color: #f8f8f8;
		border-radius: 30rpx;
	}
	.nutrition-show-tips-box {
		padding: 20rpx;
		border-radius: 10rpx;
		background-color: #f6f7fb;
	}
	.table-box {
		table,
		td {
			border: 5rpx solid #f2f2f2;
			border-collapse: collapse;
		}
		thead {
			background-color: #f0f0f0;
			opacity: 0.7;
		}
	}
	.charts {
    width: 350rpx;
    height: 260rpx;
  }
}
</style>
