<template>
  <view class="healthScoreLine">
    <view class="ls-card">
      <view class="md f-w-500">健康分统计</view>
      <view class="line-charts m-t-20">
        <qiun-data-charts type="line" :opts="optsLine" :ontouch="true" :chartData="chartDataLine" background="none"/>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    healthyIndexData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      chartDataLine: {
        categories: [],
        series: [
          {
            name: '健康分统计',
            data: []
          }
        ]
      },
      optsLine: {
        padding: [15, 10, 0, 15],
        enableScroll: true,
				color:['#18e6a2'],
        xAxis: {
          disableGrid: true,
          itemCount: 3,
          scrollShow: true,
					scrollAlign:'right'
        },
        yAxis: {
          gridType: 'dash',
          dashLength: 2,
					splitNumber:5,
					data:[
						{
							min:0,
							max:100
						}
					]

        },
        extra: {
          line: {
            type: 'straight',
            width: 2
          }
        }
      }
    }
  },
  created() {
    this.init()
  },
  onShow() {},
  mounted() {},
  methods: {
    init() {
      let categoriesData = [] // 曲线图日期
      let seriesData = [] //曲线图数据
      // 曲线图
      this.healthyIndexData.healthy_score_list.map(v => {
        categoriesData.push(uni.$u.timeFormat(new Date(v.date).getTime(), 'mm-dd'))
        seriesData.push(v.score)
      })
			console.log(seriesData)
      this.chartDataLine = {
        categories: categoriesData,
        series: [
          {
            name: '健康分统计',
            data: seriesData
          }
        ]
      }
			// this.optsLine.yAxis.splitNumber = this.setSplitNumber(seriesData);
    },
		// setSplitNumber(arr) {
		// 		let splitNumber = 5;
		// 		if (arr.length != 0) {
		// 			let max = Math.max.apply(null, arr);
		// 			let min = Math.min.apply(null, arr);
		// 			console.log(max, min)
		// 			if(Number(max)-Number(min)>=5 || Number(max)-Number(min)==0) {
		// 				splitNumber = 5;
		// 			}else{
		// 				splitNumber = Number(max)-Number(min)
		// 			}
		// 		}
		// 		return splitNumber
		// 	},
  }
}
</script>

<style lang="scss" scoped>
.healthScoreLine {
  .ls-card {
    padding: 30rpx;
    border-radius: 20rpx;
    background-color: #ffffff;
  }
  .line-charts {
    width: 100%;
    height: 350rpx;
  }
}
</style>
