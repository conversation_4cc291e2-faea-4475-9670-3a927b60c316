<template>
  <view class="intake-part bg-white m-b-20">
    <view class="top">
      <view class="title m-b-30">
        <text class="md f-w-600">今日摄入</text>
      </view>
      <view class="heat-show flex col-center m-b-20">
        <!-- <u-icon size="80" :name="intakeData.ingested ? imgPath.img_hot2 : imgPath.img_hot1"></u-icon> -->
        <circle-progress 
          :activeColor="intakeData.ingested===0 ? '#E9E9E9' : (intakeData.ingested>intakeData.intakeRange[1] ? '#FF3333' : (intakeData.ingested<intakeData.intakeRange[0] && intakeData.ingested>0 ? '#FF9901' : '#11E69E'))" 
          :percent="percent" borderWidth="4" width="100" :bgImg="hotOrNot" :bgShow="true">
        </circle-progress>
        <view class="heat-show-text flex col-center m-l-20">
          <text class="xxxl">{{ intakeData.ingested }}</text>
          <view>
            <text class="m-l-10 grey" v-if="intakeData.intakeRange[0] !== intakeData.intakeRange[1]">/ {{ intakeData.intakeRange[0] }}~{{ intakeData.intakeRange[1] }} kcal</text>
            <text class="m-l-10 grey" v-else>/ {{ intakeData.intakeRange[0] }} g</text> 
          </view>
        </view>
      </view>
      <view class="intake-data">
        <view class="nutrient-card flex row-between col-center m-b-20"  v-for="(item, index) in tableData" :key="index">
          <view class="flex flex-center">
            <u-icon size="60" :name="item.icon_url"></u-icon>
            <text class="black m-l-20">{{ item.name }}</text>
            <text 
                :class="['tag', 'mini', 'text-center', 'm-l-4', 'p-t-2', 'p-b-2']" 
                style="border-radius: 10rpx;"
                :style="[item.intake_Data>item.maxIntake? outOfStandard.style : item.intake_Data<item.minIntake? insufficient.style : suitable.style]"
                v-if="intakeData.ingested !== 0">
                {{ showText(item) }}
              </text>
          </view>
          <view class="flex flex-center">
            <text class="xxl black">{{ item.intake_Data>1000? '1000+' : item.intake_Data }}</text>
            <view>
              <text class="m-l-10 grey" v-if="item.minIntake !== item.maxIntake">/ {{ item.minIntake }}~{{ item.maxIntake }} g</text> 
              <text class="m-l-10 grey" v-else>/ {{ item.minIntake }} g</text> 
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom flex row-around col-center p-l-50 p-r-50">
      <view class="button-r flex flex-center">
        <!-- <u-icon size="26" :name="intakeData.ingested !== 0 ? imgPath.img_analyze : imgPath.img_analyze_off" color="#8f9295"></u-icon> -->
        <!-- <text :class="['m-l-10', intakeData.ingested !== 0 ? 'primary' : 'grey-light']">营养分析</text> -->
        <u-button type="primary" text="营养分析" :plain="true" :customStyle="customBtnStyle" @click="goToNutritionalAnalysis"></u-button>
      </view>
      <text class="line-title"></text>
      <view class="button-l flex flex-center" @click="clickManualRecord">
        <!-- <u-icon size="26" :name="themeImgPath.img_record"></u-icon> -->
        <text class="primary m-l-20">记录饮食</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getApiIsMiniappAuth } from '@/api/app.js'
import Cache from '@/utils/cache'
export default {
  name: 'intakeToday',
  props: {
    intakeData: {
      type: Object,
      default:() => {
        return {
          ingested: 0,
          intakeRange: [],
          actual: {
            protein: 0,
            fat: 0,
            carbohydrate: 0
          },
          should: {
            Protein: [],
            Fat: [],
            Carbohydrate: []
          }
        }
      }
    }
  },
  onLoad() {
    this.hotOrNot_color()
  },
  data() {
    return {
      customBtnStyle: {
				minWidth: '180rpx',
				height: '50rpx',
				lineHeight: '50rpx',
        border:'0rpx',
			},
      imgPath: this.$imgPath,
      tableData: [
        {
          icon_url: this.$imgPath.img_protein1,
          name: '蛋白质',
          intake_Data: 0,
          minIntake: 0,
          maxIntake: 0
        },
        {
          icon_url: this.$imgPath.img_fat,
          name: '脂肪',
          intake_Data: 0,
          minIntake: 0,
          maxIntake: 0
        },
        {
          icon_url: this.$imgPath.img_carbohydrate,
          name: '碳水化合物',
          intake_Data: 0,
          minIntake: 0,
          maxIntake: 0
        }
      ],
      suitable: {
        style: {
          width: '64rpx',
          height: '32rpx',
          lineHeight: '32rpx',
          backgroundColor: '#11E69E1F',
          color: '#11E69E'
        },
        text: '适宜'
      },
      insufficient: {
        style: {
          width: '64rpx',
          height: '32rpx',
          lineHeight: '32rpx',
          backgroundColor: '#FF96561F',
          color: '#F8A73C'
        },
        text: '不足'
      },
      outOfStandard: {
        style: {
          width: '64rpx',
          height: '32rpx',
          lineHeight: '32rpx',
          backgroundColor: '#FF5F5F1F',
          color: '#FF5757'
        },
        text: '超标'
      },
      userInfo: Cache.get('userInfo') || {}
    }
  },
  computed: {
    // 今日摄入的热量图标显示
    hotOrNot() {
      if (this.intakeData.ingested===0) {
        return 0
      } else if (this.intakeData.ingested>this.intakeData.intakeRange[1]) {
        return 3
      } else if (this.intakeData.ingested<this.intakeData.intakeRange[0] && this.intakeData.ingested>0) {
        return 2
      } else {
        return 1
      }
    },
    percent() {
      // 判断
      if (this.intakeData.ingested === 0) {
        return 0
      } else if (this.intakeData.ingested > ((this.intakeData.intakeRange[0]+this.intakeData.intakeRange[1])/2) ) {
        return 100
      } else {
        return Number(this.intakeData.ingested/((this.intakeData.intakeRange[0]+this.intakeData.intakeRange[1])/2)*100)
      }
    }
  },
  watch: {
    intakeData: {
      handler(val) {
        this.watchTableData(val)
      },
      deep: true
    }
  },
  methods: {
    getIsMiniappAuth() {
			getApiIsMiniappAuth({
        user_id: this.userInfo.user_id,
        appid: 'wx1ff45c78e3f8888f'
        // secret: '45fd1815c38af427bda4389902d7db98' // 写死
      })
				.then(async res => {
          if (res.code === 0) {
            let authData = res.data
            let hrefData = ""
            if (authData.is_auth) {
              hrefData = `weixin://dl/business/?appid=wx1ff45c78e3f8888f&path=pages/index/index&query=bg_sessionid=${authData.bg_sessionid}`
            } else {
              hrefData = `weixin://dl/business/?appid=wx1ff45c78e3f8888f&path=pages/index/index`
            }
            if (process.env.NODE_ENV === 'development' || (location && location.href.indexOf('debug') > -1)) {
              hrefData += '&env_version=trial'
            }
            window.location.href = hrefData
          }else {
            uni.$u.toast(res.msg)
          }
				})
				.catch(err => {
					uni.$u.toast(err)
				})
		},
    // hotOrNot_color() {
    //   if (this.intakeData.ingested===0) {
    //     return '#E9E9E9'
    //   } else if (this.intakeData.ingested>this.intakeData.intakeRange[1]) {
    //     return '#FF3333'
    //   } else if (this.intakeData.ingested<this.intakeData.intakeRange[0] && this.intakeData.ingested>0) {
    //     return '#FF9901'
    //   } else {
    //     return '#11E69E'
    //   }
    // },
    // 根据条件切换标签内容
    showText(item) {
      if (item.intake_Data > item.maxIntake) {
        return this.outOfStandard.text
      } else if (item.intake_Data < item.minIntake) {
        return this.insufficient.text
      } else {
        return this.suitable.text
      }
    },
    clickManualRecord() {
			this.$miRouter.push({
				path: '/pages_health/record_food/manual_record'
			})
		},
    goToNutritionalAnalysis() {
      this.getIsMiniappAuth()
      // 跳营养分析
      // this.$miRouter.push({
			// 	path: '/pages_health_pomr/nutritional_analysis/index'
			// })
    },
    watchTableData(val) {
      this.tableData = this.tableData.map((item) => {
        switch (item.name) {
          case '蛋白质':
            item.intake_Data = val.actual.protein
            item.minIntake = val.should.Protein[0]
            item.maxIntake = val.should.Protein[1]
            break
          case '脂肪':
            item.intake_Data = val.actual.fat
            item.minIntake = val.should.Fat[0]
            item.maxIntake = val.should.Fat[1]
            break
          case '碳水化合物':
            item.intake_Data = val.actual.carbohydrate
            item.minIntake = val.should.Carbohydrate[0]
            item.maxIntake = val.should.Carbohydrate[1]
            break;
          default:
            break;
        }
        return item
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.intake-part {
  border-radius: 20rpx;
  .top{
    padding: 30rpx;

    .heat-show {
      padding: 20rpx 30rpx;
      border-radius: 20rpx;
      background-color: #F6F7FB;
      height: 120rpx;
    }
    .uni-table-td {
      border-bottom: none;
      padding: 8rpx 10rpx;
    }
  }
  .bottom {
    height: 90rpx;
    border-top: 1rpx solid #e4e4e4;
    .line-title {
				width: 1rpx;
				height: 90rpx;
				background-color: #e6e6e6;
			}
  }
  .is-disabled{
    opacity: .3;
  }
}
</style>