<template>
	<view class="habitFormation">
		<view class="ls-card m-t-20">
			<view class="flex row-between md f-w-500" @click="$miRouter.push('/pages_health/healthy/habit_formation/index')">
				<text>习惯养成</text>
				<view class="xs muted flex col-center">
					更多
					<u-icon class="m-l-10" name="arrow-right" size="24rpx"></u-icon>
				</view>
			</view>
			<view class="progress-box p-20 m-t-20 m-b-20">
				<view class="flex row-between m-b-20">
					<view class="muted">当前进度</view>
					<view class="">
						{{ habitData.recode }}
						<text class="muted">/{{ habitData.need_record }}</text>
					</view>
				</view>
				<u-line-progress :percentage="habitProgress" :showText="false" height="14" :activeColor="variables.colorPrimary"></u-line-progress>
			</view>

			<view class="habit-formation-content" v-if="habitList.length">
				<view class="habit-formation-list-item flex row-between col-center" v-for="(item, index) in habitList" :key="index">
					<view class="flex col-center">
						<view class="m-r-20">
							<u-image width="90rpx" height="90rpx" :radius="10" mode="scaleToFill" :src="item.image" v-if="item.image"></u-image>
							<view
								class="custom-style"
								:style="{
									backgroundColor: item.color
								}"
								v-else
							>
								{{ item.name ? item.name.substring(0, 1) : '' }}
							</view>
						</view>
						<view>
							<view class="f-w-500 md m-b-10">{{ item.name }}</view>
							<view class="muted mini">坚持打卡 {{ item.use_day }} 天</view>
						</view>
					</view>
					<view>
						<u-button
							v-if="item.is_record"
							text="已打卡"
							size="small"
							shape="circle"
							plain
							color="#d9dbe1"
							:customStyle="customBtnStyle"
							@click="clickRecord(item, 'cancelRecord')"
						></u-button>
						<u-button
							v-else
							text="打卡"
							iconColor="#fff"
							size="small"
							shape="circle"
							:color="variables.bgLinearGradient1"
							:customStyle="customBtnStyle"
							@click="clickRecord(item, 'record')"
						></u-button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getApiHealthyAddHabitRecord, getApiCancelHabitRecord } from '@/api/healthy.js'
export default {
	props: {
		healthyIndexData: {
			type: Object,
			default: {}
		},
	},
	data() {
		return {
			habitData: {},
			habitList: [],
			habitProgress: 0,
			customBtnStyle: {
				width: '150rpx',
				height: '55rpx'
			}
		}
	},
	onLoad(option) {},
	created() {},
	watch: {
		healthyIndexData(newValue, oldValue) {
			this.habitData = newValue.habit_data
			this.habitList = newValue.habit_data.data
			this.init()
		}
	},
	onShow() {},
	mounted() {
	},
	methods: {
		init() {
			let scale = parseInt((this.habitData.recode / this.habitData.need_record) * 100)
			this.habitProgress = scale ? (scale <= 100 ? scale : 100) : 0
		},
		clickRecord(data, type) {
			if (type === 'record') {
				this.getHealthyAddHabitRecord(data)
			} else {
				this.getCancelHabitRecord(data)
			}
		},
		// 打卡
		getHealthyAddHabitRecord(params) {
			// this.$showLoading({
			// 	title: '加载中....',
			// 	mask: true
			// })
			getApiHealthyAddHabitRecord({
				id: params.id
			})
				.then(async res => {
					// uni.hideLoading()
					if (res.code == 0) {
						uni.$u.toast('打卡成功')
						// params.is_record = !params.is_record
						// params.use_day++
						await this.$sleep(500)
						this.$emit('confirmGetHealthyIndex')
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					// uni.hideLoading()
					console.log('获取用户信息err', err)
				})
		},
		// 取消打卡
		getCancelHabitRecord(params) {
			// this.$showLoading({
			// 	title: '加载中....',
			// 	mask: true
			// })
			getApiCancelHabitRecord({
				id: params.habit_record_id
			})
				.then(async res => {
					// uni.hideLoading()
					if (res.code == 0) {
						uni.$u.toast('已取消打卡')
						// params.is_record = !params.is_record
						// params.use_day--
						await this.$sleep(500)
						this.$emit('confirmGetHealthyIndex')
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					// uni.hideLoading()
				})
		}
	}
}
</script>

<style lang="scss" scoped>
.habitFormation {
	.ls-card {
		padding: 30rpx;
		border-radius: 20rpx;
		background-color: #ffffff;
	}
	.custom-style {
		width: 90rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background-color: #11e69e;
		color: #ffffff;
		font-size: 42rpx;
		border-radius: 14rpx;
	}
	.progress-box {
		background-color: #f6f7fb;
		border-radius: 16rpx;
	}
	.habit-formation-content {
		// max-height: 500rpx;
		// overflow: auto;
		.habit-formation-list-item {
			padding: 20rpx 0rpx;
			border-radius: 16rpx;
		}
	}
}
</style>
