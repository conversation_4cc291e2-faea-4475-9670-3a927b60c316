<template>
  <view class="drinking-part bg-white">
    <view class="title m-b-30">
      <text class="md f-w-600">饮水推荐</text>
    </view>
    <view class="drinking-show flex col-center m-b-20">
      <u-icon size="80" :name="themeImgPath.img_water"></u-icon>
      <view class="drinking-show-text flex flex-col m-l-20">
        <text class="xxxl">{{ waterIntake }} ml</text>
        <text class="grey xs">水是维持生命和健康所必需的营养素</text>        
      </view>
    </view>
    <view class="drinking-suggestion">
      <text class="normal xs">{{ waterSuggestion }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'drinkingWaterRecommendation',
  props: {
    waterIntake: {
      type: Number,
      default: 0
    },
    waterSuggestion: {
      type: String,
      default: '暂无任何建议'
    }
  },
  data() {
    return {
      imgPath: this.$imgPath,
    }
  }
}
</script>

<style lang="scss" scoped>
.drinking-part {
  border-radius: 20rpx;
  padding: 30rpx;
  .drinking-show {
    padding: 20rpx 30rpx;
    border-radius: 20rpx;
    background-color: #F6F7FB;
    height: 120rpx;
  }
}
</style>