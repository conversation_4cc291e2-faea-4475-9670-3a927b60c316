<template>
	<view class="header flex">
		<view class="header--left ls-card">
			<view class="flex col-center row-between md f-w-500">
				<text>体重目标</text>
				<u-image
					width="40rpx"
					height="30rpx"
					:radius="10"
					mode="scaleToFill"
					@click="clickShoWeight(!healthyVux.showWeight)"
					
					:src="healthyVux.showWeight ? imgPath.img_eye : imgPath.img_eye_off"
				></u-image>
				<!-- :src="healthyVux.showWeight ? require('@/static/icons/eye.png') : require('@/static/icons/eye-off.png')" -->
			</view>
			<view class="flex col-center row-between p-t-20 p-b-20">
				<view class="flex col-center">
					<text class="muted">BMI：</text>
					<text>{{ healthyVux.showWeight ? bmi.bmi : '**' }}</text>
				</view>
				<view
					class="bmi-text"
					:style="{
						color: funWeightColor(bmi.bmi).color,
						backgroundColor: funWeightColor(bmi.bmi).bgColor
					}"
				>
					{{ bmi.bmi_text ? bmi.bmi_text : "" }}
				</view>
			</view>
			<view class="u-percentage-box">
				<u-line-progress
					:height="heightProgress"
					:percentage="progressWeight"
					:showText="false"
					:activeColor="variables.colorPrimary"
				></u-line-progress>
			</view>
			<view class="p-t-20">
				<view class="flex row-between">
					<!-- 目标体重 -->
					<view class="flex col-center">
						<text>{{ showWeightText() }}</text>
						<text class="muted p-l-5">kg</text>
					</view>
					<!-- 当前体重 -->
					<view class="flex col-center">
						<view>{{ healthyVux.showWeight ? bmi.weight : '**' }}</view>
						<view class="muted p-l-5">kg</view>
					</view>
				</view>
				<view class="flex row-between">
					<!-- 目标体重 -->
					<view class="text-c muted mini">
						{{ bmi.weight_target ? bmi.healthy_target_text : '' }}{{ bmi.healthy_target === 'keep' ? '' : '目标' }}
					</view>
					<!-- 当前体重 -->
					<view class="text-c muted mini">当前</view>
				</view>
			</view>
			<view class="p-t-25">
				<u-button
					size="small"
					shape="circle"
					:color="variables.bgLinearGradient1"
					@click="clickRecordWeight"
				>
					<view class="flex col-center">
						<text>{{ bmi.weight_target ? '记录体重' : '设置目标' }}</text>
						<text class="iconfont icon-arrow-r p-l-10"></text>
					</view>
				</u-button>
			</view>
		</view>
		<view class="header--right ls-card m-l-20">
			<view class=" md f-w-500 header-right-top">
				<view class="flex" @click.stop="healthScoreTips">
					健康分
					<u-icon name="question-circle" size="28"></u-icon>
				</view>
				<view class="tag sm flex">
					<view class="tag-item flex flex-center" :class="type === 'today' ? ' select' : ''" @click="changeFunc('today')">
						当天
					</view>
					<view class="tag-item flex flex-center" :class="type === 'all' ? ' select' : ''" @click="changeFunc('all')">累计</view>
				</view>
			</view>
			<view class="radar-box">
				<template v-if="type === 'today'">
					<qiun-data-charts
						type="radar"
						:canvas2d="canvas2dStatus"
						:chartData="radarData"
						:opts="radarOpts"
						:errorReload="false"
						background="none"
						tooltipFormat="healthArcbar"
					/>
					<!-- 暂时找不到第二方案解决，直接定位上去 -->
					<view class="num">{{ healthyCurrentScoreNumberData }}</view>
					<view class="location text-1">食物多样</view>
					<view class="location text-2">运动情况</view>
					<view class="location text-3">BMI</view>
					<view class="location text-4">能量摄入</view>
					<view class="location text-5">营养均衡</view>
				</template>

				<template v-else>
					<qiun-data-charts
						type="radar"
						:canvas2d="canvas2dStatus"
						:chartData="radarData2"
						:opts="radarOpts"
						:errorReload="false"
						background="none"
						tooltipFormat="healthArcbar"
					/>
					<!-- 暂时找不到第二方案解决，直接定位上去  因为文字会图缩小-->
					<view class="num">{{ healthyTotalCurrentScoreNumberData }}</view>
					<view class="location text-1">食物多样</view>
					<view class="location text-2">运动情况</view>
					<view class="location text-3">BMI</view>
					<view class="location text-4">能量摄入</view>
					<view class="location text-5">营养均衡</view>
				</template>
			</view>
		</view>
		<u-modal
			:show="showhealthScoreTips"
			class="healthScoreTips-box"
			confirmText="我知道了"
			:confirmColor="variables.colorPrimary"
			@confirm="showhealthScoreTips = false"
		>
			<view slot="default">
				<scroll-view scroll-y="true" style="height: 700rpx;" :z-index="100">
					<view v-for="(item, key, index) in healthyDataObj.tips" :key="index">
						<view class="text-center p-b-20">{{ funHealthyName(key) }}</view>
						<view class="p-b-20">{{ item }}</view>
					</view>
				</scroll-view>
			</view>
		</u-modal>
		<u-popup v-if="recordWeightShow" :show="recordWeightShow" closeable :round="10" @close="recordWeightShow = false">
			<view class="popup-weight-box">
				<view class="select-weight-box p-b-20">
					<view class="select-weight-text m-t-20" @click="clickWeightDateShow">
						<text>{{ physicalRecordParams.date }}</text>
						<text class="iconfont icon-arrow-down-filling"></text>
					</view>
				</view>
				<view class="text-center">
					<text class="weight-text f-w-500">{{ physicalRecordParams.number }}</text>
					<text class="muted sm p-l-10">kg</text>
				</view>
				<!-- 刻度 -->
				<scroll-choose
					@scroll="scroll"
					:scrollStart="25"
					:scrollEnd="200"
					:scrollLeft="scrollLeft"
					:maginL="5"
					:scrollUnit="0.1"
				></scroll-choose>
				<view class="p-t-20 p-b-30" @click="determinChoose">
					<u-button shape="circle" hover-class="none" :loading="false" :custom-style="customStyleCropper">确定</u-button>
				</view>
			</view>
		</u-popup>
		<!-- 选择体重日期 -->
		<u-datetime-picker
			ref="datetimePicker"
			:show="selectWeightDateShow"
			v-model="weightDateValue"
			mode="date"
			:formatter="formatter"
			:confirmColor="variables.colorPrimary"
			@cancel="selectWeightDateShow = false"
			@confirm="confirmWeightDate"
		></u-datetime-picker>
	</view>
</template>

<script>
import { checkClient } from '@/utils/util.js'
import { getApiAddPhysicalRecord } from '@/api/healthy.js'
import { mapActions, mapGetters, mapMutations } from 'vuex'
import NP from '@/utils/np.js'
export default {
	name: 'health-weight-score-charts',
	props: {
		healthyIndexData: {
			type: Object,
			default: {}
		}
	},
	data() {
		return {
			imgPath: this.$imgPath,
			heightProgress: uni.upx2px(30),
			showhealthScoreTips: false,
			bmi: {},
			healthyDataObj: {},
			physicalRecordParams: {
				type: 'weight',
				number: 0,
				date: uni.$u.timeFormat(new Date().getTime(), 'yyyy-mm-dd')
			},
			healthyCurrentScoreNumberData: 0, //当天健康分数值
			healthyTotalCurrentScoreNumberData: 0, //累计 健康分数值
			type: 'today',
			radarData: {
				categories: ['', '', '', '', ''],
				// categories: ['食物多样性', '运动情况', 'BMI', '能量摄入', '营养均衡'],
				series: [
					{
						name: '',
						data: [50, 20, 30, 20, 30]
					}
				]
			},
			radarData2: {
				categories: ['', '', '', '', ''],
				// categories: ["营养风险", "就餐习惯", "身体情况", "运动情况", "营养均衡"],
				series: [
					{
						name: '',
						data: [50, 20, 30, 20, 30]
					}
				]
			},
			radarOpts: {
				// width: '600rpx',
				// height: '600rpx',
				color: ['#52da98'],
				extra: {
					radar: {
						max: 100
					}
				},
				legend: {
					show: false,
					fontSize: 10
				}
			},
			platform: checkClient(), // 平台， 微信or支付宝
			canvas2dStatus: false,
			recordWeightShow: false,
			scrollLeft: 0, //初始值
			customStyleCropper: {
				width: '670rpx',
				backgroundColor: '#52da98',
				marginTop: '20rpx',
				marginBottom: '20rpx',
				color: '#fff',
				border: 'none'
			},
			selectWeightDateShow: false,
			weightDateValue: Number(new Date()) // 体重日期
		}
	},
	watch: {
		healthyIndexData(newValue, oldValue) {
			this.healthyDataObj = newValue
			this.bmi = newValue.bmi
			this.init()
		}
	},
	computed: {
		...mapGetters(['healthyVux']),
		progressWeight() {
			let num = 0
			if (this.bmi.healthy_target === 'lose') {
				//  减重（原始体重-最新体重）/（原始体重-目标体重）
				let numWeight = ((this.bmi.start_weight - this.bmi.weight) / (this.bmi.start_weight - this.bmi.weight_target)) * 100
				num = parseInt(numWeight)
			} else if (this.bmi.healthy_target === 'gain') {
				// 增重  （最新体重-原始体重）/（目标体重-原始体重）
				let numWeight = ((this.bmi.start_weight - this.bmi.weight) / (this.bmi.weight_target - this.bmi.weight)) * 100
				num = parseInt(numWeight)
			} else {
				num = 100
			}
			return num
		}
	},
	onReady() {
		// 微信小程序需要用此写法
		this.$refs.datetimePicker.setFormatter(this.formatter)
	},
	created() {
		this.canvas2dStatus = this.platform === 'mp-alipay' ? false : true
	},
	onShow() {},
	mounted() {},
	methods: {
		...mapActions({
			setHealthyVux: 'setHealthyVux'
		}),
		formatter(type, value) {
			if (type === 'year') {
				return `${value}年`
			}
			if (type === 'month') {
				return `${value}月`
			}
			if (type === 'day') {
				return `${value}日`
			}
			return value
		},
		// 文字
		showWeightText() {
			let text = '**'
			if (!this.healthyVux.showWeight) {
				text = '**'
			} else {
				if (this.bmi.weight_target && this.bmi.healthy_target !== 'keep') {
					text = this.bmi.weight_target
				} else {
					text = '--'
				}
			}
			return text
		},
		init() {
			let healthyScoreList = ['food', 'sport', 'bmi', 'energy', 'nutrition']
			let healthyCurrentScoreData = [] //当天累计 健康分
			let healthyTotalCurrentScoreData = [] //累计 健康分
			let healthyCurrentScoreNumberData = 0 //当天累计健康分数值
			let healthyTotalCurrentScoreNumberData = 0 //累计 健康分数值
			healthyScoreList.map(v => {
				if (this.healthyDataObj.healthy_score[v]) {
					healthyCurrentScoreData.push(
						Number(
							(
								(this.healthyDataObj.healthy_score[v].current_score / this.healthyDataObj.healthy_score[v].default_score) *
								100
							).toFixed(2)
						)
					)
					healthyCurrentScoreNumberData += this.healthyDataObj.healthy_score[v].current_score
				}
				if (this.healthyDataObj.total_healthy_score[v]) {
					healthyTotalCurrentScoreData.push(
						Number(
							(
								(this.healthyDataObj.total_healthy_score[v].current_score /
									this.healthyDataObj.total_healthy_score[v].default_score) *
								100
							).toFixed(2)
						)
					)
					healthyTotalCurrentScoreNumberData += this.healthyDataObj.total_healthy_score[v].current_score
				}
			})
			// 当天累计 健康分
			this.radarData.series[0].data = healthyCurrentScoreData
			// 累计 健康分
			this.radarData2.series[0].data = healthyTotalCurrentScoreData
			this.healthyCurrentScoreNumberData = healthyCurrentScoreNumberData.toFixed(2)
			this.healthyTotalCurrentScoreNumberData = healthyTotalCurrentScoreNumberData.toFixed(2)
		},
		changeFunc(val) {
			this.type = val
		},
		clickWeightDateShow() {
			this.selectWeightDateShow = true
		},
		clickShoWeight(data) {
			this.setHealthyVux({ key: 'showWeight', value: data })
		},
		clickRecordWeight() {
			if (!this.bmi.weight_target) {
				this.$miRouter.push({
					path: '/pages_health_pomr/weight_target',
					query: {
						height: this.bmi.height,
						weight: this.bmi.weight
					}
				})
			} else {
				this.scrollLeft = this.bmi.weight
				this.physicalRecordParams.number = this.bmi.weight
				this.recordWeightShow = true
			}
			// uni.$u.toast('功能建设中，请期待')
		},
		/**
		 * 滑动时触发
		 */
		scroll(val) {
			this.physicalRecordParams.number = val
		},
		confirmWeightDate(val) {
			this.physicalRecordParams.date = uni.$u.timeFormat(val.value, 'yyyy-mm-dd')
			this.selectWeightDateShow = false
		},
		determinChoose() {
			this.getAddPhysicalRecord(this.physicalRecordParams)
		},
		getAddPhysicalRecord(params) {
			this.$showLoading({
				title: '修改中....',
				mask: true
			})
			getApiAddPhysicalRecord(params)
				.then(async res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.recordWeightShow = false
						this.$emit('confirmGetHealthyIndex')
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err)
				})
		},
		funWeightColor(bmi) {
			let params = {
				color: '',
				bgColor: ''
			}
			if (bmi < 18.5) {
				params.color = '#30c3fc'
				params.bgColor = '#f8f8f8'
			} else if (bmi <= 24) {
				params.color = '#18e6a2'
				params.bgColor = '#dcfbf1'
			} else if (bmi < 28) {
				params.color = '#ffa149'
				params.bgColor = '#f8f8f8'
			} else if (bmi >= 28) {
				params.color = '#ff7d67'
				params.bgColor = '#f8f8f8'
			}
			return params
		},
		healthScoreTips() {
			this.showhealthScoreTips = true
		},
		funHealthyName(key) {
			let name = ''
			switch (key) {
				case 'bmi':
					name = 'bmi'
					break
				case 'energy':
					name = '能量摄入'
					break
				case 'food':
					name = '食物多样性'
					break
				case 'nutrition':
					name = '营养均衡'
					break
				case 'sport':
					name = '运动情况'
					break
				default:
					break
			}
			return name
		}
	}
}
</script>

<style lang="scss" scoped>
.healthScoreTips-box {
	// color: ;
	// position: relative;
	// z-index: 10000;
}
.ls-card {
	padding: 30rpx;
	border-radius: 20rpx;
	background-color: #ffffff;

	.nutrition-box {
		background-color: #f6f7fb;
		border-radius: 16rpx;
	}
}

.header {
	&--left {
		width: 280rpx;
		height: 380rpx;

		.arcbar-box {
			width: 100%;
			height: 150rpx;
			position: relative;

			.position {
				top: 30rpx;
				left: -90rpx;
				width: 400rpx;
				height: 220rpx;
				position: absolute;
			}
		}
		.bmi-text {
			font-size: 20rpx;
			padding: 7rpx 10rpx 7rpx 10rpx;
			color: #11e69e;
			background-color: #dcfbf1;
			border-radius: 16rpx;
		}
		.u-percentage-box {
			.u-percentage-slot {
				padding: 1px 5px;
				background-color: $u-warning;
				color: #fff;
				border-radius: 100px;
				font-size: 10px;
				margin-right: -5px;
			}
		}
	}

	&--right {
		width: 370rpx;
		height: 380rpx;
		position: relative;

		.tag {
			margin-right: 35rpx;
			border-radius: 10rpx;
			overflow: hidden;

			.tag-item {
				width: 74rpx;
				height: 42rpx;
				background: #e6e6e6;
				text-align: center;
				color: #999;
			}

			.select {
				color: #ffffff;
				background-color: $color-primary;
			}
		}

		.header-right-top {
			position: absolute;
			z-index: 5;
			width: 100%;
			display: flex;
			justify-content: space-between;
			// flex col-center row-between
		}
		.radar-box {
			position: relative;
			position: absolute;
			top: 60rpx;
			left: -10rpx;
			width: 350rpx;
			height: 350rpx;
			.num {
				top: 50%;
				left: 50%;
				// color: #FFFFFF;
				// z-index: 1;
				font-size: 38rpx;
				font-weight: bold;
				position: absolute;
				transform: translate(-50%, -50%);
			}

			.location {
				position: absolute;
				// z-index: 1;
				font-size: 20rpx;
				color: #1d201e;
			}

			.text-1 {
				top: 40rpx;
				left: 140rpx;
			}

			.text-2 {
				width: 50rpx;
				height: 80rpx;
				white-space: pre-wrap;
				top: 120rpx;
				left: 290rpx;
			}

			.text-3 {
				top: 270rpx;
				left: 250rpx;
			}

			.text-4 {
				top: 270rpx;
				left: 30rpx;
			}

			.text-5 {
				width: 50rpx;
				height: 80rpx;
				white-space: pre-wrap;
				top: 120rpx;
				left: 30rpx;
			}
		}
	}
	.popup-weight-box {
		.weight-text {
			font-size: 60rpx;
		}
		.select-weight-box {
			display: flex;
			justify-content: center;
			align-items: center;
			.select-weight-text {
				display: flex;
				padding: 3rpx 20rpx 3rpx 40rpx;
				background-color: #f6f8f8;
				border-radius: 25rpx;
				text-align: center;
				line-height: 50rpx;
				.icon-arrow-down-filling {
					padding-left: 20rpx;
					color: #a8acac;
				}
			}
		}
	}
}
</style>
