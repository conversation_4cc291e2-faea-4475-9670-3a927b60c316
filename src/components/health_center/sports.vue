<template>
	<view class="sports">
		<view class="ls-card m-t-20">
			<view class="flex row-between md">
				<view>
					<text class="f-w-500">今日运动</text>
					<!-- <text class="mini muted">（建议每天步行6000步）</text> -->
				</view>
				<!-- <view class="xs muted flex col-center" @click="gotoSportDetail">
					查看更多
					<u-icon name="arrow-right" size="24rpx"></u-icon>
				</view> -->
			</view>
			<!-- <view class="flex row-around m-t-20"> -->
			<!--  <view class="flex">
          <view class="flex flex-col flex-center">
            <u-image :src="themeImgPath.img_bundle_hot_5" width="60rpx" height="60rpx"></u-image>
          </view>
          <view class="m-l-10">
            <view class="muted xs">消耗能量</view>
            <view class="xxl f-w-500">
              {{ sportsData.sports_energy_kcal }}
              <text class="xxs muted">kcal</text>
            </view>
          </view>
        </view> -->
			<!--    <view class="flex">
          <view class="flex flex-col flex-center">
            <u-image :src="themeImgPath.img_bushu.png" width="60rpx" height="60rpx"></u-image>
          </view>
          <view class="m-l-10">
            <view class="muted xs">相当于慢跑</view>
            <view class="xxl f-w-500">
              {{ energyMin }}
              <text class="xxs muted">min</text>
            </view>
          </view>
        </view> -->
			<!-- </view> -->
			<view class="classification m-t-30 flex col-center row-between">
				<view class="motion-consume flex row-between col-center">
					<view class="classification-item m-t-20 row-between m-b-10">
						<view class="flex col-center">
							<image :src="themeImgPath.img_motion_hot" class="yundong"></image>
							<text class="muted xs">运动消耗能量</text>
						</view>
						<view class="">{{ sportsData.use_energy_kcal }}kcal</view>
					</view>
					<view class="motion-btn p-t-20" @click="pathAddSports">
						<u-button
							text="记录运动"
							size="small"
							shape="circle"
							:color="variables.bgLinearGradient1"
						></u-button>
					</view>
					<!-- <u-line-progress
						:percentage="useEnergyKcalPercentage"
						height="15"
						:showText="false"
						activeColor="#11e69e"
					></u-line-progress> -->
				</view>
			</view>
			<view class="sport" v-if="sportList.length">
				<!-- <view
					class="sport-list-item flex row-between col-center"
					v-for="(item, index) in sportList"
					:key="index"
					@click="changeSportReport(item)"
				>
					<view class="flex col-center">
						<text class="dot"></text>
						<text class="m-l-10">{{ item.name }}</text>
					</view>
					<text class="line"></text>
					<view class="xs flex col-center m-l-10">
						<text class="grey">{{ item.use_count }}分钟 ·</text>
						<text>{{ item.use_energy_kcal }}kcal</text>
						<u-icon name="arrow-right" color="#8f9295" size="24rpx"></u-icon>
					</view>
				</view> -->
				<view 
					class="sport-list-item flex row-between col-center"
					v-for="(item, index) in sportList"
					:key="index"
					@click="changeSportReport(item)"
				>
					<view class="flex">
						<text>{{ item.name }}</text>
					</view>
					<view class="flex">
						<text class="grey">{{ item.use_count }}分钟 ·</text>
						<text class="m-l-6">{{ item.use_energy_kcal }} kcal</text>
						<u-icon class="m-l-10" name="arrow-right" color="#8f9295" size="24rpx"></u-icon>
					</view>
				</view>
			</view>
			<view class="muted xs p-t-25">根据你的运动喜好推荐，请判断身体情况适当运动</view>
			<!-- 推荐运动 -->
			<view class="recommended-sport m-t-20" v-if="recommendedSportList.length|| true">
				<view 
					class="recommended-sport-item flex row-between col-center"
					v-for="(item, index) in recommendedSportList"
					:key="index"
					@click="showAddSportPopup(item)"
				>
					<view class="flex">
						<text>{{ item.name }}</text>
					</view>
					<view class="flex">
						<text class="grey">{{ item.use_count }}分钟 ·</text>
						<text class="m-l-6">{{ item.use_energy_kcal }} kcal</text>
						<u-icon class="m-l-10" name="arrow-right" color="#8f9295" size="24rpx"></u-icon>
					</view>
				</view>
			</view>
		</view>
		<u-popup :show="dietRecordShow" closeable :round="40" @close="dietRecordShow = false">
			<!-- <view class="popup-sports-box">
				<view class="flex p-30">
					<view
						class="custom-style"
						:style="{
							backgroundColor: sportsModifyItem.color
						}"
						v-else
					>
						{{ sportsModifyItem.name ? sportsModifyItem.name.substring(0, 1) : '' }}
					</view>
					<view class="flex flex-col row-center m-l-20">
						<view class="flex col-center">
							<view class="name-box">
								<text>{{ sportsModifyItem.name }}</text>
							</view>
						</view>
						<view class="m-t-10">
							<text class="xs primary">热量{{ sportsModifyItem.use_energy_kcal }}</text>
							<text class="muted xs p-r-20">千卡/100g</text>
						</view>
					</view>
				</view>
				<view class="content-wrapp">
					<view class="content-box">
						<view class="content-left">
							<text class="content">{{ scrollLeft }}</text>
							<text class="muted sm p-l-15">分钟</text>
						</view>
						<view class="line"></view>
						<view class="content-right">
							<text class="muted sm p-r-15">消耗</text>
							<text class="content">{{ energyKcalValue }}</text>
							<text class="muted sm p-l-15">kcal</text>
						</view>
					</view>
				</view>
				<scroll-choose @scroll="scroll" :scrollStart="0" :scrollEnd="450" :scrollLeft="scrollLeft" :maginL="5"></scroll-choose>
				<view class="text-center"><text class="muted sm">拖动分钟到0视为删除当前运动</text></view>
				<u-button shape="circle" hover-class="none" :loading="false" :custom-style="customStyleCropper" @click="clickDetermine">
					保存
				</u-button>
			</view> -->
			<view class="popup-sports-box">
				<view class="text-center p-20 m-t-10"><view class="lg">选择运动时长</view></view>
				<view class="content-wrapp">
					<view class="content-box">
						<view class="content-left">
							<text class="content">{{ scrollLeft }}</text>
							<text class="muted sm p-l-15">分钟</text>
						</view>
						<view class="line"></view>
						<view class="content-right">
							<text class="muted sm p-r-15">消耗</text>
							<text class="content">{{ energyKcalValue }}</text>
							<text class="muted sm p-l-15">kcal</text>
						</view>
					</view>
				</view>
				<!-- <view class="exercise-duration flex col-baseline row-center primary">
					<text class="xxxl">{{ scrollLeft }}</text>
					<text class="sm p-l-15">分钟</text>
				</view> -->
				<!-- 刻度 -->
				<scroll-choose @scroll="scroll" :scrollStart="0" :scrollEnd="200" :scrollLeft="scrollLeft" :maginL="5"></scroll-choose>
				<view class="text-center"><text class="muted sm">拖动分钟到0视为删除当前运动</text></view>
				<u-button shape="circle" hover-class="none" :loading="false" :custom-style="customStyleCropper" type="primary" @click="clickDetermine">
					确定
				</u-button>
			</view>
		</u-popup>
		<u-modal
			:show="deleteSports"
			title="提示"
			confirmColor="#ff6b6c"
			confirmText="删除"
			@confirm="clickDeleteSportsDiet"
			:showCancelButton="true"
			cancelText="暂不删除"
			@cancel="deleteSports = false"
			class="modal-box"
		>
			<view class="p-b-20">确定要删除该运动吗？</view>
		</u-modal>

		<!-- 弹窗 -->
		<u-popup v-if="showAddSport" mode="bottom" :show="showAddSport" closeable :round="40" @close="showAddSport = false" @open="openAddSport">
			<view class="popup-sports-box m-b-20">
				<view class="text-center p-20 m-t-10 m-b-40"><view class="lg">选择运动时长</view></view>
				<view class="content-wrapp p-b-40">
					<view class="content-box">
						<view class="content-left">
							<text class="content">{{ duration }}</text>
							<text class="muted sm p-l-15">分钟</text>
						</view>
						<view class="line"></view>
						<view class="content-right">
							<text class="muted sm p-r-15">消耗</text>
							<text class="content">{{ energyKcalValue }}</text>
							<text class="muted sm p-l-15">kcal</text>
						</view>
					</view>
				</view>
				<!-- <view class="exercise-duration flex col-baseline row-center primary">
					<text class="xxxl">{{ duration }}</text>
					<text class="sm p-l-15">分钟</text>
				</view> -->
				<!-- 刻度 -->
				<scroll-choose @scroll="scroll" :scrollStart="0" :scrollEnd="200" :scrollLeft="duration" :maginL="5"></scroll-choose>
				<view class="text-center" :style="duration===0 ? {visibility:'visible'} : {visibility: 'hidden'}"><text class="muted sm">运动时长不能为0</text></view>
				<u-button shape="circle" hover-class="none" :loading="false" :custom-style="customStyleCropper" type="primary" @click="showSportsAdd()" :disabled="duration===0 ? true : false">
					确定
				</u-button>
			</view>
		</u-popup>
	</view>
</template>
<script>
import scrollChoose from '@/components/scroll-choose/scroll-choose'
import { getApiSportsModify, getApiSportsdel, getApiSportsAdd } from '@/api/healthy.js'
export default {
	props: {
		healthyIndexData: {
			type: Object,
			default: {}
		}
	},
	components: {
		scrollChoose
	},
	data() {
		return {
			duration: 0, //页面显示
			showAddSport: false,
			imgPath: this.$imgPath,
			sportsData: {}, //能量
			sportList: [],
			recommendedExercise: {}, // 暂存推荐运动
			recommendedSportList: [],
			useEnergyKcalPercentage: 0, // 需要消耗的能量百分比
			energyMin: 0,
			sportsModifyItem: {},
			scrollLeft: 0, //初始值
			customStyleCropper: {
				width: '670rpx',
				marginTop: '20rpx',
				marginBottom: '20rpx',
				color: '#fff',
				border: 'none'
			},
			dietRecordShow: false,
			deleteSports: false
		}
	},
	watch: {
		healthyIndexData(newValue, oldValue) {
			console.log(newValue)
			this.sportsData = newValue.sports
			this.sportList = newValue.sports.sport_data
			this.recommendedSportList = newValue.sports.recommended_sports
			this.init()
		}
	},
	computed: {
		energyKcalValue() {
			let num = 0
			// 如果是
			if (this.dietRecordShow) {
				num = Number((this.sportsModifyItem.use_energy_kcal / this.sportsModifyItem.use_count).toFixed(2))
				return Number((Number(this.scrollLeft) * num).toFixed(1))
			} else if (this.showAddSport) {
				num = Number((this.recommendedExercise.use_energy_kcal / this.recommendedExercise.use_count).toFixed(2))
				return Number((Number(this.duration) * num).toFixed(1))
			}
		},
	},
	onShow() {
	},
	methods: {
		changeSportReport(item) {
			this.scrollLeft = item.use_count
			this.sportsModifyItem = item
			this.dietRecordShow = true
		},
		showAddSportPopup(item) {
			this.duration = item.use_count
			this.recommendedExercise= item
			this.showAddSport = true
		},
		showSportsAdd() {
			if (!this.duration) return uni.$u.toast('请选择运动分钟')
			let params = {
				recommend_id: this.recommendedExercise.id,
				count: this.duration,
				intensity: this.recommendedExercise.intensity,
				name: this.recommendedExercise.name,
				energy_kcal: Number(((this.duration * this.recommendedExercise.use_energy_kcal) / this.recommendedExercise.use_count).toFixed(2))
			}
			this.getSportsAdd(params)
		},
		// 添加运动数据
		getSportsAdd(params) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiSportsAdd(params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.showAddSport = false
						uni.$u.toast('保存成功')
						this.$emit('refresh')
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
		init() {
			// this.sportsData = this.healthyIndexData.sports //能量
			// 需要消耗的能量百分比
			this.useEnergyKcalPercentage =
				this.sportsData.use_energy_kcal / this.sportsData.sports_energy_kcal
					? Number(((this.sportsData.use_energy_kcal / this.sportsData.sports_energy_kcal) * 100).toFixed(2))
					: 0
			// 换算成分钟
			// let energyMinTotal = 300 / 40
			// 固定 每300卡跑40分钟
			// this.energyMin = (this.healthyIndexData.sports.sports_energy_kcal / energyMinTotal).toFixed(0)
		},
		// 修改运动
		clickModifySport(item) {
			this.scrollLeft = this.sportsModifyItem.use_count
			this.dietRecordShow = true
		},
		/**
		 * 滑动时触发
		 */
		scroll(val) {
			switch (true) {
				case this.dietRecordShow:
					this.scrollLeft = val
					break
				case this.showAddSport:
					this.duration = val
					break
			}
		},
		clickDetermine() {
			if (this.scrollLeft === 0) {
				this.deleteSports = true
			} else {
				let params = {
					id: this.sportsModifyItem.id,
					count: this.scrollLeft
				}
				this.getSportsModify(params)
			}
		},
		getSportsModify(params) {
			this.$showLoading({
				title: '修改中....',
				mask: true
			})
			getApiSportsModify(params)
				.then(async res => {
					uni.hideLoading()
					if (res.code == 0) {
						uni.$u.toast('修改成功')
						this.dietRecordShow = false
						this.$emit('confirmGetHealthyIndex')
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.$u.toast(err)
				})
		},
		clickDeleteSportsDiet() {
			this.getSportsdel({ id: this.sportsModifyItem.id })
		},
		// 删除运动数据
		getSportsdel(params) {
			this.$showLoading({
				title: '加载中...',
				mask: true
			})
			getApiSportsdel(params)
				.then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.deleteSports = false
						this.dietRecordShow = false
						this.$emit('confirmGetHealthyIndex')
					} else {
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.msg)
				})
		},
		gotoSportDetail() {
			this.$miRouter.push('/pages_health_pomr/motion_archives/index')
		},
		pathAddSports() {
			this.$miRouter.push({
				path: '/pages_health/healthy/sport/add_sport',
				query: {
					weight: this.healthyIndexData.bmi.weight
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.sports {
	.ls-card {
		padding: 30rpx;
		border-radius: 20rpx;
		background-color: #ffffff;
	}
	.classification {
		// border-top: $border-base;

		.classification-item {
			width: 60%;
			// padding: 0 25rpx;
			display: flex;
			align-items: center;

			image {
				width: 34rpx;
				height: 34rpx;
				margin-right: 14rpx;
			}
		}

		.motion-consume {
			width: 100%;
		}

		.motion-btn {
			width: 30%;
		}
	}
	.yundong {
		width: 46rpx;
		height: 46rpx;
	}
	.recommended-sport {
		.recommended-sport-item {
			background-color: #F6F7FB;
			width: 100%;
			padding: 20rpx 20rpx;
			margin-bottom: 20rpx;
			border-radius: 16rpx;
		}
	}
	.sport {
		margin-top: 20rpx;
		// max-height: 500rpx;
		// overflow: auto;
		.sport-list-item {
			background-color: #FBF8F6;
			width: 100%;
			padding: 20rpx 20rpx;
			margin-bottom: 20rpx;
			border-radius: 16rpx;
			.dot {
				width: 18rpx;
				height: 18rpx;
				background-color: #ffffff;
				border: 6rpx solid #11e69e;
				border-radius: 40rpx;
			}
			.line {
				border: 2rpx dashed #e4e4e4;
				min-width: 30%;
				max-width: 50%;
			}
		}
		.sport-list-item:last-child {
			margin-bottom: 0;
		}
	}
	.custom-style {
		width: 90rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background-color: #11e69e;
		color: #ffffff;
		font-size: 42rpx;
		border-radius: 14rpx;
	}
	.popup-sports-box {
		.name-box {
			display: flex;
			align-items: center;
			font-weight: bold;
		}
		.content-wrapp {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-bottom: 40rpx;
			.content-box {
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 670rpx;
				height: 140rpx;
				background-color: #f6f7fb;
				border-radius: 20rpx;
				.content-left {
					padding-left: 70rpx;
					.content {
						font-size: 60rpx;
					}
				}
				.line {
					width: 2rpx;
					height: 80rpx;
					background-color: #e9eaf1;
				}
				.content-right {
					padding-right: 70rpx;
					.content {
						font-size: 60rpx;
					}
				}
			}
		}
	}
}
</style>
