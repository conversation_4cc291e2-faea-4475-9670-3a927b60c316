<template>
  <view class="ingestionMealFood">
    <view class="ls-card m-t-20">
      <view class="flex row-between md f-w-500">
        <text>总摄入量</text>
        <view class="xs muted flex col-center" @click="pathMealIngestion">摄入明细</view>
      </view>
      <view class="nutrition-box flex flex-wrap flex-center row-around p-t-20 p-b-20 p-l-30 m-t-20 m-b-20">
        <view class="p-l-10 p-b-10" style="width: 50%">
          <text class="dot m-r-10" style="background-color: #11e69e"></text>
          <text class="m-b-5 xxs muted">推荐摄入</text>
          <text class="xxs p-l-10 f-w-500">{{ mealIntakeData.need_energy_kcal }} kcal</text>
        </view>
        <view class="p-l-10 p-b-10" style="width: 50%">
          <text class="dot m-r-10" style="background-color: #ff5757"></text>
          <text class="m-b-5 xxs muted">摄入超标</text>
          <text class="xxs p-l-10 f-w-500">{{ beyondEnergy }} kcal</text>
        </view>
        <view class="p-l-10" style="width: 50%">
          <text class="dot m-r-10" style="background-color: #16e0f2"></text>
          <text class="m-b-5 xxs muted">合计摄入</text>
          <text class="xxs p-l-10 f-w-500">{{ mealIntakeData.energy_kcal }} kcal</text>
        </view>
        <view class="p-l-10" style="width: 50%">
          <text class="dot m-r-10" style="background-color: #d46bf7"></text>
          <text class="m-b-5 xxs muted">合计消费</text>
          <text class="xxs p-l-10 f-w-500">
            <!-- 150 kcal -->
            <price-format :price="mealIntakeData.intake_total_money" :size="32" color="#FF5757"></price-format>
          </text>
        </view>
      </view>
      <view class="flex row-between md f-w-500 p-t-20" style="border-top: 1px dashed #ecf1f5">
        <text>摄入餐段</text>
        <view class="xs muted flex col-center" @click="$miRouter.push('/pages_order/order/order_lists')">
          查看订单
          <u-icon name="arrow-right" size="24rpx"></u-icon>
        </view>
      </view>
			<view class="" v-if="mealIntakeData.is_food_data">
      <view class="m-t-10 p-t-20" v-for="(mealTypeItem, mealTypeIndex) in mealIntakeData.meal_type_data" :key="mealTypeIndex">
        <view class="flex row-between md f-w-500" v-if="Object.keys(mealTypeItem.food_data).length">
          <view class="flex flex-center">
            <!-- excess_energy_kcal 有值就是超标 显示红色 没有值就是没超标显示绿色 -->
            <text class="dot m-r-10" v-if="mealTypeItem.excess_energy_kcal" style="background-color: #ff5757"></text>
            <text class="dot m-r-10" v-else style="background-color: #11e69e"></text>
            <text>{{ funMealTypeName(mealTypeItem.meal_type) }}</text>
            <u-icon name="question-circle" size="28" @click="clickMealTips()"></u-icon>
          </view>
        </view>
        <view class="item-goods">
          <view class="" v-for="(foodItem, foodKey, foodIndex) in mealTypeItem.food_data" :key="foodIndex">
            <view class="" v-for="(foodMealItem, foodMealIndex) in foodItem" :key="foodMealIndex">
              <scroll-view class="goods-scroll" :scroll-x="true">
                <view class="flex">
                  <view class="m-r-20" v-for="(foodMealDataItem, foodMealDataIndex) in foodMealItem" :key="foodMealDataIndex">
                    <!-- <view class="goods-item"  v-for="(foodMealDataValueItem,foodMealDataValueItemDataIndex) in foodMealDataItem" :key="foodMealDataValueItemDataIndex"> -->
                    <view class="goods-price">
                      <!-- <price-format :price="foodMealItem.total_money" :size="32" color=""></price-format> -->
                    </view>
                    <u-image width="110rpx" height="110rpx" radius="10" :src="foodMealDataItem.image"></u-image>
                    <view class="line-1 xs m-t-14 food-name">{{ foodMealDataItem.name }}</view>
                    <!-- </view> -->
                  </view>
                </view>
              </scroll-view>
            </view>
          </view>
        </view>
        <view class="classification flex flex-1 flex-center row-around" v-if="Object.keys(mealTypeItem.food_data).length">
          <view class="classification-item m-t-20">
            <view><image :src="themeImgPath.img_hot_2"></image></view>
            <view>
              <view class="m-b-5 xxs f-w-500">推荐摄入</view>
              <view class="xxs">{{ mealTypeItem.need_energy_kcal }} kcal</view>
            </view>
          </view>
          <view class="classification-item m-t-20">
            <view><image :src="themeImgPath.img_hot_3"></image></view>
            <view>
              <view class="m-b-5 xxs f-w-500">单餐摄入</view>
              <view class="xxs">{{ mealTypeItem.energy_kcal }} kcal</view>
            </view>
          </view>
          <view class="classification-item m-t-20">
            <view><image :src="themeImgPath.img_hot_4"></image></view>
            <view>
              <view class="m-b-5 xxs f-w-500">摄入超标</view>
              <view class="xxs u-error">{{ mealTypeItem.excess_energy_kcal }} kcal</view>
            </view>
          </view>
        </view>
      </view>
			</view>
			<view class="" v-else>
				<view class="text-center p-40 muted">
						暂无菜品信息
				</view>
			</view>
      <view class="text-center muted" @click="pathMealSummary">查看更多</view>
    </view>
    <u-modal
      :show="showMealTips"
      closeOnClickOverlay
      :confirmColor="variables.colorPrimary"
      confirmText="我知道了"
      @confirm="showMealTips = false"
      @close="showMealTips = false"
    >
      <view class="slot-content"><rich-text nodes="凌晨餐归入早餐<br>下午茶归入午餐<br>宵夜归入晚餐"></rich-text></view>
    </u-modal>
  </view>
</template>

<script>
export default {
  props: {
    healthyIndexData: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      imgPath: this.$imgPath,
      mealIntakeData: {}, //菜品
      beyondEnergy: 0,
      showMealTips: false //提示
    }
  },
  created() {
    this.init()
  },
  onShow() {},
  mounted() {},
  methods: {
    init() {
      this.mealIntakeData = this.healthyIndexData.intake //菜品
      // 摄入超标能量
      let energyNum = this.mealIntakeData.energy_kcal - this.mealIntakeData.need_energy_kcal
      this.beyondEnergy = energyNum >= 0 ? (energyNum).toFixed(2) : 0
    },
    funMealTypeName(meal) {
      let name = ''
      switch (meal) {
        case 'breakfast':
          name = '早餐'
          break
        case 'lunch':
          name = '午餐'
          break
        case 'dinner':
          name = '晚餐'
          break
        default:
          break
      }
      return name
    },
    funFoodLength(data) {
      let len = 0
      for (let key in data) {
        len = data[key].length
      }
      return len
    },
    pathMealIngestion() {
      this.$miRouter.push('/pages_health/healthy/meal/meal_ingestion_detailed')
    },
    pathMealSummary() {
      this.$miRouter.push('/pages_health/healthy/meal/meal_segment_summary')
    },
    clickMealTips() {
      this.showMealTips = true
    }
  }
}
</script>

<style lang="scss" scoped>
.ingestionMealFood {
  .ls-card {
    padding: 30rpx;
    border-radius: 20rpx;
    background-color: #ffffff;

    .nutrition-box {
      background-color: #f6f7fb;
      border-radius: 16rpx;
    }
  }
  .dot {
    display: inline-block;
    width: 15rpx;
    height: 15rpx;
    border-radius: 10rpx;
  }
  .classification {
    border-top: $border-base;

    .classification-item {
      // padding: 0 25rpx;
      display: flex;
      align-items: center;

			image {
				width: 34rpx;
				height: 34rpx;
				margin-right: 14rpx;
			}
		}
	}
	.item-goods {
		position: relative;
		.goods-scroll {
			// height: 190rpx;
			// width: 100%;
			white-space: nowrap;
		}
		.goods-lists {
			padding: 20rpx 0;

			.goods-item {
				margin-right: 15rpx;
			}
		}
		.goods-price {
			position: absolute;
			width: 150rpx;
			height: 110rpx;
			line-height: 110rpx;
			text-align: right;
			right: 0;
			top: 55rpx;
			z-index: 10;
		}
	}
	.goods-num {
		position: absolute;
		width: 150rpx;
		height: 110rpx;
		line-height: 110rpx;
		text-align: right;
		background: linear-gradient(90deg, rgba(256, 256, 256, 0) 0%, #ffffff 100%);
		right: 0;
		top: 20rpx;
		z-index: 10;
		opacity: 0.7;
	}
	.food-name{
		text-align: center;
		white-space:nowrap;  //段落中的文本不进行换行
		overflow:hidden;  //超出宽度部分隐藏
		text-overflow:ellipsis;  //当文本溢出时，要发生的事。有三个值：  clip -  修剪文本；ellipsis - 显示省略号来代表被修剪的文本；string - 使用给定的字符串来代表被修剪的文本
		width: 100rpx;  //宽度自己设定，一定要有
	}
}
</style>
