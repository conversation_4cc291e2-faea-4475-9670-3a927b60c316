<template>
  <view class="coupon-box">
    <view class="title md f-w-500">一颗糖甜品店</view>
    <view class="coupon-card flex col-center m-t-20" :class="'type-' + type" @click="onCardSelf">
      <!-- Aside -->
      <view class="coupon-card__aside primary">
        <view>
          <text class="f-w-500" style="font-size: 100rpx">10</text>
          <text class="xxl">元</text>
        </view>
        <view class="desc xxs">满10元可用</view>
      </view>

      <!-- Main -->
      <view class="coupon-card__main flex col-center">
        <view class="coupon-card__main-section">
          <view class="nr f-w-500 primary m-b-6">{{ name || '午餐满减券' }}</view>
          <view class="xxs f-w-500 lighter m-b-6">{{ time || '限账户钱包可用' }}</view>
          <view class="xxs f-w-500 lighter m-b-6">{{ scene || '限午餐可用' }}</view>
          <view class="xxs lighter line-1">{{ scene || '有效期至：2021-12-01  23:25:590099999' }}</view>

          <template v-if="desc">
            <view class="flex xxs m-t-24" @click="showTips = true">
              <text class="m-r-10">使用说明</text>
              <u-icon name="arrow-right" size="22rpx"></u-icon>
            </view>
          </template>
        </view>

        <template v-if="mode === 'radio'">
          <u-checkbox
            class="u-checkbox"
            v-model="checked"
            shape="circle"
            :name="id"
            :disabled="false"
            :active-color="themeColor"
          />
        </template>

        <template v-if="mode === 'get'">
          <image class="pendant pendant--receive" v-if="gotten" src="/static/images/coupon_receive.png" />
        </template>
        <template v-if="button.show">
          <u-button
            :text="button.name"
            @click="onButton"
            :disabled="button.disable"
            type="primary"
            color="#FF790C"
            shape="circle"
          ></u-button>
        </template>
      </view>
    </view>

    <!-- Components Modal Start -->
    <!-- 弹窗-余额上限 -->
    <u-modal :show="showTips" :title="'提示'" confirmColor="#5A6080">
      <view class="xl text-center" v-html="content"></view>
    </u-modal>
    <!-- Components Modal End -->
  </view>
</template>

<script>
/**
 * @description 优惠券卡片
 * @property {String} mode 卡片模式[normal|get|radio] (默认值: normal)
 *   注：radio模式下需要配合u-radio-group标签
 *
 */

export default {
  name: 'CouponCard',

  props: {
    // 卡片模式
    mode: {
      type: String,
      default: 'normal'
    },

    // 优惠券ID
    couponId: {
      type: Number | String,
      default: ''
    },

    // 名称
    name: {
      type: String,
      default: ''
    },

    // 金额
    money: {
      type: String | Number,
      default: 0
    },

    // 使用条件
    condition: {
      type: String,
      default: ''
    },

    // 使用时间
    time: {
      type: String,
      required: true
    },

    // 使用场景
    scene: {
      type: String,
      // used == 已经使用
      // expired == 已过期
      default: ''
    },

    // 按钮名称
    button: {
      type: Object | Boolean,
      // default: {
      // 	name: '领取',
      // 	disabled: false,
      // }
      default: true
    },

    // 是否已得到【get模式】
    gotten: {
      type: Boolean,
      default: false
    },

    // 是否选中【radio模式】
    checked: {
      type: Boolean,
      default: false
    },

    // 使用说明
    desc: {
      type: Object | Boolean,
      default: true
    },

    type: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      showTips: false
    }
  },

  computed: {
    // 优惠券卡片ID
    id() {
      return this.couponId
    }
  },

  methods: {
    // 领取优惠券
    onButton() {
      if (!this.isLogin) {
        this.$miRouter.push('/pages/login/login')
        return
      }
      // if (this.button.disable) return
      this.$emit('button', this.couponId)
    }
  }
}
</script>

<style lang="scss" scoped>
.coupon-box {
  padding: 30rpx;
  border-radius: 20rpx;
  background-color: #ffffff;
  margin-bottom: 30rpx;

  .coupon-card {
    height: 240rpx;
    padding: 30rpx;
    border-radius: 20rpx;
    background-color: rgba(255, 156, 75, 0.1);
    -webkit-mask: radial-gradient(circle at left center, transparent 20rpx, red 20rpx);
    background-size: 122rpx 122rpx;
    background-repeat: no-repeat;
    background-position: 102% -30rpx;

    .primary {
      color: #ff790c;
    }

    &__aside {
      width: 200rpx;

      .desc {
        border-radius: 20rpx;
        padding: 4rpx 16rpx;
        display: inline-block;
        background-color: rgba(255, 156, 75, 0.2);
      }
    }

    &__main-section {
      width: 330rpx;
    }
  }

  .type-1 {
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%); //兼容
    background-image: url(https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/images/used.png);
  }

  .type-2 {
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%); //兼容
    background-image: url(https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/images/expired.png);
  }

  // 立即领取修改竖样式
  ::v-deep button {
    width: 20rpx;
    height: 160rpx;
    text {
      line-height: 30rpx;
      white-space: pre-wrap;
      font-size: $font-size-sm !important;
    }
  }
}
</style>
