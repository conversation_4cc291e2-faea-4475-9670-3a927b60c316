<template>
  <view v-if="showSettingIcon" class="setting-popup-wrapper">
    <view class="setting-icon">
      <u-icon ref="settingRef" name="setting" size="50" @click="showSettingHandle"></u-icon>
    </view>
    <!-- 弹窗设置baseUrl -->
    <u-popup :show="showSetting" mode="bottom" :round="20" :closeOnClickOverlay="true" :closeable="true" @close="closePopupHandle" @open="openPopupHandle">
      <view class="setting-popup-title">设置</view>
      <view class="setting-popup-content">
        <view class="block-popup block-flex ">
          <view class="tip-text">是否自定义请求地址:</view>
          <u-switch v-model="isCustom" size="35" activeColor="#2adba7" ></u-switch>
        </view>
        <view class="block-popup block-flex">
          <view class="tip-text">请求地址:</view>
          <u-textarea :disabled="!isCustom" v-model="baseUrl" :height="100" placeholder="请输入内容" ></u-textarea>
        </view>
        <view class="block-popup">
          <u-button class="save-btn" type="primary" :color="variables.colorPrimary" text="保 存" @click="saveSettingHandle"></u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  import Cache from '@/utils/cache'
  export default {
    data() {
      return {
        showSettingIcon: false,
        showSetting: false,
        isCustom: false,
        baseUrl: process.env.VUE_APP_MINAPP
      }
    },
    mounted() {
      if (process.env.NODE_ENV === 'development' || (location && location.href.indexOf('debug') > -1)) {
				this.showSettingIcon = true
			}
      const settingUrl = Cache.get('BASEURL')
      if (settingUrl) {
        this.baseUrl = settingUrl
        this.isCustom = true
      }
    },
    methods: {
      showSettingHandle() {
        this.showSetting = true
      },
      closePopupHandle(e) {
        this.showSetting = false
      },
      openPopupHandle(e) {},
      // 保存操作
      saveSettingHandle() {
        if (this.isCustom) {
          this.$store.dispatch('setBaseUrl', this.baseUrl)
        } else {
          this.$store.dispatch('setBaseUrl', '')
        }
        uni.$u.toast('保存成功')
        // uni.getStorageSync(this.getKey(key))
      }
    }
  }
</script>

<style lang="scss">
.setting-popup-wrapper{
  .setting-icon{
    display: inline-block;
    position: fixed;
    right: 30rpx;
    /*  #ifdef  H5  */
    bottom: 130rpx;
    /*  #endif  */
    /*  #ifndef  H5  */
    bottom: 180rpx;
    /*  #endif  */
    z-index: 9;
  }
  .setting-popup-title{
    padding: 20rpx 0;
    font-size: 32rpx;
    text-align: center;
  }
  .setting-popup-content{
    padding: 50rpx 20rpx 60px;
    // height: 30vh;
    .block-popup{
      margin-bottom: 30rpx;
    }
    .tip-text{
      margin: 0 10rpx 0 0;
      font-size: 24rpx;
    }
    .block-flex{
      display: flex;
      align-items: top;
    }
    .save-btn{
      width: 200rpx;
      margin-top: 50rpx;
    }
  }
}
</style>
