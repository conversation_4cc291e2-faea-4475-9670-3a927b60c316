<template>
  <view class="recipe-plain-wrap-banner m-b-20 bg-white" v-if="indexMealPlanList.length">
			<swiper class="swiper-wrapper" :indicator-dots="false" :autoplay="true" @change="changeSwiper">
				<swiper-item v-for="(item, i) in indexMealPlanList" :key="i">
					<view
						:style="{ backgroundImage: 'url(' + item + ')' }"
						class="selected-wrapp selected-item flex row-center flex-col m-b-20"
						@click="clickMenuPlanDetails(item)"
					>
					</view>
				</swiper-item>
			</swiper>
			<!-- 指示器 -->
			<view class="swiper-dot-wrapper flex flex-center">
				<view
					v-for="(j, k) in indexMealPlanList"
					:key="k"
					:class="['swiper-dot', currentSwiper === k ? 'active' : '']"
				></view>
			</view>
		</view>
</template>

<script>
import { apiBookingMemberUserPromotionalPictureUrl } from '@/api/member.js'
import Cache from '@/utils/cache'
import { isEmptyArray } from '@/utils/type.js'
import { deepClone } from '@/utils/util'
export default {
  created() {
		this.getGeneralizationMap()
	},
  props: {
    uiType: {
      type: String,
      default: ''
    },
    pageType: {
      type: String,
      default: ''
    }
  },
  // watch:{
  //   indexMealPlanList(newVal) {
  //     if (newVal) {
  //       console.log('存入数据')
  //     }
  //   }
  // },
  data() {
    return {
      indexMealPlanList: [], // 数据列表
      currentSwiper: 0,
      defaultBannerImg: this.$imgPath.kefu_banner
    }
  },
  methods: {
    getGeneralizationMap() {
			this.$showLoading({
				title: '获取中....',
				mask: true
			})
      let params = {
        user_id: Cache.get('userInfo').user_id,
        company_id: Cache.get('userInfo').company_id,
        ui_type: this.uiType,
      }
			apiBookingMemberUserPromotionalPictureUrl(params)
				.then(res => {
					uni.hideLoading()
					if (res.code === 0) {
            if (isEmptyArray(res.data.urls)) {
              this.$emit('isShow', false)
            }
            if (this.pageType === 'kefu') {
              console.log('defaultBannerImg', this.defaultBannerImg)
              this.indexMealPlanList = res.data.urls
              if (!this.indexMealPlanList.length) {
                this.indexMealPlanList.push(this.defaultBannerImg)
              }
            } else {
              this.indexMealPlanList = deepClone(res.data.urls)
            }
            this.$emit('transfer', res.data.urls)
					} else {
						uni.hideLoading()
						uni.$u.toast(res.msg)
					}
				})
				.catch(err => {
					uni.hideLoading()
					uni.$u.toast(err.message)
				})
		},
    changeSwiper(e) {
			this.currentSwiper = e.detail.current
		},
    clickMenuPlanDetails() {
      if (this.pageType === 'index') {
        // 跳转至会员中心
        this.$miRouter.push({
          path: '/pages_member/member_center/VIP_page',
          query: {
            type: 1
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.recipe-plain-wrap-banner {
  height: 250rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  .swiper-dot-wrapper {
    position: absolute;
    bottom: 5%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 200rpx;
    .swiper-dot {
      width: 35rpx;
      height: 7rpx;
      border-radius: 200rpx;
      margin: 0 4rpx;
      transition: transform 0.3s;
      background-color: #e3e3e3;
      &.active {
        width: 35rpx;
        background-color: $color-primary;
      }
    }
  }
  .swiper-wrapper {
    height: 100%;
    .selected-item {
      padding: 30rpx;
      width: 670rpx;
      height: 250rpx;
      box-sizing: border-box;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
    }
    .selected-wrapp {
      width: 100%;
      border-radius: 10rpx;
      .title {
        font-size: 42rpx;
        font-weight: 500;
        color: #1d201e;
      }
      .text {
        color: #1d201e;
      }
    }
  }
}
</style>
