<template>
  <view class="floating-widget" v-show="showFloatingWidget">
    <view style="position: relative;">
      <u-icon class="det-icon" size="40" name="https://packer-data-files.oss-cn-shenzhen.aliyuncs.com/mapp_static/icons/det.png"  @click="toggleFloatingWidget"></u-icon>
      <img :src="imgInfo.img_url" alt="Floating Icon" class="floating-icon"  @click="gotoTargetPath">
    </view>
  </view>
</template>

<script>
import { mapGetters } from 'vuex'
import { getApiRechargeWalletList } from '@/api/user.js'
import { allMenuList } from '@/constants/menu-list'
import { getMarketingSettings } from '@/api/app.js'

export default {
  data() {
    return {
      showFloatingWidget: false,
      imgInfo: {},
      jumpList: {}, // 权限对应的地址
      popupType: ''
    }
  },
  props: {
    floatingPopupShow: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'marketingSetting', 'isVIP']),
  },
  created() {
    // this.getMarketingSettingHandle()
  },
  watch: {
    imgInfo(newVal) {
      if (newVal && newVal.img_url) {
        this.showFloatingWidget = true
      } else {
        this.showFloatingWidget = false
      }
    },
    // 控制点击关闭后还能显示浮窗
    floatingPopupShow: {
      handler(newVal) {
        // 判断是否是游客以及访客
        console.log('组件v-if判断', newVal, this.isVIP, this.marketingSetting)
        if (!this.$store.state.appoint.isAddressVisitor && !this.$store.state.appoint.isMealVisitor && this.isVIP && this.marketingSetting) {
          this.findFabPopupImg()
        }
      },
      immediate: true
    },
    // 根据isVIP判断是否显示浮窗
    isVIP: {
      handler(newVal) {
        // 判断是否是游客以及访客
        console.log('组件v-if判断', newVal, this.isVIP, this.marketingSetting)
        if (!this.$store.state.appoint.isAddressVisitor && !this.$store.state.appoint.isMealVisitor && this.marketingSetting) {
          this.findFabPopupImg()
        }
      },
      immediate: true
    }
  },
  methods: {
    // async getMarketingSettingHandle() {
    //   const [err, res] = await this.$to(getMarketingSettings({
    //     test: "這是浮窗組件出發的getMarketingSettings"
    //   }))
    //   if (err) {
    //     return
    //   }
    //   if (res.code === 0) {
    //     this.marketingSetting = res.data
    //     if (this.$store.state.app.isVIP) {
    //       this.findFabPopupImg()
    //     }
    //   } else {
    //     // uni.$u.toast(res.msg)
    //   }
    // },
    // 获取当前页面的类型
    getCurrentPagePermission(){
      let path = this.$Route.path
      this.path = path
      let type = ''
      for (let index = 0; index < allMenuList.length; index++) {
        const item = allMenuList[index]
        if(item && item.permission){
          this.jumpList[item.permission] = item
        }
        if (item && item.path && item.path.split('?')[0] === path) {
          type = item.permission
          this.popupType = type
          // break
        }
      }
      this.popupType = type
      return type
    },
    findFabPopupImg() {
      // 判断该项目点是否开启会员功能
      let list = []
      this.getCurrentPagePermission()
      if (this.marketingSetting && this.marketingSetting.popup_list) {
        list = this.marketingSetting.popup_list.filter(item => {
          let is = false
          // show_page_url不为空，
          if (item.show_page_url && item.show_page_url === this.popupType && item.window_type === 'fab_popup') is = true
          // 首页的话show_page_url为空
          if (item.show_page_url == '' && this.path == '/pages/index/index' && item.window_type === 'fab_popup') is = true
          return is
        })
      }
      this.imgInfo = list[0] ? list[0] : {}
      console.log('this.imgInfo', this.imgInfo)
    },
    toggleFloatingWidget() {
      this.showFloatingWidget = false
    },
    gotoTargetPath() {
      this.showFloatingWidget = false
      if (this.imgInfo.jump_type === 'inner') {
        if (this.imgInfo.jump_url && this.jumpList[this.imgInfo.jump_url]) {
          this.handleClickMenu(this.jumpList[this.imgInfo.jump_url])
        }
      } else { // 外部链接
        // 是bocapp 中行标识
        if(this.platform === 'bocapp'){
          if(this.imgInfo.jump_url.includes('BOCBANK')){
            window.c_plugins.merchantBridge.goToNative(
            function () {}, 
            function (err) {}, 
            {
              page: this.imgInfo.jump_url,
            } // 包括BOCBANK:协议类型；xxxx中行加密串类型；传0则返回app首页
            )
          }
          }else{
            // 小程序用外部链接要配H5域名(这里先不做小程序的外部链接)
            // #ifdef H5
            console.log(this.imgInfo.jump_url)
            if (this.imgInfo.jump_url) {
              window.location.href = this.imgInfo.jump_url
            }
            // #endif
          }
      }
    },
    handleClickMenu(item) {
      let _this = this
      console.log('内部跳转', item)
      if (item.type == 'bind_buffet') {
        this.$miRouter.push('/pages/index/index')
      } else if (item.type == 'recharge') {
        this.getRechargeWalletList()
      } else if (item.type == 'reservation') {
        this.SET_SELECT({
          key: 'payment_order_type',
          data: item.type
        })
      }else if (item.type == 'intention_menu') {
        // 因为要跳到选择组织页面
        if (this.$store.state.appoint) {
          const state = this.$store.state.appoint
          if (state.select.intent_org_id && state.select.intent_org_id.id) {
            this.$miRouter.push(item.path)
          } else {
            this.$miRouter.push(item.orgPath)
          }
        } else {
            this.$miRouter.push(item.orgPath)
        }
      } else {
        if (item.path) {
          if (item.type == 'report_meal') {
            this.SET_SELECT({
              key: 'payment_order_type',
              data: item.type
            })
          }
          // 因为有了同一个字段
          if (item.type == 'myReservation') {
            this.SET_SELECT({
              key: 'payment_order_type',
              data: 'reservation'
            })
          }
          if (item.type == 'shop_feedback') {
            this.$miRouter.push({
              path: item.path,
              query: {
                type: 'shop',
                person_no: this.userInfo.person_no // 兼容多用户
              }
            })
            return
          }
          this.$miRouter.push({
            path: item.path,
            query: {
              person_no: this.userInfo.person_no // 兼容多用户
            }
          })
        } else {
        }
      }
    },
    getRechargeWalletList() {
    this.$showLoading({
      title: '获取中....',
      mask: true
    })
    getApiRechargeWalletList()
      .then(res => {
        uni.hideLoading()
        if (res.code == 0) {
          // 如果该手机账号只有一个项目点，且只有一个组织的储值钱包时，不需要选择钱包，直接进入充值页面
          if (res.data && res.data.length == 1 && res.data[0].wallet_list && res.data[0].wallet_list.length == 1) {
            this.setWallet(res.data[0].wallet_list[0])
            this.$miRouter.push({
              path: '/pages_bundle/recharge/recharge',
              query: {
                person_no: this.userInfo.person_no // 兼容多用户
              }
            })
          } else {
            if (res.data && res.data.length) {
              this.$miRouter.push({
                path: '/pages_bundle/recharge/select_wallet',
                query: {
                  routeType: 'index',
                  person_no: this.userInfo.person_no // 兼容多用户
                }
              })
            } else {
              uni.$u.toast('暂无充值钱包')
            }
          }
          // /pages_bundle/recharge/recharge
          // /pages_bundle/recharge/select_wallet
        } else {
          uni.$u.toast(res.msg)
        }
      })
      .catch(err => {
        uni.hideLoading()
        uni.$u.toast(err.msg)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.floating-widget {
  position: fixed;
  bottom: calc(140rpx + constant(safe-area-inset-bottom));
  bottom: calc(140rpx + env(safe-area-inset-bottom));
  // safe-area-inset-bottom: 140rpx;
  right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.det-icon {
  position: absolute;
  top: -20rpx;
  right: 0rpx;
}
.floating-icon {
  width: 230rpx;
}
</style>
