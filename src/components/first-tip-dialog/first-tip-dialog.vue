<template>
  <view class="first-pop" catchtouchmove="true" @tap.stop>
    <!-- 弹窗设置baseUrl -->
    <u-popup :show="showFirstDialog" mode="bottom" :round="20" :closeOnClickOverlay="false" :closeable="false" 
      @close="closePopupHandle" @open="openPopupHandle">
      <view class="setting-popup-title">声明</view>
      <view class="setting-popup-content">
        <view class="block-popup block-flex">
          <view class="tip-text">本服务由广州市派客朴食信息科技有限责任公司提供，相关责任由该公司承担。我行将在法律法规和合同约定范围内合规开展经营活动，维护客户信息安全，保护消费者合法权益。</view>
        </view>
        <view class="block-popup block-flex">
          <view class="tip-text">
            <u-checkbox-group v-model="isCheckout" :activeColor="variables.colorPrimary">
              <u-checkbox class="u-checkbox" shape="circle" :active-color="variables.colorPrimary" name="first" />
            </u-checkbox-group>
          </view>
          <view>确认后15天内不再提示</view>
        </view>
        <view class="block-popup">
          <u-button class="save-btn" type="primary" :color="variables.colorYellow" text="我知道了" @click="saveFirstBank"
            :disabled="loadingBtnDisabled"></u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { apiBookingAbcSetAbcStatement } from "@/api/app"
export default {
  data() {
    return {
      showFirstDialog: false,
      isCheckout: [],
      loadingBtnDisabled: false
    }
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
  },
  watch: {
    show(val) {
      this.showFirstDialog = val
    }
  },
  mounted() {
  },
  methods: {
    showFirstDialogHandle() {
      this.showFirstDialog = true
    },
    closePopupHandle(e) {
      this.showFirstDialog = false
      this.$emit('close')
    },
    openPopupHandle(e) { },
    // 保存操作
    saveFirstBank() {
      if (!this.isCheckout || this.isCheckout.length <= 0) {
        return this.$u.toast('请选择确认后15天内不再提示！')
      }
      if (this.loadingBtnDisabled) {
        return
      }
      this.$showLoading({
        title: '请稍后....',
        mask: true
      })
      this.loadingBtnDisabled = true
      apiBookingAbcSetAbcStatement().then(res => {
        uni.hideLoading()
        this.loadingBtnDisabled = false
        if (res && res.code == 0) {
          this.showFirstDialog = false
          this.$emit('close')
        } else {
          uni.$u.toast(res.msg || '设置失败')
        }

      }).catch(error => {
        this.loadingBtnDisabled = false
        uni.$u.toast(error.message || '设置失败')
      }
      )
    }
  }
}
</script>

<style lang="scss">
.first-pop {
   overflow: hidden;
  .setting-popup-title {
    padding: 20rpx 0;
    font-size: 32rpx;
    text-align: center;
  }

  .setting-popup-content {
    padding: 30rpx 20rpx 10px;

    // height: 30vh;
    .block-popup {
      margin-bottom: 30rpx;
    }

    .tip-text {
      margin: 0 10rpx 0 0;
      font-size: 26rpx;
    }

    .block-flex {
      display: flex;
      align-items: top;
    }

    .save-btn {
      width: 700rpx;
      margin-top: 10rpx;
    }
  }
}
</style>
