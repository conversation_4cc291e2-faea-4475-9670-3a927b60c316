<template>
  <view class="popup-wrapper">
    <view v-if="popupList && type==='popup'">
      <u-overlay opacity="0.1" :show="show">
        <view class="popup-content" v-if="popupList[currentIndex] && popupList[currentIndex].img_url == ''">
          <view class="text-center popup-bg-wrapper">
            <image class="popup_bg" mode="widthFix" src="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/h5/8efd05b6619d99fefe9801616b01b1731675822082997.png" @click="jumpHandle(popupList[currentIndex])"></image>
          </view>
          <view class="text-center">
            <image class="popup_icon" :src="themeImgPath.img_popup_icon" @click="hidePopup"></image>
          </view>
          <text class="popup_text">{{ popupList[currentIndex]['template_text'] }}</text>
        </view>
        <view class="popup-content" v-else-if="popupList[currentIndex]">
          <view class="text-center popup-bg-wrapper">
            <image class="popup_bg" mode="widthFix" :src="popupList[currentIndex].img_url" @click="jumpHandle(popupList[currentIndex])"></image>
          </view>
          <view class="text-center">
            <image class="popup_icon" :src="themeImgPath.img_popup_icon" @click="hidePopup"></image>
          </view>
        </view>
      </u-overlay>
    </view>
    <view class="notice" v-if="type==='notice'">
      <u-overlay opacity="0.1" :show="showNotice">
        <view class="popup-content">
          <view class="text-center popup-bg-wrapper">
            <image class="popup_bg" mode="widthFix" src="https://packer-static-assets.oss-cn-shenzhen.aliyuncs.com/mini/26fd5dcbfc7fcce8b85a2aa8cea5b4901675755583732.png"></image>
          </view>
          <view class="notice-text-wrapper flex col-center">
            <view class="notice-text n-l" @click="showNotice=false"></view>
            <view class="notice-text n-r" @click="gotoNotice"></view>
          </view>
        </view>
      </u-overlay>
    </view>

    <view v-if="type==='message'">
      <u-overlay opacity="0.1" :show="message_show">
        <view >
          <u-modal :show="message_show" @confirm="hideMessage" title="温馨提示" :content='message'></u-modal>
          <!-- <u-button @click="hideMessage"></u-button> -->
        </view>
      </u-overlay>
    </view>
  </view>
</template>

<script>
  import Cache from '@/utils/cache'
  import { mapActions, mapGetters, mapMutations } from 'vuex'
  import { allMenuList } from '@/constants/menu-list'
  import { getMarketingSettings, setIsPopup, setIsMessagePopup} from '@/api/app.js'
  import { getApiRechargeWalletList } from '@/api/user.js'
  import { getApiBookingUserGetCardUserList, getApiBookingUserGetCanteenList, getApiTakeMealTypeList } from '@/api/reservation'
  
  export default {
    props: {
      type: {
        type: String,
        default: 'popup' //popup, notice, message
      }
    },
    data() {
      return {
        imgPath: this.$imgPath,
        show: false,
        showNotice: false,
        currentIndex: 0, // 当前弹出窗的index(到第几条了,递增)
        popupType: null, // 当前页面全选的类型
        jumpList: {}, // 权限对应的地址
        path: '', // 当前的地址
        popupList: null, // 当前允许的弹窗内容列表
        message: '', // 消息通知内容
        message_queue: null, // 消息通知
        message_show: false
      }
    },
    computed: {
      ...mapGetters(['userInfo', 'marketingSetting','platform']),
      // popupList(){
      //   let list = []
      //   this.getCurrentPagePermission()
      //   if (this.marketingSetting && this.marketingSetting.popup_list) {
      //     list = this.marketingSetting.popup_list.filter(item => {
      //       // 首页的话show_page_url为空
      //       return ((item.show_page_url === this.type) || (item.show_page_url == '') && this.path == '/pages/index/index') && item.is_user_popup
      //     })
      //   }
      //   if (list && list.length) {
      //     this.show = true
      //   }
      //   return list
      // }
    },
    watch: {
      show(val) {
        let current = this.popupList[this.currentIndex]
        if(val) {
          this.setShowPopup(current)
        }
      }
    },
    created () {
      // 每次调一下接口吧, 不调了
      // this.getMarketingSettingHandle()
    },
    mounted() {
      this.initLoad()
    },
    methods: {
      ...mapMutations(['SET_SELECT']),
      ...mapActions({
        setMarkketing: 'setMarkketing',
        setWallet: 'setWallet'
      }),
      async getMarketingSettingHandle() {
        // this.$showLoading({
        //   title: '获取设置中...',
        //   mask: true
        // })
        const [err, res] = await this.$to(getMarketingSettings())
        // uni.hideLoading()
        if (err) {
          return
        }
        if (res.code === 0) {
          this.setMarkketing(res.data)
          this.initLoad()
        } else {
          // uni.$u.toast(res.msg)
        }
      },
      initLoad() {
        let list = []
        this.getCurrentPagePermission()
        if (this.marketingSetting && this.marketingSetting.popup_list) {
          list = this.marketingSetting.popup_list.filter(item => {
            let is = false
            // show_page_url不为空，
            if (item.show_page_url && item.show_page_url === this.popupType && item.is_user_popup && item.window_type === 'popup') is = true
            // 首页的话show_page_url为空
            if (item.show_page_url == '' && item.is_user_popup && this.path == '/pages/index/index' && item.window_type === 'popup') is = true
            return is
          })
        }
        if (list && list.length && this.type === 'popup') {
          this.show = true
        }
        if (this.type === 'notice' && this.marketingSetting && this.marketingSetting.is_alter_new_notice) {
          this.showNotice = true
        }
        if (this.marketingSetting) {
          this.message = (this.marketingSetting.message_queue) ? this.marketingSetting.message_queue.message: ""
          if (this.marketingSetting.message_queue) {
            this.message_queue = this.marketingSetting.message_queue
          }
        }
        if (this.message_queue && this.message_queue.is_user_popup === true) {
          this.message_show = true
        }
        this.popupList = list
      },
      // 获取当前页面的类型
      getCurrentPagePermission(){
        let path = this.$Route.path
        this.path = path
        let type = ''
        for (let index = 0; index < allMenuList.length; index++) {
          const item = allMenuList[index]
          if(item && item.permission){
            this.jumpList[item.permission] = item
          }
          if (item && item.path && item.path.split('?')[0] === path) {
            type = item.permission
            this.popupType = type
            // break
          }
        }
        this.popupType = type
        return type
      },
      jumpHandle(v) {
        this.show = false
        if (v.jump_type === 'inner') {
          if (this.jumpList[v.jump_url]) {
            this.handleClickMenu(this.jumpList[v.jump_url])
          }
        } else { // 外部链接
					// 是bocapp 中行标识
					if(this.platform === 'bocapp'){
						if(v.jump_url.includes('BOCBANK')){
							window.c_plugins.merchantBridge.goToNative(
							function () {}, 
							function (err) {}, 
							{
								page: v.jump_url,
							} // 包括BOCBANK:协议类型；xxxx中行加密串类型；传0则返回app首页
							)
						}
						}else{
							// 小程序用外部链接要配H5域名(这里先不做小程序的外部链接)
							// #ifdef H5
							window.location.href = v.jump_url
							// #endif
						}
        }
      },
      async hidePopup() {
        this.show = false
        await this.$sleep(300)
        if (this.currentIndex < (this.popupList.length - 1)) {
          this.currentIndex++
          this.show = true
        }
      },
      async hideMessage() {
        this.message_show = false
        const [err, res] = await this.$to(setIsMessagePopup({id: this.message_queue.id}))
        if (err) {
          return
        }
        if (res.code === 0) {
        } else {
        }
      },
      async setShowPopup(item) {
        //   this.$showLoading({
        //   title: '获取设置中...',
        //   mask: true
        // })
        const [err, res] = await this.$to(setIsPopup({id: item.id}))
          // uni.hideLoading()
        if (err) {
          return
        }
        if (res.code === 0) {
        } else {
          // uni.$u.toast(res.msg)
        }
      },
      handleClickMenu(item) {
        let _this = this
        if (item.type == 'bind_buffet') {
          this.$miRouter.push('/pages/index/index')
        } else if (item.type == 'recharge') {
          this.getRechargeWalletList()
        } else if (item.type == 'reservation') {
          this.SET_SELECT({
            key: 'payment_order_type',
            data: item.type
          })
        }else if (item.type == 'intention_menu') {
          // 因为要跳到选择组织页面
          if (this.$store.state.appoint) {
            const state = this.$store.state.appoint
            if (state.select.intent_org_id && state.select.intent_org_id.id) {
              this.$miRouter.push(item.path)
            } else {
              this.$miRouter.push(item.orgPath)
            }
          } else {
              this.$miRouter.push(item.orgPath)
          }
        } else {
          if (item.path) {
            if (item.type == 'report_meal') {
              this.SET_SELECT({
                key: 'payment_order_type',
                data: item.type
              })
            }
            // 因为有了同一个字段
            if (item.type == 'myReservation') {
              this.SET_SELECT({
                key: 'payment_order_type',
                data: 'reservation'
              })
            }
            if (item.type == 'shop_feedback') {
              this.$miRouter.push({
                path: item.path,
                query: {
                  type: 'shop',
                  person_no: this.userInfo.person_no // 兼容多用户
                }
              })
              return
            }
            this.$miRouter.push({
              path: item.path,
              query: {
                person_no: this.userInfo.person_no // 兼容多用户
              }
            })
          } else {
          }
        }
      },
      // 获取充值钱包列表
    getRechargeWalletList() {
      this.$showLoading({
        title: '获取中....',
        mask: true
      })
      getApiRechargeWalletList()
        .then(res => {
          uni.hideLoading()
          if (res.code == 0) {
            // 如果该手机账号只有一个项目点，且只有一个组织的储值钱包时，不需要选择钱包，直接进入充值页面
            if (res.data && res.data.length == 1 && res.data[0].wallet_list && res.data[0].wallet_list.length == 1) {
              this.setWallet(res.data[0].wallet_list[0])
              this.$miRouter.push({
                path: '/pages_bundle/recharge/recharge',
                query: {
                  person_no: this.userInfo.person_no // 兼容多用户
                }
              })
            } else {
              if (res.data && res.data.length) {
                this.$miRouter.push({
                  path: '/pages_bundle/recharge/select_wallet',
                  query: {
                    routeType: 'index',
                    person_no: this.userInfo.person_no // 兼容多用户
                  }
                })
              } else {
                uni.$u.toast('暂无充值钱包')
              }
            }
            // /pages_bundle/recharge/recharge
            // /pages_bundle/recharge/select_wallet
          } else {
            uni.$u.toast(res.msg)
          }
        })
        .catch(err => {
          uni.hideLoading()
          uni.$u.toast(err.msg)
        })
      },
      gotoNotice() {
        this.showNotice = false
        this.$miRouter.push('/pages_info/news/project_news')
      }
    }
  }
</script>

<style lang="scss">
.popup-wrapper{
  .popup-content{
    position: absolute;
    left: 50%;
    top: 46%;
    transform: translate(-50%, -46%);
    .popup-bg-wrapper{
      width: 420rpx;
      .popup_bg{
        // width: 610rpx;
        // height: 570rpx;
        width: 100%;
      }
    }
    .popup_icon{
      width: 40rpx;
      height: 40rpx;
    }
    .popup_text{
      position: absolute;
      font-size: 20rpx;
      top: 340rpx;
      left: 52%;
      transform: translateX(-50%);
      width: 320rpx;
    }
    .notice-text-wrapper{
      position: relative;
      margin-top: -68rpx;
      z-index: 9;
      .notice-text{
        width: 120rpx;
        height: 50rpx;
        &.n-l{
          margin-left: 60rpx;
        }
        &.n-r{
          margin-left: 80rpx;
        }
      }
    }
  }
}
</style>
