<template>
  <view class="popup-layout">
    <u-overlay :opacity="opacity" :show="visible" :zindex="zIndex">
      <view class="popup-content">
        <slot>
          <view class="content-box">
            <view v-if="showTitle" class="title text-center">{{ title }}</view>
            <view :class="['content m-t-40', alignCenter?'text-center':'']">
              {{ content }}
              <slot v-if="showTip" name="tip"></slot>
            </view>
            <slot name="other"></slot>
            <view v-if="showFooter" class="popup-footer m-t-40">
              <slot name="footer">
                <view class="flex row-around">
                  <u-button
                    v-if="showCancel"
                    hover-class="none"
                    :custom-style="cancelStyle"
                    :text="cancelText"
                    :shape="buttonShape"
                    @click="cancelHandle"
                    :disabled="loading"
                  ></u-button>
                  <u-button
                    v-if="showConfirm"
                    :custom-style="confirmStyle"
                    :text="confirmText"
                    :shape="buttonShape"
                    :loading="loading"
                    @click="confirmHandle"
                  ></u-button>
                </view>
              </slot>
            </view>
          </view>
        </slot>
      </view>
    </u-overlay>
  </view>
</template>

<script>
  import Cache from '@/utils/cache'
  import { mapActions, mapGetters, mapMutations } from 'vuex'
  import variables from "@/styles/export_vars.scss";

  export default {
    name: 'pupupLayout',
    props: {
      show: {
        type: Boolean,
        required: true
      },
      loading: {
        type: Boolean,
        default: false
      },
      opacity: { // 类型
        type: String,
        default: '0.5'
      },
      title: { // 标题
        type: String,
        default: '提示'
      },
      content: { // 内容
        type: String,
        default: ''
      },
      showTitle: { // 是否显示标题
        type: Boolean,
        default: true
      },
      showFooter: { // 是否显示地下操作按钮
        type: Boolean,
        default: true
      },
      cancelText: { // 取消按钮文字
        type: String,
        default: '取消'
      },
      showCancel: { // 是否显示取消按钮
        type: Boolean,
        default: true
      },
      confirmText: { // 确认按钮文字
        type: String,
        default: '确定'
      },
      showConfirm: { // 是否显示确定按钮
        type: Boolean,
        default: true
      },
      buttonShape: { // 确认按钮文字
        type: String,
        default: 'circle'
      },
      alignCenter: { // 内容是否居中
        type: Boolean,
        default: false
      },
      confirmStyle: { // 确定按钮样式
        type: Object,
        default:() => {
          return {
            backgroundColor: variables.colorPrimary,
            color: '#fff',
            width: '35%',
            height: '65rpx'
          }
        }
      },
      cancelStyle: { // 取消按钮样式
        type: Object,
        default:() => {
          return {
            width: '35%',
            height: '65rpx',
            color: variables.colorPrimary, // '#52da98',
            border: `1px solid ${variables.colorPrimary}`
          }
        }
      },
      zIndex: { // 确认按钮文字
        type: String,
        default: '10070'
      },
      showTip: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showNotice: false,
        currentIndex: 0, // 当前弹出窗的index(到第几条了,递增)
        popupType: null, // 当前页面全选的类型
        customStyleTo: {
          backgroundColor: '#52da98',
          color: '#fff',
          width: '35%',
          height: '65rpx'
        },
        customStyleBack: {
          width: '35%',
          height: '65rpx',
          color: '#52da98',
          border: '1px solid #52da98'
        },
      }
    },
    computed: {
      ...mapGetters(['userInfo']),
      visible: {
        get() {
          return this.show
        },
        set(val) {
          this.$emit('update:show', val)
        }
      }
    },
    watch: {
    },
    created () {
    },
    mounted() {
      // this.initLoad()
    },
    methods: {
      initLoad() {
      },
      // 关闭
      cancelHandle(e) {
        // 还在加载中的状态不允许点击取消
        if (this.loading) return
        this.closeHandle()
        this.$emit('cancel', 'cancel')
      },
      // 确定
      confirmHandle(e) {
        // 还在加载中的状态不允许点击重复点击
        if (this.loading) return
        // this.closeHandle()
        this.$emit('confirm', 'confirm')
        
      },
      // 关闭弹窗事件都触发
      closeHandle(e) {
        this.visible = false
        this.$emit('close', this.visible)
      }
    }
  }
</script>

<style lang="scss">
.popup-layout{
  .popup-content{
    position: absolute;
    left: 50%;
    top: 46%;
    width: 75%;
    padding: 30rpx;
    transform: translate(-50%, -46%);
    background-color: #fff;
    border-radius: 16rpx;
    .title {
      overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
      font-size: 36rpx;
    }
    .content{
      font-size: 30rpx;
      word-break: break-all;
    }
  }
}
</style>
