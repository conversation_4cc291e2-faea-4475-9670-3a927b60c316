/*
 * @Author: jiuylq
 * @Date: 2022-12-08 14:22:07
 * @LastEditors: jiuylq
 * @LastEditTime: 2022-12-13 14:48:26
 * @Description: 请填写简介
 */
import request from '@/utils/request'

// 获取取餐柜订单信息
export const apiGetCupboardCell = data => request.post("/booking/cupboard/get_cupboard_cell", data)

// 通知设备开柜
export const apiOpenCupboardCell = data => request.post("/booking/cupboard/open_cupboard_cell", data)

// 手机尾号查看订单信息
export const apiPhoneGetCupboardCell = data => request.post("/booking/cupboard/phone_get_cupboard_cell", data)

// 手机号取餐接口
export const apiPhoneOpenCupboardCell = data => request.post("/booking/cupboard/phone_open_cupboard_cell", data)
