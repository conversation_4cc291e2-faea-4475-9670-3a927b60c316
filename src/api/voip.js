import request from '@/utils/request'

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  获取用户通话信息
 */
export const getApiUserVoipInfo = data => request.post("/booking/voip/user_voip_info",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  获取用户通话套餐列表
 */
export const getApiUserVoipPackageList = data => request.post("/booking/voip/get_user_voip_package_list",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  获取用户通话记录
 */
export const getApiCallRecord = data => request.post("/booking/voip/get_call_record",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  获取用户留言箱
 */
export const getApiVoipMessage = data => request.post("/booking/voip/get_user_voip_message",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  添加用户留言
 */
export const addApiUserVoipMessage = data => request.post("/booking/voip/add_user_voip_message",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  获取用户联系人
 */
export const getApiUserContacts = data => request.post("/booking/voip/get_user_contacts",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  删除用户联系人
 */
export const delApiUserContacts = data => request.post("/booking/voip/del_user_contacts",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  修改用户联系人
 */
export const modifyApiUserContacts = data => request.post("/booking/voip/modify_user_contacts",data)
