import request from '@/utils/request'


/**
 * @param { Object }
 * @return { Promise } 
 * @description 添加购物车
 */
export const apiShopcardAdd = data => request.post("/booking/shopcard/add", data)


/**
 * @param { Object }
 * @return { Promise } 
 * @description 清空全部购物车
 */
export const apiShopcardClean = data => request.post("/booking/shopcard/clean", data)


/**
 * @param { Object }
 * @return { Promise } 
 * @description 删除购物车菜品
 */
export const apiShopcardDelete = data => request.post("/booking/shopcard/delete", data)


/**
 * @param { Object }
 * @return { Promise } 
 * @description 获取购物车
 */
export const apiShopcardList = data => request.post("/booking/shopcard/list", data)

/**
 * @param { Object }
 * @return { Promise } 
 * @description 获取提交订单页面购物(把单餐跟总金额算出来)
 */
export const apiShopcardCalcList = data => request.post("/booking/shopcard/calc_list", data)

/**
 * @param { Object }
 * @return { Promise } 
 * @description 取餐柜打包费
 */
export const getApiReservationChange = data => request.post("/booking/shopcard/reservation_change", data)


/**
 * @param { Object }
 * @return { Promise } 
 * @description 添加档口的总份数
 */
export const apiShopcardAddCount = data => request.post("/booking/shopcard/add_count", data)

/**
 * @param { Object }
 * @return { Promise } 
 * @description 添加档口的总份数
 */
export const apiShopcardReduceCount = data => request.post("/booking/shopcard/reduce_count", data)

/**
 * @param { Object }
 * @return { Promise } 
 * @description 营养反馈
 */
export const apiBookingHealthyNutritionFeedback = data => request.post("/booking/healthy/nutrition_feedback", data)