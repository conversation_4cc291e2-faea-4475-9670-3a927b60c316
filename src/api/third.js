import request from '@/utils/request'

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  扫码充值
 */
export const createApiThirdRecharge = data => request.post("/booking/user_order/order_create",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description  授权
 */
export const thirdLogin = data => request.post("/booking/user_order/h5_login_verify",data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 获取扫码支付的支付设置
 */
export const getThirdSetting = data => request.post("/booking/user_order/get_settings",data)


/**
 * @param { Object } data
 * @return { Promise } 
 * @description 获取用户信息
 */
export const getUserInfoApi = data => request.post("/booking/user_order/get_user_info", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 充值
 */
export const apiScanPayRecharge = data => request.post("/booking/user_order/scanpay_recharge", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 农行项目jf点列表
 */
export const apiGetJfCompanyInfo = data => request.post("/booking/abc/backdoor/get_jf_compamny_info", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 农行jf订单查询
 */
export const apiQueryJfOrder = data => request.post("/booking/abc/backdoor/query_abc_jf_order", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 农行jf订单查询
 */
export const apiRefundJfOrder = data => request.post("/booking/abc/backdoor/refund_abc_jf_order", data)

/**
 * 获取第三方中卡洗衣房跳转链接
 * @param { Object } data
 * @return { Promise } 
 * @description 充值
 */
export const apiBookingZkLaundryGetZkLaundryScheme = data => request.post("/booking/zk_laundry/get_zk_laundry_scheme", data)
