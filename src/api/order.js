import request from '@/utils/request'
/**
 * @param { Object } data
 * @return { Promise } 
 * @description 用户订单
 */

export const getApiUserOrderUserOrderList = data => request.post("/booking/user_order/user_order_list", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 详情营养
 */

export const getApiOrderNutrition = data => request.post("/booking/user_order/get_order_nutrition", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 发起申诉
 */

export const getApiOrderCreateAppeal = data => request.post("/booking/order_appeal/create_appeal", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 退款详情
 */

export const getApiRefundDetailList = data => request.post("/booking/order_appeal/refund_detail_list", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 审核订单列表
 */

 export const getReviewList = data => request.post("/booking/review/review_list", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 审核订单详情
 */

 export const getReviewListInfo = data => request.post("/booking/review/review_info_list", data)

 /**
 * @param { Object } data
 * @return { Promise } 
 * @description 获取预约批量取消列表
 */

 export const getReservationRefundList = data => request.post("/booking/reservation/reservation_batch_refund_list", data)

 /**
 * @param { Object } data
 * @return { Promise } 
 * @description 获取报餐批量取消列表
 */

  export const getMealRefundList = data => request.post("/booking/report_meal/report_meal_batch_refund_list", data)

 /**
 * @param { Object } data
 * @return { Promise } 
 * @description 撤销审核
 */

  export const changeReviewApi = data => request.post("/booking/review/review_status_change", data)


/**
 * @param { Object } data
 * @return { Promise } 
 * @description 获取评价列表
 */

 export const getApiUserEvaluationList = data => request.post("/booking/user/evaluation_list", data)

 /**
 * @param { Object } data
 * @return { Promise } 
 * @description 获取运营配置和组织配置
 */

export const getApiToolsOperationManagement = data => request.post("/booking/tools/operation_management", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 评价订单
 */

export const getApiUserOrderEvaluationOrder = data => request.post("/booking/user_order/evaluation_order", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 添加建议/投诉
 */

export const addFeedbackApi = data => request.post("/booking/feedback_record/add", data)
 
// 菜品纠错相关---start
// 获取超管分类
export const apiSuperFoodCategory = data => request.post("/booking/healthy/super_food_category", data)
 
 // 搜索菜品
export const apiFoodSearch = data => request.post("/booking/healthy/food_search", data)
	
 // 菜品数据纠错
export const apiFoodErrorFeedback = data => request.post("/booking/user_order/error_feedback", data)

// 添加自定义菜品
export const apiAddCustomFood = data => request.post("/booking/healthy/add_food", data)


/**
 * @param { Object } data
 * @return { Promise } 
 * @description 发票申请
 */
export const apiBookingInvoiceApply = data => request.post("/booking/invoice/apply", data)

/**
* @param { Object } data
* @return { Promise } 
* @description 开票列表
*/
export const apiBookingInvoiceUserInvoiceList = data => request.post("/booking/invoice/user_invoice_list", data)


/**
* @param { Object } data
* @return { Promise } 
* @description 开票详情
*/
export const apiBookingInvoiceOrderInvoiceDetailInfo = data => request.post("/booking/invoice/order_invoice_detail_info", data)
 
// 获取用户最后一笔订单（四种类型）
export const apiBookingUserOrderGetUserLastOrder = data => request.post("/booking/user_order/get_user_last_order", data)


// 获取点餐组织信息(合并接口)
export const apiBookingUserGetUserOrderSetting = data => request.post("/booking/user/get_user_order_setting", data)

// 获取解绑托盘订单
export const apiBookingUserOrderReleaseOrder = data => request.post("/booking/user_order/release_order", data)

// 充值退款申请
export const apiBookingRechargeRefundApproval = data => request.post("/booking/recharge/refund_approval", data)

// 充值退款申请撤回 
export const apiBookingRechargeChargeApprovalCancel = data => request.post("/booking/recharge/charge_approval_cancel", data)

// 充值退款配置 
export const apiBookingRechargeChargeWithdrawRefundOn = data => request.post("/booking/recharge/charge_withdraw_refund_on", data)

// 离线订单重新发起支付 
export const apiBookingUserOrderOfflineOrderPay = data => request.post("/booking/user_order/offline_order_pay", data)

// 美团 订单详情
export const getApiMeituanOrderInfo = data => request.post("/booking/mei_tuan/get_order_info", data)

// 美团 订单支付
export const getApiMeituanOrderPay = data => request.post("/booking/mei_tuan/order_pay", data)

// 美团 订单跳详情
export const getApiLoginOrderDetail = data => request.post("/booking/mei_tuan/login_order_detail", data)
// 更正菜品 
export const apiBookingUserOrderCorrectFood = data => request.post("/booking/user_order/correct_food", data)

// 反馈历史
export const apiBookingFeedbackRecordList = data => request.post("/booking/feedback_record/list", data)

// 建议反馈是否有未读订
export const apiBookingFeedbackRecordGetReadState = data => request.post("/booking/feedback_record/get_read_state", data)

// /api/booking/feedback_record/update_read_state
export const apiBookingFeedbackRecordUpdateReadState = data => request.post("/booking/feedback_record/update_read_state", data)
