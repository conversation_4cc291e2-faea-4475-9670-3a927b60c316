import request from '@/utils/request'


/**
 * @param { Object } data
 * @return { Promise }
 * @description 登录手机号
 */

export const apiLoginPhone = data => request.post("/booking/login/phone", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 登录手机号
 */

 export const apiAlipayLoginPhone = data => request.post("/booking/alipay/phone_login", data)

 /**
 * @param { Object } data
 * @return { Promise }
 * @description 登录手机号
 */

export const apiAlipayAuth = data => request.post("/booking/alipay/auth", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取验证码
 */

export const apiSmsSend = data => request.post("/booking/sms/send_validate_code", data)



/**
 * @param { Object } data
 * @return { Promise }
 * @description 微信验证接口
 */

export const apiWechatAuth = data => request.post("/booking/wechat/miniapp_auth", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 小程序微信手机号一键登录
 */

export const apiWechatPhoneLogin = data => request.post("/booking/wechat/miniapp_phone_login", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description  获取已录入相同手机号
 */

export const getApiBindProjectCardUserList = data => request.post("/booking/user/get_bind_project_card_user_list", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description H5登录
 */

export const apiBookingLoginH5Login = data => request.post("/booking/login/h5_login", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 本地H5登录
 */

export const apiBookingLoginH5LoginTemp = data => request.post("/booking/login/h5_login_temp", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取项目点
 */

export const apiBookingUserProjectList = data => request.post("/booking/user/project_list", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 绑定项目点
 */

export const setUserBindProjectPoint = data => request.post("/booking/user/bind_project_point", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 切换项目点
 */

export const setApiChangeProjectPoint = data => request.post("/booking/user/change_project_point", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 用户修改项目点手机号
 */

export const setChangeCardInfoPhone = data => request.post("/booking/user/change_card_info_phone", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 登出
 */

export const setUserLogout = data => request.post("/booking/user/logout", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 上传人脸图片
 */

export const uploadUserFace = data => request.post("/booking/user/upload_user_face", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取上传图片token
 */

export const uploadOssToken = data => request.post("/booking/upload_file/get_oss_token", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取上传图片sts
 */

export const uploadStsOssToken = data => request.post("/booking/upload_file/get_sts_oss_token", data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 微信用户全部项目点下的用户
 */

export const  getApiUserGetProjectCardUserList = data => request.post("/booking/user/get_project_card_user_list", data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 获取jssdk授权信
 */

export const  getApiWechatCongfigGet = data => request.post("/booking/common/wechat_congfig_get", data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 获取企业微信jssdk授权信
 */

export const  getQywechatConfigGet = data => request.post("/booking/common/qywechat_config_get", data)

/**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 支付宝代扣签约
 */

 export const getApiBookingAlipayAgreementSgin = data => request.post("/booking/alipay/agreement_sgin",data)

 /**
  *
  * @param {Object} data
  * @returns { Promise }
  * @description 支付宝代扣解约
  */
 export const getApiBookingAlipayAgreementUnsign = data => request.post("/booking/alipay/agreement_unsign",data)

 export const getApiBookingUserAgreementSignList = data => request.post("/booking/card_agent_sign/list",data)

 export const getApiPermission = data => request.post("/booking/common/organization_app_permission",data)

 /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 获取营销设置
 */

  export const getMarketingSettings = data => request.post("/booking/marketing/get_settings",data)

 /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 设置已显示过的弹窗
 */

 export const setIsPopup = data => request.post("/booking/marketing/set_is_popup",data)

  /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 设置已显示过的消息弹窗
 */

  export const setIsMessagePopup = data => request.post("/booking/marketing/set_is_message_popup",data)

 /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 获取公告列表
 */

 export const getNoticeList = data => request.post("/booking/marketing/notice_list",data)

  /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 公告签名
 */

  export const apiBookingMarketingNoticeSign = data => request.post("/booking/marketing/notice_sign",data)

    /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 跳壹德第三方
 */

    export const apiBookingYideAuthUrl = data => request.post("/booking/yide/auth_url",data)

  /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 获取公告列表详情
 */

   export const getNoticeListDetail = data => request.post("/booking/marketing/get_notice",data)

  /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 静默授权用code换取openid
 */

   export const apiGetUserOpenid = data => request.post("/booking/wechat/get_user_openid",data)

  /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 获取微信公众号appid和redirect_uri
 */

   export const apiGetWechatValidate = data => request.post("/booking/wechat/wechat_validate",data)

  /**
 *
 * @param {Object} data
 * @returns { Promise }
 * @description 获取游客配置
 */

   export const apiGetVisitorConfig = data => request.post("/booking/reservation/get_visitor_config",data)


  /**
  * ['移动端 登录']
  * booking.login.c2b_h5_login 给c扫b扫码充值或消费的H5登录接口，去除了一些不必要的限制
  * @param {{appid:string, company_id:string, target_url:string, token:string}} param appid appid,company_id 公司,target_url 验证回调页面,token 登录Token
  * @returns {{code:number, msg:string, data:object}} - rsp
  */
  export const apiBookingLoginC2BH5LoginPost = data => request.post("/booking/login/c2b_h5_login",data)



  // 获取协议
  export const getAgreementApi = data => request.post("/booking/agreement/get_booking_agreement",data)

  // 获取登录协议
  export const getLoginAgreementApi = data => request.post("/booking/agreement/booking_login_agreement",data)

  // 获取人脸上传协议
  export const getFaceAgreementApi = data => request.post("/booking/agreement/face_upload_agreement",data)

  // 检查用户是否同意了最新的协议
  export const checkAgreementApi = data => request.post("/booking/agreement/check_user_agree_agreement",data)

  // 同意协议更新记录接口
  export const updateAgreementApi = data => request.post("/booking/agreement/update_or_create_record",data)

  // 获取协议详情
  export const getAgreementDetail = data => request.post("/booking/agreement/agreement_detail",data)

  // 手机密码登录
  export const apiLoginAccount = data => request.post("/booking/login/account_login",data)

  // 手机注册
  export const apiPhoneRegister = data => request.post("/booking/user/phone_register",data)

  // 注册设置密码
  export const apiSetUserPassword = data => request.post("/booking/user/set_user_password",data)

  // 忘记密码验证码校验
  export const apiForgetPasswordValidate = data => request.post("/booking/user/forget_password_validate",data)

  // 忘记密码重新设置密码
  export const apiForgetSetUserPassword = data => request.post("/booking/user/forget_set_user_password",data)

  // 修改密码
  export const apiChangeUserPassword = data => request.post("/booking/user/change_user_password",data)

  // 易支付签约
  export const apiWingpayContractsSign = data => request.post("/booking/wingpay/contracts_sign",data)

  // 易支付签约
  export const apiWingpayTerminateContract = data => request.post("/booking/wingpay/terminate_contract",data)

  // 消息入口
  export const getUserMessageList = data => request.post("/booking/user_message/list",data)

  // 周报消息推送
  export const getWeeklyReportList = data => request.post("/booking/healthy/weekly_report_list",data)

  // 获取服务器当前时间
  export const getDatetime = data => request.post("/booking/user/get_datetime",data)

  //统计未读消息数
  export const getUserMessageCount = data => request.post('/booking/user_message/message_count',data)

  // h5判断用户是否微信小程序授权过
  export const getApiIsMiniappAuth = data => request.post("/booking/short_link/is_miniapp_auth",data)

  // 获取已签约代扣信息
  export const getUserSignInfo = data => request.post("/booking/card_agent_sign/user_sign_info",data)

  // 获取第三方商城链接
  export const getApiThirdShow = data => request.post("/booking/third/shop",data)

  // 获取项目带配置的相关
  export const getCommonServiceAgentConfig = data => request.post("/booking/common/service_agent_congfig_get",data)

  // 上传问卷
  export const sentSurvey = data => request.post("/booking/user/user_collect",data)

  // 获取问卷
  export const getSurvey = data => request.post("/booking/user/user_collect_rule",data)
  // 获取验证码
  export const getLoginVerifyCode = data => request.post("/booking/sms/get_sms_verify_code",data)
 
  // 获取第美团链接
  export const getApiMeituanLoginFree = data => request.post("/booking/mei_tuan/login_free",data)

  // 校验人脸采集登录
  export const loginVerificationUser = data => request.post("/booking/user/verification_user",data)
  
  // 设置农行首次进入声明
  export const apiBookingAbcSetAbcStatement = data => request.post("/booking/abc/set_abc_statement",data)
  
  // 设置满意度
  export const addSatisfactionRecord = data => request.post("/booking/feedback_record/add_satisfaction_record",data)
  // 获取功能满意度
  export const getSatisfactionRecord = data => request.post("/booking/feedback_record/get_satisfaction_record",data)
  // 获取本季度所有满意度评价
  export const getSatisfactionRecordList = data => request.post("/booking/feedback_record/satisfaction_record_list",data)
  // 更新已读状态
  export const updateStatisfactionReadState = data => request.post("/booking/feedback_record/update_satisfaction_read_state",data)

  // 获取问卷
  export const getQuestionnaireDetail = data => request.post("/booking/questionnaire/get_questionnaire_detail",data)

  // 答题
  export const answerQuestionnaire = data => request.post("/booking/questionnaire/answer_questionnaire",data)