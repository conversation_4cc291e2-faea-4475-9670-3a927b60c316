import request from '@/utils/request'

//获取用户需要缴费的信息
export const apiUserJiaofeiList = data => request.post("/booking/jiaofei/user_jiaofei_list", data)

//缴费订单列表
export const apiJiaofeiOrderList = data => request.post("/booking/jiaofei/order_list", data)

//创建缴费订单
export const apiJiaofeiOrderCreate = data => request.post("/booking/jiaofei/order_create", data)

//缴费申请退款
export const apiJiaofeiRefundApproval = data => request.post("/booking/jiaofei/order_approval", data)

// 发送电子凭证
export const apiBookingJiaofeiSendEmailImgUrl = data => request.post("/booking/jiaofei/send_email_img_url", data)