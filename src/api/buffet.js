import request from '@/utils/request'
/**
 * @param { Object } data
 * @return { Promise } 
 * @description 获取托盘可用消费点
 */

export const getApiTrayOrg = data => request.post("/booking/buffet/get_tray_org", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description H5 微信扫码绑定托盘
 */

export const getApiH5BingTray = data => request.post("/booking/buffet/qrcode_bing_tray", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 称重支付
 */

export const getApiorBuffOeterPaying = data => request.post("/booking/buffet/order_paying", data)
