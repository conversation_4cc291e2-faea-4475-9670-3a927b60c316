import request from '@/utils/request'

// 会员中心
export const apiGetMemberCenter = data => request.post("/booking/member_user/get_member_center",data)

// 个人中心
export const apiBookingMemberUserGetMemberInfo = data => request.post("/booking/member_user/get_member_info",data)

// 成长分明细
export const apiGetGradeGrowthList = data => request.post("/booking/member_user/member_grade_growth_list",data)

// 会员等级列表
export const apiGetMemberGradeList = data => request.post("/booking/member_user/member_grade_list",data)

// 会员创建订单
export const apiBookingMemberUserMemberOrderCreate = data => request.post("/booking/member_user/member_order_create",data)

// 会员微信支付初始化
export const apiMemberWechatConfigGet = data => request.post("/booking/member_user/wechat_config_get",data)

// 获取是否为会员
export const apiGetMemberInfo= data => request.post("/booking/member_user/get_member_info", data)

// 获取是否为会员
export const apiGetMemberOrderInfo= data => request.post("/booking/member_user/get_member_order_info", data)

// 获取推广图
export const apiBookingMemberUserPromotionalPictureUrl= data => request.post("/booking/member_user/promotional_picture_url", data)

// 会员获取发送短信的手机号码
export const getApiMemberSmsPhone= data => request.post("/booking/member_user/get_member_sms_phone", data)

// 会员修改发送短信的手机号码
export const getApiUpdateMemberSmsPhone= data => request.post("/booking/member_user/update_member_sms_phone", data)

// 会员用户绑定
export const getApiMemberUserAuthBind= data => request.post("/booking/member_user/member_user_auth_bind", data)
