import request from '@/utils/request'


/**
 * @param { Object } data
 * @return { Promise }
 * @description 健康问卷列表
 */

export const getApiHealthyQuestionList = data => request.post("/booking/healthy_question/list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 健康问卷提交
 */

export const getApiHealthyQuestionCommit = data => request.post("/booking/healthy_question/commit", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 健康文章列表
 */

export const getApiArticleList = data => request.post("/booking/article/list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 健康文章详情
 */

export const getApiArticleDetails = data => request.post("/booking/article/article_details", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 健康文章分类
 */

export const getApiArticleCategory = data => request.post("/booking/article/article_category_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 首页营养数据
 */

export const getApiIndexNutrient = data => request.post("/booking/healthy/index_nutrient", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 点击消除能量球
 */

export const getApiClearEnergyEgg = data => request.post("/booking/healthy/clear_energy_egg", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 健康页面数据
 */

export const getApiHealthyIndex = data => request.post("/booking/healthy/healthy_index", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 今天添加餐段摄入
 */

export const getApiMealTypeIntakeAdd = data => request.post("/booking/healthy/meal_type_intake_add", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 今天修改餐段摄入
 */

export const getApiMealTypeIntakeModify = data => request.post("/booking/healthy/meal_type_intake_modify", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 今天修改餐段摄入
 */

export const getApiMealTypeIntakeDelete = data => request.post("/booking/healthy/meal_type_intake_delete", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 运动配置列表
 */

export const getApiSportsConfigList = data => request.post("/booking/healthy/sports_config_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 添加运动数据
 */

export const getApiSportsAdd = data => request.post("/booking/healthy/sports_add", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 用户添加自定义配置
 */

export const getApiSportsConfigAdd = data => request.post("/booking/healthy/sports_config_add", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 用户删除自定义配置
 */

export const getApiSportsConfigdel = data => request.post("/booking/healthy/sports_config_delete", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 删除运动数据
 */

export const getApiSportsdel = data => request.post("/booking/healthy/sports_delete", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 系统食材列表
 */

export const getApiIngredientList = data => request.post("/booking/healthy/ingredient_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取特征
 */

export const apiBookingHealthyDiseaseList = data => request.post("/booking/healthy/disease_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 创建健康档案
 */

export const getApiHealthyAdd = data => request.post("/booking/healthy/add", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 修改健康档案
 */

export const getApiHealthyModify = data => request.post("/booking/healthy/modify", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 修改健康档案
 */

export const getApiModifyDisplay = data => request.post("/booking/user/modify_display", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取日期餐段购物车营养数据
 */

export const getApiMealTypeNutrient = data => request.post("/booking/healthy/get_meal_type_nutrient", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 全局用户推荐菜品
 */

export const getApiUserRecommendFood = data => request.post("/booking/healthy/user_recommend_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 合理搭配
 */

export const getApiMatchFood = data => request.post("/booking/healthy/match_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 替换购物车所有菜品
 */

export const getApiReplaceShopCard = data => request.post("/booking/shopcard/replace_shop_card", data)
/**
 * @param { Object } data
 * @return { Promise }
 * @description 预约点餐套餐菜品营养
 */

export const getApiReservationGetFoodNutrition = data => request.post("/booking/reservation/get_food_nutrition", data)
/**
 * @param { Object } data
 * @return { Promise }
 * @description 收藏菜品
 */

export const getApiHealthyCollectFood = data => request.post("/booking/healthy/collect_food", data)
/**
 * @param { Object } data
 * @return { Promise }
 * @description 绑定健康信息
 */

export const getApiBindHealthyInfo = data => request.post("/booking/healthy/bind_healthy_info", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 修改体重目标
 */

export const getApiWeightTarget = data => request.post("/booking/healthy/weight_target", data)
/**
 * @param { Object } data
 * @return { Promise }
 * @description 记录体重记录
 */

export const getApiAddPhysicalRecord = data => request.post("/booking/healthy/add_physical_record", data)
/**
 * @param { Object } data
 * @return { Promise }
 * @description 修改运动数据
 */

export const getApiSportsModify = data => request.post("/booking/healthy/sports_modify", data)
/**
 * @param { Object } data
 * @return { Promise }
 * @description 摄入营养分析
 */

export const getApiIntakeNutrition = data => request.post("/booking/healthy/intake_nutrition", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 运动档案记录
 */
export const getApiHealthySportRecord = data => request.post("/booking/healthy/sport_record", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 饮食健康
 */
export const getApiIntakeHealthy = data => request.post("/booking/healthy/intake_healthy", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 饮食健康
 */
export const getApiHealthyAddFood = data => request.post("/booking/healthy/add_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 健康档案
 */
export const getApiHealthyHealthyRecord = data => request.post("/booking/healthy/healthy_record", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 体重记录
 */
export const getApiHealthyPhysicalRecord = data => request.post("/booking/healthy/physical_record", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 饮食档案
 */

export const getApiHealthyIntakeRecord = data => request.post("/booking/healthy/intake_record", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 习惯养成列表数据
 */

export const getApiHealthyHabitList = data => request.post("/booking/healthy/habit_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 修改习惯权重
 */

export const getApiHealthyModifyHabitWeight = data => request.post("/booking/healthy/modify_habit_weight", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 开启习惯
 */

export const getApiHealthyEnableHabit = data => request.post("/booking/healthy/enable_habit", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 关闭习惯
 */

export const getApiHealthyDisableHabit = data => request.post("/booking/healthy/disable_habit", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 删除自定义习惯
 */

export const getApiHealthyDeleteHabit = data => request.post("/booking/healthy/delete_habit", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 习惯打卡
 */

export const getApiHealthyAddHabitRecord = data => request.post("/booking/healthy/add_habit_record", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 取消习惯打卡
 */

export const getApiCancelHabitRecord = data => request.post("/booking/healthy/cancel_habit_record", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 添加习惯
 */

export const getApiHealthyAddHabit = data => request.post("/booking/healthy/add_habit", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 食谱推荐
 */

export const getApiapiHealthyFoodRecommend = data => request.post("/booking/healthy/food_recommend", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 超管菜品分类
 */

export const getApiSuperFoodCategory = data => request.post("/booking/healthy/super_food_category", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 菜品搜索
 */

export const getApiHealthyFoodSearch = data => request.post("/booking/healthy/food_search", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 菜品搜索
 */

export const getApiHealthyMealTypeIntakeReplace = data => request.post("/booking/healthy/meal_type_intake_replace", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 手动记录
 */

export const getApiHealthyMealTypeIntakeBatchAdd = data => request.post("/booking/healthy/meal_type_intake_batch_add", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取健康设置
 */

export const getApiGetHealthyInfoSetting = data => request.post("/booking/healthy/get_healthy_info_setting", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 今天餐段摄入营养
 */

export const getApiMealTypeIntakeFood = data => request.post("/booking/healthy/meal_type_intake_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取超管的当前餐段
 */

export const getApiHealthyScoreMeal = data => request.post("/booking/healthy/healthy_score_meal", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 当日当前餐段推荐菜品
 */

export const getApiDietFoodRecommend = data => request.post("/booking/healthy/diet_food_recommend", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 饮食推荐菜谱计划列表
 */

export const getApiMenuPlanList = data => request.post("/booking/healthy/menu_plan_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 三餐配置列表
 */

export const getApiThreeMealList = data => request.post("/booking/healthy/three_meal_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 三餐配置列表详情
 */

export const getApiThreeMealDetails = data => request.post("/booking/healthy/three_meal_details", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 食谱计划详情
 */

export const getApiMenuPlanDetails = data => request.post("/booking/healthy/menu_plan_details", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 添加食谱计划
 */

export const getApiAddMenuPlan = data => request.post("/booking/healthy/add_menu_plan", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 使用中的菜谱计划
 */

export const getApiUseMenuPlanDate = data => request.post("/booking/healthy/use_menu_plan_date", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 留言列表
 */

export const getApiHealthyMessageList = data => request.post("/booking/healthy/message_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 添加留言列表
 */

export const getApiHealthyAddMessage = data => request.post("/booking/healthy/add_message", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 点赞留言
 */

export const getApiHealthyLikeMessage = data => request.post("/booking/healthy/like_message", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 首页菜谱计划
 */

export const getApiHealthyIndexMealPlan = data => request.post("/booking/healthy/index_meal_plan", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 添加记录饮食
 */

export const getApiAddMealPlanFood = data => request.post("/booking/healthy/add_meal_plan_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 删除记录饮食
 */

export const getApiDeleteMealPlanFood = data => request.post("/booking/healthy/delete_meal_plan_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 一键记录饮食
 */

export const getApiAddAllMealPlanFood = data => request.post("/booking/healthy/add_all_meal_plan_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 食谱计划详情
 */

export const getApiUseMenuPlanDetails = data => request.post("/booking/healthy/use_menu_plan_details", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 用户删除菜品
 */

export const getApiHealthyDeleteFood = data => request.post("/booking/healthy/delete_food", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 用户编辑菜品
 */

export const getApiHealthyEditFood = data => request.post("/booking/healthy/modify_food", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 营养周报弹窗
 */
  
export const apiBookingHealthyPopWeeklyReport = data => request.post("/booking/healthy/pop_weekly_report",data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 营养周报详情
 */
  
export const apiBookingHealthyWeeklyReportDetails = data => request.post("/booking/healthy/weekly_report_details",data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 营养分析
 */
  
export const apiBookingHealthyNutritionAnalyze = data => request.post("/booking/healthy/nutrition_analyze",data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取当月有营养分析记录
 */
  
export const apiBookingHealthyDayNutritionAnalyzeCalendar = data => request.post("/booking/healthy/day_nutrition_analyze_calendar",data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取食物列表
 */
  
export const apiBookingHealthyNutritionFoodList = data => request.post("/booking/healthy/nutrition_food_list",data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取饮食禁忌前五
 */
  
export const apiBookingHealthyHostIngredientTaboo = data => request.post("/booking/healthy/host_ingredient_taboo",data)



/**
 * @param { Object } data
 * @return { Promise }
 * @description 记录饮食菜品数据页面
 */
  
export const getApiBookingHealthyRecordDiet = data => request.post("/booking/healthy/record_diet",data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 清空搜索数据
 */
  
export const clearApiBookingHealthyClearSearch = data => request.post("/booking/healthy/clear_search",data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 搜索数据
 */
  
export const getApiBookingHealthyFoodSearch = data => request.post("/booking/healthy/food_search",data)
