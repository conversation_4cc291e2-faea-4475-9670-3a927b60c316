import request from '@/utils/request'

// 获取用户的账户钱包
export const apiGetUserWalletList = data => request.post("/booking/user/get_user_wallet_list", data)

// 钱包统计
export const apiGetUserWalletStatistical = data => request.post("/booking/user/get_user_wallet_statistical", data)

// 钱包账单
export const apiGetUserWalletInfo = data => request.post("/booking/user_order/get_user_wallet_info", data)

// 钱包账单统计
export const apiGetUserWalletInfoTotal = data => request.post("/booking/user_order/get_user_wallet_info_collect", data)

//钱包中心
export const apiGetCompanyWalletList = data => request.post("/booking/user/get_user_company_wallet_list", data)

//获取用户待领取的补贴
export const apiGetUserUnreceivedSubsidy = data => request.post("/booking/subsidy/get_user_unreceived_subsidy_list", data)

// 领取补贴
export const apiReceivedSubsidy = data => request.post("/booking/subsidy/receive_subsidies", data)

// 充值提现配置
export const getApiWithdrawInfo = data => request.post("/booking/recharge/withdraw_info", data)

// 充值提现配置
export const getApiRechargeWithdraw = data => request.post("/booking/recharge/withdraw", data)

// 充值提现申请记录
export const apiBookingRechargeGetWithdrawApproval = data => request.post("/booking/recharge/get_withdraw_approval", data)

// 充值提现申请
export const apiBookingRechargeWithdrawApproval = data => request.post("/booking/recharge/withdraw_approval", data)

// 充值提现申请撤回
export const apiBookingRechargeWithdrawApprovalCancel = data => request.post("/booking/recharge/withdraw_approval_cancel", data)

// 是否提现申请中
export const apiBookingRechargeWithdrawApprovalIsApplying = data => request.post("/booking/recharge/withdraw_approval_is_applying", data)


