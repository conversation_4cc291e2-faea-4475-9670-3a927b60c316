import request from '@/utils/request'


/**
 *  @param { Object } data
 * @return { Promise }  user_id 个人id
 * 健康系统 个人中心接口
 */

export const apiGetMemberInfo= data => request.post("/booking/member_user/get_member_info", data)

/**
 *   @param { Object } data
 * @return { Promise }  company_id 个人id
 * 健康系统 饮食报告
 */

export const apiGetBookingHealthyDietReport= data => request.post("/booking/healthy/diet_report", data)

/**
 *   @param { Object } data
 * @return { Promise }  page 查询页数   page_size 显示条数   update_cache 刷新缓存（boolean）
 * 
 * 健康系统 报告列表
 */

export const  apiBookingHealthyDietReportList= data => request.post("/booking/healthy/diet_report_list", data)

/**
 *   @param { Object } data
 * @return { Promise }  
 * 健康系统 饮食报告详情 id  interer or null { "id":0}
 */

export const apiBookingHealthyDietReportDetail= data => request.post("/booking/healthy/diet_report_detail", data)

/**
 *   @param { Object } data
 * @return { Promise }  
 * 健康系统 饮食健康  date  日期   {"data": "2019-08-24"}
 */

export const apiBookingHealthyIntakeHealthy= data => request.post("/booking/healthy/intake_healthy", data)

/**
 *   @param { Object } data
 * @return { Promise }  
 * 健康系统 摄入营养分析 date  日期   {"data": "2019-08-24"}  meal_type string 取餐类型
 * breakfast : "早餐"
lunch : "午餐"
afternoon : "下午茶"
dinner : "晚餐"
supper : "夜宵"
morning : "凌晨餐"
all : "全部"
 */

export const apiBookingHealthyIntakeNutrition= data => request.post("/booking/healthy/intake_nutrition", data)


/**
 *   @param { Object } data
 * @return { Promise }  
 * 健康系统 饮食档案  start_date string 开始时间 end_date string 结束时间 {"start_date":"2019-08-24","end_date":"2019-08-24"}
 */

export const apiBookingHealthyIntakeRecord= data => request.post("/booking/healthy/intake_record", data)


