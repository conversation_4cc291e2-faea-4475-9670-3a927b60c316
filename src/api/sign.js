/*
 * @Author: jiuylq
 * @Date: 2022-12-08 14:22:07
 * @LastEditors: jiuylq
 * @LastEditTime: 2022-12-13 14:48:26
 * @Description: 请填写简介
 */
import request from '@/utils/request'

// 申请短信
export const apiAbcErmbAgreementApply = data => request.post("/booking/abc/abc_ermb_agreement_apply", data)

// 数币免密协议申请&签名 
export const apiAbcErmbAgreementSign = data => request.post("/booking/abc/abc_ermb_agreement_sign", data)

// 数币免密协议查询
export const apiAbcErmbAgreementCancel = data => request.post("/booking/abc/abc_ermb_agreement_cancel", data)

// 签约数币个人信息
export const apiUserAgentInfo = data => request.post("/booking/abc/user_agent_info", data)

// 邮储数币签约获取验证码
export const apiPsbcErmbAgreementSignApply = data => request.post("/booking/psbc/ermb_agreement_apply", data)

// 邮储数币签约
export const apiPsbcErmbAgreementSign = data => request.post("/booking/psbc/ermb_agreement_sign", data)

// 邮储数币签约查询
export const apiPsbcErmbAgreementQuery = data => request.post("/booking/psbc/ermb_agreement_query", data)

// 邮储数币解约
export const apiPsbcErmbAgreementUnsign = data => request.post("/booking/psbc/ermb_agreement_unsign", data)

// 邮储服开
export const apiPsbcFkToken = data => request.post("/booking/psbc/fk_token", data)

// 检查农行代扣配置情况
export const apiCheckAbcDaikouPayinfo = data => request.post("/booking/card_agent_sign/check_abc_daikou_payinfo", data)

// 检查农行电子账户开户
export const apiCheckEaccountOpen = data => request.post("/booking/abc/check_eaccount_open", data)

// 农行代扣获取验证码
export const apiPayAgentSignContract = data => request.post("/booking/abc/pay_agent_sign_contract", data)

// 农行授权代扣签约
export const apiPayAgentSignConfirm = data => request.post("/booking/abc/pay_agent_sign_confirm", data)

// 农行授权代扣解约
export const apiPayAgentUnSignConfirm = data => request.post("/booking/abc/pay_agent_unsign_contract", data)

// 去农行开户
export const apiH5EaccountOpen = data => request.post("/booking/abc/h5_eaccount_open", data)

// 获取用户该组织的埋点
export const apiBookingAbcGetOrgBuryInfo = data => request.post("/booking/abc/get_org_bury_info", data)


 /**
* ['移动端 支付宝']
* booking.alipay.facepass_chage 支付宝代扣签约 一脸通行签约
* @param {AlipayFacePayApplyReqSer} data
* @returns {Object}
*/
export const apiAlipayFacepassChagePost = data => request.post('/booking/alipay/facepass_change', data)

/**
* ['移动端 支付宝']
* booking.alipay.facepass_cb_chage 支付宝代扣签约 一脸通行餐补签约
* @param {AlipayFacePassCBReqSer} param
* @returns {Object}
*/
export const apiAlipayFacepassCbChagePost = data => request.post('/booking/alipay/facepass_cb_change', data)

/**
* ['移动端 支付宝']
* booking.alipay.get_facepass_status 获取签约状态，直接查数据库 :data request: :data args: :data kwargs: :return:
* @param {Empty} data
* @returns {Object}
*/
export const apiAlipayGetFacepassStatusPost = data => request.post('/booking/alipay/get_facepass_status', data)

/**
* ['移动端 支付宝']
* booking.alipay.get_facepass_status 获取餐补签约状态
* @param {Empty} data
* @returns {Object}
*/
export const apiAlipayGetFacepassCbStatusPost = data => request.post('/booking/alipay/get_facepass_cb_status', data)

/**
* ['移动端 支付宝']
* 查看人脸图片
* @param {Empty} data
* @returns {Object}
*/
export const apiAlipayGetFacepassImgPost = data => request.post('/booking/alipay/get_order_face_url', data)

 /**
  * ['移动端 支付宝企业码']
  * booking.alipay_qycode.agreement_sgin 支付宝代扣签约
  * @param {{}} param 
  * @returns {{code:number, msg:string, data:Empty}} - rsp
  */
export const apiBookingAlipayQycodeAgreementSginPost = data => request.post('/booking/alipay_qycode/agreement_sgin', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.agreement_unsign 支付宝代扣解约
* @param {{}} param 
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeAgreementUnsignPost = data => request.post('/booking/alipay_qycode/agreement_unsign', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.delete_employee 员工离职
* @param {{}} param 
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeDeleteEmployeePost = data => request.post('/booking/alipay_qycode/delete_employee', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.facepay_sign 刷脸付签约
* @param {{}} param 
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeFacepaySignPost = data => request.post('/booking/alipay_qycode/facepay_sign', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.facepay_unsign 刷脸付签约
* @param {{}} param 
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeFacepayUnsignPost = data => request.post('/booking/alipay_qycode/facepay_unsign', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.get_account_id 企业入驻 :return:
* @param {{enterprise_id:string}} param enterprise_id 支付宝账号
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeGetAccountIdPost = data => request.post('/booking/alipay_qycode/get_account_id', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.get_enterprisepay_create_url 企业入驻 :return:
* @param {{identity_type:string, identity:string, o_id:number, enterprise_name:string, enterprise_alias:string, out_biz_no:string}} param identity_type 支付宝账号类型,identity 支付宝账号,o_id 组织ID,enterprise_name 企业名称,enterprise_alias 企业简称,out_biz_no biz_no
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeGetEnterprisepayCreateUrlPost = data => request.post('/booking/alipay_qycode/get_enterprisepay_create_url', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.get_sign_status_list 获取企业码签约状态
* @param {{}} param 
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeGetSignStatusListPost = data => request.post('/booking/alipay_qycode/get_sign_status_list', data)

/**
* ['移动端 支付宝企业码']
* booking.alipay_qycode.invite_employee 邀请员工/添加员工
* @param {{}} param 
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingAlipayQycodeInviteEmployeePost = data => request.post('/booking/alipay_qycode/invite_employee', data)
// 去建行开户
export const apiCcbOpenAccount = data => request.post("/booking/cbb/open_account", data)

// 去建行开户
export const apiCcbAccount = data => request.post("/booking/cbb/account", data)

// 去建行开户
export const apiCcbAccountInfo = data => request.post("/booking/cbb/get_account_info", data)

// 翼支付签约检测 
export const apiWingPayCheckSign = data => request.post("/booking/wingpay/check_sign", data)
