import request from '@/utils/request'
import { baseURL } from '@/config'

/**
 * @param { Object }
 * @return { Promise } 
 * @description 获取上传token
 */
export const getApiUploadFileGetOssToken = data => request.post("/booking/upload_file/get_oss_token", data)

/**
 * @param { Object }
 * @return { Promise } 
 * @description 获取上传图片
 */
export const getApiUploadUserFace = data => request.post("/booking/user/upload_user_face", data)

/**
 * @param { String } prefix 上传的前缀 key 传的文件名
 * @param { String } key 传的文件名
 * @return { Promise } 
 * @description 通用文件上传
 */
 export const uploadApiCommon = data => request.post("/booking/common/upload", data)

 export const uploadApiUrl = baseURL + "/api/booking/common/upload"
 export const uploadApiUrlForTourist = baseURL + "/api/booking/questionnaire/upload"
export const uploadApiUrlForTools = baseURL + "/api/booking/tools/upload"

 
 /**
 * @param { Object }
 * @return { Promise } 
 * @description 农行第三方获取上传图片
 */
export const getApiUploadAbcUserFace = data => request.post("/booking/user/abc_upload_user_face", data)