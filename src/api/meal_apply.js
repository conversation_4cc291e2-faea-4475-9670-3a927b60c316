import request from '@/utils/request'

// 访客餐申请
export const apiCreateApproveOrder = data => request.post("/booking/approve_order/create_approve_order", data)

// 获取访客餐规则信息
export const apiGetApproveOrderRuleInfo = data => request.post("/booking/approve_order/get_approve_order_rule_info", data)

// 获取访客餐规则信息
export const apiGetApproveList = data => request.post("/booking/approve_order/approve_list", data)

// 获取访客餐规则信息
export const apiRevokeApproveOrder = data => request.post("/booking/approve_order/revoke_approve_order", data)

// 获取访客餐规则信息
export const apiOrderVisitorPay = data => request.post("/booking/approve_order/order_visitor_pay", data)

// 获取访客餐规则信息
export const apiGetMealApplyPayMethod = data => request.post("/booking/approve_order/get_pay_method_list", data)

