import request from '@/utils/request'

 /**
  * @param { Object } data
  * @return { Promise } 
  * @description 获取报餐的餐段
  */
  export const reportMealGetMealType = data => request.post("/booking/report_meal/get_meal_type", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 获取能报餐的组织
   */
  export const getReportMealStall = data => request.post("/booking/report_meal/get_report_meal_stall", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-添加购物车
   */
  export const reportShopcardAdd = data => request.post("/booking/shopcard/report_add", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-删除购物车菜品
   */
  export const reportShopcardDelete = data => request.post("/booking/shopcard/report_delete", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-清空全部购物车
   */
  export const reportShopcardClean = data => request.post("/booking/shopcard/report_clean", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-购物车列表
   */
  export const reportShopcardList = data => request.post("/booking/shopcard/report_meal_calc_list", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-删除某个餐段某个组织里购物车
   */
  export const reportMealOrgDelete = data => request.post("/booking/shopcard/report_meal_org_delete", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-报餐下单接口
   */
  export const reportMealOrderCreate = data => request.post("/booking/report_meal/report_meal_order_create", data)
  
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-报餐订单结算支付
   */
  export const reportMealOrderPay = data => request.post("/booking/report_meal/report_meal_order_pay", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-查看用户一个月的报餐日期
   */
  export const reportMealMonthOrder = data => request.post("/booking/report_meal/month_report_meal_order", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-查看报餐汇总订单
   */
  export const reportMealCollectionOrder = data => request.post("/booking/report_meal/collection_report_meal_order", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-查看用户一个月报餐订单详情
   */
  export const reportMealOrderDetailInfo = data => request.post("/booking/report_meal/month_report_meal_order_detail_info", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-报餐订单退款或取消订单
   */
  export const reportMealOrderRefund = data => request.post("/booking/report_meal/report_meal_order_refund", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-报餐某月内的每餐详情
   */
  export const reportMealOrderMonthDetail = data => request.post("/booking/report_meal/month_order_meal_detail_info", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐-获取今年的节假日
   */
  export const getApiYearHoliday = data => request.post("/booking/report_meal/get_year_holiday", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐订单结算金额计算
   */
  export const getApiReportOrderGetMoney = data => request.post("/booking/report_meal/report_order_get_money", data)
  
  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 报餐订单结算金额计算
   */
  export const getApiReportMealPackList = data => request.post("/booking/report_meal_pack/settings_list", data)

  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 餐包下单接口
   */
  export const getApiReportMealPackOrderCreate = data => request.post("/booking/report_meal_pack/report_meal_pack_order_create", data)

  

  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 餐包详情
   */
  export const getApiReportMealOrderInfoCount = data => request.post("/booking/report_meal/report_meal_order_info_count", data)

  
  

  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 餐包状态修改
   */
  export const getApiMealPackOrderDiningStatus = data => request.post("/booking/report_meal_pack/report_meal_pack_order_dining_status", data)

  
  

  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 用户餐包退费订单列表
   */
  export const getApiMealPackOrderRefundList = data => request.post("/booking/report_meal_pack/user_report_meal_pack_order_refund_list", data)

  
  

  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 用户餐包批量恢复就餐
   */
  export const getApiMealPackOrderResume = data => request.post("/booking/report_meal_pack/user_report_meal_pack_order_resume", data)


  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 用户餐包订单列表
   */
  export const getApiMealPackOrderList = data => request.post("/booking/report_meal_pack/user_order_report_meal_pack_list", data)


  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 用户餐包订单详情
   */
  export const getApiMealPackOrderDetail = data => request.post("/booking/report_meal_pack/user_order_report_meal_list", data)

  

  /**
   * @param { Object } data
   * @return { Promise } 
   * @description 用户餐包停餐申请列表
   */
  export const getApiMealPackOrderStopList = data => request.post("/booking/report_meal_pack/user_order_report_meal_stop_list", data)

  

  
  

  


  

  


 
 