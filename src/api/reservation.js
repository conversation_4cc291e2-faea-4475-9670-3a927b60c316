import request from '@/utils/request'


/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取点餐组织信息
 */

export const getApiBookingUserGetCanteenList = data => request.post("/booking/user/get_canteen_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 微信就餐人
 */
// export const getApiCanteenUserList = data => request.post("/booking/user/get_canteen_user_list", data)
export const getApiBookingUserGetCardUserList = data => request.post("/booking/user/get_card_user_list", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 预约订单退款或取消订单
 */

export const getApiUserReservationOrderRefund = data => request.post("/booking/reservation/reservation_order_refund", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 预约配置时间
 */

export const getApiUserReservationMealType= data => request.post("booking/reservation/get_meal_type", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description  获取取餐方式
 */

export const getApiTakeMealTypeList= data => request.post("booking/user/get_take_meal_type_list", data)

// 获取取餐柜地址和余量
export const apiGetCupboardAddress= data => request.post("booking/reservation/get_cupboard_address", data)

// 预约批量取消控制
export const getApiUserReservationSettings= data => request.post("booking/reservation/get_user_reservation_settings", data)

// 报餐批量取消控制
export const getApiUserReportMealSettings= data => request.post("booking/report_meal/get_user_report_meal_settings", data)

// 获取地址详情
export const getApiGetAddrDetails= data => request.post("booking/reservation/get_addr_details_by_addr_id", data)

// 地址列表
export const getApiGetAddrList= data => request.post("booking/reservation/filter_address", data)

// 获取档口预约点餐限制相关的参数
export const getApiStallLimitData= data => request.post("booking/reservation/get_reservation_stall_limit_data", data)

// 是否判断取餐柜已满
export const getApiCheckCupboardNum= data => request.post("booking/reservation/check_cupboard_num", data)

// 手续费
export const getApiCommissionChargeFee= data => request.post("booking/user/get_commission_charge_fee", data)

// 预约订单结算金额计算
export const getApiReservationOrderGetMoney= data => request.post("booking/reservation/reservation_order_get_money", data)

// 预约订单结算金额计算(所有开启的钱包类型)
export const getApiReservationOrderGetMoneyAll = data => request.post("booking/reservation/reservation_order_get_money_all", data)