import request from '@/utils/request'

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 菜单管理菜谱意向菜品
 */

export const getApiBookingIntentFoodOrgList = data => request.post("/booking/intent_food/org_list", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 菜谱意向菜品列表
 */

export const getApiBookingIntentFoodList = data => request.post("/booking/intent_food/list", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 菜品点赞
 */

export const getApiBookingIntentFoodLikeFood = data => request.post("/booking/intent_food/like_food", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 菜品点赞
 */

export const getApiBookingIntentFoodCancelLikeFood = data => request.post("/booking/intent_food/cancel_like_food", data)

/**
 * @param { Object } data
 * @return { Promise } 
 * @description 查食材详情
 */

export const getApiBookingHealthyIngredientDetails = data => request.post("/booking/healthy/ingredient_details", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 食材
 */

export const getApiFindIngredient = data =>
  request.post('/booking/healthy/find_ingredient', data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 查食材二级分类
 */

export const getApiFindIngredientSort = data =>
  request.post('/booking/healthy/find_ingredient_sort', data)
