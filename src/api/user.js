import request from '@/utils/request'

// 获取选择钱包
export const getApiRechargeWalletList = data => request.post("/booking/recharge/wallet_list",data)

// 获取充值中心数据
export const getApiRechargeGetSettings = data => request.post("/booking/recharge/get_settings",data)

// 获取充值中心数据,302需要跳农行缴费
export const getApiRechargeGetSettingsV2 = data => request.post("/booking/recharge/get_settings_v2",data)

// 获取充值手续费金额
export const getApiRechargeRateFee = data => request.post("/booking/recharge/get_recharge_rate_fee",data)

// 获取创建订单
export const getApiRechargeOrderCreate = data => request.post("/booking/recharge/order_create",data)

// 充值订单查询
export const getApiRechargeOrderQuery = data => request.post("/booking/recharge/order_query",data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 查询用户信息
 */
export const apiQueryUserinfo = data => request.post("/booking/login/query_userinfo", data)

/**
 * @param { Object } data
 * @return { Promise }
 * @description 查看汇总订单
 */
export const apiCollectionReservationOrder = data => request.post("/booking/reservation/collection_reservation_order", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 移动端用户创建用户
 */
export const apiCreateCardInfo = data => request.post("/booking/user/create_card_info", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取项目点用户电子码
 */
export const apiGenerateEaccountCode = data => request.post("/booking/user/generate_eaccount_code", data)


/**
 * @param { Object } data
 * @return { Promise }
 * @description 获取食堂下的档口
 */
export const apiGetReservationStallFood = data => request.post("/booking/reservation/get_reservation_stall_food", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取微信用户的基本信息
 */
export const apiGetWechatUserInfo = data => request.post("/booking/user/get_wechat_user_info", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 查看用户一个月每天预约大概图
 */
export const apiMonthReservationOrder = data => request.post("/booking/reservation/month_reservation_order", data)
/**
 * @param { Object }
 * @return { Promise }
 * @description 查看用户一个月预约订单详情
 */
export const apiMonthReservationOrderDetailInfo = data => request.post("/booking/reservation/month_reservation_order_detail_info", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取项目点
 */
export const apiProjectList = data => request.post("/booking/user/project_list", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 预约下单接口
 */

export const apiReservationOrderCreate = data => request.post("/booking/reservation/reservation_order_create", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取点餐组织信息
 */
export const apiGetCanteenList = data => request.post("/booking/user/get_canteen_list", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取项目点用户电子码
 */
export const getApiGenerateEaccountCode = data => request.post("/booking/user/generate_eaccount_code", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取支付方式列表
 */
export const apiGetPayMethodList = data => request.post("/booking/user/get_pay_method_list", data)


// /**
//  * @param { Object }
//  * @return { Promise }
//  * @description 个人中心接口
//  */
// export const GetMemberInfo = data => request.post("booking/member_user/get_member_info", data)


/**
 * @param { Object }
 * @return { Promise }
 * @description 获取支付方式列表
 */
export const apiReservationGetOrderPay = data => request.post("/booking/reservation/reservation_order_pay", data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 获取钱包列表
 */
export const getApiUserWalletList = data => request.post("/booking/user/get_user_wallet_list", data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 获取上传token
 */
export const getApiUploadFileGetOssToken = data => request.post("/booking/upload_file/get_oss_token", data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 获取上传图片
 */
export const getApiUploadUserFace = data => request.post("/booking/user/upload_user_face", data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 是否启用人脸支付
 */
export const getApiUserSwitchFacepay = data => request.post("/booking/user/switch_facepay", data)

// 获取所有项目点下的用户信息
export const apiGetUserInfoList = data => request.post("/booking/user/get_user_info_list", data)

// 用户注销记录
export const getApiLogoutRecord = data => request.post("/booking/user/logout_record", data)

// 用户注销申请
export const getApiAddLogoutRecord = data => request.post("/booking/user/add_logout_record", data)

// 撤销注销用户
export const getApiCancelLogoutRecord = data => request.post("/booking/user/cancel_logout_record", data)

// 卡挂失
export const getApiCardLoss = data => request.post("/booking/user/card_loss", data)

// 解绑项目点
export const getApiCardUnbind = data => request.post("/booking/user/card_unbind", data)

// 获取扫码支付结果
export const queryOrderInfo = data => request.post("/booking/user_order/get_order_info",data)

// 取消挂失
export const getApicardEnable = data => request.post("/booking/user/card_enable",data)

// 搜索项目点用户
export const searchCompanyUser = data => request.post("/booking/tools/search_company_user",data)

// 修改项目点人脸图片
export const changeCompanyUserFace = data => request.post("/booking/tools/change_company_user_face",data)

// 修改密码
export const apiChangePassword = data => request.post("/booking/user/card_change_password",data)

// 删除人脸
export const apiDeleteUserFace = data => request.post("/booking/user/delete_facetoken",data)
// 获取支付方式列表
export const apiBackgroundMarketingRechargeGetRechargePayinfoPost = data => request.post("/background_marketing/recharge/get_recharge_payinfo",data)

// 充值活动-获取自定义金额的服务费和赠送金额
export const apibookingRechargeGetRechargeRateDiscountPost = data => request.post("/booking/recharge/get_recharge_rate_discount",data)

// 设置用户地址
export const apiSetUserAddress = data => request.post("/booking/user/set_address",data)

// 获取用户地址
export const apiGetUserAddress = data => request.post("/booking/user/get_address",data)

/**
 * @param { Object }
 * @return { Promise }
 * @description 查询农行第三方用户信息
 */
 export const apiQueryAbcUserinfo = data => request.post("/booking/user/query_abc_user_face", data)
/**
 * @param { Object }
 * @return { Promise }
 * @description 删除农行第三方用户人脸信息
 */
 export const apiDeleteAbcFacetoken = data => request.post("/booking/user/delete_abc_facetoken", data)

 /**
 * @param { Object }
 * @return { Promise }
 * @description 组合支付接口
 */
export const apiReservationOrderCombinePay = data => request.post("/booking/reservation/reservation_order_combine_pay", data)

 /**
 * @param { Object }
 * @return { Promise }
 * @description 获取付款码二维码有效时长
 */
 export const apiGetPaymentQrcodeRefresh = data => request.post("/booking/user/get_payment_qrcode_refresh", data)
