import request from '@/utils/request'

// 扫码自注册
/**
* ['移动端 自注册']
* @param {{page:number, page_size:number, update_cache:boolean, start_date:string, end_date:string, phone:string, name:string, deal_status:string, approve_status:string, company_id:number}} param page Page,page_size Page size,update_cache 刷新缓存,start_date 开始时间,end_date 结束时间,phone 手机号,name 用户姓名,deal_status 处理状态,approve_status 审批状态,company_id 项目点ID
* @returns {{code:number, msg:string, data:H5ListApproveOrderVisitorRspSer}} - rsp
*/
export const apiBookingScanRegisterPost = (param) => request.post('/booking/approve_register/scan_register', param)

/**
* ['移动端 自注册']
* booking.approve_register.approve_list 审批详情
* @param {{page:number, page_size:number, update_cache:boolean, start_date:string, end_date:string, phone:string, name:string, deal_status:string, approve_status:string, company_id:number}} param page Page,page_size Page size,update_cache 刷新缓存,start_date 开始时间,end_date 结束时间,phone 手机号,name 用户姓名,deal_status 处理状态,approve_status 审批状态,company_id 项目点ID
* @returns {{code:number, msg:string, data:H5ListApproveOrderVisitorRspSer}} - rsp
*/
export const apiBookingApproveRegisterListPost = (param) => request.post('/booking/approve_register/approve_list', param)

/**
* ['移动端 自注册']
* booking.approve_register.get_approve_register_info 没有绑定卡信息则获取自注册审批的信息
* @param {{company_id:number}} param company_id 项目点id
* @returns {{code:number, msg:string, data:ApproveRegisterInfo}} - rsp
*/
export const apiBookingApproveRegisterGetInfoPost = (param) => request.post('/booking/approve_register/get_approve_register_info', param)

/**
* ['移动端 自注册']
* booking.approve_register.revoke_approve_register 自注册撤销
* @param {{id:number, company_id:number}} param id 审批id,company_id 项目点ID
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingApproveRegisterRevokePost = (param) => request.post('/booking/approve_register/revoke_approve_register', param)

/**
* ['移动端 获取身份证号配置']
* booking.approve_register.get_allow_register 
* @param {{id:number, company_id:number}} param id 
* @returns {{code:number, msg:string, data:Empty}} - rsp
*/
export const apiBookingApproveRegisterGetAllowRegisterPost = (param) => request.post('/booking/approve_register/get_allow_register', param)

/**
* ['移动端 获取部门列表']
* booking.approve_register.get_register_department_list 获取部门列表
* @param {{company_id:number}} param company_id 项目点ID
* @returns {{code:number, msg:string, data:Array}} - rsp
*/
export const apiBookingApproveRegisterGetDepartmentListPost = (param) => request.post('/booking/approve_register/get_register_department_list', param)


